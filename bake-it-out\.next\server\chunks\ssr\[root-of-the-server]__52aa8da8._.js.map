{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/contexts/LanguageContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect } from 'react'\n\ntype Language = 'en' | 'cs'\n\ninterface LanguageContextType {\n  language: Language\n  setLanguage: (lang: Language) => void\n  t: (key: string, params?: Record<string, string>) => string\n}\n\nconst LanguageContext = createContext<LanguageContextType | undefined>(undefined)\n\n// Comprehensive translations object\nconst translations = {\n  en: {\n    // Main game\n    'game.title': 'Bake It Out',\n    'game.subtitle': 'Master the art of bakery management in this engaging multiplayer game. Complete orders, unlock recipes, automate your processes, and compete with friends!',\n    'game.play': '🎮 Start Playing',\n    'game.multiplayer': '👥 Multiplayer',\n    'game.english': '🇺🇸 English',\n    'game.czech': '🇨🇿 Čeština',\n    'game.home': '🏠 Home',\n    'game.close': '✕ Close',\n    'game.continue': '🚀 Continue Playing',\n\n    // Features\n    'features.manage.title': 'Manage Your Bakery',\n    'features.manage.description': 'Take orders, bake delicious goods, and serve happy customers',\n    'features.levelup.title': 'Level Up & Automate',\n    'features.levelup.description': 'Unlock new recipes, buy equipment, and automate your processes',\n    'features.multiplayer.title': 'Play Together',\n    'features.multiplayer.description': 'Cooperative and competitive multiplayer modes with friends',\n    'status.development': '🚧 Game in Development - Phase 5: Multilayer Support! 🚧',\n\n    // Game interface\n    'ui.level': 'Level {{level}}',\n    'ui.money': '${{amount}}',\n    'ui.experience': 'XP: {{current}}/{{max}}',\n    'ui.skillPoints': 'SP: {{points}}',\n    'ui.achievements': '🏆 Achievements',\n    'ui.skills': '🌟 Skills',\n    'ui.automation': '🤖 Automation',\n\n    // Kitchen\n    'kitchen.title': '🏪 Kitchen',\n    'kitchen.clickToUse': 'Click to use',\n    'kitchen.making': 'Making: {{recipe}}',\n    'kitchen.timeRemaining': 'Time: {{time}}',\n\n    // Inventory\n    'inventory.title': '📦 Inventory',\n    'inventory.quantity': 'Qty: {{qty}}',\n    'inventory.cost': '${{cost}} each',\n\n    // Orders\n    'orders.title': '📋 Orders',\n    'orders.newOrder': '+ New Order',\n    'orders.accept': 'Accept',\n    'orders.decline': 'Decline',\n    'orders.complete': 'Complete',\n    'orders.inProgress': 'In Progress',\n    'orders.timeLimit': 'Time: {{time}}',\n    'orders.reward': '${{amount}}',\n    'orders.customer': 'Customer: {{name}}',\n\n    // Quick Actions\n    'actions.title': '⚡ Quick Actions',\n    'actions.buyIngredients': '🛒 Buy Ingredients',\n    'actions.viewRecipes': '📖 View Recipes',\n    'actions.equipmentShop': '🔧 Equipment Shop',\n\n    // Modals\n    'modal.recipes.title': '📖 Recipe Book',\n    'modal.shop.title': '🛒 Ingredient Shop',\n    'modal.baking.title': '🔥 {{equipment}} - Select Recipe',\n    'modal.achievements.title': '🏆 Achievements',\n    'modal.skills.title': '🌟 Skill Tree',\n    'modal.automation.title': '🤖 Automation Control',\n    'modal.equipmentShop.title': '🏪 Equipment Shop',\n    'modal.settings.title': '⚙️ Settings',\n    'modal.bakeries.title': '🏪 Bakery Manager',\n    'modal.levelUp.title': 'Level Up!',\n    'modal.levelUp.subtitle': 'You reached Level {{level}}!',\n\n    // Recipe Modal\n    'recipes.all': 'All',\n    'recipes.cookies': 'Cookies',\n    'recipes.cakes': 'Cakes',\n    'recipes.bread': 'Bread',\n    'recipes.pastries': 'Pastries',\n    'recipes.ingredients': 'Ingredients:',\n    'recipes.difficulty': 'Difficulty:',\n    'recipes.time': 'Time:',\n    'recipes.canCraft': '✅ Can Craft',\n    'recipes.unlockLevel': 'Unlocked at Level {{level}}',\n    'recipes.noRecipes': 'No recipes available in this category.',\n    'recipes.levelUpToUnlock': 'Level up to unlock more recipes!',\n\n    // Shop Modal\n    'shop.currentStock': 'Current stock: {{quantity}}',\n    'shop.buy': 'Buy',\n    'shop.tooExpensive': 'Too Expensive',\n    'shop.tips.title': '💡 Shopping Tips',\n    'shop.tips.bulk': '• Buy ingredients in bulk to save time',\n    'shop.tips.stock': '• Keep an eye on your stock levels',\n    'shop.tips.rare': '• Some recipes require rare ingredients',\n    'shop.tips.prices': '• Prices may vary based on availability',\n\n    // Baking Modal\n    'baking.selectRecipe': 'Select Recipe',\n    'baking.noRecipes': 'No recipes available',\n    'baking.noIngredients': 'You don\\'t have enough ingredients to craft any recipes.',\n    'baking.buyIngredients': 'Buy Ingredients',\n    'baking.startBaking': '🔥 Start Baking',\n    'baking.instructions': '📋 Baking Instructions for {{recipe}}',\n    'baking.expectedReward': 'Expected reward: ${{amount}}',\n    'baking.makesSure': 'Make sure you have all ingredients before starting!',\n\n    // Achievements Modal\n    'achievements.completed': '{{completed}} of {{total}} achievements completed',\n    'achievements.overallProgress': 'Overall Progress',\n    'achievements.progress': 'Progress',\n    'achievements.reward': 'Reward:',\n    'achievements.noAchievements': 'No achievements in this category.',\n\n    // Skills Modal\n    'skills.availablePoints': 'Available Skill Points: {{points}}',\n    'skills.efficiency': 'Efficiency',\n    'skills.automation': 'Automation',\n    'skills.quality': 'Quality',\n    'skills.business': 'Business',\n    'skills.effects': 'Effects:',\n    'skills.requires': 'Requires: {{requirements}}',\n    'skills.requiresLevel': 'Requires Level {{level}}',\n    'skills.maxed': '✅ Maxed',\n    'skills.upgrade': '⬆️ Upgrade ({{cost}} SP)',\n    'skills.locked': '🔒 Locked',\n    'skills.noSkills': 'No skills in this category.',\n    'skills.tips.title': '💡 Skill Tips',\n    'skills.tips.earnPoints': '• Earn skill points by leveling up (1 point every 2 levels)',\n    'skills.tips.prerequisites': '• Some skills require other skills to be unlocked first',\n    'skills.tips.playstyle': '• Focus on skills that match your playstyle',\n    'skills.tips.efficiency': '• Efficiency skills help with resource management',\n\n    // Automation Modal\n    'automation.masterControl': '🎛️ Master Control',\n    'automation.enableAutomation': 'Enable Automation',\n    'automation.autoStart': 'Auto-start Equipment',\n    'automation.priorityMode': '🎯 Priority Mode',\n    'automation.efficiency': 'Efficiency (Orders First)',\n    'automation.profit': 'Profit (Highest Value)',\n    'automation.speed': 'Speed (Fastest Recipes)',\n    'automation.priorityDescription': 'How automation chooses what to bake',\n    'automation.performance': '⚡ Performance',\n    'automation.maxJobs': 'Max Concurrent Jobs: {{jobs}}',\n    'automation.safety': '🛡️ Safety',\n    'automation.stopWhenLow': 'Stop when ingredients below: {{threshold}}',\n    'automation.upgrades': '💡 Automation Upgrades',\n    'automation.upgradesDescription': 'Improve your automation efficiency, speed, and intelligence with these upgrades.',\n    'automation.purchase': 'Purchase',\n    'automation.noUpgrades': 'No upgrades available at your current level.',\n    'automation.levelUpForUpgrades': 'Level up to unlock more automation upgrades!',\n    'automation.automatedEquipment': 'Automated Equipment',\n    'automation.activeUpgrades': 'Active Upgrades',\n    'automation.automationStatus': 'Automation Status',\n    'automation.equipmentStatus': '🏭 Equipment Status',\n    'automation.running': 'Running',\n    'automation.idle': 'Idle',\n    'automation.noAutomatedEquipment': 'No automated equipment available.',\n    'automation.purchaseAutoEquipment': 'Purchase auto-equipment from the shop to get started!',\n\n    // Equipment Shop Modal\n    'equipmentShop.upgradeYourBakery': 'Upgrade your bakery with professional equipment',\n    'equipmentShop.basic': 'Basic',\n    'equipmentShop.automated': 'Automated',\n    'equipmentShop.advanced': 'Advanced',\n    'equipmentShop.efficiency': 'Efficiency: {{efficiency}}x',\n    'equipmentShop.automation': 'Automation:',\n    'equipmentShop.unlockLevel': 'Unlock Level: {{level}}',\n    'equipmentShop.purchase': '💰 Purchase',\n    'equipmentShop.noEquipment': 'No equipment available in this category.',\n    'equipmentShop.levelUpForEquipment': 'Level up to unlock more equipment!',\n    'equipmentShop.tips.title': '💡 Equipment Tips',\n    'equipmentShop.tips.automated': '• Automated equipment can run without your supervision',\n    'equipmentShop.tips.efficiency': '• Higher efficiency means faster production and better quality',\n    'equipmentShop.tips.conveyor': '• Conveyor belts connect equipment for seamless workflow',\n    'equipmentShop.tips.advanced': '• Advanced equipment unlocks at higher levels',\n\n    // Level Up Modal\n    'levelUp.levelRewards': '🎁 Level Rewards',\n    'levelUp.whatsNext': '💡 What\\'s Next?',\n    'levelUp.checkRecipes': '• Check out new recipes in your recipe book',\n    'levelUp.visitShop': '• Visit the shop for new equipment',\n    'levelUp.challengingOrders': '• Take on more challenging orders',\n    'levelUp.investSkills': '• Invest in skill upgrades',\n\n    // Settings Modal\n    'settings.title': '⚙️ Settings',\n    'settings.general': 'General',\n    'settings.audio': 'Audio',\n    'settings.graphics': 'Graphics',\n    'settings.save': 'Save & Data',\n    'settings.language': '🌍 Language',\n    'settings.gameplay': '🎮 Gameplay',\n    'settings.notifications': 'Enable Notifications',\n    'settings.tutorials': 'Show Tutorials',\n    'settings.animationSpeed': 'Animation Speed',\n    'settings.sound': 'Sound Effects',\n    'settings.music': 'Background Music',\n    'settings.quality': '🎨 Graphics Quality',\n    'settings.autoSave': '💾 Auto-Save',\n    'settings.enableAutoSave': 'Enable Auto-Save',\n    'settings.dataManagement': '📁 Data Management',\n    'settings.exportSave': '📤 Export Save',\n    'settings.importSave': '📥 Import Save',\n    'settings.cloudSync': '☁️ Cloud Sync',\n    'settings.cloudSyncDescription': 'Cloud sync allows you to save your progress online and play across multiple devices.',\n    'settings.comingSoon': 'Coming Soon',\n\n    // Bakery Manager Modal\n    'bakeries.title': '🏪 Bakery Manager',\n    'bakeries.subtitle': 'Manage your bakery empire',\n    'bakeries.owned': 'My Bakeries',\n    'bakeries.available': 'Available',\n    'bakeries.current': 'Current',\n    'bakeries.level': 'Level',\n    'bakeries.specialization': 'Specialization',\n    'bakeries.equipment': 'Equipment',\n    'bakeries.orders': 'Active Orders',\n    'bakeries.switchTo': 'Switch To',\n    'bakeries.noOwned': 'You don\\'t own any bakeries yet.',\n    'bakeries.purchase': '💰 Purchase',\n    'bakeries.tooExpensive': '💸 Too Expensive',\n    'bakeries.allOwned': 'You own all available bakeries!',\n    'bakeries.tips': '💡 Bakery Tips',\n    'bakeries.tip1': 'Each bakery specializes in different products for bonus efficiency',\n    'bakeries.tip2': 'Switch between bakeries to manage multiple locations',\n    'bakeries.tip3': 'Specialized bakeries attract customers looking for specific items',\n    'bakeries.tip4': 'Upgrade each bakery independently for maximum profit',\n\n    // Notifications\n    'notifications.orderAccepted': 'Order Accepted',\n    'notifications.orderAcceptedMessage': 'You have accepted a new order!',\n    'notifications.orderCompleted': 'Order Completed!',\n    'notifications.orderCompletedMessage': 'You earned ${{reward}} and gained experience!',\n    'notifications.orderDeclined': 'Order Declined',\n    'notifications.orderDeclinedMessage': 'Order has been removed from your queue.',\n    'notifications.bakeryPurchased': 'Bakery Purchased!',\n    'notifications.bakeryPurchasedMessage': 'You now own {{name}}!',\n    'notifications.bakerySwitched': 'Bakery Switched',\n    'notifications.bakerySwitchedMessage': 'Switched to {{name}}',\n\n    // Common buttons and actions\n    'common.accept': 'Accept',\n    'common.decline': 'Decline',\n    'common.complete': 'Complete',\n    'common.purchase': 'Purchase',\n    'common.upgrade': 'Upgrade',\n    'common.cancel': 'Cancel',\n    'common.confirm': 'Confirm',\n    'common.save': 'Save',\n    'common.load': 'Load',\n    'common.delete': 'Delete',\n    'common.edit': 'Edit',\n    'common.back': 'Back',\n    'common.next': 'Next',\n    'common.previous': 'Previous',\n    'common.yes': 'Yes',\n    'common.no': 'No'\n  },\n  cs: {\n    // Main game\n    'game.title': 'Bake It Out',\n    'game.subtitle': 'Ovládněte umění řízení pekárny v této poutavé multiplayerové hře. Plňte objednávky, odemykejte recepty, automatizujte procesy a soutěžte s přáteli!',\n    'game.play': '🎮 Začít hrát',\n    'game.multiplayer': '👥 Multiplayer',\n    'game.english': '🇺🇸 English',\n    'game.czech': '🇨🇿 Čeština',\n    'game.home': '🏠 Domů',\n    'game.close': '✕ Zavřít',\n    'game.continue': '🚀 Pokračovat ve hře',\n\n    // Features\n    'features.manage.title': 'Spravujte svou pekárnu',\n    'features.manage.description': 'Přijímejte objednávky, pečte lahodné výrobky a obsluhujte spokojené zákazníky',\n    'features.levelup.title': 'Postupujte a automatizujte',\n    'features.levelup.description': 'Odemykejte nové recepty, kupujte vybavení a automatizujte své procesy',\n    'features.multiplayer.title': 'Hrajte společně',\n    'features.multiplayer.description': 'Kooperativní a soutěžní multiplayerové režimy s přáteli',\n    'status.development': '🚧 Hra ve vývoji - Fáze 5: Vícevrstvá podpora! 🚧',\n\n    // Game interface\n    'ui.level': 'Úroveň {{level}}',\n    'ui.money': '{{amount}} Kč',\n    'ui.experience': 'XP: {{current}}/{{max}}',\n    'ui.skillPoints': 'SP: {{points}}',\n    'ui.achievements': '🏆 Úspěchy',\n    'ui.skills': '🌟 Dovednosti',\n    'ui.automation': '🤖 Automatizace',\n\n    // Kitchen\n    'kitchen.title': '🏪 Kuchyně',\n    'kitchen.clickToUse': 'Klikněte pro použití',\n    'kitchen.making': 'Připravuje: {{recipe}}',\n    'kitchen.timeRemaining': 'Čas: {{time}}',\n\n    // Inventory\n    'inventory.title': '📦 Sklad',\n    'inventory.quantity': 'Množství: {{qty}}',\n    'inventory.cost': '{{cost}} Kč za kus',\n\n    // Orders\n    'orders.title': '📋 Objednávky',\n    'orders.newOrder': '+ Nová objednávka',\n    'orders.accept': 'Přijmout',\n    'orders.decline': 'Odmítnout',\n    'orders.complete': 'Dokončit',\n    'orders.inProgress': 'Probíhá',\n    'orders.timeLimit': 'Čas: {{time}}',\n    'orders.reward': '{{amount}} Kč',\n    'orders.customer': 'Zákazník: {{name}}',\n\n    // Quick Actions\n    'actions.title': '⚡ Rychlé akce',\n    'actions.buyIngredients': '🛒 Koupit suroviny',\n    'actions.viewRecipes': '📖 Zobrazit recepty',\n    'actions.equipmentShop': '🔧 Obchod s vybavením',\n\n    // Modals\n    'modal.recipes.title': '📖 Kniha receptů',\n    'modal.shop.title': '🛒 Obchod se surovinami',\n    'modal.baking.title': '🔥 {{equipment}} - Vyberte recept',\n    'modal.achievements.title': '🏆 Úspěchy',\n    'modal.skills.title': '🌟 Strom dovedností',\n    'modal.automation.title': '🤖 Ovládání automatizace',\n    'modal.equipmentShop.title': '🏪 Obchod s vybavením',\n    'modal.settings.title': '⚙️ Nastavení',\n    'modal.bakeries.title': '🏪 Správce pekáren',\n    'modal.levelUp.title': 'Postup na vyšší úroveň!',\n    'modal.levelUp.subtitle': 'Dosáhli jste úrovně {{level}}!',\n\n    // Recipe Modal\n    'recipes.all': 'Vše',\n    'recipes.cookies': 'Sušenky',\n    'recipes.cakes': 'Dorty',\n    'recipes.bread': 'Chléb',\n    'recipes.pastries': 'Pečivo',\n    'recipes.ingredients': 'Suroviny:',\n    'recipes.difficulty': 'Obtížnost:',\n    'recipes.time': 'Čas:',\n    'recipes.canCraft': '✅ Lze vyrobit',\n    'recipes.unlockLevel': 'Odemčeno na úrovni {{level}}',\n    'recipes.noRecipes': 'V této kategorii nejsou k dispozici žádné recepty.',\n    'recipes.levelUpToUnlock': 'Postupte na vyšší úroveň pro odemčení dalších receptů!',\n\n    // Shop Modal\n    'shop.currentStock': 'Aktuální zásoba: {{quantity}}',\n    'shop.buy': 'Koupit',\n    'shop.tooExpensive': 'Příliš drahé',\n    'shop.tips.title': '💡 Tipy pro nakupování',\n    'shop.tips.bulk': '• Kupujte suroviny ve velkém množství pro úsporu času',\n    'shop.tips.stock': '• Sledujte úroveň svých zásob',\n    'shop.tips.rare': '• Některé recepty vyžadují vzácné suroviny',\n    'shop.tips.prices': '• Ceny se mohou lišit podle dostupnosti',\n\n    // Baking Modal\n    'baking.selectRecipe': 'Vyberte recept',\n    'baking.noRecipes': 'Žádné recepty k dispozici',\n    'baking.noIngredients': 'Nemáte dostatek surovin pro výrobu jakéhokoli receptu.',\n    'baking.buyIngredients': 'Koupit suroviny',\n    'baking.startBaking': '🔥 Začít péct',\n    'baking.instructions': '📋 Pokyny pro pečení {{recipe}}',\n    'baking.expectedReward': 'Očekávaná odměna: {{amount}} Kč',\n    'baking.makesSure': 'Ujistěte se, že máte všechny suroviny před začátkem!',\n\n    // Achievements Modal\n    'achievements.completed': '{{completed}} z {{total}} úspěchů dokončeno',\n    'achievements.overallProgress': 'Celkový pokrok',\n    'achievements.progress': 'Pokrok',\n    'achievements.reward': 'Odměna:',\n    'achievements.noAchievements': 'V této kategorii nejsou žádné úspěchy.',\n\n    // Skills Modal\n    'skills.availablePoints': 'Dostupné body dovedností: {{points}}',\n    'skills.efficiency': 'Efektivita',\n    'skills.automation': 'Automatizace',\n    'skills.quality': 'Kvalita',\n    'skills.business': 'Podnikání',\n    'skills.effects': 'Efekty:',\n    'skills.requires': 'Vyžaduje: {{requirements}}',\n    'skills.requiresLevel': 'Vyžaduje úroveň {{level}}',\n    'skills.maxed': '✅ Maximální',\n    'skills.upgrade': '⬆️ Vylepšit ({{cost}} SP)',\n    'skills.locked': '🔒 Uzamčeno',\n    'skills.noSkills': 'V této kategorii nejsou žádné dovednosti.',\n    'skills.tips.title': '💡 Tipy pro dovednosti',\n    'skills.tips.earnPoints': '• Získávejte body dovedností postupem na vyšší úroveň (1 bod každé 2 úrovně)',\n    'skills.tips.prerequisites': '• Některé dovednosti vyžadují nejprve odemčení jiných dovedností',\n    'skills.tips.playstyle': '• Zaměřte se na dovednosti, které odpovídají vašemu stylu hry',\n    'skills.tips.efficiency': '• Dovednosti efektivity pomáhají se správou zdrojů',\n\n    // Automation Modal\n    'automation.masterControl': '🎛️ Hlavní ovládání',\n    'automation.enableAutomation': 'Povolit automatizaci',\n    'automation.autoStart': 'Automatické spuštění vybavení',\n    'automation.priorityMode': '🎯 Režim priority',\n    'automation.efficiency': 'Efektivita (objednávky první)',\n    'automation.profit': 'Zisk (nejvyšší hodnota)',\n    'automation.speed': 'Rychlost (nejrychlejší recepty)',\n    'automation.priorityDescription': 'Jak automatizace vybírá, co péct',\n    'automation.performance': '⚡ Výkon',\n    'automation.maxJobs': 'Max současných úloh: {{jobs}}',\n    'automation.safety': '🛡️ Bezpečnost',\n    'automation.stopWhenLow': 'Zastavit, když suroviny klesnou pod: {{threshold}}',\n    'automation.upgrades': '💡 Vylepšení automatizace',\n    'automation.upgradesDescription': 'Vylepšete efektivitu, rychlost a inteligenci vaší automatizace.',\n    'automation.purchase': 'Koupit',\n    'automation.noUpgrades': 'Na vaší současné úrovni nejsou k dispozici žádná vylepšení.',\n    'automation.levelUpForUpgrades': 'Postupte na vyšší úroveň pro odemčení dalších vylepšení automatizace!',\n    'automation.automatedEquipment': 'Automatizované vybavení',\n    'automation.activeUpgrades': 'Aktivní vylepšení',\n    'automation.automationStatus': 'Stav automatizace',\n    'automation.equipmentStatus': '🏭 Stav vybavení',\n    'automation.running': 'Běží',\n    'automation.idle': 'Nečinné',\n    'automation.noAutomatedEquipment': 'Žádné automatizované vybavení k dispozici.',\n    'automation.purchaseAutoEquipment': 'Kupte si auto-vybavení z obchodu pro začátek!',\n\n    // Equipment Shop Modal\n    'equipmentShop.upgradeYourBakery': 'Vylepšete svou pekárnu profesionálním vybavením',\n    'equipmentShop.basic': 'Základní',\n    'equipmentShop.automated': 'Automatizované',\n    'equipmentShop.advanced': 'Pokročilé',\n    'equipmentShop.efficiency': 'Efektivita: {{efficiency}}x',\n    'equipmentShop.automation': 'Automatizace:',\n    'equipmentShop.unlockLevel': 'Úroveň odemčení: {{level}}',\n    'equipmentShop.purchase': '💰 Koupit',\n    'equipmentShop.noEquipment': 'V této kategorii není k dispozici žádné vybavení.',\n    'equipmentShop.levelUpForEquipment': 'Postupte na vyšší úroveň pro odemčení dalšího vybavení!',\n    'equipmentShop.tips.title': '💡 Tipy pro vybavení',\n    'equipmentShop.tips.automated': '• Automatizované vybavení může běžet bez vašeho dohledu',\n    'equipmentShop.tips.efficiency': '• Vyšší efektivita znamená rychlejší výrobu a lepší kvalitu',\n    'equipmentShop.tips.conveyor': '• Dopravní pásy spojují vybavení pro bezproblémový pracovní tok',\n    'equipmentShop.tips.advanced': '• Pokročilé vybavení se odemyká na vyšších úrovních',\n\n    // Level Up Modal\n    'levelUp.levelRewards': '🎁 Odměny za úroveň',\n    'levelUp.whatsNext': '💡 Co dál?',\n    'levelUp.checkRecipes': '• Podívejte se na nové recepty ve své knize receptů',\n    'levelUp.visitShop': '• Navštivte obchod pro nové vybavení',\n    'levelUp.challengingOrders': '• Přijměte náročnější objednávky',\n    'levelUp.investSkills': '• Investujte do vylepšení dovedností',\n\n    // Settings Modal\n    'settings.title': '⚙️ Nastavení',\n    'settings.general': 'Obecné',\n    'settings.audio': 'Zvuk',\n    'settings.graphics': 'Grafika',\n    'settings.save': 'Uložení a data',\n    'settings.language': '🌍 Jazyk',\n    'settings.gameplay': '🎮 Hratelnost',\n    'settings.notifications': 'Povolit oznámení',\n    'settings.tutorials': 'Zobrazit návody',\n    'settings.animationSpeed': 'Rychlost animace',\n    'settings.sound': 'Zvukové efekty',\n    'settings.music': 'Hudba na pozadí',\n    'settings.quality': '🎨 Kvalita grafiky',\n    'settings.autoSave': '💾 Automatické ukládání',\n    'settings.enableAutoSave': 'Povolit automatické ukládání',\n    'settings.dataManagement': '📁 Správa dat',\n    'settings.exportSave': '📤 Exportovat uložení',\n    'settings.importSave': '📥 Importovat uložení',\n    'settings.cloudSync': '☁️ Cloudová synchronizace',\n    'settings.cloudSyncDescription': 'Cloudová synchronizace vám umožňuje uložit pokrok online a hrát na více zařízeních.',\n    'settings.comingSoon': 'Již brzy',\n\n    // Bakery Manager Modal\n    'bakeries.title': '🏪 Správce pekáren',\n    'bakeries.subtitle': 'Spravujte své pekárenské impérium',\n    'bakeries.owned': 'Moje pekárny',\n    'bakeries.available': 'Dostupné',\n    'bakeries.current': 'Aktuální',\n    'bakeries.level': 'Úroveň',\n    'bakeries.specialization': 'Specializace',\n    'bakeries.equipment': 'Vybavení',\n    'bakeries.orders': 'Aktivní objednávky',\n    'bakeries.switchTo': 'Přepnout na',\n    'bakeries.noOwned': 'Ještě nevlastníte žádné pekárny.',\n    'bakeries.purchase': '💰 Koupit',\n    'bakeries.tooExpensive': '💸 Příliš drahé',\n    'bakeries.allOwned': 'Vlastníte všechny dostupné pekárny!',\n    'bakeries.tips': '💡 Tipy pro pekárny',\n    'bakeries.tip1': 'Každá pekárna se specializuje na různé produkty pro bonusovou efektivitu',\n    'bakeries.tip2': 'Přepínejte mezi pekárnami pro správu více lokalit',\n    'bakeries.tip3': 'Specializované pekárny přitahují zákazníky hledající konkrétní položky',\n    'bakeries.tip4': 'Vylepšujte každou pekárnu nezávisle pro maximální zisk',\n\n    // Notifications\n    'notifications.orderAccepted': 'Objednávka přijata',\n    'notifications.orderAcceptedMessage': 'Přijali jste novou objednávku!',\n    'notifications.orderCompleted': 'Objednávka dokončena!',\n    'notifications.orderCompletedMessage': 'Získali jste {{reward}} Kč a zkušenosti!',\n    'notifications.orderDeclined': 'Objednávka odmítnuta',\n    'notifications.orderDeclinedMessage': 'Objednávka byla odstraněna z vaší fronty.',\n    'notifications.bakeryPurchased': 'Pekárna zakoupena!',\n    'notifications.bakeryPurchasedMessage': 'Nyní vlastníte {{name}}!',\n    'notifications.bakerySwitched': 'Pekárna přepnuta',\n    'notifications.bakerySwitchedMessage': 'Přepnuto na {{name}}',\n\n    // Common buttons and actions\n    'common.accept': 'Přijmout',\n    'common.decline': 'Odmítnout',\n    'common.complete': 'Dokončit',\n    'common.purchase': 'Koupit',\n    'common.upgrade': 'Vylepšit',\n    'common.cancel': 'Zrušit',\n    'common.confirm': 'Potvrdit',\n    'common.save': 'Uložit',\n    'common.load': 'Načíst',\n    'common.delete': 'Smazat',\n    'common.edit': 'Upravit',\n    'common.back': 'Zpět',\n    'common.next': 'Další',\n    'common.previous': 'Předchozí',\n    'common.yes': 'Ano',\n    'common.no': 'Ne'\n  }\n}\n\nexport function LanguageProvider({ children }: { children: React.ReactNode }) {\n  const [language, setLanguage] = useState<Language>('en')\n\n  useEffect(() => {\n    // Load language from localStorage on client side\n    const savedLanguage = localStorage.getItem('language') as Language\n    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'cs')) {\n      setLanguage(savedLanguage)\n    }\n  }, [])\n\n  const handleSetLanguage = (lang: Language) => {\n    setLanguage(lang)\n    localStorage.setItem('language', lang)\n  }\n\n  const t = (key: string, params?: Record<string, string>) => {\n    let translation = translations[language][key as keyof typeof translations[typeof language]] || key\n    \n    if (params) {\n      Object.entries(params).forEach(([param, value]) => {\n        translation = translation.replace(`{{${param}}}`, value)\n      })\n    }\n    \n    return translation\n  }\n\n  return (\n    <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage, t }}>\n      {children}\n    </LanguageContext.Provider>\n  )\n}\n\nexport function useLanguage() {\n  const context = useContext(LanguageContext)\n  if (context === undefined) {\n    throw new Error('useLanguage must be used within a LanguageProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAYA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAEvE,oCAAoC;AACpC,MAAM,eAAe;IACnB,IAAI;QACF,YAAY;QACZ,cAAc;QACd,iBAAiB;QACjB,aAAa;QACb,oBAAoB;QACpB,gBAAgB;QAChB,cAAc;QACd,aAAa;QACb,cAAc;QACd,iBAAiB;QAEjB,WAAW;QACX,yBAAyB;QACzB,+BAA+B;QAC/B,0BAA0B;QAC1B,gCAAgC;QAChC,8BAA8B;QAC9B,oCAAoC;QACpC,sBAAsB;QAEtB,iBAAiB;QACjB,YAAY;QACZ,YAAY;QACZ,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,aAAa;QACb,iBAAiB;QAEjB,UAAU;QACV,iBAAiB;QACjB,sBAAsB;QACtB,kBAAkB;QAClB,yBAAyB;QAEzB,YAAY;QACZ,mBAAmB;QACnB,sBAAsB;QACtB,kBAAkB;QAElB,SAAS;QACT,gBAAgB;QAChB,mBAAmB;QACnB,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,qBAAqB;QACrB,oBAAoB;QACpB,iBAAiB;QACjB,mBAAmB;QAEnB,gBAAgB;QAChB,iBAAiB;QACjB,0BAA0B;QAC1B,uBAAuB;QACvB,yBAAyB;QAEzB,SAAS;QACT,uBAAuB;QACvB,oBAAoB;QACpB,sBAAsB;QACtB,4BAA4B;QAC5B,sBAAsB;QACtB,0BAA0B;QAC1B,6BAA6B;QAC7B,wBAAwB;QACxB,wBAAwB;QACxB,uBAAuB;QACvB,0BAA0B;QAE1B,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,iBAAiB;QACjB,iBAAiB;QACjB,oBAAoB;QACpB,uBAAuB;QACvB,sBAAsB;QACtB,gBAAgB;QAChB,oBAAoB;QACpB,uBAAuB;QACvB,qBAAqB;QACrB,2BAA2B;QAE3B,aAAa;QACb,qBAAqB;QACrB,YAAY;QACZ,qBAAqB;QACrB,mBAAmB;QACnB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,oBAAoB;QAEpB,eAAe;QACf,uBAAuB;QACvB,oBAAoB;QACpB,wBAAwB;QACxB,yBAAyB;QACzB,sBAAsB;QACtB,uBAAuB;QACvB,yBAAyB;QACzB,oBAAoB;QAEpB,qBAAqB;QACrB,0BAA0B;QAC1B,gCAAgC;QAChC,yBAAyB;QACzB,uBAAuB;QACvB,+BAA+B;QAE/B,eAAe;QACf,0BAA0B;QAC1B,qBAAqB;QACrB,qBAAqB;QACrB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,mBAAmB;QACnB,wBAAwB;QACxB,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QACjB,mBAAmB;QACnB,qBAAqB;QACrB,0BAA0B;QAC1B,6BAA6B;QAC7B,yBAAyB;QACzB,0BAA0B;QAE1B,mBAAmB;QACnB,4BAA4B;QAC5B,+BAA+B;QAC/B,wBAAwB;QACxB,2BAA2B;QAC3B,yBAAyB;QACzB,qBAAqB;QACrB,oBAAoB;QACpB,kCAAkC;QAClC,0BAA0B;QAC1B,sBAAsB;QACtB,qBAAqB;QACrB,0BAA0B;QAC1B,uBAAuB;QACvB,kCAAkC;QAClC,uBAAuB;QACvB,yBAAyB;QACzB,iCAAiC;QACjC,iCAAiC;QACjC,6BAA6B;QAC7B,+BAA+B;QAC/B,8BAA8B;QAC9B,sBAAsB;QACtB,mBAAmB;QACnB,mCAAmC;QACnC,oCAAoC;QAEpC,uBAAuB;QACvB,mCAAmC;QACnC,uBAAuB;QACvB,2BAA2B;QAC3B,0BAA0B;QAC1B,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,0BAA0B;QAC1B,6BAA6B;QAC7B,qCAAqC;QACrC,4BAA4B;QAC5B,gCAAgC;QAChC,iCAAiC;QACjC,+BAA+B;QAC/B,+BAA+B;QAE/B,iBAAiB;QACjB,wBAAwB;QACxB,qBAAqB;QACrB,wBAAwB;QACxB,qBAAqB;QACrB,6BAA6B;QAC7B,wBAAwB;QAExB,iBAAiB;QACjB,kBAAkB;QAClB,oBAAoB;QACpB,kBAAkB;QAClB,qBAAqB;QACrB,iBAAiB;QACjB,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,sBAAsB;QACtB,2BAA2B;QAC3B,kBAAkB;QAClB,kBAAkB;QAClB,oBAAoB;QACpB,qBAAqB;QACrB,2BAA2B;QAC3B,2BAA2B;QAC3B,uBAAuB;QACvB,uBAAuB;QACvB,sBAAsB;QACtB,iCAAiC;QACjC,uBAAuB;QAEvB,uBAAuB;QACvB,kBAAkB;QAClB,qBAAqB;QACrB,kBAAkB;QAClB,sBAAsB;QACtB,oBAAoB;QACpB,kBAAkB;QAClB,2BAA2B;QAC3B,sBAAsB;QACtB,mBAAmB;QACnB,qBAAqB;QACrB,oBAAoB;QACpB,qBAAqB;QACrB,yBAAyB;QACzB,qBAAqB;QACrB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QAEjB,gBAAgB;QAChB,+BAA+B;QAC/B,sCAAsC;QACtC,gCAAgC;QAChC,uCAAuC;QACvC,+BAA+B;QAC/B,sCAAsC;QACtC,iCAAiC;QACjC,wCAAwC;QACxC,gCAAgC;QAChC,uCAAuC;QAEvC,6BAA6B;QAC7B,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,kBAAkB;QAClB,eAAe;QACf,eAAe;QACf,iBAAiB;QACjB,eAAe;QACf,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,cAAc;QACd,aAAa;IACf;IACA,IAAI;QACF,YAAY;QACZ,cAAc;QACd,iBAAiB;QACjB,aAAa;QACb,oBAAoB;QACpB,gBAAgB;QAChB,cAAc;QACd,aAAa;QACb,cAAc;QACd,iBAAiB;QAEjB,WAAW;QACX,yBAAyB;QACzB,+BAA+B;QAC/B,0BAA0B;QAC1B,gCAAgC;QAChC,8BAA8B;QAC9B,oCAAoC;QACpC,sBAAsB;QAEtB,iBAAiB;QACjB,YAAY;QACZ,YAAY;QACZ,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,aAAa;QACb,iBAAiB;QAEjB,UAAU;QACV,iBAAiB;QACjB,sBAAsB;QACtB,kBAAkB;QAClB,yBAAyB;QAEzB,YAAY;QACZ,mBAAmB;QACnB,sBAAsB;QACtB,kBAAkB;QAElB,SAAS;QACT,gBAAgB;QAChB,mBAAmB;QACnB,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,qBAAqB;QACrB,oBAAoB;QACpB,iBAAiB;QACjB,mBAAmB;QAEnB,gBAAgB;QAChB,iBAAiB;QACjB,0BAA0B;QAC1B,uBAAuB;QACvB,yBAAyB;QAEzB,SAAS;QACT,uBAAuB;QACvB,oBAAoB;QACpB,sBAAsB;QACtB,4BAA4B;QAC5B,sBAAsB;QACtB,0BAA0B;QAC1B,6BAA6B;QAC7B,wBAAwB;QACxB,wBAAwB;QACxB,uBAAuB;QACvB,0BAA0B;QAE1B,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,iBAAiB;QACjB,iBAAiB;QACjB,oBAAoB;QACpB,uBAAuB;QACvB,sBAAsB;QACtB,gBAAgB;QAChB,oBAAoB;QACpB,uBAAuB;QACvB,qBAAqB;QACrB,2BAA2B;QAE3B,aAAa;QACb,qBAAqB;QACrB,YAAY;QACZ,qBAAqB;QACrB,mBAAmB;QACnB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,oBAAoB;QAEpB,eAAe;QACf,uBAAuB;QACvB,oBAAoB;QACpB,wBAAwB;QACxB,yBAAyB;QACzB,sBAAsB;QACtB,uBAAuB;QACvB,yBAAyB;QACzB,oBAAoB;QAEpB,qBAAqB;QACrB,0BAA0B;QAC1B,gCAAgC;QAChC,yBAAyB;QACzB,uBAAuB;QACvB,+BAA+B;QAE/B,eAAe;QACf,0BAA0B;QAC1B,qBAAqB;QACrB,qBAAqB;QACrB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,mBAAmB;QACnB,wBAAwB;QACxB,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QACjB,mBAAmB;QACnB,qBAAqB;QACrB,0BAA0B;QAC1B,6BAA6B;QAC7B,yBAAyB;QACzB,0BAA0B;QAE1B,mBAAmB;QACnB,4BAA4B;QAC5B,+BAA+B;QAC/B,wBAAwB;QACxB,2BAA2B;QAC3B,yBAAyB;QACzB,qBAAqB;QACrB,oBAAoB;QACpB,kCAAkC;QAClC,0BAA0B;QAC1B,sBAAsB;QACtB,qBAAqB;QACrB,0BAA0B;QAC1B,uBAAuB;QACvB,kCAAkC;QAClC,uBAAuB;QACvB,yBAAyB;QACzB,iCAAiC;QACjC,iCAAiC;QACjC,6BAA6B;QAC7B,+BAA+B;QAC/B,8BAA8B;QAC9B,sBAAsB;QACtB,mBAAmB;QACnB,mCAAmC;QACnC,oCAAoC;QAEpC,uBAAuB;QACvB,mCAAmC;QACnC,uBAAuB;QACvB,2BAA2B;QAC3B,0BAA0B;QAC1B,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,0BAA0B;QAC1B,6BAA6B;QAC7B,qCAAqC;QACrC,4BAA4B;QAC5B,gCAAgC;QAChC,iCAAiC;QACjC,+BAA+B;QAC/B,+BAA+B;QAE/B,iBAAiB;QACjB,wBAAwB;QACxB,qBAAqB;QACrB,wBAAwB;QACxB,qBAAqB;QACrB,6BAA6B;QAC7B,wBAAwB;QAExB,iBAAiB;QACjB,kBAAkB;QAClB,oBAAoB;QACpB,kBAAkB;QAClB,qBAAqB;QACrB,iBAAiB;QACjB,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,sBAAsB;QACtB,2BAA2B;QAC3B,kBAAkB;QAClB,kBAAkB;QAClB,oBAAoB;QACpB,qBAAqB;QACrB,2BAA2B;QAC3B,2BAA2B;QAC3B,uBAAuB;QACvB,uBAAuB;QACvB,sBAAsB;QACtB,iCAAiC;QACjC,uBAAuB;QAEvB,uBAAuB;QACvB,kBAAkB;QAClB,qBAAqB;QACrB,kBAAkB;QAClB,sBAAsB;QACtB,oBAAoB;QACpB,kBAAkB;QAClB,2BAA2B;QAC3B,sBAAsB;QACtB,mBAAmB;QACnB,qBAAqB;QACrB,oBAAoB;QACpB,qBAAqB;QACrB,yBAAyB;QACzB,qBAAqB;QACrB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QAEjB,gBAAgB;QAChB,+BAA+B;QAC/B,sCAAsC;QACtC,gCAAgC;QAChC,uCAAuC;QACvC,+BAA+B;QAC/B,sCAAsC;QACtC,iCAAiC;QACjC,wCAAwC;QACxC,gCAAgC;QAChC,uCAAuC;QAEvC,6BAA6B;QAC7B,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,kBAAkB;QAClB,eAAe;QACf,eAAe;QACf,iBAAiB;QACjB,eAAe;QACf,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,cAAc;QACd,aAAa;IACf;AACF;AAEO,SAAS,iBAAiB,EAAE,QAAQ,EAAiC;IAC1E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iDAAiD;QACjD,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,IAAI,iBAAiB,CAAC,kBAAkB,QAAQ,kBAAkB,IAAI,GAAG;YACvE,YAAY;QACd;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,YAAY;QACZ,aAAa,OAAO,CAAC,YAAY;IACnC;IAEA,MAAM,IAAI,CAAC,KAAa;QACtB,IAAI,cAAc,YAAY,CAAC,SAAS,CAAC,IAAkD,IAAI;QAE/F,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,OAAO,MAAM;gBAC5C,cAAc,YAAY,OAAO,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;YACpD;QACF;QAEA,OAAO;IACT;IAEA,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;YAAU,aAAa;YAAmB;QAAE;kBAC5E;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 550, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 571, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}