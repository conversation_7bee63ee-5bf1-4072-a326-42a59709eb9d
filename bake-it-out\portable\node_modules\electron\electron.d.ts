// Type definitions for Electron 37.2.3
// Project: http://electronjs.org/
// Definitions by: The Electron Team <https://github.com/electron/electron>
// Definitions: https://github.com/electron/typescript-definitions

/// <reference types="node" />

type DOMEvent = Event;
type GlobalResponse = Response;
type GlobalRequest = Request;

declare namespace Electron {
  const NodeEventEmitter: typeof import('events').EventEmitter;

  type Accelerator = string;
  type Event<Params extends object = {}> = {
    preventDefault: () => void;
    readonly defaultPrevented: boolean;
  } & Params;

  interface App extends NodeJS.EventEmitter {

    // Docs: https://electronjs.org/docs/api/app

    /**
     * Emitted when Chrome's accessibility support changes. This event fires when
     * assistive technologies, such as screen readers, are enabled or disabled. See
     * https://www.chromium.org/developers/design-documents/accessibility for more
     * details.
     *
     * @platform darwin,win32
     */
    on(event: 'accessibility-support-changed', listener: (event: Event,
                                                          /**
                                                           * `true` when Chrome's accessibility support is enabled, `false` otherwise.
                                                           */
                                                          accessibilitySupportEnabled: boolean) => void): this;
    /**
     * @platform darwin,win32
     */
    off(event: 'accessibility-support-changed', listener: (event: Event,
                                                          /**
                                                           * `true` when Chrome's accessibility support is enabled, `false` otherwise.
                                                           */
                                                          accessibilitySupportEnabled: boolean) => void): this;
    /**
     * @platform darwin,win32
     */
    once(event: 'accessibility-support-changed', listener: (event: Event,
                                                          /**
                                                           * `true` when Chrome's accessibility support is enabled, `false` otherwise.
                                                           */
                                                          accessibilitySupportEnabled: boolean) => void): this;
    /**
     * @platform darwin,win32
     */
    addListener(event: 'accessibility-support-changed', listener: (event: Event,
                                                          /**
                                                           * `true` when Chrome's accessibility support is enabled, `false` otherwise.
                                                           */
                                                          accessibilitySupportEnabled: boolean) => void): this;
    /**
     * @platform darwin,win32
     */
    removeListener(event: 'accessibility-support-changed', listener: (event: Event,
                                                          /**
                                                           * `true` when Chrome's accessibility support is enabled, `false` otherwise.
                                                           */
                                                          accessibilitySupportEnabled: boolean) => void): this;
    /**
     * Emitted when the application is activated. Various actions can trigger this
     * event, such as launching the application for the first time, attempting to
     * re-launch the application when it's already running, or clicking on the
     * application's dock or taskbar icon.
     *
     * @platform darwin
     */
    on(event: 'activate', listener: (event: Event,
                                     hasVisibleWindows: boolean) => void): this;
    /**
     * @platform darwin
     */
    off(event: 'activate', listener: (event: Event,
                                     hasVisibleWindows: boolean) => void): this;
    /**
     * @platform darwin
     */
    once(event: 'activate', listener: (event: Event,
                                     hasVisibleWindows: boolean) => void): this;
    /**
     * @platform darwin
     */
    addListener(event: 'activate', listener: (event: Event,
                                     hasVisibleWindows: boolean) => void): this;
    /**
     * @platform darwin
     */
    removeListener(event: 'activate', listener: (event: Event,
                                     hasVisibleWindows: boolean) => void): this;
    /**
     * Emitted during Handoff after an activity from this device was successfully
     * resumed on another one.
     *
     * @platform darwin
     */
    on(event: 'activity-was-continued', listener: (event: Event,
                                                   /**
                                                    * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                    */
                                                   type: string,
                                                   /**
                                                    * Contains app-specific state stored by the activity.
                                                    */
                                                   userInfo: unknown) => void): this;
    /**
     * @platform darwin
     */
    off(event: 'activity-was-continued', listener: (event: Event,
                                                   /**
                                                    * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                    */
                                                   type: string,
                                                   /**
                                                    * Contains app-specific state stored by the activity.
                                                    */
                                                   userInfo: unknown) => void): this;
    /**
     * @platform darwin
     */
    once(event: 'activity-was-continued', listener: (event: Event,
                                                   /**
                                                    * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                    */
                                                   type: string,
                                                   /**
                                                    * Contains app-specific state stored by the activity.
                                                    */
                                                   userInfo: unknown) => void): this;
    /**
     * @platform darwin
     */
    addListener(event: 'activity-was-continued', listener: (event: Event,
                                                   /**
                                                    * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                    */
                                                   type: string,
                                                   /**
                                                    * Contains app-specific state stored by the activity.
                                                    */
                                                   userInfo: unknown) => void): this;
    /**
     * @platform darwin
     */
    removeListener(event: 'activity-was-continued', listener: (event: Event,
                                                   /**
                                                    * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                    */
                                                   type: string,
                                                   /**
                                                    * Contains app-specific state stored by the activity.
                                                    */
                                                   userInfo: unknown) => void): this;
    /**
     * Emitted before the application starts closing its windows. Calling
     * `event.preventDefault()` will prevent the default behavior, which is terminating
     * the application.
     *
     * > [!NOTE] If application quit was initiated by `autoUpdater.quitAndInstall()`,
     * then `before-quit` is emitted _after_ emitting `close` event on all windows and
     * closing them.
     *
     * > [!NOTE] On Windows, this event will not be emitted if the app is closed due to
     * a shutdown/restart of the system or a user logout.
     */
    on(event: 'before-quit', listener: (event: Event) => void): this;
    off(event: 'before-quit', listener: (event: Event) => void): this;
    once(event: 'before-quit', listener: (event: Event) => void): this;
    addListener(event: 'before-quit', listener: (event: Event) => void): this;
    removeListener(event: 'before-quit', listener: (event: Event) => void): this;
    /**
     * Emitted when a browserWindow gets blurred.
     */
    on(event: 'browser-window-blur', listener: (event: Event,
                                                window: BrowserWindow) => void): this;
    off(event: 'browser-window-blur', listener: (event: Event,
                                                window: BrowserWindow) => void): this;
    once(event: 'browser-window-blur', listener: (event: Event,
                                                window: BrowserWindow) => void): this;
    addListener(event: 'browser-window-blur', listener: (event: Event,
                                                window: BrowserWindow) => void): this;
    removeListener(event: 'browser-window-blur', listener: (event: Event,
                                                window: BrowserWindow) => void): this;
    /**
     * Emitted when a new browserWindow is created.
     */
    on(event: 'browser-window-created', listener: (event: Event,
                                                   window: BrowserWindow) => void): this;
    off(event: 'browser-window-created', listener: (event: Event,
                                                   window: BrowserWindow) => void): this;
    once(event: 'browser-window-created', listener: (event: Event,
                                                   window: BrowserWindow) => void): this;
    addListener(event: 'browser-window-created', listener: (event: Event,
                                                   window: BrowserWindow) => void): this;
    removeListener(event: 'browser-window-created', listener: (event: Event,
                                                   window: BrowserWindow) => void): this;
    /**
     * Emitted when a browserWindow gets focused.
     */
    on(event: 'browser-window-focus', listener: (event: Event,
                                                 window: BrowserWindow) => void): this;
    off(event: 'browser-window-focus', listener: (event: Event,
                                                 window: BrowserWindow) => void): this;
    once(event: 'browser-window-focus', listener: (event: Event,
                                                 window: BrowserWindow) => void): this;
    addListener(event: 'browser-window-focus', listener: (event: Event,
                                                 window: BrowserWindow) => void): this;
    removeListener(event: 'browser-window-focus', listener: (event: Event,
                                                 window: BrowserWindow) => void): this;
    /**
     * Emitted when failed to verify the `certificate` for `url`, to trust the
     * certificate you should prevent the default behavior with
     * `event.preventDefault()` and call `callback(true)`.
     */
    on(event: 'certificate-error', listener: (event: Event,
                                              webContents: WebContents,
                                              url: string,
                                              /**
                                               * The error code
                                               */
                                              error: string,
                                              certificate: Certificate,
                                              callback: (isTrusted: boolean) => void,
                                              isMainFrame: boolean) => void): this;
    off(event: 'certificate-error', listener: (event: Event,
                                              webContents: WebContents,
                                              url: string,
                                              /**
                                               * The error code
                                               */
                                              error: string,
                                              certificate: Certificate,
                                              callback: (isTrusted: boolean) => void,
                                              isMainFrame: boolean) => void): this;
    once(event: 'certificate-error', listener: (event: Event,
                                              webContents: WebContents,
                                              url: string,
                                              /**
                                               * The error code
                                               */
                                              error: string,
                                              certificate: Certificate,
                                              callback: (isTrusted: boolean) => void,
                                              isMainFrame: boolean) => void): this;
    addListener(event: 'certificate-error', listener: (event: Event,
                                              webContents: WebContents,
                                              url: string,
                                              /**
                                               * The error code
                                               */
                                              error: string,
                                              certificate: Certificate,
                                              callback: (isTrusted: boolean) => void,
                                              isMainFrame: boolean) => void): this;
    removeListener(event: 'certificate-error', listener: (event: Event,
                                              webContents: WebContents,
                                              url: string,
                                              /**
                                               * The error code
                                               */
                                              error: string,
                                              certificate: Certificate,
                                              callback: (isTrusted: boolean) => void,
                                              isMainFrame: boolean) => void): this;
    /**
     * Emitted when the child process unexpectedly disappears. This is normally because
     * it was crashed or killed. It does not include renderer processes.
     */
    on(event: 'child-process-gone', listener: (event: Event,
                                               details: Details) => void): this;
    off(event: 'child-process-gone', listener: (event: Event,
                                               details: Details) => void): this;
    once(event: 'child-process-gone', listener: (event: Event,
                                               details: Details) => void): this;
    addListener(event: 'child-process-gone', listener: (event: Event,
                                               details: Details) => void): this;
    removeListener(event: 'child-process-gone', listener: (event: Event,
                                               details: Details) => void): this;
    /**
     * Emitted during Handoff when an activity from a different device wants to be
     * resumed. You should call `event.preventDefault()` if you want to handle this
     * event.
     *
     * A user activity can be continued only in an app that has the same developer Team
     * ID as the activity's source app and that supports the activity's type. Supported
     * activity types are specified in the app's `Info.plist` under the
     * `NSUserActivityTypes` key.
     *
     * @platform darwin
     */
    on(event: 'continue-activity', listener: (event: Event,
                                              /**
                                               * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                               */
                                              type: string,
                                              /**
                                               * Contains app-specific state stored by the activity on another device.
                                               */
                                              userInfo: unknown,
                                              details: ContinueActivityDetails) => void): this;
    /**
     * @platform darwin
     */
    off(event: 'continue-activity', listener: (event: Event,
                                              /**
                                               * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                               */
                                              type: string,
                                              /**
                                               * Contains app-specific state stored by the activity on another device.
                                               */
                                              userInfo: unknown,
                                              details: ContinueActivityDetails) => void): this;
    /**
     * @platform darwin
     */
    once(event: 'continue-activity', listener: (event: Event,
                                              /**
                                               * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                               */
                                              type: string,
                                              /**
                                               * Contains app-specific state stored by the activity on another device.
                                               */
                                              userInfo: unknown,
                                              details: ContinueActivityDetails) => void): this;
    /**
     * @platform darwin
     */
    addListener(event: 'continue-activity', listener: (event: Event,
                                              /**
                                               * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                               */
                                              type: string,
                                              /**
                                               * Contains app-specific state stored by the activity on another device.
                                               */
                                              userInfo: unknown,
                                              details: ContinueActivityDetails) => void): this;
    /**
     * @platform darwin
     */
    removeListener(event: 'continue-activity', listener: (event: Event,
                                              /**
                                               * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                               */
                                              type: string,
                                              /**
                                               * Contains app-specific state stored by the activity on another device.
                                               */
                                              userInfo: unknown,
                                              details: ContinueActivityDetails) => void): this;
    /**
     * Emitted during Handoff when an activity from a different device fails to be
     * resumed.
     *
     * @platform darwin
     */
    on(event: 'continue-activity-error', listener: (event: Event,
                                                    /**
                                                     * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                     */
                                                    type: string,
                                                    /**
                                                     * A string with the error's localized description.
                                                     */
                                                    error: string) => void): this;
    /**
     * @platform darwin
     */
    off(event: 'continue-activity-error', listener: (event: Event,
                                                    /**
                                                     * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                     */
                                                    type: string,
                                                    /**
                                                     * A string with the error's localized description.
                                                     */
                                                    error: string) => void): this;
    /**
     * @platform darwin
     */
    once(event: 'continue-activity-error', listener: (event: Event,
                                                    /**
                                                     * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                     */
                                                    type: string,
                                                    /**
                                                     * A string with the error's localized description.
                                                     */
                                                    error: string) => void): this;
    /**
     * @platform darwin
     */
    addListener(event: 'continue-activity-error', listener: (event: Event,
                                                    /**
                                                     * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                     */
                                                    type: string,
                                                    /**
                                                     * A string with the error's localized description.
                                                     */
                                                    error: string) => void): this;
    /**
     * @platform darwin
     */
    removeListener(event: 'continue-activity-error', listener: (event: Event,
                                                    /**
                                                     * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                     */
                                                    type: string,
                                                    /**
                                                     * A string with the error's localized description.
                                                     */
                                                    error: string) => void): this;
    /**
     * Emitted when the application becomes active. This differs from the `activate`
     * event in that `did-become-active` is emitted every time the app becomes active,
     * not only when Dock icon is clicked or application is re-launched. It is also
     * emitted when a user switches to the app via the macOS App Switcher.
     *
     * @platform darwin
     */
    on(event: 'did-become-active', listener: (event: Event) => void): this;
    /**
     * @platform darwin
     */
    off(event: 'did-become-active', listener: (event: Event) => void): this;
    /**
     * @platform darwin
     */
    once(event: 'did-become-active', listener: (event: Event) => void): this;
    /**
     * @platform darwin
     */
    addListener(event: 'did-become-active', listener: (event: Event) => void): this;
    /**
     * @platform darwin
     */
    removeListener(event: 'did-become-active', listener: (event: Event) => void): this;
    /**
     * Emitted when the app is no longer active and doesn’t have focus. This can be
     * triggered, for example, by clicking on another application or by using the macOS
     * App Switcher to switch to another application.
     *
     * @platform darwin
     */
    on(event: 'did-resign-active', listener: (event: Event) => void): this;
    /**
     * @platform darwin
     */
    off(event: 'did-resign-active', listener: (event: Event) => void): this;
    /**
     * @platform darwin
     */
    once(event: 'did-resign-active', listener: (event: Event) => void): this;
    /**
     * @platform darwin
     */
    addListener(event: 'did-resign-active', listener: (event: Event) => void): this;
    /**
     * @platform darwin
     */
    removeListener(event: 'did-resign-active', listener: (event: Event) => void): this;
    /**
     * Emitted whenever there is a GPU info update.
     */
    on(event: 'gpu-info-update', listener: () => void): this;
    off(event: 'gpu-info-update', listener: () => void): this;
    once(event: 'gpu-info-update', listener: () => void): this;
    addListener(event: 'gpu-info-update', listener: () => void): this;
    removeListener(event: 'gpu-info-update', listener: () => void): this;
    /**
     * Emitted when `webContents` or Utility process wants to do basic auth.
     *
     * The default behavior is to cancel all authentications. To override this you
     * should prevent the default behavior with `event.preventDefault()` and call
     * `callback(username, password)` with the credentials.
     *
     * If `callback` is called without a username or password, the authentication
     * request will be cancelled and the authentication error will be returned to the
     * page.
     */
    on(event: 'login', listener: (event: Event,
                                  webContents: WebContents,
                                  authenticationResponseDetails: AuthenticationResponseDetails,
                                  authInfo: AuthInfo,
                                  callback: (username?: string, password?: string) => void) => void): this;
    off(event: 'login', listener: (event: Event,
                                  webContents: WebContents,
                                  authenticationResponseDetails: AuthenticationResponseDetails,
                                  authInfo: AuthInfo,
                                  callback: (username?: string, password?: string) => void) => void): this;
    once(event: 'login', listener: (event: Event,
                                  webContents: WebContents,
                                  authenticationResponseDetails: AuthenticationResponseDetails,
                                  authInfo: AuthInfo,
                                  callback: (username?: string, password?: string) => void) => void): this;
    addListener(event: 'login', listener: (event: Event,
                                  webContents: WebContents,
                                  authenticationResponseDetails: AuthenticationResponseDetails,
                                  authInfo: AuthInfo,
                                  callback: (username?: string, password?: string) => void) => void): this;
    removeListener(event: 'login', listener: (event: Event,
                                  webContents: WebContents,
                                  authenticationResponseDetails: AuthenticationResponseDetails,
                                  authInfo: AuthInfo,
                                  callback: (username?: string, password?: string) => void) => void): this;
    /**
     * Emitted when the user clicks the native macOS new tab button. The new tab button
     * is only visible if the current `BrowserWindow` has a `tabbingIdentifier`
     *
     * @platform darwin
     */
    on(event: 'new-window-for-tab', listener: (event: Event) => void): this;
    /**
     * @platform darwin
     */
    off(event: 'new-window-for-tab', listener: (event: Event) => void): this;
    /**
     * @platform darwin
     */
    once(event: 'new-window-for-tab', listener: (event: Event) => void): this;
    /**
     * @platform darwin
     */
    addListener(event: 'new-window-for-tab', listener: (event: Event) => void): this;
    /**
     * @platform darwin
     */
    removeListener(event: 'new-window-for-tab', listener: (event: Event) => void): this;
    /**
     * Emitted when the user wants to open a file with the application. The `open-file`
     * event is usually emitted when the application is already open and the OS wants
     * to reuse the application to open the file. `open-file` is also emitted when a
     * file is dropped onto the dock and the application is not yet running. Make sure
     * to listen for the `open-file` event very early in your application startup to
     * handle this case (even before the `ready` event is emitted).
     *
     * You should call `event.preventDefault()` if you want to handle this event.
     *
     * On Windows, you have to parse `process.argv` (in the main process) to get the
     * filepath.
     *
     * @platform darwin
     */
    on(event: 'open-file', listener: (event: Event,
                                      path: string) => void): this;
    /**
     * @platform darwin
     */
    off(event: 'open-file', listener: (event: Event,
                                      path: string) => void): this;
    /**
     * @platform darwin
     */
    once(event: 'open-file', listener: (event: Event,
                                      path: string) => void): this;
    /**
     * @platform darwin
     */
    addListener(event: 'open-file', listener: (event: Event,
                                      path: string) => void): this;
    /**
     * @platform darwin
     */
    removeListener(event: 'open-file', listener: (event: Event,
                                      path: string) => void): this;
    /**
     * Emitted when the user wants to open a URL with the application. Your
     * application's `Info.plist` file must define the URL scheme within the
     * `CFBundleURLTypes` key, and set `NSPrincipalClass` to `AtomApplication`.
     *
     * As with the `open-file` event, be sure to register a listener for the `open-url`
     * event early in your application startup to detect if the application is being
     * opened to handle a URL. If you register the listener in response to a `ready`
     * event, you'll miss URLs that trigger the launch of your application.
     *
     * @platform darwin
     */
    on(event: 'open-url', listener: (event: Event,
                                     url: string) => void): this;
    /**
     * @platform darwin
     */
    off(event: 'open-url', listener: (event: Event,
                                     url: string) => void): this;
    /**
     * @platform darwin
     */
    once(event: 'open-url', listener: (event: Event,
                                     url: string) => void): this;
    /**
     * @platform darwin
     */
    addListener(event: 'open-url', listener: (event: Event,
                                     url: string) => void): this;
    /**
     * @platform darwin
     */
    removeListener(event: 'open-url', listener: (event: Event,
                                     url: string) => void): this;
    /**
     * Emitted when the application is quitting.
     *
     * > [!NOTE] On Windows, this event will not be emitted if the app is closed due to
     * a shutdown/restart of the system or a user logout.
     */
    on(event: 'quit', listener: (event: Event,
                                 exitCode: number) => void): this;
    off(event: 'quit', listener: (event: Event,
                                 exitCode: number) => void): this;
    once(event: 'quit', listener: (event: Event,
                                 exitCode: number) => void): this;
    addListener(event: 'quit', listener: (event: Event,
                                 exitCode: number) => void): this;
    removeListener(event: 'quit', listener: (event: Event,
                                 exitCode: number) => void): this;
    /**
     * Emitted once, when Electron has finished initializing. On macOS, `launchInfo`
     * holds the `userInfo` of the `NSUserNotification` or information from
     * `UNNotificationResponse` that was used to open the application, if it was
     * launched from Notification Center. You can also call `app.isReady()` to check if
     * this event has already fired and `app.whenReady()` to get a Promise that is
     * fulfilled when Electron is initialized.
     *
     * > [!NOTE] The `ready` event is only fired after the main process has finished
     * running the first tick of the event loop. If an Electron API needs to be called
     * before the `ready` event, ensure that it is called synchronously in the
     * top-level context of the main process.
     */
    on(event: 'ready', listener: (event: Event,
                                  /**
                                   * @platform darwin
                                   */
                                  launchInfo: (Record<string, any>) | (NotificationResponse)) => void): this;
    off(event: 'ready', listener: (event: Event,
                                  /**
                                   * @platform darwin
                                   */
                                  launchInfo: (Record<string, any>) | (NotificationResponse)) => void): this;
    once(event: 'ready', listener: (event: Event,
                                  /**
                                   * @platform darwin
                                   */
                                  launchInfo: (Record<string, any>) | (NotificationResponse)) => void): this;
    addListener(event: 'ready', listener: (event: Event,
                                  /**
                                   * @platform darwin
                                   */
                                  launchInfo: (Record<string, any>) | (NotificationResponse)) => void): this;
    removeListener(event: 'ready', listener: (event: Event,
                                  /**
                                   * @platform darwin
                                   */
                                  launchInfo: (Record<string, any>) | (NotificationResponse)) => void): this;
    /**
     * Emitted when the renderer process unexpectedly disappears.  This is normally
     * because it was crashed or killed.
     */
    on(event: 'render-process-gone', listener: (event: Event,
                                                webContents: WebContents,
                                                details: RenderProcessGoneDetails) => void): this;
    off(event: 'render-process-gone', listener: (event: Event,
                                                webContents: WebContents,
                                                details: RenderProcessGoneDetails) => void): this;
    once(event: 'render-process-gone', listener: (event: Event,
                                                webContents: WebContents,
                                                details: RenderProcessGoneDetails) => void): this;
    addListener(event: 'render-process-gone', listener: (event: Event,
                                                webContents: WebContents,
                                                details: RenderProcessGoneDetails) => void): this;
    removeListener(event: 'render-process-gone', listener: (event: Event,
                                                webContents: WebContents,
                                                details: RenderProcessGoneDetails) => void): this;
    /**
     * This event will be emitted inside the primary instance of your application when
     * a second instance has been executed and calls `app.requestSingleInstanceLock()`.
     *
     * `argv` is an Array of the second instance's command line arguments, and
     * `workingDirectory` is its current working directory. Usually applications
     * respond to this by making their primary window focused and non-minimized.
     *
     * > [!NOTE] `argv` will not be exactly the same list of arguments as those passed
     * to the second instance. The order might change and additional arguments might be
     * appended. If you need to maintain the exact same arguments, it's advised to use
     * `additionalData` instead.
     *
     * > [!NOTE] If the second instance is started by a different user than the first,
     * the `argv` array will not include the arguments.
     *
     * This event is guaranteed to be emitted after the `ready` event of `app` gets
     * emitted.
     *
     * > [!NOTE] Extra command line arguments might be added by Chromium, such as
     * `--original-process-start-time`.
     */
    on(event: 'second-instance', listener: (event: Event,
                                            /**
                                             * An array of the second instance's command line arguments
                                             */
                                            argv: string[],
                                            /**
                                             * The second instance's working directory
                                             */
                                            workingDirectory: string,
                                            /**
                                             * A JSON object of additional data passed from the second instance
                                             */
                                            additionalData: unknown) => void): this;
    off(event: 'second-instance', listener: (event: Event,
                                            /**
                                             * An array of the second instance's command line arguments
                                             */
                                            argv: string[],
                                            /**
                                             * The second instance's working directory
                                             */
                                            workingDirectory: string,
                                            /**
                                             * A JSON object of additional data passed from the second instance
                                             */
                                            additionalData: unknown) => void): this;
    once(event: 'second-instance', listener: (event: Event,
                                            /**
                                             * An array of the second instance's command line arguments
                                             */
                                            argv: string[],
                                            /**
                                             * The second instance's working directory
                                             */
                                            workingDirectory: string,
                                            /**
                                             * A JSON object of additional data passed from the second instance
                                             */
                                            additionalData: unknown) => void): this;
    addListener(event: 'second-instance', listener: (event: Event,
                                            /**
                                             * An array of the second instance's command line arguments
                                             */
                                            argv: string[],
                                            /**
                                             * The second instance's working directory
                                             */
                                            workingDirectory: string,
                                            /**
                                             * A JSON object of additional data passed from the second instance
                                             */
                                            additionalData: unknown) => void): this;
    removeListener(event: 'second-instance', listener: (event: Event,
                                            /**
                                             * An array of the second instance's command line arguments
                                             */
                                            argv: string[],
                                            /**
                                             * The second instance's working directory
                                             */
                                            workingDirectory: string,
                                            /**
                                             * A JSON object of additional data passed from the second instance
                                             */
                                            additionalData: unknown) => void): this;
    /**
     * Emitted when a client certificate is requested.
     *
     * The `url` corresponds to the navigation entry requesting the client certificate
     * and `callback` can be called with an entry filtered from the list. Using
     * `event.preventDefault()` prevents the application from using the first
     * certificate from the store.
     */
    on(event: 'select-client-certificate', listener: (event: Event,
                                                      webContents: WebContents,
                                                      url: string,
                                                      certificateList: Certificate[],
                                                      callback: (certificate?: Certificate) => void) => void): this;
    off(event: 'select-client-certificate', listener: (event: Event,
                                                      webContents: WebContents,
                                                      url: string,
                                                      certificateList: Certificate[],
                                                      callback: (certificate?: Certificate) => void) => void): this;
    once(event: 'select-client-certificate', listener: (event: Event,
                                                      webContents: WebContents,
                                                      url: string,
                                                      certificateList: Certificate[],
                                                      callback: (certificate?: Certificate) => void) => void): this;
    addListener(event: 'select-client-certificate', listener: (event: Event,
                                                      webContents: WebContents,
                                                      url: string,
                                                      certificateList: Certificate[],
                                                      callback: (certificate?: Certificate) => void) => void): this;
    removeListener(event: 'select-client-certificate', listener: (event: Event,
                                                      webContents: WebContents,
                                                      url: string,
                                                      certificateList: Certificate[],
                                                      callback: (certificate?: Certificate) => void) => void): this;
    /**
     * Emitted when Electron has created a new `session`.
     */
    on(event: 'session-created', listener: (session: Session) => void): this;
    off(event: 'session-created', listener: (session: Session) => void): this;
    once(event: 'session-created', listener: (session: Session) => void): this;
    addListener(event: 'session-created', listener: (session: Session) => void): this;
    removeListener(event: 'session-created', listener: (session: Session) => void): this;
    /**
     * Emitted when Handoff is about to be resumed on another device. If you need to
     * update the state to be transferred, you should call `event.preventDefault()`
     * immediately, construct a new `userInfo` dictionary and call
     * `app.updateCurrentActivity()` in a timely manner. Otherwise, the operation will
     * fail and `continue-activity-error` will be called.
     *
     * @platform darwin
     */
    on(event: 'update-activity-state', listener: (event: Event,
                                                  /**
                                                   * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                   */
                                                  type: string,
                                                  /**
                                                   * Contains app-specific state stored by the activity.
                                                   */
                                                  userInfo: unknown) => void): this;
    /**
     * @platform darwin
     */
    off(event: 'update-activity-state', listener: (event: Event,
                                                  /**
                                                   * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                   */
                                                  type: string,
                                                  /**
                                                   * Contains app-specific state stored by the activity.
                                                   */
                                                  userInfo: unknown) => void): this;
    /**
     * @platform darwin
     */
    once(event: 'update-activity-state', listener: (event: Event,
                                                  /**
                                                   * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                   */
                                                  type: string,
                                                  /**
                                                   * Contains app-specific state stored by the activity.
                                                   */
                                                  userInfo: unknown) => void): this;
    /**
     * @platform darwin
     */
    addListener(event: 'update-activity-state', listener: (event: Event,
                                                  /**
                                                   * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                   */
                                                  type: string,
                                                  /**
                                                   * Contains app-specific state stored by the activity.
                                                   */
                                                  userInfo: unknown) => void): this;
    /**
     * @platform darwin
     */
    removeListener(event: 'update-activity-state', listener: (event: Event,
                                                  /**
                                                   * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                   */
                                                  type: string,
                                                  /**
                                                   * Contains app-specific state stored by the activity.
                                                   */
                                                  userInfo: unknown) => void): this;
    /**
     * Emitted when a new webContents is created.
     */
    on(event: 'web-contents-created', listener: (event: Event,
                                                 webContents: WebContents) => void): this;
    off(event: 'web-contents-created', listener: (event: Event,
                                                 webContents: WebContents) => void): this;
    once(event: 'web-contents-created', listener: (event: Event,
                                                 webContents: WebContents) => void): this;
    addListener(event: 'web-contents-created', listener: (event: Event,
                                                 webContents: WebContents) => void): this;
    removeListener(event: 'web-contents-created', listener: (event: Event,
                                                 webContents: WebContents) => void): this;
    /**
     * Emitted during Handoff before an activity from a different device wants to be
     * resumed. You should call `event.preventDefault()` if you want to handle this
     * event.
     *
     * @platform darwin
     */
    on(event: 'will-continue-activity', listener: (event: Event,
                                                   /**
                                                    * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                    */
                                                   type: string) => void): this;
    /**
     * @platform darwin
     */
    off(event: 'will-continue-activity', listener: (event: Event,
                                                   /**
                                                    * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                    */
                                                   type: string) => void): this;
    /**
     * @platform darwin
     */
    once(event: 'will-continue-activity', listener: (event: Event,
                                                   /**
                                                    * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                    */
                                                   type: string) => void): this;
    /**
     * @platform darwin
     */
    addListener(event: 'will-continue-activity', listener: (event: Event,
                                                   /**
                                                    * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                    */
                                                   type: string) => void): this;
    /**
     * @platform darwin
     */
    removeListener(event: 'will-continue-activity', listener: (event: Event,
                                                   /**
                                                    * A string identifying the activity. Maps to `NSUserActivity.activityType`.
                                                    */
                                                   type: string) => void): this;
    /**
     * Emitted when the application has finished basic startup. On Windows and Linux,
     * the `will-finish-launching` event is the same as the `ready` event; on macOS,
     * this event represents the `applicationWillFinishLaunching` notification of
     * `NSApplication`.
     *
     * In most cases, you should do everything in the `ready` event handler.
     */
    on(event: 'will-finish-launching', listener: () => void): this;
    off(event: 'will-finish-launching', listener: () => void): this;
    once(event: 'will-finish-launching', listener: () => void): this;
    addListener(event: 'will-finish-launching', listener: () => void): this;
    removeListener(event: 'will-finish-launching', listener: () => void): this;
    /**
     * Emitted when all windows have been closed and the application will quit. Calling
     * `event.preventDefault()` will prevent the default behavior, which is terminating
     * the application.
     *
     * See the description of the `window-all-closed` event for the differences between
     * the `will-quit` and `window-all-closed` events.
     *
     * > [!NOTE] On Windows, this event will not be emitted if the app is closed due to
     * a shutdown/restart of the system or a user logout.
     */
    on(event: 'will-quit', listener: (event: Event) => void): this;
    off(event: 'will-quit', listener: (event: Event) => void): this;
    once(event: 'will-quit', listener: (event: Event) => void): this;
    addListener(event: 'will-quit', listener: (event: Event) => void): this;
    removeListener(event: 'will-quit', listener: (event: Event) => void): this;
    /**
     * Emitted when all windows have been closed.
     *
     * If you do not subscribe to this event and all windows are closed, the default
     * behavior is to quit the app; however, if you subscribe, you control whether the
     * app quits or not. If the user pressed `Cmd + Q`, or the developer called
     * `app.quit()`, Electron will first try to close all the windows and then emit the
     * `will-quit` event, and in this case the `window-all-closed` event would not be
     * emitted.
     */
    on(event: 'window-all-closed', listener: () => void): this;
    off(event: 'window-all-closed', listener: () => void): this;
    once(event: 'window-all-closed', listener: () => void): this;
    addListener(event: 'window-all-closed', listener: () => void): this;
    removeListener(event: 'window-all-closed', listener: () => void): this;
    /**
     * Adds `path` to the recent documents list.
     *
     * This list is managed by the OS. On Windows, you can visit the list from the task
     * bar, and on macOS, you can visit it from dock menu.
     *
     * @platform darwin,win32
     */
    addRecentDocument(path: string): void;
    /**
     * Clears the recent documents list.
     *
     * @platform darwin,win32
     */
    clearRecentDocuments(): void;
    /**
     * Configures host resolution (DNS and DNS-over-HTTPS). By default, the following
     * resolvers will be used, in order:
     *
     * * DNS-over-HTTPS, if the DNS provider supports it, then
     * * the built-in resolver (enabled on macOS only by default), then
     * * the system's resolver (e.g. `getaddrinfo`).
     *
     * This can be configured to either restrict usage of non-encrypted DNS
     * (`secureDnsMode: "secure"`), or disable DNS-over-HTTPS (`secureDnsMode: "off"`).
     * It is also possible to enable or disable the built-in resolver.
     *
     * To disable insecure DNS, you can specify a `secureDnsMode` of `"secure"`. If you
     * do so, you should make sure to provide a list of DNS-over-HTTPS servers to use,
     * in case the user's DNS configuration does not include a provider that supports
     * DoH.
     *
     * This API must be called after the `ready` event is emitted.
     */
    configureHostResolver(options: ConfigureHostResolverOptions): void;
    /**
     * By default, Chromium disables 3D APIs (e.g. WebGL) until restart on a per domain
     * basis if the GPU processes crashes too frequently. This function disables that
     * behavior.
     *
     * This method can only be called before app is ready.
     */
    disableDomainBlockingFor3DAPIs(): void;
    /**
     * Disables hardware acceleration for current app.
     *
     * This method can only be called before app is ready.
     */
    disableHardwareAcceleration(): void;
    /**
     * Enables full sandbox mode on the app. This means that all renderers will be
     * launched sandboxed, regardless of the value of the `sandbox` flag in
     * `WebPreferences`.
     *
     * This method can only be called before app is ready.
     */
    enableSandbox(): void;
    /**
     * Exits immediately with `exitCode`. `exitCode` defaults to 0.
     *
     * All windows will be closed immediately without asking the user, and the
     * `before-quit` and `will-quit` events will not be emitted.
     */
    exit(exitCode?: number): void;
    /**
     * On Linux, focuses on the first visible window. On macOS, makes the application
     * the active app. On Windows, focuses on the application's first window.
     *
     * You should seek to use the `steal` option as sparingly as possible.
     */
    focus(options?: FocusOptions): void;
    /**
     * Resolve with an object containing the following:
     *
     * * `icon` NativeImage - the display icon of the app handling the protocol.
     * * `path` string  - installation path of the app handling the protocol.
     * * `name` string - display name of the app handling the protocol.
     *
     * This method returns a promise that contains the application name, icon and path
     * of the default handler for the protocol (aka URI scheme) of a URL.
     *
     * @platform darwin,win32
     */
    getApplicationInfoForProtocol(url: string): Promise<Electron.ApplicationInfoForProtocolReturnValue>;
    /**
     * Name of the application handling the protocol, or an empty string if there is no
     * handler. For instance, if Electron is the default handler of the URL, this could
     * be `Electron` on Windows and Mac. However, don't rely on the precise format
     * which is not guaranteed to remain unchanged. Expect a different format on Linux,
     * possibly with a `.desktop` suffix.
     *
     * This method returns the application name of the default handler for the protocol
     * (aka URI scheme) of a URL.
     */
    getApplicationNameForProtocol(url: string): string;
    /**
     * Array of `ProcessMetric` objects that correspond to memory and CPU usage
     * statistics of all the processes associated with the app.
     */
    getAppMetrics(): ProcessMetric[];
    /**
     * The current application directory.
     */
    getAppPath(): string;
    /**
     * The current value displayed in the counter badge.
     *
     * @platform linux,darwin
     */
    getBadgeCount(): number;
    /**
     * The type of the currently running activity.
     *
     * @platform darwin
     */
    getCurrentActivityType(): string;
    /**
     * fulfilled with the app's icon, which is a NativeImage.
     *
     * Fetches a path's associated icon.
     *
     * On _Windows_, there a 2 kinds of icons:
     *
     * * Icons associated with certain file extensions, like `.mp3`, `.png`, etc.
     * * Icons inside the file itself, like `.exe`, `.dll`, `.ico`.
     *
     * On _Linux_ and _macOS_, icons depend on the application associated with file
     * mime type.
     */
    getFileIcon(path: string, options?: FileIconOptions): Promise<Electron.NativeImage>;
    /**
     * The Graphics Feature Status from `chrome://gpu/`.
     *
     * > [!NOTE] This information is only usable after the `gpu-info-update` event is
     * emitted.
     */
    getGPUFeatureStatus(): GPUFeatureStatus;
    /**
     * For `infoType` equal to `complete`: Promise is fulfilled with `Object`
     * containing all the GPU Information as in chromium's GPUInfo object. This
     * includes the version and driver information that's shown on `chrome://gpu` page.
     *
     * For `infoType` equal to `basic`: Promise is fulfilled with `Object` containing
     * fewer attributes than when requested with `complete`. Here's an example of basic
     * response:
     *
     * Using `basic` should be preferred if only basic information like `vendorId` or
     * `deviceId` is needed.
     */
    getGPUInfo(infoType: 'basic' | 'complete'): Promise<unknown>;
    /**
     * * `minItems` Integer - The minimum number of items that will be shown in the
     * Jump List (for a more detailed description of this value see the MSDN docs).
     * * `removedItems` JumpListItem[] - Array of `JumpListItem` objects that
     * correspond to items that the user has explicitly removed from custom categories
     * in the Jump List. These items must not be re-added to the Jump List in the
     * **next** call to `app.setJumpList()`, Windows will not display any custom
     * category that contains any of the removed items.
     *
     * @platform win32
     */
    getJumpListSettings(): JumpListSettings;
    /**
     * The current application locale, fetched using Chromium's `l10n_util` library.
     * Possible return values are documented here.
     *
     * To set the locale, you'll want to use a command line switch at app startup,
     * which may be found here.
     *
     * > [!NOTE] When distributing your packaged app, you have to also ship the
     * `locales` folder.
     *
     * > [!NOTE] This API must be called after the `ready` event is emitted.
     *
     * > [!NOTE] To see example return values of this API compared to other locale and
     * language APIs, see `app.getPreferredSystemLanguages()`.
     */
    getLocale(): string;
    /**
     * User operating system's locale two-letter ISO 3166 country code. The value is
     * taken from native OS APIs.
     *
     * > [!NOTE] When unable to detect locale country code, it returns empty string.
     */
    getLocaleCountryCode(): string;
    /**
     * If you provided `path` and `args` options to `app.setLoginItemSettings`, then
     * you need to pass the same arguments here for `openAtLogin` to be set correctly.
     *
     *
     * * `openAtLogin` boolean - `true` if the app is set to open at login.
     * * `openAsHidden` boolean _macOS_ _Deprecated_ - `true` if the app is set to open
     * as hidden at login. This does not work on macOS 13 and up.
     * * `wasOpenedAtLogin` boolean _macOS_ - `true` if the app was opened at login
     * automatically.
     * * `wasOpenedAsHidden` boolean _macOS_ _Deprecated_ - `true` if the app was
     * opened as a hidden login item. This indicates that the app should not open any
     * windows at startup. This setting is not available on MAS builds or on macOS 13
     * and up.
     * * `restoreState` boolean _macOS_ _Deprecated_ - `true` if the app was opened as
     * a login item that should restore the state from the previous session. This
     * indicates that the app should restore the windows that were open the last time
     * the app was closed. This setting is not available on MAS builds or on macOS 13
     * and up.
     * * `status` string _macOS_ - can be one of `not-registered`, `enabled`,
     * `requires-approval`, or `not-found`.
     * * `executableWillLaunchAtLogin` boolean _Windows_ - `true` if app is set to open
     * at login and its run key is not deactivated. This differs from `openAtLogin` as
     * it ignores the `args` option, this property will be true if the given executable
     * would be launched at login with **any** arguments.
     * * `launchItems` Object[] _Windows_
     *   * `name` string _Windows_ - name value of a registry entry.
     *   * `path` string _Windows_ - The executable to an app that corresponds to a
     * registry entry.
     *   * `args` string[] _Windows_ - the command-line arguments to pass to the
     * executable.
     *   * `scope` string _Windows_ - one of `user` or `machine`. Indicates whether the
     * registry entry is under `HKEY_CURRENT USER` or `HKEY_LOCAL_MACHINE`.
     *   * `enabled` boolean _Windows_ - `true` if the app registry key is startup
     * approved and therefore shows as `enabled` in Task Manager and Windows settings.
     *
     * @platform darwin,win32
     */
    getLoginItemSettings(options?: LoginItemSettingsOptions): LoginItemSettings;
    /**
     * The current application's name, which is the name in the application's
     * `package.json` file.
     *
     * Usually the `name` field of `package.json` is a short lowercase name, according
     * to the npm modules spec. You should usually also specify a `productName` field,
     * which is your application's full capitalized name, and which will be preferred
     * over `name` by Electron.
     */
    getName(): string;
    /**
     * A path to a special directory or file associated with `name`. On failure, an
     * `Error` is thrown.
     *
     * If `app.getPath('logs')` is called without called `app.setAppLogsPath()` being
     * called first, a default log directory will be created equivalent to calling
     * `app.setAppLogsPath()` without a `path` parameter.
     */
    getPath(name: 'home' | 'appData' | 'userData' | 'sessionData' | 'temp' | 'exe' | 'module' | 'desktop' | 'documents' | 'downloads' | 'music' | 'pictures' | 'videos' | 'recent' | 'logs' | 'crashDumps'): string;
    /**
     * The user's preferred system languages from most preferred to least preferred,
     * including the country codes if applicable. A user can modify and add to this
     * list on Windows or macOS through the Language and Region settings.
     *
     * The API uses `GlobalizationPreferences` (with a fallback to
     * `GetSystemPreferredUILanguages`) on Windows, `\[NSLocale preferredLanguages\]`
     * on macOS, and `g_get_language_names` on Linux.
     *
     * This API can be used for purposes such as deciding what language to present the
     * application in.
     *
     * Here are some examples of return values of the various language and locale APIs
     * with different configurations:
     *
     * On Windows, given application locale is German, the regional format is Finnish
     * (Finland), and the preferred system languages from most to least preferred are
     * French (Canada), English (US), Simplified Chinese (China), Finnish, and Spanish
     * (Latin America):
     *
     * On macOS, given the application locale is German, the region is Finland, and the
     * preferred system languages from most to least preferred are French (Canada),
     * English (US), Simplified Chinese, and Spanish (Latin America):
     *
     * Both the available languages and regions and the possible return values differ
     * between the two operating systems.
     *
     * As can be seen with the example above, on Windows, it is possible that a
     * preferred system language has no country code, and that one of the preferred
     * system languages corresponds with the language used for the regional format. On
     * macOS, the region serves more as a default country code: the user doesn't need
     * to have Finnish as a preferred language to use Finland as the region,and the
     * country code `FI` is used as the country code for preferred system languages
     * that do not have associated countries in the language name.
     */
    getPreferredSystemLanguages(): string[];
    /**
     * The current system locale. On Windows and Linux, it is fetched using Chromium's
     * `i18n` library. On macOS, `[NSLocale currentLocale]` is used instead. To get the
     * user's current system language, which is not always the same as the locale, it
     * is better to use `app.getPreferredSystemLanguages()`.
     *
     * Different operating systems also use the regional data differently:
     *
     * * Windows 11 uses the regional format for numbers, dates, and times.
     * * macOS Monterey uses the region for formatting numbers, dates, times, and for
     * selecting the currency symbol to use.
     *
     * Therefore, this API can be used for purposes such as choosing a format for
     * rendering dates and times in a calendar app, especially when the developer wants
     * the format to be consistent with the OS.
     *
     * > [!NOTE] This API must be called after the `ready` event is emitted.
     *
     * > [!NOTE] To see example return values of this API compared to other locale and
     * language APIs, see `app.getPreferredSystemLanguages()`.
     */
    getSystemLocale(): string;
    /**
     * The version of the loaded application. If no version is found in the
     * application's `package.json` file, the version of the current bundle or
     * executable is returned.
     */
    getVersion(): string;
    /**
     * This method returns whether or not this instance of your app is currently
     * holding the single instance lock.  You can request the lock with
     * `app.requestSingleInstanceLock()` and release with
     * `app.releaseSingleInstanceLock()`
     */
    hasSingleInstanceLock(): boolean;
    /**
     * Hides all application windows without minimizing them.
     *
     * @platform darwin
     */
    hide(): void;
    /**
     * Imports the certificate in pkcs12 format into the platform certificate store.
     * `callback` is called with the `result` of import operation, a value of `0`
     * indicates success while any other value indicates failure according to Chromium
     * net_error_list.
     *
     * @platform linux
     */
    importCertificate(options: ImportCertificateOptions, callback: (result: number) => void): void;
    /**
     * Invalidates the current Handoff user activity.
     *
     * @platform darwin
     */
    invalidateCurrentActivity(): void;
    /**
     * `true` if Chrome's accessibility support is enabled, `false` otherwise. This API
     * will return `true` if the use of assistive technologies, such as screen readers,
     * has been detected. See
     * https://www.chromium.org/developers/design-documents/accessibility for more
     * details.
     *
     * @platform darwin,win32
     */
    isAccessibilitySupportEnabled(): boolean;
    /**
     * Whether the current executable is the default handler for a protocol (aka URI
     * scheme).
     *
     * > [!NOTE] On macOS, you can use this method to check if the app has been
     * registered as the default protocol handler for a protocol. You can also verify
     * this by checking `~/Library/Preferences/com.apple.LaunchServices.plist` on the
     * macOS machine. Please refer to Apple's documentation for details.
     *
     * The API uses the Windows Registry and `LSCopyDefaultHandlerForURLScheme`
     * internally.
     */
    isDefaultProtocolClient(protocol: string, path?: string, args?: string[]): boolean;
    /**
     * whether or not the current OS version allows for native emoji pickers.
     */
    isEmojiPanelSupported(): boolean;
    /**
     * `true` if the application—including all of its windows—is hidden (e.g. with
     * `Command-H`), `false` otherwise.
     *
     * @platform darwin
     */
    isHidden(): boolean;
    /**
     * Whether the application is currently running from the systems Application
     * folder. Use in combination with `app.moveToApplicationsFolder()`
     *
     * @platform darwin
     */
    isInApplicationsFolder(): boolean;
    /**
     * `true` if Electron has finished initializing, `false` otherwise. See also
     * `app.whenReady()`.
     */
    isReady(): boolean;
    /**
     * whether `Secure Keyboard Entry` is enabled.
     *
     * By default this API will return `false`.
     *
     * @platform darwin
     */
    isSecureKeyboardEntryEnabled(): boolean;
    /**
     * Whether the current desktop environment is Unity launcher.
     *
     * @platform linux
     */
    isUnityRunning(): boolean;
    /**
     * Whether the move was successful. Please note that if the move is successful,
     * your application will quit and relaunch.
     *
     * No confirmation dialog will be presented by default. If you wish to allow the
     * user to confirm the operation, you may do so using the `dialog` API.
     *
     * **NOTE:** This method throws errors if anything other than the user causes the
