{"version": 3, "file": "windowsExecutableCodeSignatureVerifier.js", "sourceRoot": "", "sources": ["../src/windowsExecutableCodeSignatureVerifier.ts"], "names": [], "mappings": ";;AASA,0CAwFC;AAjGD,+DAA8C;AAC9C,iDAAsD;AACtD,yBAAwB;AAExB,6BAA4B;AAE5B,8DAA8D;AAC9D,kJAAkJ;AAClJ,kEAAkE;AAClE,SAAgB,eAAe,CAAC,cAA6B,EAAE,uBAA+B,EAAE,MAAc;IAC5G,OAAO,IAAI,OAAO,CAAgB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACpD,6EAA6E;QAC7E,oDAAoD;QACpD,EAAE;QACF,uCAAuC;QACvC,gBAAgB;QAChB,oEAAoE;QACpE,MAAM;QACN,+FAA+F;QAC/F,wCAAwC;QACxC,EAAE;QACF,iCAAiC;QACjC,2HAA2H;QAC3H,0EAA0E;QAC1E,sEAAsE;QACtE,EAAE;QACF,0FAA0F;QAC1F,kFAAkF;QAClF,sEAAsE;QACtE,MAAM,cAAc,GAAG,uBAAuB,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QAClE,MAAM,CAAC,IAAI,CAAC,uBAAuB,cAAc,EAAE,CAAC,CAAA;QAEpD,oEAAoE;QACpE,oEAAoE;QACpE,wGAAwG;QACxG,6JAA6J;QAC7J,4FAA4F;QAC5F,IAAA,wBAAQ,EACN,wDAAwD,EACxD,CAAC,YAAY,EAAE,iBAAiB,EAAE,cAAc,EAAE,MAAM,EAAE,UAAU,EAAE,4CAA4C,cAAc,+BAA+B,CAAC,EAChK;YACE,KAAK,EAAE,IAAI;YACX,OAAO,EAAE,EAAE,GAAG,IAAI;SACnB,EACD,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;;YACxB,IAAI,CAAC;gBACH,IAAI,KAAK,IAAI,IAAI,IAAI,MAAM,EAAE,CAAC;oBAC5B,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAA;oBAC1C,OAAO,CAAC,IAAI,CAAC,CAAA;oBACb,OAAM;gBACR,CAAC;gBACD,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAA;gBAC7B,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACtB,IAAI,CAAC;wBACH,MAAM,wBAAwB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;wBAC1D,MAAM,wBAAwB,GAAG,IAAI,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAA;wBACxE,MAAM,CAAC,IAAI,CAAC,gBAAgB,wBAAwB,kBAAkB,wBAAwB,EAAE,CAAC,CAAA;wBACjG,IAAI,wBAAwB,KAAK,wBAAwB,EAAE,CAAC;4BAC1D,WAAW,CAAC,MAAM,EAAE,IAAI,KAAK,CAAC,kBAAkB,wBAAwB,sBAAsB,wBAAwB,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,CAAA;4BAC1I,OAAO,CAAC,IAAI,CAAC,CAAA;4BACb,OAAM;wBACR,CAAC;oBACH,CAAC;oBAAC,OAAO,KAAU,EAAE,CAAC;wBACpB,MAAM,CAAC,IAAI,CAAC,qHAAqH,MAAA,KAAK,CAAC,OAAO,mCAAI,KAAK,CAAC,KAAK,EAAE,CAAC,CAAA;oBAClK,CAAC;oBACD,MAAM,OAAO,GAAG,IAAA,8BAAO,EAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAA;oBACvD,IAAI,KAAK,GAAG,KAAK,CAAA;oBACjB,KAAK,MAAM,IAAI,IAAI,cAAc,EAAE,CAAC;wBAClC,MAAM,EAAE,GAAG,IAAA,8BAAO,EAAC,IAAI,CAAC,CAAA;wBACxB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;4BACZ,2CAA2C;4BAC3C,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,CAAA;4BACrC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;gCAC1B,OAAO,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;4BACzC,CAAC,CAAC,CAAA;wBACJ,CAAC;6BAAM,IAAI,IAAI,KAAK,OAAO,CAAC,GAAG,CAAC,IAAI,CAAE,EAAE,CAAC;4BACvC,MAAM,CAAC,IAAI,CAAC,qCAAqC,IAAI,gFAAgF,CAAC,CAAA;4BACtI,KAAK,GAAG,IAAI,CAAA;wBACd,CAAC;wBACD,IAAI,KAAK,EAAE,CAAC;4BACV,OAAO,CAAC,IAAI,CAAC,CAAA;4BACb,OAAM;wBACR,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,MAAM,MAAM,GAAG,mBAAmB,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAA;gBAC/J,MAAM,CAAC,IAAI,CAAC,0EAA0E,MAAM,EAAE,CAAC,CAAA;gBAC/F,OAAO,CAAC,MAAM,CAAC,CAAA;YACjB,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAA;gBACpC,OAAO,CAAC,IAAI,CAAC,CAAA;gBACb,OAAM;YACR,CAAC;QACH,CAAC,CACF,CAAA;IACH,CAAC,CAAC,CAAA;AACJ,CAAC;AAED,SAAS,QAAQ,CAAC,GAAW;IAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAC5B,OAAO,IAAI,CAAC,UAAU,CAAA;IACtB,OAAO,IAAI,CAAC,UAAU,CAAA;IACtB,OAAO,IAAI,CAAC,aAAa,CAAA;IACzB,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAA;IAChD,IAAI,iBAAiB,IAAI,IAAI,EAAE,CAAC;QAC9B,OAAO,iBAAiB,CAAC,QAAQ,CAAA;QACjC,OAAO,iBAAiB,CAAC,UAAU,CAAA;QACnC,OAAO,iBAAiB,CAAC,MAAM,CAAA;QAC/B,OAAO,iBAAiB,CAAC,aAAa,CAAA;QACtC,uDAAuD;QACvD,OAAO,iBAAiB,CAAC,WAAW,CAAA;IACtC,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAS,WAAW,CAAC,MAAc,EAAE,KAAmB,EAAE,MAAqB,EAAE,MAA6B;IAC5G,IAAI,SAAS,EAAE,EAAE,CAAC;QAChB,MAAM,CAAC,IAAI,CACT,6CAA6C,KAAK,IAAI,MAAM,kHAAkH,CAC/K,CAAA;QACD,OAAM;IACR,CAAC;IAED,IAAI,CAAC;QACH,IAAA,4BAAY,EAAC,gBAAgB,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,UAAU,EAAE,qBAAqB,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,GAAG,IAAI,EAAS,CAAC,CAAA;IACrI,CAAC;IAAC,OAAO,SAAc,EAAE,CAAC;QACxB,MAAM,CAAC,IAAI,CACT,kCAAkC,SAAS,CAAC,OAAO,kHAAkH,CACtK,CAAA;QACD,OAAM;IACR,CAAC;IAED,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;QAClB,MAAM,CAAC,KAAK,CAAC,CAAA;IACf,CAAC;IAED,IAAI,MAAM,EAAE,CAAC;QACX,MAAM,CAAC,IAAI,KAAK,CAAC,qDAAqD,MAAM,uDAAuD,CAAC,CAAC,CAAA;IACvI,CAAC;AACH,CAAC;AAED,SAAS,SAAS;IAChB,MAAM,UAAU,GAAG,EAAE,CAAC,OAAO,EAAE,CAAA;IAC/B,OAAO,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;AACrE,CAAC", "sourcesContent": ["import { parseDn } from \"builder-util-runtime\"\nimport { execFile, execFileSync } from \"child_process\"\nimport * as os from \"os\"\nimport { Logger } from \"./types\"\nimport * as path from \"path\"\n\n// $certificateInfo = (Get-AuthenticodeSignature 'xxx\\yyy.exe'\n// | where {$_.Status.Equals([System.Management.Automation.SignatureStatus]::Valid) -and $_.SignerCertificate.Subject.Contains(\"CN=siemens.com\")})\n// | Out-String ; if ($certificateInfo) { exit 0 } else { exit 1 }\nexport function verifySignature(publisherNames: Array<string>, unescapedTempUpdateFile: string, logger: Logger): Promise<string | null> {\n  return new Promise<string | null>((resolve, reject) => {\n    // Escape quotes and backticks in filenames to prevent user from breaking the\n    // arguments and perform a remote command injection.\n    //\n    // Consider example powershell command:\n    // ```powershell\n    // Get-AuthenticodeSignature 'C:\\\\path\\\\my-bad-';calc;'filename.exe'\n    // ```\n    // The above would work expected and find the file name, however, it will also execute `;calc;`\n    // command and start the calculator app.\n    //\n    // From Powershell quoting rules:\n    // https://docs.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_quoting_rules?view=powershell-7\n    // * Double quotes `\"` are treated literally within single-quoted strings;\n    // * Single quotes can be escaped by doubling them: 'don''t' -> don't;\n    //\n    // Also note that at this point the file has already been written to the disk, thus we are\n    // guaranteed that the path will not contain any illegal characters like <>:\"/\\|?*\n    // https://docs.microsoft.com/en-us/windows/win32/fileio/naming-a-file\n    const tempUpdateFile = unescapedTempUpdateFile.replace(/'/g, \"''\")\n    logger.info(`Verifying signature ${tempUpdateFile}`)\n\n    // https://github.com/electron-userland/electron-builder/issues/2421\n    // https://github.com/electron-userland/electron-builder/issues/2535\n    // Resetting PSModulePath is necessary https://github.com/electron-userland/electron-builder/issues/7127\n    // semicolon wont terminate the set command and run chcp thus leading to verification errors on certificats with special chars like german umlauts, so rather\n    //   join commands using & https://github.com/electron-userland/electron-builder/issues/8162\n    execFile(\n      `set \"PSModulePath=\" & chcp 65001 >NUL & powershell.exe`,\n      [\"-NoProfile\", \"-NonInteractive\", \"-InputFormat\", \"None\", \"-Command\", `\"Get-AuthenticodeSignature -LiteralPath '${tempUpdateFile}' | ConvertTo-Json -Compress\"`],\n      {\n        shell: true,\n        timeout: 20 * 1000,\n      },\n      (error, stdout, stderr) => {\n        try {\n          if (error != null || stderr) {\n            handleError(logger, error, stderr, reject)\n            resolve(null)\n            return\n          }\n          const data = parseOut(stdout)\n          if (data.Status === 0) {\n            try {\n              const normlaizedUpdateFilePath = path.normalize(data.Path)\n              const normalizedTempUpdateFile = path.normalize(unescapedTempUpdateFile)\n              logger.info(`LiteralPath: ${normlaizedUpdateFilePath}. Update Path: ${normalizedTempUpdateFile}`)\n              if (normlaizedUpdateFilePath !== normalizedTempUpdateFile) {\n                handleError(logger, new Error(`LiteralPath of ${normlaizedUpdateFilePath} is different than ${normalizedTempUpdateFile}`), stderr, reject)\n                resolve(null)\n                return\n              }\n            } catch (error: any) {\n              logger.warn(`Unable to verify LiteralPath of update asset due to missing data.Path. Skipping this step of validation. Message: ${error.message ?? error.stack}`)\n            }\n            const subject = parseDn(data.SignerCertificate.Subject)\n            let match = false\n            for (const name of publisherNames) {\n              const dn = parseDn(name)\n              if (dn.size) {\n                // if we have a full DN, compare all values\n                const allKeys = Array.from(dn.keys())\n                match = allKeys.every(key => {\n                  return dn.get(key) === subject.get(key)\n                })\n              } else if (name === subject.get(\"CN\")!) {\n                logger.warn(`Signature validated using only CN ${name}. Please add your full Distinguished Name (DN) to publisherNames configuration`)\n                match = true\n              }\n              if (match) {\n                resolve(null)\n                return\n              }\n            }\n          }\n\n          const result = `publisherNames: ${publisherNames.join(\" | \")}, raw info: ` + JSON.stringify(data, (name, value) => (name === \"RawData\" ? undefined : value), 2)\n          logger.warn(`Sign verification failed, installer signed with incorrect certificate: ${result}`)\n          resolve(result)\n        } catch (e: any) {\n          handleError(logger, e, null, reject)\n          resolve(null)\n          return\n        }\n      }\n    )\n  })\n}\n\nfunction parseOut(out: string): any {\n  const data = JSON.parse(out)\n  delete data.PrivateKey\n  delete data.IsOSBinary\n  delete data.SignatureType\n  const signerCertificate = data.SignerCertificate\n  if (signerCertificate != null) {\n    delete signerCertificate.Archived\n    delete signerCertificate.Extensions\n    delete signerCertificate.Handle\n    delete signerCertificate.HasPrivateKey\n    // duplicates data.SignerCertificate (contains RawData)\n    delete signerCertificate.SubjectName\n  }\n  return data\n}\n\nfunction handleError(logger: Logger, error: Error | null, stderr: string | null, reject: (reason: any) => void): void {\n  if (isOldWin6()) {\n    logger.warn(\n      `Cannot execute Get-AuthenticodeSignature: ${error || stderr}. Ignoring signature validation due to unsupported powershell version. Please upgrade to powershell 3 or higher.`\n    )\n    return\n  }\n\n  try {\n    execFileSync(\"powershell.exe\", [\"-NoProfile\", \"-NonInteractive\", \"-Command\", \"ConvertTo-Json test\"], { timeout: 10 * 1000 } as any)\n  } catch (testError: any) {\n    logger.warn(\n      `Cannot execute ConvertTo-Json: ${testError.message}. Ignoring signature validation due to unsupported powershell version. Please upgrade to powershell 3 or higher.`\n    )\n    return\n  }\n\n  if (error != null) {\n    reject(error)\n  }\n\n  if (stderr) {\n    reject(new Error(`Cannot execute Get-AuthenticodeSignature, stderr: ${stderr}. Failing signature validation due to unknown stderr.`))\n  }\n}\n\nfunction isOldWin6(): boolean {\n  const winVersion = os.release()\n  return winVersion.startsWith(\"6.\") && !winVersion.startsWith(\"6.3\")\n}\n"]}