"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[81],{2163:(e,t,a)=>{a.d(t,{p:()=>r});var s=a(5155),l=a(3741),n=a(9283);function r(e){let{order:t,onAccept:a,onDecline:r,onComplete:i}=e,{t:o}=(0,n.o)();return(0,s.jsxs)("div",{className:"p-4 rounded-lg border ".concat((()=>{switch(t.status){case"pending":return"border-yellow-300 bg-yellow-50";case"accepted":case"in_progress":return"border-blue-300 bg-blue-50";case"completed":return"border-green-300 bg-green-50";case"failed":return"border-red-300 bg-red-50";default:return"border-gray-300 bg-gray-50"}})()),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-lg",children:(()=>{let e=["\uD83D\uDC69","\uD83D\uDC68","\uD83D\uDC75","\uD83D\uDC74","\uD83D\uDC67","\uD83D\uDC66"],a=t.customerName.length%e.length;return e[a]})()}),(0,s.jsx)("h3",{className:"font-medium text-gray-800",children:t.customerName})]}),(0,s.jsx)("span",{className:"text-sm font-semibold text-green-600",children:o("orders.reward",{amount:t.reward.toString()})})]}),(0,s.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:t.items.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)("span",{children:"\uD83E\uDDC1"}),(0,s.jsx)("span",{children:e})]},t))}),(0,s.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["⏰ ",o("orders.timeLimit",{time:(e=>{let t=Math.floor(e/60);return"".concat(t,":").concat((e%60).toString().padStart(2,"0"))})(t.timeLimit)})]}),(0,s.jsx)("div",{className:"text-xs",title:"Difficulty: ".concat(t.difficulty,"/5"),children:"⭐".repeat(t.difficulty)+"☆".repeat(5-t.difficulty)})]}),"pending"===t.status&&(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)(l.$,{size:"sm",variant:"success",onClick:()=>a(t.id),className:"flex-1",children:["✅ ",o("orders.accept")]}),(0,s.jsxs)(l.$,{size:"sm",variant:"danger",onClick:()=>r(t.id),className:"flex-1",children:["❌ ",o("orders.decline")]})]}),"accepted"===t.status&&(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-blue-600 text-sm font-medium mb-2",children:"\uD83D\uDCCB Order Accepted"}),(0,s.jsxs)(l.$,{size:"sm",variant:"primary",onClick:()=>i&&i(t.id),className:"w-full",children:["\uD83C\uDFAF ",o("orders.complete")]})]}),"in_progress"===t.status&&(0,s.jsxs)("div",{className:"text-center text-orange-600 text-sm font-medium",children:["\uD83D\uDD04 ",o("orders.inProgress")]}),"completed"===t.status&&(0,s.jsx)("div",{className:"text-center text-green-600 text-sm font-medium",children:"✅ Completed!"}),"failed"===t.status&&(0,s.jsx)("div",{className:"text-center text-red-600 text-sm font-medium",children:"❌ Failed"})]})}},2785:(e,t,a)=>{a.d(t,{b:()=>o});var s=a(5155),l=a(2115),n=a(3741),r=a(9283),i=a(5877);function o(e){let{isOpen:t,onClose:a,settings:o,onSettingsChange:c}=e,{language:d,setLanguage:m,t:u}=(0,r.o)(),[g,h]=(0,l.useState)("general");if(!t)return null;let x=(e,t)=>{c({[e]:t})},v=e=>{m(e),x("language",e)},p=[{id:"general",name:u("settings.general")||"General",icon:"⚙️"},{id:"audio",name:u("settings.audio")||"Audio",icon:"\uD83D\uDD0A"},{id:"graphics",name:u("settings.graphics")||"Graphics",icon:"\uD83C\uDFA8"},{id:"save",name:u("settings.save")||"Save & Data",icon:"\uD83D\uDCBE"}];return(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden",children:[(0,s.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:u("settings.title")||"⚙️ Settings"}),(0,s.jsx)(n.$,{variant:"secondary",onClick:a,children:u("game.close")||"✕ Close"})]})}),(0,s.jsx)("div",{className:"border-b border-gray-200",children:(0,s.jsx)("div",{className:"flex space-x-0",children:p.map(e=>(0,s.jsxs)("button",{onClick:()=>h(e.id),className:"px-4 py-3 font-medium text-sm border-b-2 transition-colors ".concat(g===e.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.icon," ",e.name]},e.id))})}),(0,s.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["general"===g&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:u("settings.language")||"\uD83C\uDF0D Language"}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(n.$,{variant:"en"===d?"primary":"secondary",size:"sm",onClick:()=>v("en"),children:"\uD83C\uDDFA\uD83C\uDDF8 English"}),(0,s.jsx)(n.$,{variant:"cs"===d?"primary":"secondary",size:"sm",onClick:()=>v("cs"),children:"\uD83C\uDDE8\uD83C\uDDFF Čeština"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:u("settings.gameplay")||"\uD83C\uDFAE Gameplay"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("label",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{children:u("settings.notifications")||"Enable Notifications"}),(0,s.jsx)("input",{type:"checkbox",checked:o.notificationsEnabled,onChange:e=>x("notificationsEnabled",e.target.checked),className:"rounded"})]}),(0,s.jsxs)("label",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{children:u("settings.tutorials")||"Show Tutorials"}),(0,s.jsx)("input",{type:"checkbox",checked:o.showTutorials,onChange:e=>x("showTutorials",e.target.checked),className:"rounded"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[u("settings.animationSpeed")||"Animation Speed",": ",o.animationSpeed,"x"]}),(0,s.jsx)("input",{type:"range",min:"0.5",max:"2",step:"0.1",value:o.animationSpeed,onChange:e=>x("animationSpeed",parseFloat(e.target.value)),className:"w-full"})]})]})]})]}),"audio"===g&&(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("label",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{children:"\uD83D\uDD0A"}),(0,s.jsx)("span",{children:u("settings.sound")||"Sound Effects"})]}),(0,s.jsx)("input",{type:"checkbox",checked:o.soundEnabled,onChange:e=>x("soundEnabled",e.target.checked),className:"rounded"})]}),(0,s.jsxs)("label",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{children:"\uD83C\uDFB5"}),(0,s.jsx)("span",{children:u("settings.music")||"Background Music"})]}),(0,s.jsx)("input",{type:"checkbox",checked:o.musicEnabled,onChange:e=>x("musicEnabled",e.target.checked),className:"rounded"})]})]})}),"graphics"===g&&(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:u("settings.quality")||"\uD83C\uDFA8 Graphics Quality"}),(0,s.jsx)("div",{className:"space-x-2",children:["low","medium","high"].map(e=>(0,s.jsx)(n.$,{variant:o.graphicsQuality===e?"primary":"secondary",size:"sm",onClick:()=>x("graphicsQuality",e),children:e.charAt(0).toUpperCase()+e.slice(1)},e))})]})}),"save"===g&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:u("settings.autoSave")||"\uD83D\uDCBE Auto-Save"}),(0,s.jsxs)("label",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{children:u("settings.enableAutoSave")||"Enable Auto-Save"}),(0,s.jsx)("input",{type:"checkbox",checked:o.autoSaveEnabled,onChange:e=>x("autoSaveEnabled",e.target.checked),className:"rounded"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:u("settings.dataManagement")||"\uD83D\uDCC1 Data Management"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(n.$,{variant:"secondary",onClick:()=>{let e={version:"1.0.0",timestamp:Date.now(),player:{},equipment:[],inventory:[],achievements:[],skills:[],automationSettings:{},gameSettings:o,bakeries:[],currentBakeryId:"main"},t=new Blob([i.B.exportSave(e)],{type:"application/json"}),a=URL.createObjectURL(t),s=document.createElement("a");s.href=a,s.download="bake-it-out-save-".concat(Date.now(),".json"),s.click(),URL.revokeObjectURL(a)},className:"w-full",children:u("settings.exportSave")||"\uD83D\uDCE4 Export Save"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("input",{type:"file",accept:".json",onChange:e=>{var t;let a=null==(t=e.target.files)?void 0:t[0];if(!a)return;let s=new FileReader;s.onload=e=>{try{var t;let a=null==(t=e.target)?void 0:t.result,s=i.B.importSave(a);s?(console.log("Save imported successfully:",s),alert("Save imported successfully!")):alert("Failed to import save file")}catch(e){alert("Invalid save file")}},s.readAsText(a)},className:"hidden",id:"import-save"}),(0,s.jsx)(n.$,{variant:"secondary",onClick:()=>{var e;return null==(e=document.getElementById("import-save"))?void 0:e.click()},className:"w-full",children:u("settings.importSave")||"\uD83D\uDCE5 Import Save"})]})]})]}),(0,s.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-medium text-yellow-800 mb-2",children:u("settings.cloudSync")||"☁️ Cloud Sync"}),(0,s.jsx)("p",{className:"text-sm text-yellow-700 mb-3",children:u("settings.cloudSyncDescription")||"Cloud sync allows you to save your progress online and play across multiple devices."}),(0,s.jsx)(n.$,{variant:"secondary",size:"sm",disabled:!0,children:u("settings.comingSoon")||"Coming Soon"})]})]})]})]})})}},3741:(e,t,a)=>{a.d(t,{$:()=>l});var s=a(5155);a(2115);let l=e=>{let{variant:t="primary",size:a="md",className:l="",children:n,...r}=e,i=["font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2",{primary:"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500",secondary:"bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500",danger:"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500",success:"bg-green-600 hover:bg-green-700 text-white focus:ring-green-500"}[t],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"}[a],l].join(" ");return(0,s.jsx)("button",{className:i,...r,children:n})}},5877:(e,t,a)=>{a.d(t,{B:()=>r});let s="1.0.0",l="bakeItOut_gameSave";class n{saveToLocal(e){try{let t={version:s,timestamp:Date.now(),...e},a=JSON.stringify(t);return localStorage.setItem(l,a),console.log("Game saved to local storage"),!0}catch(e){return console.error("Failed to save game to local storage:",e),!1}}loadFromLocal(){try{let e=localStorage.getItem(l);if(!e)return null;let t=JSON.parse(e);if(t.version!==s)return console.warn("Save version mismatch, attempting migration"),this.migrateSave(t);return console.log("Game loaded from local storage"),t}catch(e){return console.error("Failed to load game from local storage:",e),null}}deleteLocalSave(){try{return localStorage.removeItem(l),console.log("Local save deleted"),!0}catch(e){return console.error("Failed to delete local save:",e),!1}}initializeAutoSave(){this.autoSaveInterval=setInterval(()=>{this.triggerAutoSave()},3e4)}triggerAutoSave(){let e=new CustomEvent("autoSave");window.dispatchEvent(e)}stopAutoSave(){this.autoSaveInterval&&(clearInterval(this.autoSaveInterval),this.autoSaveInterval=null)}async saveToCloud(e,t){try{var a;let l={id:"".concat(t,"_").concat(Date.now()),userId:t,deviceId:this.getDeviceId(),lastModified:Date.now(),gameVersion:s,bakeryCount:(null==(a=e.bakeries)?void 0:a.length)||1,playerLevel:e.player.level};return console.log("Cloud save would be implemented here",{gameData:e,metadata:l}),!0}catch(e){return console.error("Failed to save to cloud:",e),!1}}async loadFromCloud(e){try{return console.log("Cloud load would be implemented here",{userId:e}),null}catch(e){return console.error("Failed to load from cloud:",e),null}}async syncWithCloud(e,t){try{let a=await this.loadFromCloud(t);if(!a)return await this.saveToCloud(e,t),e;if(a.timestamp>e.timestamp)return console.log("Cloud save is newer, using cloud data"),a;return console.log("Local save is newer, uploading to cloud"),await this.saveToCloud(e,t),e}catch(t){return console.error("Failed to sync with cloud:",t),e}}migrateSave(e){try{var t,a,l,n,r,i,o,c,d,m,u,g,h,x,v,p,y,b;let f={version:s,timestamp:e.timestamp||Date.now(),player:{level:(null==(t=e.player)?void 0:t.level)||1,experience:(null==(a=e.player)?void 0:a.experience)||0,money:(null==(l=e.player)?void 0:l.money)||100,skillPoints:(null==(n=e.player)?void 0:n.skillPoints)||0,totalMoneyEarned:(null==(r=e.player)?void 0:r.totalMoneyEarned)||0,totalOrdersCompleted:(null==(i=e.player)?void 0:i.totalOrdersCompleted)||0,totalItemsBaked:(null==(o=e.player)?void 0:o.totalItemsBaked)||0,unlockedRecipes:(null==(c=e.player)?void 0:c.unlockedRecipes)||["chocolate_chip_cookies","vanilla_muffins"],automationUpgrades:(null==(d=e.player)?void 0:d.automationUpgrades)||[]},equipment:e.equipment||[],inventory:e.inventory||[],achievements:e.achievements||[],skills:e.skills||[],automationSettings:e.automationSettings||{},gameSettings:{language:(null==(m=e.gameSettings)?void 0:m.language)||"en",soundEnabled:null==(v=null==(u=e.gameSettings)?void 0:u.soundEnabled)||v,musicEnabled:null==(p=null==(g=e.gameSettings)?void 0:g.musicEnabled)||p,notificationsEnabled:null==(y=null==(h=e.gameSettings)?void 0:h.notificationsEnabled)||y,autoSaveEnabled:null==(b=null==(x=e.gameSettings)?void 0:x.autoSaveEnabled)||b},bakeries:e.bakeries||[],currentBakeryId:e.currentBakeryId||"main"};return console.log("Save migrated successfully"),f}catch(e){return console.error("Failed to migrate save:",e),null}}getDeviceId(){let e=localStorage.getItem("deviceId");return e||(e="device_"+Date.now()+"_"+Math.random().toString(36).substr(2,9),localStorage.setItem("deviceId",e)),e}exportSave(e){return JSON.stringify(e,null,2)}importSave(e){try{let t=JSON.parse(e);return this.migrateSave(t)}catch(e){return console.error("Failed to import save:",e),null}}createBackup(e){try{let t="".concat(l,"_backup_").concat(Date.now());return localStorage.setItem(t,JSON.stringify(e)),this.cleanupOldBackups(),!0}catch(e){return console.error("Failed to create backup:",e),!1}}cleanupOldBackups(){let e=Object.keys(localStorage).filter(e=>e.startsWith("".concat(l,"_backup_"))).sort();for(;e.length>5;){let t=e.shift();t&&localStorage.removeItem(t)}}getBackups(){let e=[];return Object.keys(localStorage).forEach(t=>{if(t.startsWith("".concat(l,"_backup_")))try{let a=JSON.parse(localStorage.getItem(t)||"{}"),s=parseInt(t.split("_").pop()||"0");e.push({key:t,timestamp:s,data:a})}catch(e){console.error("Failed to parse backup:",e)}}),e.sort((e,t)=>t.timestamp-e.timestamp)}constructor(){this.autoSaveInterval=null,this.cloudSyncEnabled=!1,this.initializeAutoSave()}}let r=new n},9419:(e,t,a)=>{a.d(t,{$:()=>n});var s=a(5155),l=a(9283);function n(e){let{equipment:t,onClick:a}=e,{t:n}=(0,l.o)();return(0,s.jsx)("div",{className:"p-4 rounded-lg border-2 cursor-pointer transition-all ".concat(t.isActive?"border-green-400 bg-green-50":"border-gray-200 bg-gray-50 hover:border-orange-300 hover:bg-orange-50"),onClick:()=>!t.isActive&&a(t.id,t.name),children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-3xl mb-2",children:(e=>{switch(e){case"oven":return"\uD83D\uDD25";case"mixer":return"\uD83E\uDD44";case"counter":return"\uD83C\uDF7D️";default:return"⚙️"}})(t.type)}),(0,s.jsx)("h3",{className:"font-medium text-gray-800",children:t.name}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["Level ",t.level]}),t.isActive&&t.timeRemaining?(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsx)("div",{className:"text-sm text-green-600",children:n("kitchen.making",{recipe:t.currentRecipe||""})}),(0,s.jsx)("div",{className:"text-lg font-mono text-green-700",children:(e=>{let t=Math.floor(e/60);return"".concat(t,":").concat((e%60).toString().padStart(2,"0"))})(t.timeRemaining)}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,s.jsx)("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-1000",style:{width:"".concat(100-t.timeRemaining/60*100,"%")}})})]}):(0,s.jsx)("div",{className:"text-sm text-gray-500 mt-2",children:t.isActive?"Busy":n("kitchen.clickToUse")})]})})}}}]);