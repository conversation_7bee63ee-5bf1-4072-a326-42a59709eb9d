'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { EquipmentData } from '@/components/game/Equipment'
import { OrderData } from '@/components/game/Order'
import { generateRandomOrder, calculateExperienceReward, canCraftRecipe, getRecipeById, getAvailableRecipes } from '@/lib/gameLogic'
import {
  getLevelFromExperience,
  LevelReward,
  Achievement,
  SkillTree,
  ACHIEVEMENTS,
  SKILL_TREE
} from '@/lib/progressionSystem'
import {
  AutomationSettings,
  AutomationJob,
  ConveyorBelt,
  AUTOMATION_UPGRADES,
  calculateAutomationEfficiency,
  calculateAutomationSpeed,
  selectOptimalRecipe,
  generateAutomationJob
} from '@/lib/automationSystem'

export interface Player {
  level: number
  experience: number
  money: number
  maxExperience: number
  skillPoints: number
  totalMoneyEarned: number
  totalOrdersCompleted: number
  totalItemsBaked: number
  unlockedRecipes: string[]
  automationUpgrades: string[]
}

export interface Ingredient {
  name: string
  quantity: number
  cost: number
  icon: string
}

interface GameContextType {
  player: Player
  equipment: EquipmentData[]
  inventory: Ingredient[]
  orders: OrderData[]
  achievements: Achievement[]
  skills: SkillTree[]
  levelUpRewards: LevelReward[]
  showLevelUp: boolean
  automationSettings: AutomationSettings
  automationJobs: AutomationJob[]
  conveyorBelts: ConveyorBelt[]
  updatePlayer: (updates: Partial<Player>) => void
  updateEquipment: (equipmentId: string, updates: Partial<EquipmentData>) => void
  addExperience: (amount: number) => void
  addMoney: (amount: number) => void
  spendMoney: (amount: number) => boolean
  useIngredient: (name: string, quantity: number) => boolean
  addIngredient: (name: string, quantity: number) => void
  acceptOrder: (orderId: string) => void
  completeOrder: (orderId: string) => void
  declineOrder: (orderId: string) => void
  generateNewOrder: () => void
  upgradeSkill: (skillId: string) => void
  checkAchievements: () => void
  dismissLevelUp: () => void
  updateAutomationSettings: (updates: Partial<AutomationSettings>) => void
  purchaseAutomationUpgrade: (upgradeId: string) => void
  startAutomationJob: (equipmentId: string) => void
}

const GameContext = createContext<GameContextType | undefined>(undefined)

const RECIPES = [
  'Chocolate Chip Cookies',
  'Vanilla Muffins',
  'Cinnamon Rolls',
  'Brownies',
  'Croissants',
  'Bread Loaf',
  'Cupcakes',
  'Apple Pie'
]

const CUSTOMER_NAMES = [
  'Alice Johnson', 'Bob Smith', 'Carol Davis', 'David Wilson',
  'Emma Brown', 'Frank Miller', 'Grace Taylor', 'Henry Anderson',
  'Ivy Thomas', 'Jack Martinez', 'Kate Garcia', 'Liam Rodriguez'
]

export function GameProvider({ children }: { children: React.ReactNode }) {
  const [player, setPlayer] = useState<Player>({
    level: 1,
    experience: 0,
    money: 100,
    maxExperience: 100,
    skillPoints: 0,
    totalMoneyEarned: 0,
    totalOrdersCompleted: 0,
    totalItemsBaked: 0,
    unlockedRecipes: ['chocolate_chip_cookies', 'vanilla_muffins'],
    automationUpgrades: []
  })

  const [equipment, setEquipment] = useState<EquipmentData[]>([
    { id: 'oven1', name: 'Basic Oven', type: 'oven', isActive: false, level: 1, efficiency: 1.0, automationLevel: 0 },
    { id: 'mixer1', name: 'Hand Mixer', type: 'mixer', isActive: false, level: 1, efficiency: 1.0, automationLevel: 0 },
    { id: 'counter1', name: 'Work Counter', type: 'counter', isActive: false, level: 1, efficiency: 1.0, automationLevel: 0 }
  ])

  const [inventory, setInventory] = useState<Ingredient[]>([
    { name: 'Flour', quantity: 15, cost: 5, icon: '🌾' },
    { name: 'Sugar', quantity: 12, cost: 8, icon: '🍯' },
    { name: 'Eggs', quantity: 10, cost: 12, icon: '🥚' },
    { name: 'Butter', quantity: 8, cost: 15, icon: '🧈' },
    { name: 'Chocolate Chips', quantity: 6, cost: 20, icon: '🍫' },
    { name: 'Vanilla', quantity: 5, cost: 25, icon: '🌿' },
    { name: 'Salt', quantity: 10, cost: 3, icon: '🧂' }
  ])

  const [orders, setOrders] = useState<OrderData[]>([
    {
      id: '1',
      customerName: 'Alice Johnson',
      items: ['Chocolate Chip Cookies'],
      timeLimit: 300,
      reward: 25,
      status: 'pending',
      difficulty: 1
    }
  ])

  const [achievements, setAchievements] = useState<Achievement[]>(ACHIEVEMENTS)
  const [skills, setSkills] = useState<SkillTree[]>(SKILL_TREE)
  const [levelUpRewards, setLevelUpRewards] = useState<LevelReward[]>([])
  const [showLevelUp, setShowLevelUp] = useState(false)

  const [automationSettings, setAutomationSettings] = useState<AutomationSettings>({
    enabled: false,
    autoStart: false,
    preferredRecipes: [],
    maxConcurrentJobs: 2,
    priorityMode: 'efficiency',
    ingredientThreshold: 5
  })

  const [automationJobs, setAutomationJobs] = useState<AutomationJob[]>([])
  const [conveyorBelts, setConveyorBelts] = useState<ConveyorBelt[]>([])

  // Equipment timer effect
  useEffect(() => {
    const interval = setInterval(() => {
      setEquipment(prev => prev.map(eq => {
        if (eq.isActive && eq.timeRemaining && eq.timeRemaining > 0) {
          return { ...eq, timeRemaining: eq.timeRemaining - 1 }
        } else if (eq.isActive && eq.timeRemaining === 0) {
          // Baking completed - could add notification here
          return { ...eq, isActive: false, timeRemaining: undefined, currentRecipe: undefined }
        }
        return eq
      }))
    }, 1000)

    return () => clearInterval(interval)
  }, [])

  // Order timer effect
  useEffect(() => {
    const interval = setInterval(() => {
      setOrders(prev => prev.map(order => {
        if ((order.status === 'accepted' || order.status === 'in_progress') && order.timeLimit > 0) {
          const newTimeLimit = order.timeLimit - 1
          if (newTimeLimit === 0) {
            return { ...order, status: 'failed', timeLimit: 0 }
          }
          return { ...order, timeLimit: newTimeLimit }
        }
        return order
      }))
    }, 1000)

    return () => clearInterval(interval)
  }, [])

  const updatePlayer = (updates: Partial<Player>) => {
    setPlayer(prev => ({ ...prev, ...updates }))
  }

  const updateEquipment = (equipmentId: string, updates: Partial<EquipmentData>) => {
    setEquipment(prev => prev.map(eq => 
      eq.id === equipmentId ? { ...eq, ...updates } : eq
    ))
  }

  const addExperience = (amount: number) => {
    setPlayer(prev => {
      const newTotalExp = prev.experience + amount
      const levelData = getLevelFromExperience(newTotalExp)
      const leveledUp = levelData.level > prev.level

      if (leveledUp) {
        setLevelUpRewards(levelData.rewards)
        setShowLevelUp(true)

        // Add skill points for level up
        const skillPointsGained = levelData.level % 2 === 0 ? 1 : 0

        return {
          ...prev,
          level: levelData.level,
          experience: newTotalExp,
          maxExperience: levelData.experienceRequired,
          skillPoints: prev.skillPoints + skillPointsGained
        }
      }

      return {
        ...prev,
        experience: newTotalExp,
        maxExperience: levelData.experienceRequired
      }
    })
  }

  const addMoney = (amount: number) => {
    setPlayer(prev => ({
      ...prev,
      money: prev.money + amount,
      totalMoneyEarned: prev.totalMoneyEarned + amount
    }))
  }

  const spendMoney = (amount: number): boolean => {
    if (player.money >= amount) {
      setPlayer(prev => ({ ...prev, money: prev.money - amount }))
      return true
    }
    return false
  }

  const useIngredient = (name: string, quantity: number): boolean => {
    const ingredient = inventory.find(ing => ing.name === name)
    if (ingredient && ingredient.quantity >= quantity) {
      setInventory(prev => prev.map(ing => 
        ing.name === name 
          ? { ...ing, quantity: ing.quantity - quantity }
          : ing
      ))
      return true
    }
    return false
  }

  const addIngredient = (name: string, quantity: number) => {
    setInventory(prev => prev.map(ing => 
      ing.name === name 
        ? { ...ing, quantity: ing.quantity + quantity }
        : ing
    ))
  }

  const acceptOrder = (orderId: string) => {
    setOrders(prev => prev.map(order => 
      order.id === orderId 
        ? { ...order, status: 'accepted' }
        : order
    ))
  }

  const completeOrder = (orderId: string) => {
    const order = orders.find(o => o.id === orderId)
    if (order) {
      // Check if player has required ingredients
      const canComplete = order.items.every(itemName => {
        const recipe = getRecipeById(itemName.toLowerCase().replace(/\s+/g, '_'))
        return recipe ? canCraftRecipe(recipe, inventory) : false
      })

      if (canComplete) {
        // Consume ingredients
        order.items.forEach(itemName => {
          const recipe = getRecipeById(itemName.toLowerCase().replace(/\s+/g, '_'))
          if (recipe) {
            recipe.ingredients.forEach(ingredient => {
              useIngredient(ingredient.name, ingredient.quantity)
            })
          }
        })

        // Complete order
        setOrders(prev => prev.map(o =>
          o.id === orderId
            ? { ...o, status: 'completed' }
            : o
        ))

        // Calculate rewards
        const timeBonus = order.timeLimit > 60 // Bonus if completed with time to spare
        const expReward = calculateExperienceReward(order.difficulty, timeBonus)

        addMoney(order.reward)
        addExperience(expReward)
      }
    }
  }

  const declineOrder = (orderId: string) => {
    setOrders(prev => prev.filter(order => order.id !== orderId))
  }

  const generateNewOrder = () => {
    const newOrder = generateRandomOrder(player.level)
    setOrders(prev => [...prev, newOrder])
  }

  const upgradeSkill = (skillId: string) => {
    const skill = skills.find(s => s.id === skillId)
    if (!skill || skill.level >= skill.maxLevel || player.skillPoints < skill.cost) {
      return
    }

    setSkills(prev => prev.map(s =>
      s.id === skillId
        ? { ...s, level: s.level + 1 }
        : s
    ))

    setPlayer(prev => ({
      ...prev,
      skillPoints: prev.skillPoints - skill.cost
    }))
  }

  const checkAchievements = () => {
    setAchievements(prev => prev.map(achievement => {
      if (achievement.completed) return achievement

      const requirement = achievement.requirements[0]
      let current = 0

      switch (requirement.type) {
        case 'orders_completed':
          current = player.totalOrdersCompleted
          break
        case 'money_earned':
          current = player.totalMoneyEarned
          break
        case 'recipes_unlocked':
          current = player.unlockedRecipes.length
          break
        case 'level_reached':
          current = player.level
          break
        case 'items_baked':
          current = player.totalItemsBaked
          break
      }

      const completed = current >= requirement.target

      return {
        ...achievement,
        requirements: [{ ...requirement, current }],
        completed
      }
    }))
  }

  const dismissLevelUp = () => {
    setShowLevelUp(false)
    setLevelUpRewards([])
  }

  const updateAutomationSettings = (updates: Partial<AutomationSettings>) => {
    setAutomationSettings(prev => ({ ...prev, ...updates }))
  }

  const purchaseAutomationUpgrade = (upgradeId: string) => {
    const upgrade = AUTOMATION_UPGRADES.find(u => u.id === upgradeId)
    if (!upgrade || player.money < upgrade.cost) return

    if (spendMoney(upgrade.cost)) {
      setPlayer(prev => ({
        ...prev,
        automationUpgrades: [...prev.automationUpgrades, upgradeId]
      }))
    }
  }

  const startAutomationJob = (equipmentId: string) => {
    if (!automationSettings.enabled) return

    const targetEquipment = equipment.find(eq => eq.id === equipmentId)
    if (!targetEquipment || targetEquipment.isActive || targetEquipment.automationLevel === 0) return

    const availableRecipes = getAvailableRecipes(player.level)
    const optimalRecipeId = selectOptimalRecipe(
      availableRecipes,
      inventory,
      automationSettings.priorityMode,
      orders
    )

    if (!optimalRecipeId) return

    const recipe = getRecipeById(optimalRecipeId)
    if (!recipe || !canCraftRecipe(recipe, inventory)) return

    const efficiency = calculateAutomationEfficiency(
      targetEquipment.efficiency,
      targetEquipment.automationLevel,
      player.automationUpgrades
    )

    const job = generateAutomationJob(equipmentId, optimalRecipeId, recipe, efficiency)

    // Start the job
    setAutomationJobs(prev => [...prev, { ...job, status: 'running' }])
    updateEquipment(equipmentId, {
      isActive: true,
      timeRemaining: job.duration,
      currentRecipe: recipe.name
    })

    // Consume ingredients
    job.ingredients.forEach(ingredient => {
      useIngredient(ingredient.name, ingredient.quantity)
    })
  }

  return (
    <GameContext.Provider value={{
      player,
      equipment,
      inventory,
      orders,
      achievements,
      skills,
      levelUpRewards,
      showLevelUp,
      automationSettings,
      automationJobs,
      conveyorBelts,
      updatePlayer,
      updateEquipment,
      addExperience,
      addMoney,
      spendMoney,
      useIngredient,
      addIngredient,
      acceptOrder,
      completeOrder,
      declineOrder,
      generateNewOrder,
      upgradeSkill,
      checkAchievements,
      dismissLevelUp,
      updateAutomationSettings,
      purchaseAutomationUpgrade,
      startAutomationJob
    }}>
      {children}
    </GameContext.Provider>
  )
}

export function useGame() {
  const context = useContext(GameContext)
  if (context === undefined) {
    throw new Error('useGame must be used within a GameProvider')
  }
  return context
}
