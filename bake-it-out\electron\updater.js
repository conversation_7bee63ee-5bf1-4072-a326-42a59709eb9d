const { autoUpdater } = require('electron-updater')
const { dialog, BrowserWindow } = require('electron')
const log = require('electron-log')

// Configure logging
log.transports.file.level = 'info'
autoUpdater.logger = log

class AppUpdater {
  constructor(mainWindow) {
    this.mainWindow = mainWindow
    this.setupAutoUpdater()
  }

  setupAutoUpdater() {
    // Configure auto-updater
    autoUpdater.checkForUpdatesAndNotify()
    
    // Auto-updater events
    autoUpdater.on('checking-for-update', () => {
      log.info('Checking for update...')
      this.sendStatusToWindow('Checking for update...')
    })

    autoUpdater.on('update-available', (info) => {
      log.info('Update available.')
      this.sendStatusToWindow('Update available.')
      
      // Show update available dialog
      dialog.showMessageBox(this.mainWindow, {
        type: 'info',
        title: 'Update Available',
        message: 'A new version of Bake It Out is available!',
        detail: `Version ${info.version} is now available. It will be downloaded in the background.`,
        buttons: ['OK']
      })
    })

    autoUpdater.on('update-not-available', (info) => {
      log.info('Update not available.')
      this.sendStatusToWindow('Update not available.')
    })

    autoUpdater.on('error', (err) => {
      log.error('Error in auto-updater. ' + err)
      this.sendStatusToWindow('Error in auto-updater: ' + err)
    })

    autoUpdater.on('download-progress', (progressObj) => {
      let log_message = "Download speed: " + progressObj.bytesPerSecond
      log_message = log_message + ' - Downloaded ' + progressObj.percent + '%'
      log_message = log_message + ' (' + progressObj.transferred + "/" + progressObj.total + ')'
      log.info(log_message)
      this.sendStatusToWindow(log_message)
    })

    autoUpdater.on('update-downloaded', (info) => {
      log.info('Update downloaded')
      this.sendStatusToWindow('Update downloaded')
      
      // Show update ready dialog
      dialog.showMessageBox(this.mainWindow, {
        type: 'info',
        title: 'Update Ready',
        message: 'Update downloaded successfully!',
        detail: 'The update will be applied when you restart Bake It Out. Would you like to restart now?',
        buttons: ['Restart Now', 'Later'],
        defaultId: 0
      }).then((result) => {
        if (result.response === 0) {
          autoUpdater.quitAndInstall()
        }
      })
    })
  }

  sendStatusToWindow(text) {
    log.info(text)
    if (this.mainWindow && this.mainWindow.webContents) {
      this.mainWindow.webContents.send('update-status', text)
    }
  }

  checkForUpdates() {
    autoUpdater.checkForUpdatesAndNotify()
  }

  downloadUpdate() {
    autoUpdater.downloadUpdate()
  }

  quitAndInstall() {
    autoUpdater.quitAndInstall()
  }
}

module.exports = AppUpdater
