{"version": 3, "sources": ["../src/errors.js"], "names": ["UnexpectedStateError", "ExtendableError", "constructor", "message", "code"], "mappings": ";;;;;;;AAIA;;;;AAFA;AAIO,MAAMA,oBAAN,SAAmCC,iBAAnC,CAAmD;AAGxDC,EAAAA,WAAW,CAAEC,OAAF,EAAmBC,IAAY,GAAG,wBAAlC,EAA4D;AACrE,UAAMD,OAAN;AAEA,SAAKC,IAAL,GAAYA,IAAZ;AACD;;AAPuD", "sourcesContent": ["// @flow\n\n/* eslint-disable fp/no-class, fp/no-this */\n\nimport ExtendableError from 'es6-error';\n\nexport class UnexpectedStateError extends ExtendableError {\n  code: string;\n\n  constructor (message: string, code: string = 'UNEXPECTED_STATE_ERROR') {\n    super(message);\n\n    this.code = code;\n  }\n}\n"], "file": "errors.js"}