{"version": 3, "file": "takeWhile.d.ts", "sourceRoot": "", "sources": ["../../../../src/internal/operators/takeWhile.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,wBAAwB,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAIrF,wBAAgB,SAAS,CAAC,CAAC,EAAE,SAAS,EAAE,kBAAkB,EAAE,SAAS,EAAE,IAAI,GAAG,wBAAwB,CAAC,CAAC,CAAC,CAAC;AAC1G,wBAAgB,SAAS,CAAC,CAAC,EAAE,SAAS,EAAE,kBAAkB,EAAE,SAAS,EAAE,KAAK,GAAG,gBAAgB,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AACrH,wBAAgB,SAAS,CAAC,CAAC,EAAE,SAAS,EAAE,kBAAkB,GAAG,gBAAgB,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AACnG,wBAAgB,SAAS,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,KAAK,KAAK,IAAI,CAAC,GAAG,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACtH,wBAAgB,SAAS,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,KAAK,KAAK,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,GAAG,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxI,wBAAgB,SAAS,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,KAAK,OAAO,EAAE,SAAS,CAAC,EAAE,OAAO,GAAG,wBAAwB,CAAC,CAAC,CAAC,CAAC"}