{"version": 3, "file": "socket.io.js", "sources": ["../../engine.io-parser/build/esm/commons.js", "../../engine.io-parser/build/esm/encodePacket.browser.js", "../../engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../../engine.io-parser/build/esm/decodePacket.browser.js", "../../engine.io-parser/build/esm/index.js", "../../socket.io-component-emitter/lib/esm/index.js", "../../engine.io-client/build/esm/globals.js", "../../engine.io-client/build/esm/util.js", "../../engine.io-client/build/esm/contrib/parseqs.js", "../../engine.io-client/build/esm/transport.js", "../../engine.io-client/build/esm/transports/polling.js", "../../engine.io-client/build/esm/contrib/has-cors.js", "../../engine.io-client/build/esm/transports/polling-xhr.js", "../../engine.io-client/build/esm/transports/websocket.js", "../../engine.io-client/build/esm/transports/webtransport.js", "../../engine.io-client/build/esm/transports/index.js", "../../engine.io-client/build/esm/contrib/parseuri.js", "../../engine.io-client/build/esm/socket.js", "../../engine.io-client/build/esm/index.js", "../../../node_modules/ms/index.js", "../node_modules/debug/src/common.js", "../node_modules/debug/src/browser.js", "../build/esm-debug/url.js", "../../socket.io-parser/build/esm/is-binary.js", "../../socket.io-parser/build/esm/binary.js", "../../socket.io-parser/build/esm/index.js", "../build/esm-debug/on.js", "../build/esm-debug/socket.js", "../build/esm-debug/contrib/backo2.js", "../build/esm-debug/manager.js", "../build/esm-debug/index.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach((key) => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data.arrayBuffer().then(toArray).then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, (encoded) => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n", "// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE, } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType),\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType),\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1),\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type],\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            if (data instanceof Blob) {\n                // from WebSocket + binaryType \"blob\"\n                return data;\n            }\n            else {\n                // from HTTP long-polling or WebTransport\n                return new Blob([data]);\n            }\n        case \"arraybuffer\":\n        default:\n            if (data instanceof ArrayBuffer) {\n                // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n    }\n};\n", "import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET, } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, (encodedPacket) => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, (encodedPacket) => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        },\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* State.READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* State.READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* State.READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* State.READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* State.READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* State.READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else if (state === 2 /* State.READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* State.READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        },\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload, };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\nexport const defaultBinaryType = \"arraybuffer\";\nexport function createCookieJar() { }\n", "import { globalThisShim as globalThis } from \"./globals.node.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n/**\n * Generates a random 8-characters string.\n */\nexport function randomString() {\n    return (Date.now().toString(36).substring(3) +\n        Math.random().toString(36).substring(2, 5));\n}\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nexport class TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n        this.supportsBinary = !opts.forceBase64;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n    createUri(schema, query = {}) {\n        return (schema +\n            \"://\" +\n            this._hostname() +\n            this._port() +\n            this.opts.path +\n            this._query(query));\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port &&\n            ((this.opts.secure && Number(this.opts.port !== 443)) ||\n                (!this.opts.secure && Number(this.opts.port) !== 80))) {\n            return \":\" + this.opts.port;\n        }\n        else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = encode(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { randomString } from \"../util.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nexport class Polling extends Transport {\n    constructor() {\n        super(...arguments);\n        this._polling = false;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this._poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this._polling || !this.writable) {\n            let total = 0;\n            if (this._polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    _poll() {\n        this._polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this._polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this._poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "import { Polling } from \"./polling.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globals.node.js\";\nimport { hasCORS } from \"../contrib/has-cors.js\";\nfunction empty() { }\nexport class BaseXHR extends Polling {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(createRequest, uri, opts) {\n        super();\n        this.createRequest = createRequest;\n        installTimerFunctions(this, opts);\n        this._opts = opts;\n        this._method = opts.method || \"GET\";\n        this._uri = uri;\n        this._data = undefined !== opts.data ? opts.data : null;\n        this._create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    _create() {\n        var _a;\n        const opts = pick(this._opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this._opts.xd;\n        const xhr = (this._xhr = this.createRequest(opts));\n        try {\n            xhr.open(this._method, this._uri, true);\n            try {\n                if (this._opts.extraHeaders) {\n                    // @ts-ignore\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this._opts.extraHeaders) {\n                        if (this._opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this._opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this._method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this._opts.withCredentials;\n            }\n            if (this._opts.requestTimeout) {\n                xhr.timeout = this._opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(\n                    // @ts-ignore\n                    xhr.getResponseHeader(\"set-cookie\"));\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this._onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this._onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this._data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this._onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this._index = Request.requestsCount++;\n            Request.requests[this._index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    _onError(err) {\n        this.emitReserved(\"error\", err, this._xhr);\n        this._cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    _cleanup(fromError) {\n        if (\"undefined\" === typeof this._xhr || null === this._xhr) {\n            return;\n        }\n        this._xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this._xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this._index];\n        }\n        this._xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    _onLoad() {\n        const data = this._xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this._cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this._cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\nconst hasXHR2 = (function () {\n    const xhr = newRequest({\n        xdomain: false,\n    });\n    return xhr && xhr.responseType !== null;\n})();\n/**\n * HTTP long-polling based on the built-in `XMLHttpRequest` object.\n *\n * Usage: browser\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */\nexport class XHR extends BaseXHR {\n    constructor(opts) {\n        super(opts);\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd }, this.opts);\n        return new Request(newRequest, this.uri(), opts);\n    }\n}\nfunction newRequest(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { pick, randomString } from \"../util.js\";\nimport { encodePacket } from \"engine.io-parser\";\nimport { globalThisShim as globalThis, nextTick } from \"../globals.node.js\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class BaseWS extends Transport {\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws = this.createSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    this.doWrite(packet, data);\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.onerror = () => { };\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\nconst WebSocketCtor = globalThis.WebSocket || globalThis.MozWebSocket;\n/**\n * WebSocket transport based on the built-in `WebSocket` object.\n *\n * Usage: browser, Node.js (since v21), Deno, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n * @see https://nodejs.org/api/globals.html#websocket\n */\nexport class WS extends BaseWS {\n    createSocket(uri, protocols, opts) {\n        return !isReactNative\n            ? protocols\n                ? new WebSocketCtor(uri, protocols)\n                : new WebSocketCtor(uri)\n            : new WebSocketCtor(uri, protocols, opts);\n    }\n    doWrite(_packet, data) {\n        this.ws.send(data);\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { nextTick } from \"../globals.node.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\n/**\n * WebTransport transport based on the built-in `WebTransport` object.\n *\n * Usage: browser, Node.js (with the `@fails-components/webtransport` package)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebTransport\n * @see https://caniuse.com/webtransport\n */\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        try {\n            // @ts-ignore\n            this._transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this._transport.closed\n            .then(() => {\n            this.onClose();\n        })\n            .catch((err) => {\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this._transport.ready.then(() => {\n            this._transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this._writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            return;\n                        }\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this._writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this._writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this._transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n", "import { XHR } from \"./polling-xhr.node.js\";\nimport { WS } from \"./websocket.node.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: XHR,\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    if (str.length > 8000) {\n        throw \"URI too long\";\n    }\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports as DEFAULT_TRANSPORTS } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { createCookieJar, defaultBinaryType, nextTick, } from \"./globals.node.js\";\nconst withEventListeners = typeof addEventListener === \"function\" &&\n    typeof removeEventListener === \"function\";\nconst OFFLINE_EVENT_LISTENERS = [];\nif (withEventListeners) {\n    // within a ServiceWorker, any event handler for the 'offline' event must be added on the initial evaluation of the\n    // script, so we create one single event listener here which will forward the event to the socket instances\n    addEventListener(\"offline\", () => {\n        OFFLINE_EVENT_LISTENERS.forEach((listener) => listener());\n    }, false);\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes without upgrade mechanism, which means that it will keep the first low-level transport that\n * successfully establishes the connection.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithoutUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithoutUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithUpgrade\n * @see Socket\n */\nexport class SocketWithoutUpgrade extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        this._prevBufferLen = 0;\n        this._pingInterval = -1;\n        this._pingTimeout = -1;\n        this._maxPayload = -1;\n        /**\n         * The expiration timestamp of the {@link _pingTimeoutTimer} object is tracked, in case the timer is throttled and the\n         * callback is not fired on time. This can happen for example when a laptop is suspended or when a phone is locked.\n         */\n        this._pingTimeoutTime = Infinity;\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            const parsedUri = parse(uri);\n            opts.hostname = parsedUri.host;\n            opts.secure =\n                parsedUri.protocol === \"https\" || parsedUri.protocol === \"wss\";\n            opts.port = parsedUri.port;\n            if (parsedUri.query)\n                opts.query = parsedUri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = [];\n        this._transportsByName = {};\n        opts.transports.forEach((t) => {\n            const transportName = t.prototype.name;\n            this.transports.push(transportName);\n            this._transportsByName[transportName] = t;\n        });\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        if (withEventListeners) {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this._beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this._offlineEventListener = () => {\n                    this._onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                OFFLINE_EVENT_LISTENERS.push(this._offlineEventListener);\n            }\n        }\n        if (this.opts.withCredentials) {\n            this._cookieJar = createCookieJar();\n        }\n        this._open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        return new this._transportsByName[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    _open() {\n        if (this.transports.length === 0) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        const transportName = this.opts.rememberUpgrade &&\n            SocketWithoutUpgrade.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1\n            ? \"websocket\"\n            : this.transports[0];\n        this.readyState = \"opening\";\n        const transport = this.createTransport(transportName);\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this._onDrain.bind(this))\n            .on(\"packet\", this._onPacket.bind(this))\n            .on(\"error\", this._onError.bind(this))\n            .on(\"close\", (reason) => this._onClose(\"transport close\", reason));\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        SocketWithoutUpgrade.priorWebsocketSuccess =\n            \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    _onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this._sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    this._resetPingTimeout();\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this._onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this._pingInterval = data.pingInterval;\n        this._pingTimeout = data.pingTimeout;\n        this._maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this._resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    _resetPingTimeout() {\n        this.clearTimeoutFn(this._pingTimeoutTimer);\n        const delay = this._pingInterval + this._pingTimeout;\n        this._pingTimeoutTime = Date.now() + delay;\n        this._pingTimeoutTimer = this.setTimeoutFn(() => {\n            this._onClose(\"ping timeout\");\n        }, delay);\n        if (this.opts.autoUnref) {\n            this._pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    _onDrain() {\n        this.writeBuffer.splice(0, this._prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this._prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this._getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this._prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    _getWritablePackets() {\n        const shouldCheckPayloadSize = this._maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this._maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Checks whether the heartbeat timer has expired but the socket has not yet been notified.\n     *\n     * Note: this method is private for now because it does not really fit the WebSocket API, but if we put it in the\n     * `write()` method then the message would not be buffered by the Socket.IO client.\n     *\n     * @return {boolean}\n     * @private\n     */\n    /* private */ _hasPingExpired() {\n        if (!this._pingTimeoutTime)\n            return true;\n        const hasExpired = Date.now() > this._pingTimeoutTime;\n        if (hasExpired) {\n            this._pingTimeoutTime = 0;\n            nextTick(() => {\n                this._onClose(\"ping timeout\");\n            }, this.setTimeoutFn);\n        }\n        return hasExpired;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a message. Alias of {@link Socket#write}.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    send(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    _sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this._onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    _onError(err) {\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        if (this.opts.tryAllTransports &&\n            this.transports.length > 1 &&\n            this.readyState === \"opening\") {\n            this.transports.shift();\n            return this._open();\n        }\n        this.emitReserved(\"error\", err);\n        this._onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    _onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this._pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (withEventListeners) {\n                if (this._beforeunloadEventListener) {\n                    removeEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n                }\n                if (this._offlineEventListener) {\n                    const i = OFFLINE_EVENT_LISTENERS.indexOf(this._offlineEventListener);\n                    if (i !== -1) {\n                        OFFLINE_EVENT_LISTENERS.splice(i, 1);\n                    }\n                }\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this._prevBufferLen = 0;\n        }\n    }\n}\nSocketWithoutUpgrade.protocol = protocol;\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see Socket\n */\nexport class SocketWithUpgrade extends SocketWithoutUpgrade {\n    constructor() {\n        super(...arguments);\n        this._upgrades = [];\n    }\n    onOpen() {\n        super.onOpen();\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            for (let i = 0; i < this._upgrades.length; i++) {\n                this._probe(this._upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    _probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    SocketWithoutUpgrade.priorWebsocketSuccess =\n                        \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this._upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    onHandshake(data) {\n        this._upgrades = this._filterUpgrades(data.upgrades);\n        super.onHandshake(data);\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    _filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        for (let i = 0; i < upgrades.length; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * @example\n * import { Socket } from \"engine.io-client\";\n *\n * const socket = new Socket();\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see SocketWithUpgrade\n */\nexport class Socket extends SocketWithUpgrade {\n    constructor(uri, opts = {}) {\n        const o = typeof uri === \"object\" ? uri : opts;\n        if (!o.transports ||\n            (o.transports && typeof o.transports[0] === \"string\")) {\n            o.transports = (o.transports || [\"polling\", \"websocket\", \"webtransport\"])\n                .map((transportName) => DEFAULT_TRANSPORTS[transportName])\n                .filter((t) => !!t);\n        }\n        super(uri, o);\n    }\n}\n", "import { Socket } from \"./socket.js\";\nexport { Socket };\nexport { SocketWithoutUpgrade, SocketWithUpgrade, } from \"./socket.js\";\nexport const protocol = Socket.protocol;\nexport { Transport, TransportError } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./globals.node.js\";\nexport { Fetch } from \"./transports/polling-fetch.js\";\nexport { XHR as NodeXHR } from \"./transports/polling-xhr.node.js\";\nexport { XHR } from \"./transports/polling-xhr.js\";\nexport { WS as NodeWebSocket } from \"./transports/websocket.node.js\";\nexport { WS as WebSocket } from \"./transports/websocket.js\";\nexport { WT as WebTransport } from \"./transports/webtransport.js\";\n", "/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar w = d * 7;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function(val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isFinite(val)) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'weeks':\n    case 'week':\n    case 'w':\n      return n * w;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (msAbs >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (msAbs >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (msAbs >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return plural(ms, msAbs, d, 'day');\n  }\n  if (msAbs >= h) {\n    return plural(ms, msAbs, h, 'hour');\n  }\n  if (msAbs >= m) {\n    return plural(ms, msAbs, m, 'minute');\n  }\n  if (msAbs >= s) {\n    return plural(ms, msAbs, s, 'second');\n  }\n  return ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, msAbs, n, name) {\n  var isPlural = msAbs >= n * 1.5;\n  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n}\n", "\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\n\nfunction setup(env) {\n\tcreateDebug.debug = createDebug;\n\tcreateDebug.default = createDebug;\n\tcreateDebug.coerce = coerce;\n\tcreateDebug.disable = disable;\n\tcreateDebug.enable = enable;\n\tcreateDebug.enabled = enabled;\n\tcreateDebug.humanize = require('ms');\n\tcreateDebug.destroy = destroy;\n\n\tObject.keys(env).forEach(key => {\n\t\tcreateDebug[key] = env[key];\n\t});\n\n\t/**\n\t* The currently active debug mode names, and names to skip.\n\t*/\n\n\tcreateDebug.names = [];\n\tcreateDebug.skips = [];\n\n\t/**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/\n\tcreateDebug.formatters = {};\n\n\t/**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/\n\tfunction selectColor(namespace) {\n\t\tlet hash = 0;\n\n\t\tfor (let i = 0; i < namespace.length; i++) {\n\t\t\thash = ((hash << 5) - hash) + namespace.charCodeAt(i);\n\t\t\thash |= 0; // Convert to 32bit integer\n\t\t}\n\n\t\treturn createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n\t}\n\tcreateDebug.selectColor = selectColor;\n\n\t/**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/\n\tfunction createDebug(namespace) {\n\t\tlet prevTime;\n\t\tlet enableOverride = null;\n\t\tlet namespacesCache;\n\t\tlet enabledCache;\n\n\t\tfunction debug(...args) {\n\t\t\t// Disabled?\n\t\t\tif (!debug.enabled) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = debug;\n\n\t\t\t// Set `diff` timestamp\n\t\t\tconst curr = Number(new Date());\n\t\t\tconst ms = curr - (prevTime || curr);\n\t\t\tself.diff = ms;\n\t\t\tself.prev = prevTime;\n\t\t\tself.curr = curr;\n\t\t\tprevTime = curr;\n\n\t\t\targs[0] = createDebug.coerce(args[0]);\n\n\t\t\tif (typeof args[0] !== 'string') {\n\t\t\t\t// Anything else let's inspect with %O\n\t\t\t\targs.unshift('%O');\n\t\t\t}\n\n\t\t\t// Apply any `formatters` transformations\n\t\t\tlet index = 0;\n\t\t\targs[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {\n\t\t\t\t// If we encounter an escaped % then don't increase the array index\n\t\t\t\tif (match === '%%') {\n\t\t\t\t\treturn '%';\n\t\t\t\t}\n\t\t\t\tindex++;\n\t\t\t\tconst formatter = createDebug.formatters[format];\n\t\t\t\tif (typeof formatter === 'function') {\n\t\t\t\t\tconst val = args[index];\n\t\t\t\t\tmatch = formatter.call(self, val);\n\n\t\t\t\t\t// Now we need to remove `args[index]` since it's inlined in the `format`\n\t\t\t\t\targs.splice(index, 1);\n\t\t\t\t\tindex--;\n\t\t\t\t}\n\t\t\t\treturn match;\n\t\t\t});\n\n\t\t\t// Apply env-specific formatting (colors, etc.)\n\t\t\tcreateDebug.formatArgs.call(self, args);\n\n\t\t\tconst logFn = self.log || createDebug.log;\n\t\t\tlogFn.apply(self, args);\n\t\t}\n\n\t\tdebug.namespace = namespace;\n\t\tdebug.useColors = createDebug.useColors();\n\t\tdebug.color = createDebug.selectColor(namespace);\n\t\tdebug.extend = extend;\n\t\tdebug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n\n\t\tObject.defineProperty(debug, 'enabled', {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: false,\n\t\t\tget: () => {\n\t\t\t\tif (enableOverride !== null) {\n\t\t\t\t\treturn enableOverride;\n\t\t\t\t}\n\t\t\t\tif (namespacesCache !== createDebug.namespaces) {\n\t\t\t\t\tnamespacesCache = createDebug.namespaces;\n\t\t\t\t\tenabledCache = createDebug.enabled(namespace);\n\t\t\t\t}\n\n\t\t\t\treturn enabledCache;\n\t\t\t},\n\t\t\tset: v => {\n\t\t\t\tenableOverride = v;\n\t\t\t}\n\t\t});\n\n\t\t// Env-specific initialization logic for debug instances\n\t\tif (typeof createDebug.init === 'function') {\n\t\t\tcreateDebug.init(debug);\n\t\t}\n\n\t\treturn debug;\n\t}\n\n\tfunction extend(namespace, delimiter) {\n\t\tconst newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n\t\tnewDebug.log = this.log;\n\t\treturn newDebug;\n\t}\n\n\t/**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/\n\tfunction enable(namespaces) {\n\t\tcreateDebug.save(namespaces);\n\t\tcreateDebug.namespaces = namespaces;\n\n\t\tcreateDebug.names = [];\n\t\tcreateDebug.skips = [];\n\n\t\tlet i;\n\t\tconst split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n\t\tconst len = split.length;\n\n\t\tfor (i = 0; i < len; i++) {\n\t\t\tif (!split[i]) {\n\t\t\t\t// ignore empty strings\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tnamespaces = split[i].replace(/\\*/g, '.*?');\n\n\t\t\tif (namespaces[0] === '-') {\n\t\t\t\tcreateDebug.skips.push(new RegExp('^' + namespaces.slice(1) + '$'));\n\t\t\t} else {\n\t\t\t\tcreateDebug.names.push(new RegExp('^' + namespaces + '$'));\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/\n\tfunction disable() {\n\t\tconst namespaces = [\n\t\t\t...createDebug.names.map(toNamespace),\n\t\t\t...createDebug.skips.map(toNamespace).map(namespace => '-' + namespace)\n\t\t].join(',');\n\t\tcreateDebug.enable('');\n\t\treturn namespaces;\n\t}\n\n\t/**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/\n\tfunction enabled(name) {\n\t\tif (name[name.length - 1] === '*') {\n\t\t\treturn true;\n\t\t}\n\n\t\tlet i;\n\t\tlet len;\n\n\t\tfor (i = 0, len = createDebug.skips.length; i < len; i++) {\n\t\t\tif (createDebug.skips[i].test(name)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tfor (i = 0, len = createDebug.names.length; i < len; i++) {\n\t\t\tif (createDebug.names[i].test(name)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t* Convert regexp to namespace\n\t*\n\t* @param {RegExp} regxep\n\t* @return {String} namespace\n\t* @api private\n\t*/\n\tfunction toNamespace(regexp) {\n\t\treturn regexp.toString()\n\t\t\t.substring(2, regexp.toString().length - 2)\n\t\t\t.replace(/\\.\\*\\?$/, '*');\n\t}\n\n\t/**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/\n\tfunction coerce(val) {\n\t\tif (val instanceof Error) {\n\t\t\treturn val.stack || val.message;\n\t\t}\n\t\treturn val;\n\t}\n\n\t/**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/\n\tfunction destroy() {\n\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t}\n\n\tcreateDebug.enable(createDebug.load());\n\n\treturn createDebug;\n}\n\nmodule.exports = setup;\n", "/* eslint-env browser */\n\n/**\n * This is the web browser implementation of `debug()`.\n */\n\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (() => {\n\tlet warned = false;\n\n\treturn () => {\n\t\tif (!warned) {\n\t\t\twarned = true;\n\t\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t\t}\n\t};\n})();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n\t'#0000CC',\n\t'#0000FF',\n\t'#0033CC',\n\t'#0033FF',\n\t'#0066CC',\n\t'#0066FF',\n\t'#0099CC',\n\t'#0099FF',\n\t'#00CC00',\n\t'#00CC33',\n\t'#00CC66',\n\t'#00CC99',\n\t'#00CCCC',\n\t'#00CCFF',\n\t'#3300CC',\n\t'#3300FF',\n\t'#3333CC',\n\t'#3333FF',\n\t'#3366CC',\n\t'#3366FF',\n\t'#3399CC',\n\t'#3399FF',\n\t'#33CC00',\n\t'#33CC33',\n\t'#33CC66',\n\t'#33CC99',\n\t'#33CCCC',\n\t'#33CCFF',\n\t'#6600CC',\n\t'#6600FF',\n\t'#6633CC',\n\t'#6633FF',\n\t'#66CC00',\n\t'#66CC33',\n\t'#9900CC',\n\t'#9900FF',\n\t'#9933CC',\n\t'#9933FF',\n\t'#99CC00',\n\t'#99CC33',\n\t'#CC0000',\n\t'#CC0033',\n\t'#CC0066',\n\t'#CC0099',\n\t'#CC00CC',\n\t'#CC00FF',\n\t'#CC3300',\n\t'#CC3333',\n\t'#CC3366',\n\t'#CC3399',\n\t'#CC33CC',\n\t'#CC33FF',\n\t'#CC6600',\n\t'#CC6633',\n\t'#CC9900',\n\t'#CC9933',\n\t'#CCCC00',\n\t'#CCCC33',\n\t'#FF0000',\n\t'#FF0033',\n\t'#FF0066',\n\t'#FF0099',\n\t'#FF00CC',\n\t'#FF00FF',\n\t'#FF3300',\n\t'#FF3333',\n\t'#FF3366',\n\t'#FF3399',\n\t'#FF33CC',\n\t'#FF33FF',\n\t'#FF6600',\n\t'#FF6633',\n\t'#FF9900',\n\t'#FF9933',\n\t'#FFCC00',\n\t'#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\n// eslint-disable-next-line complexity\nfunction useColors() {\n\t// NB: In an Electron preload script, document will be defined but not fully\n\t// initialized. Since we know we're in Chrome, we'll just detect this case\n\t// explicitly\n\tif (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {\n\t\treturn true;\n\t}\n\n\t// Internet Explorer and Edge do not support colors.\n\tif (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t\treturn false;\n\t}\n\n\t// Is webkit? http://stackoverflow.com/a/16459606/376773\n\t// document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\treturn (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n\t\t// Is firebug? http://stackoverflow.com/a/398120/376773\n\t\t(typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n\t\t// Is firefox >= v31?\n\t\t// https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31) ||\n\t\t// Double check webkit in userAgent just in case we are in a worker\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n\targs[0] = (this.useColors ? '%c' : '') +\n\t\tthis.namespace +\n\t\t(this.useColors ? ' %c' : ' ') +\n\t\targs[0] +\n\t\t(this.useColors ? '%c ' : ' ') +\n\t\t'+' + module.exports.humanize(this.diff);\n\n\tif (!this.useColors) {\n\t\treturn;\n\t}\n\n\tconst c = 'color: ' + this.color;\n\targs.splice(1, 0, c, 'color: inherit');\n\n\t// The final \"%c\" is somewhat tricky, because there could be other\n\t// arguments passed either before or after the %c, so we need to\n\t// figure out the correct index to insert the CSS into\n\tlet index = 0;\n\tlet lastC = 0;\n\targs[0].replace(/%[a-zA-Z%]/g, match => {\n\t\tif (match === '%%') {\n\t\t\treturn;\n\t\t}\n\t\tindex++;\n\t\tif (match === '%c') {\n\t\t\t// We only are interested in the *last* %c\n\t\t\t// (the user may have provided their own)\n\t\t\tlastC = index;\n\t\t}\n\t});\n\n\targs.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */\nexports.log = console.debug || console.log || (() => {});\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\nfunction save(namespaces) {\n\ttry {\n\t\tif (namespaces) {\n\t\t\texports.storage.setItem('debug', namespaces);\n\t\t} else {\n\t\t\texports.storage.removeItem('debug');\n\t\t}\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\nfunction load() {\n\tlet r;\n\ttry {\n\t\tr = exports.storage.getItem('debug');\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n\n\t// If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\tif (!r && typeof process !== 'undefined' && 'env' in process) {\n\t\tr = process.env.DEBUG;\n\t}\n\n\treturn r;\n}\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n\ttry {\n\t\t// TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n\t\t// The Browser also has localStorage in the global context.\n\t\treturn localStorage;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\nmodule.exports = require('./common')(exports);\n\nconst {formatters} = module.exports;\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nformatters.j = function (v) {\n\ttry {\n\t\treturn JSON.stringify(v);\n\t} catch (error) {\n\t\treturn '[UnexpectedJSONParseError]: ' + error.message;\n\t}\n};\n", "import { parse } from \"engine.io-client\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"socket.io-client:url\"); // debug()\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            debug(\"protocol-less url %s\", uri);\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        debug(\"parse %s\", uri);\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n", "const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexport function hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n", "import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    delete packet.attachments; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder === true) {\n        const isIndexValid = typeof data.num === \"number\" &&\n            data.num >= 0 &&\n            data.num < buffers.length;\n        if (isIndexValid) {\n            return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n        }\n        else {\n            throw new Error(\"illegal attachments\");\n        }\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n", "import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * These strings must not be used as event names, as they have a special meaning.\n */\nconst RESERVED_EVENTS = [\n    \"connect\", // used on the client side\n    \"connect_error\", // used on the client side\n    \"disconnect\", // used on both sides\n    \"disconnecting\", // used on the server side\n    \"newListener\", // used by the Node.js EventEmitter\n    \"removeListener\", // used by the Node.js EventEmitter\n];\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */\n    constructor(replacer) {\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (hasBinary(obj)) {\n                return this.encodeAsBinary({\n                    type: obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK,\n                    nsp: obj.nsp,\n                    data: obj.data,\n                    id: obj.id,\n                });\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */\n    constructor(reviver) {\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            if (this.reconstructor) {\n                throw new Error(\"got plaintext data when reconstructing a packet\");\n            }\n            packet = this.decodeString(obj);\n            const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n            if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n                packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK;\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        }\n        else if (isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return isObject(payload);\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || isObject(payload);\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return (Array.isArray(payload) &&\n                    (typeof payload[0] === \"number\" ||\n                        (typeof payload[0] === \"string\" &&\n                            RESERVED_EVENTS.indexOf(payload[0]) === -1)));\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n            this.reconstructor = null;\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\nfunction isNamespaceValid(nsp) {\n    return typeof nsp === \"string\";\n}\n// see https://caniuse.com/mdn-javascript_builtins_number_isinteger\nconst isInteger = Number.isInteger ||\n    function (value) {\n        return (typeof value === \"number\" &&\n            isFinite(value) &&\n            Math.floor(value) === value);\n    };\nfunction isAckIdValid(id) {\n    return id === undefined || isInteger(id);\n}\n// see https://stackoverflow.com/questions/8511281/check-if-a-value-is-an-object-in-javascript\nfunction isObject(value) {\n    return Object.prototype.toString.call(value) === \"[object Object]\";\n}\nfunction isDataValid(type, payload) {\n    switch (type) {\n        case PacketType.CONNECT:\n            return payload === undefined || isObject(payload);\n        case PacketType.DISCONNECT:\n            return payload === undefined;\n        case PacketType.EVENT:\n            return (Array.isArray(payload) &&\n                (typeof payload[0] === \"number\" ||\n                    (typeof payload[0] === \"string\" &&\n                        RESERVED_EVENTS.indexOf(payload[0]) === -1)));\n        case PacketType.ACK:\n            return Array.isArray(payload);\n        case PacketType.CONNECT_ERROR:\n            return typeof payload === \"string\" || isObject(payload);\n        default:\n            return false;\n    }\n}\nexport function isPacketValid(packet) {\n    return (isNamespaceValid(packet.nsp) &&\n        isAckIdValid(packet.id) &&\n        isDataValid(packet.type, packet.data));\n}\n", "export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n", "import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"socket.io-client:socket\"); // debug()\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     */\n    constructor(io, nsp, opts) {\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */\n        this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */\n        this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */\n        this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */\n        this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */\n        this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */\n        this._queueSeq = 0;\n        this.ids = 0;\n        /**\n         * A map containing acknowledgement handlers.\n         *\n         * The `withError` attribute is used to differentiate handlers that accept an error as first argument:\n         *\n         * - `socket.emit(\"test\", (err, value) => { ... })` with `ackTimeout` option\n         * - `socket.timeout(5000).emit(\"test\", (err, value) => { ... })`\n         * - `const value = await socket.emitWithAck(\"test\")`\n         *\n         * From those that don't:\n         *\n         * - `socket.emit(\"test\", (value) => { ... });`\n         *\n         * In the first case, the handlers will be called with an error when:\n         *\n         * - the timeout is reached\n         * - the socket gets disconnected\n         *\n         * In the second case, the handlers will be simply discarded upon disconnection, since the client will never receive\n         * an acknowledgement from the server.\n         *\n         * @private\n         */\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */\n    emit(ev, ...args) {\n        var _a, _b, _c;\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            debug(\"emitting packet with ack id %d\", id);\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = (_b = (_a = this.io.engine) === null || _a === void 0 ? void 0 : _a.transport) === null || _b === void 0 ? void 0 : _b.writable;\n        const isConnected = this.connected && !((_c = this.io.engine) === null || _c === void 0 ? void 0 : _c._hasPingExpired());\n        const discardPacket = this.flags.volatile && !isTransportWritable;\n        if (discardPacket) {\n            debug(\"discard packet as the transport is not currently writable\");\n        }\n        else if (isConnected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    debug(\"removing packet with ack id %d from the buffer\", id);\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            debug(\"event with ack id %d has timed out after %d ms\", id, timeout);\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        const fn = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, args);\n        };\n        fn.withError = true;\n        this.acks[id] = fn;\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */\n    emitWithAck(ev, ...args) {\n        return new Promise((resolve, reject) => {\n            const fn = (arg1, arg2) => {\n                return arg1 ? reject(arg1) : resolve(arg2);\n            };\n            fn.withError = true;\n            args.push(fn);\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */\n    _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({ fromQueue: true }, this.flags),\n        };\n        args.push((err, ...responseArgs) => {\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    debug(\"packet [%d] is discarded after %d tries\", packet.id, packet.tryCount);\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            }\n            else {\n                debug(\"packet [%d] was successfully sent\", packet.id);\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */\n    _drainQueue(force = false) {\n        debug(\"draining queue\");\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            debug(\"packet [%d] has already been sent and is waiting for an ack\", packet.id);\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        debug(\"sending packet [%d] (try n°%d)\", packet.id, packet.tryCount);\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        debug(\"transport is open - connecting\");\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this._sendConnectPacket(data);\n            });\n        }\n        else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */\n    _sendConnectPacket(data) {\n        this.packet({\n            type: PacketType.CONNECT,\n            data: this._pid\n                ? Object.assign({ pid: this._pid, offset: this._lastOffset }, data)\n                : data,\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        debug(\"close (%s)\", reason);\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n        this._clearAcks();\n    }\n    /**\n     * Clears the acknowledgement handlers upon disconnection, since the client will never receive an acknowledgement from\n     * the server.\n     *\n     * @private\n     */\n    _clearAcks() {\n        Object.keys(this.acks).forEach((id) => {\n            const isBuffered = this.sendBuffer.some((packet) => String(packet.id) === id);\n            if (!isBuffered) {\n                // note: handlers that do not accept an error as first argument are ignored here\n                const ack = this.acks[id];\n                delete this.acks[id];\n                if (ack.withError) {\n                    ack.call(this, new Error(\"socket has been disconnected\"));\n                }\n            }\n        });\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        debug(\"emitting event %j\", args);\n        if (null != packet.id) {\n            debug(\"attaching ack callback to event\");\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            debug(\"sending ack %j\", args);\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowledgement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (typeof ack !== \"function\") {\n            debug(\"bad ack %s\", packet.id);\n            return;\n        }\n        delete this.acks[packet.id];\n        debug(\"calling ack %s with %j\", packet.id, packet.data);\n        // @ts-ignore FIXME ack is incorrectly inferred as 'never'\n        if (ack.withError) {\n            packet.data.unshift(null);\n        }\n        // @ts-ignore\n        ack.apply(this, packet.data);\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id, pid) {\n        debug(\"socket connected with id %s\", id);\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        debug(\"server disconnect (%s)\", this.nsp);\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */\n    disconnect() {\n        if (this.connected) {\n            debug(\"performing disconnect (%s)\", this.nsp);\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n", "/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n", "import { Socket as Engine, installTimerFunctions, nextTick, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"socket.io-client:manager\"); // debug()\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        if (!v) {\n            this.skipReconnect = true;\n        }\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        debug(\"readyState %s\", this._readyState);\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        debug(\"opening %s\", this.uri);\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        const onError = (err) => {\n            debug(\"error\");\n            this.cleanup();\n            this._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                this.maybeReconnectOnOpen();\n            }\n        };\n        // emit `error`\n        const errorSub = on(socket, \"error\", onError);\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            debug(\"connect attempt will timeout after %d\", timeout);\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                debug(\"connect attempt timed out after %d\", timeout);\n                openSubDestroy();\n                onError(new Error(\"timeout\"));\n                socket.close();\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        debug(\"open\");\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), \n        // @ts-ignore\n        on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        try {\n            this.decoder.add(data);\n        }\n        catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        nextTick(() => {\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        debug(\"error\", err);\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                debug(\"socket %s is still active, skipping close\", nsp);\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        debug(\"writing packet %j\", packet);\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        debug(\"cleanup\");\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        debug(\"disconnect\");\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called when:\n     *\n     * - the low-level engine is closed\n     * - the parser encountered a badly formatted packet\n     * - all sockets are disconnected\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        var _a;\n        debug(\"closed due to %s\", reason);\n        this.cleanup();\n        (_a = this.engine) === null || _a === void 0 ? void 0 : _a.close();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            debug(\"reconnect failed\");\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            debug(\"will wait %dms before reconnect attempt\", delay);\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                debug(\"attempting reconnect\");\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        debug(\"reconnect attempt error\");\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        debug(\"reconnect success\");\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n", "import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"socket.io-client\"); // debug()\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        debug(\"ignoring socket cache for %s\", source);\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            debug(\"new io instance for %s\", source);\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\nexport { Fetch, NodeXHR, XHR, NodeWebSocket, WebSocket, WebTransport, } from \"engine.io-client\";\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "encodePacket", "_ref", "supportsBinary", "callback", "encodeBlobAsBase64", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "toArray", "Uint8Array", "byteOffset", "byteLength", "TEXT_ENCODER", "encodePacketToBinary", "packet", "arrayBuffer", "then", "encoded", "TextEncoder", "encode", "chars", "lookup", "i", "length", "charCodeAt", "decode", "base64", "bufferLength", "len", "p", "encoded1", "encoded2", "encoded3", "encoded4", "arraybuffer", "bytes", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "packetType", "decoded", "SEPARATOR", "String", "fromCharCode", "encodePayload", "packets", "encodedPackets", "Array", "count", "join", "decodePayload", "encodedPayload", "decodedPacket", "push", "createPacketEncoderStream", "TransformStream", "transform", "controller", "payloadLength", "header", "DataView", "setUint8", "view", "setUint16", "setBigUint64", "BigInt", "enqueue", "TEXT_DECODER", "totalLength", "chunks", "reduce", "acc", "chunk", "concatChunks", "size", "shift", "j", "slice", "createPacketDecoderStream", "maxPayload", "TextDecoder", "state", "<PERSON><PERSON><PERSON><PERSON>", "isBinary", "headerArray", "getUint16", "n", "getUint32", "Math", "pow", "protocol", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "_callbacks", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "callbacks", "cb", "splice", "emit", "args", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "nextTick", "isPromiseAvailable", "Promise", "resolve", "setTimeoutFn", "globalThisShim", "self", "window", "Function", "defaultBinaryType", "createCookieJar", "pick", "_len", "attr", "_key", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "globalThis", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "bind", "clearTimeoutFn", "BASE64_OVERHEAD", "utf8Length", "ceil", "str", "c", "l", "randomString", "Date", "now", "random", "encodeURIComponent", "qs", "qry", "pairs", "pair", "decodeURIComponent", "TransportError", "_Error", "reason", "description", "context", "_this", "_inherits<PERSON><PERSON>e", "_wrapNativeSuper", "Error", "Transport", "_Emitter", "_this2", "writable", "query", "socket", "forceBase64", "_proto", "onError", "open", "readyState", "doOpen", "close", "doClose", "onClose", "send", "write", "onOpen", "onData", "onPacket", "details", "pause", "onPause", "createUri", "schema", "undefined", "_hostname", "_port", "path", "_query", "hostname", "indexOf", "port", "secure", "Number", "<PERSON><PERSON><PERSON><PERSON>", "Polling", "_Transport", "_polling", "_poll", "total", "doPoll", "_this3", "_this4", "_this5", "doWrite", "uri", "timestampRequests", "timestampParam", "sid", "b64", "_createClass", "get", "value", "XMLHttpRequest", "err", "hasCORS", "empty", "BaseXHR", "_Polling", "location", "isSSL", "xd", "req", "request", "method", "xhrStatus", "pollXhr", "Request", "createRequest", "_opts", "_method", "_uri", "_data", "_create", "_proto2", "_a", "xdomain", "xhr", "_xhr", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "e", "cookieJar", "addCookies", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "parseCookies", "getResponseHeader", "status", "_onLoad", "_onError", "document", "_index", "requestsCount", "requests", "_cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "terminationEvent", "hasXHR2", "newRequest", "responseType", "XHR", "_BaseXHR", "_this6", "_proto3", "_extends", "concat", "isReactNative", "navigator", "product", "toLowerCase", "BaseWS", "protocols", "headers", "ws", "createSocket", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "_loop", "lastPacket", "WebSocketCtor", "WebSocket", "MozWebSocket", "WS", "_BaseWS", "_packet", "WT", "_transport", "WebTransport", "transportOptions", "name", "closed", "ready", "createBidirectionalStream", "stream", "decoderStream", "MAX_SAFE_INTEGER", "reader", "readable", "pipeThrough", "<PERSON><PERSON><PERSON><PERSON>", "encoderStream", "pipeTo", "_writer", "getWriter", "read", "done", "transports", "websocket", "webtransport", "polling", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "query<PERSON><PERSON>", "regx", "names", "$0", "$1", "$2", "withEventListeners", "OFFLINE_EVENT_LISTENERS", "listener", "SocketWithoutUpgrade", "writeBuffer", "_prevBufferLen", "_pingInterval", "_pingTimeout", "_maxPayload", "_pingTimeoutTime", "Infinity", "_typeof", "parsed<PERSON><PERSON>", "_transportsByName", "t", "transportName", "agent", "upgrade", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "closeOnBeforeunload", "_beforeunloadEventListener", "transport", "_offlineEventListener", "_onClose", "_cookieJar", "_open", "createTransport", "EIO", "id", "priorWebsocketSuccess", "setTransport", "_onDrain", "_onPacket", "flush", "onHandshake", "JSON", "_sendPacket", "_resetPingTimeout", "code", "pingInterval", "pingTimeout", "_pingTimeoutTimer", "delay", "upgrading", "_getWritablePackets", "shouldCheckPayloadSize", "payloadSize", "_hasPingExpired", "hasExpired", "msg", "options", "compress", "cleanupAndClose", "waitForUpgrade", "tryAllTransports", "SocketWithUpgrade", "_SocketWithoutUpgrade", "_this7", "_upgrades", "_probe", "_this8", "failed", "onTransportOpen", "cleanup", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "_filterUpgrades", "upgrades", "filteredUpgrades", "Socket", "_SocketWithUpgrade", "o", "map", "DEFAULT_TRANSPORTS", "filter", "s", "h", "d", "w", "y", "ms", "val", "isFinite", "fmtLong", "fmtShort", "stringify", "match", "parseFloat", "msAbs", "abs", "round", "plural", "isPlural", "setup", "env", "createDebug", "debug", "coerce", "disable", "enable", "enabled", "humanize", "require$$0", "destroy", "skips", "formatters", "selectColor", "namespace", "hash", "colors", "prevTime", "enableOverride", "namespacesCache", "enabledCache", "curr", "diff", "prev", "unshift", "index", "format", "formatter", "formatArgs", "logFn", "log", "useColors", "color", "extend", "defineProperty", "enumerable", "configurable", "namespaces", "set", "v", "init", "delimiter", "newDebug", "save", "RegExp", "_toConsumableArray", "toNamespace", "test", "regexp", "stack", "message", "console", "warn", "load", "common", "exports", "storage", "localstorage", "warned", "process", "__nwjs", "userAgent", "documentElement", "style", "WebkitAppearance", "firebug", "exception", "table", "parseInt", "module", "lastC", "setItem", "removeItem", "r", "getItem", "DEBUG", "localStorage", "debugModule", "url", "loc", "ipv6", "href", "withNativeFile", "File", "hasBinary", "toJSON", "isArray", "deconstructPacket", "buffers", "packetData", "pack", "_deconstructPacket", "attachments", "placeholder", "_placeholder", "num", "newData", "reconstructPacket", "_reconstructPacket", "isIndexValid", "RESERVED_EVENTS", "PacketType", "Encoder", "replacer", "EVENT", "ACK", "encodeAsBinary", "BINARY_EVENT", "BINARY_ACK", "nsp", "encodeAsString", "deconstruction", "Decoder", "reviver", "add", "reconstructor", "decodeString", "isBinaryEvent", "BinaryReconstructor", "takeBinaryData", "start", "buf", "next", "payload", "try<PERSON><PERSON><PERSON>", "substr", "isPayloadValid", "CONNECT", "isObject", "DISCONNECT", "CONNECT_ERROR", "finishedReconstruction", "reconPack", "binData", "isNamespaceValid", "isInteger", "floor", "isAckIdValid", "isDataValid", "isPacketValid", "subDestroy", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "io", "connected", "recovered", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "_queue", "_queueSeq", "ids", "acks", "flags", "auth", "_autoConnect", "subEvents", "subs", "onpacket", "_readyState", "_b", "_c", "_len2", "_key2", "retries", "fromQueue", "_addToQueue", "ack", "pop", "_registerAckCallback", "isTransportWritable", "engine", "isConnected", "discardPacket", "notifyOutgoingListeners", "ackTimeout", "timer", "_len3", "_key3", "with<PERSON><PERSON><PERSON>", "emitWithAck", "_len4", "_key4", "reject", "arg1", "arg2", "tryCount", "pending", "<PERSON><PERSON><PERSON><PERSON>", "_len5", "responseArgs", "_key5", "_drainQueue", "force", "_sendConnectPacket", "_pid", "pid", "offset", "_lastOffset", "_clearAcks", "isBuffered", "some", "sameNamespace", "onconnect", "onevent", "onack", "ondisconnect", "emitEvent", "_anyListeners", "_iterator", "_createForOfIteratorHelper", "_step", "f", "sent", "_len6", "_key6", "emitBuffered", "onAny", "prependAny", "offAny", "listenersAny", "onAnyOutgoing", "_anyOutgoingListeners", "prependAnyOutgoing", "offAnyOutgoing", "listenersAnyOutgoing", "_iterator2", "_step2", "Backoff", "min", "max", "factor", "jitter", "attempts", "duration", "rand", "deviation", "reset", "setMin", "setMax", "setJitter", "Manager", "nsps", "reconnection", "reconnectionAttempts", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "_parser", "parser", "encoder", "decoder", "autoConnect", "_reconnection", "skipReconnect", "_reconnectionAttempts", "_reconnectionDelay", "_randomizationFactor", "_reconnectionDelayMax", "_timeout", "maybeReconnectOnOpen", "_reconnecting", "reconnect", "Engine", "openSubDestroy", "errorSub", "onping", "ondata", "ondecoded", "active", "_destroy", "_i", "_nsps", "_close", "onreconnect", "attempt", "cache", "parsed", "newConnection", "forceNew", "multiplex"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA,IAAMA,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;EACzCF,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;EAC1BA,YAAY,CAAC,OAAO,CAAC,GAAG,GAAG,CAAA;EAC3BA,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;EAC1BA,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;EAC1BA,YAAY,CAAC,SAAS,CAAC,GAAG,GAAG,CAAA;EAC7BA,YAAY,CAAC,SAAS,CAAC,GAAG,GAAG,CAAA;EAC7BA,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;EAC1B,IAAMG,oBAAoB,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAA;EAChDD,MAAM,CAACG,IAAI,CAACJ,YAAY,CAAC,CAACK,OAAO,CAAC,UAACC,GAAG,EAAK;EACvCH,EAAAA,oBAAoB,CAACH,YAAY,CAACM,GAAG,CAAC,CAAC,GAAGA,GAAG,CAAA;EACjD,CAAC,CAAC,CAAA;EACF,IAAMC,YAAY,GAAG;EAAEC,EAAAA,IAAI,EAAE,OAAO;EAAEC,EAAAA,IAAI,EAAE,cAAA;EAAe,CAAC;;ECX5D,IAAMC,gBAAc,GAAG,OAAOC,IAAI,KAAK,UAAU,IAC5C,OAAOA,IAAI,KAAK,WAAW,IACxBV,MAAM,CAACW,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACH,IAAI,CAAC,KAAK,0BAA2B,CAAA;EAC5E,IAAMI,uBAAqB,GAAG,OAAOC,WAAW,KAAK,UAAU,CAAA;EAC/D;EACA,IAAMC,QAAM,GAAG,SAATA,MAAMA,CAAIC,GAAG,EAAK;IACpB,OAAO,OAAOF,WAAW,CAACC,MAAM,KAAK,UAAU,GACzCD,WAAW,CAACC,MAAM,CAACC,GAAG,CAAC,GACvBA,GAAG,IAAIA,GAAG,CAACC,MAAM,YAAYH,WAAW,CAAA;EAClD,CAAC,CAAA;EACD,IAAMI,YAAY,GAAG,SAAfA,YAAYA,CAAAC,IAAA,EAAoBC,cAAc,EAAEC,QAAQ,EAAK;EAAA,EAAA,IAA3Cf,IAAI,GAAAa,IAAA,CAAJb,IAAI;MAAEC,IAAI,GAAAY,IAAA,CAAJZ,IAAI,CAAA;EAC9B,EAAA,IAAIC,gBAAc,IAAID,IAAI,YAAYE,IAAI,EAAE;EACxC,IAAA,IAAIW,cAAc,EAAE;QAChB,OAAOC,QAAQ,CAACd,IAAI,CAAC,CAAA;EACzB,KAAC,MACI;EACD,MAAA,OAAOe,kBAAkB,CAACf,IAAI,EAAEc,QAAQ,CAAC,CAAA;EAC7C,KAAA;EACJ,GAAC,MACI,IAAIR,uBAAqB,KACzBN,IAAI,YAAYO,WAAW,IAAIC,QAAM,CAACR,IAAI,CAAC,CAAC,EAAE;EAC/C,IAAA,IAAIa,cAAc,EAAE;QAChB,OAAOC,QAAQ,CAACd,IAAI,CAAC,CAAA;EACzB,KAAC,MACI;QACD,OAAOe,kBAAkB,CAAC,IAAIb,IAAI,CAAC,CAACF,IAAI,CAAC,CAAC,EAAEc,QAAQ,CAAC,CAAA;EACzD,KAAA;EACJ,GAAA;EACA;IACA,OAAOA,QAAQ,CAACvB,YAAY,CAACQ,IAAI,CAAC,IAAIC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAA;EACtD,CAAC,CAAA;EACD,IAAMe,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIf,IAAI,EAAEc,QAAQ,EAAK;EAC3C,EAAA,IAAME,UAAU,GAAG,IAAIC,UAAU,EAAE,CAAA;IACnCD,UAAU,CAACE,MAAM,GAAG,YAAY;EAC5B,IAAA,IAAMC,OAAO,GAAGH,UAAU,CAACI,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;EAC/CP,IAAAA,QAAQ,CAAC,GAAG,IAAIK,OAAO,IAAI,EAAE,CAAC,CAAC,CAAA;KAClC,CAAA;EACD,EAAA,OAAOH,UAAU,CAACM,aAAa,CAACtB,IAAI,CAAC,CAAA;EACzC,CAAC,CAAA;EACD,SAASuB,OAAOA,CAACvB,IAAI,EAAE;IACnB,IAAIA,IAAI,YAAYwB,UAAU,EAAE;EAC5B,IAAA,OAAOxB,IAAI,CAAA;EACf,GAAC,MACI,IAAIA,IAAI,YAAYO,WAAW,EAAE;EAClC,IAAA,OAAO,IAAIiB,UAAU,CAACxB,IAAI,CAAC,CAAA;EAC/B,GAAC,MACI;EACD,IAAA,OAAO,IAAIwB,UAAU,CAACxB,IAAI,CAACU,MAAM,EAAEV,IAAI,CAACyB,UAAU,EAAEzB,IAAI,CAAC0B,UAAU,CAAC,CAAA;EACxE,GAAA;EACJ,CAAA;EACA,IAAIC,YAAY,CAAA;EACT,SAASC,oBAAoBA,CAACC,MAAM,EAAEf,QAAQ,EAAE;EACnD,EAAA,IAAIb,gBAAc,IAAI4B,MAAM,CAAC7B,IAAI,YAAYE,IAAI,EAAE;EAC/C,IAAA,OAAO2B,MAAM,CAAC7B,IAAI,CAAC8B,WAAW,EAAE,CAACC,IAAI,CAACR,OAAO,CAAC,CAACQ,IAAI,CAACjB,QAAQ,CAAC,CAAA;EACjE,GAAC,MACI,IAAIR,uBAAqB,KACzBuB,MAAM,CAAC7B,IAAI,YAAYO,WAAW,IAAIC,QAAM,CAACqB,MAAM,CAAC7B,IAAI,CAAC,CAAC,EAAE;MAC7D,OAAOc,QAAQ,CAACS,OAAO,CAACM,MAAM,CAAC7B,IAAI,CAAC,CAAC,CAAA;EACzC,GAAA;EACAW,EAAAA,YAAY,CAACkB,MAAM,EAAE,KAAK,EAAE,UAACG,OAAO,EAAK;MACrC,IAAI,CAACL,YAAY,EAAE;EACfA,MAAAA,YAAY,GAAG,IAAIM,WAAW,EAAE,CAAA;EACpC,KAAA;EACAnB,IAAAA,QAAQ,CAACa,YAAY,CAACO,MAAM,CAACF,OAAO,CAAC,CAAC,CAAA;EAC1C,GAAC,CAAC,CAAA;EACN;;EClEA;EACA,IAAMG,KAAK,GAAG,kEAAkE,CAAA;EAChF;EACA,IAAMC,QAAM,GAAG,OAAOZ,UAAU,KAAK,WAAW,GAAG,EAAE,GAAG,IAAIA,UAAU,CAAC,GAAG,CAAC,CAAA;EAC3E,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACnCD,QAAM,CAACD,KAAK,CAACI,UAAU,CAACF,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAA;EACnC,CAAA;EAiBO,IAAMG,QAAM,GAAG,SAATA,MAAMA,CAAIC,MAAM,EAAK;EAC9B,EAAA,IAAIC,YAAY,GAAGD,MAAM,CAACH,MAAM,GAAG,IAAI;MAAEK,GAAG,GAAGF,MAAM,CAACH,MAAM;MAAED,CAAC;EAAEO,IAAAA,CAAC,GAAG,CAAC;MAAEC,QAAQ;MAAEC,QAAQ;MAAEC,QAAQ;MAAEC,QAAQ,CAAA;IAC9G,IAAIP,MAAM,CAACA,MAAM,CAACH,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;EACnCI,IAAAA,YAAY,EAAE,CAAA;MACd,IAAID,MAAM,CAACA,MAAM,CAACH,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;EACnCI,MAAAA,YAAY,EAAE,CAAA;EAClB,KAAA;EACJ,GAAA;EACA,EAAA,IAAMO,WAAW,GAAG,IAAI1C,WAAW,CAACmC,YAAY,CAAC;EAAEQ,IAAAA,KAAK,GAAG,IAAI1B,UAAU,CAACyB,WAAW,CAAC,CAAA;IACtF,KAAKZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,GAAG,EAAEN,CAAC,IAAI,CAAC,EAAE;MACzBQ,QAAQ,GAAGT,QAAM,CAACK,MAAM,CAACF,UAAU,CAACF,CAAC,CAAC,CAAC,CAAA;MACvCS,QAAQ,GAAGV,QAAM,CAACK,MAAM,CAACF,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;MAC3CU,QAAQ,GAAGX,QAAM,CAACK,MAAM,CAACF,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;MAC3CW,QAAQ,GAAGZ,QAAM,CAACK,MAAM,CAACF,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;MAC3Ca,KAAK,CAACN,CAAC,EAAE,CAAC,GAAIC,QAAQ,IAAI,CAAC,GAAKC,QAAQ,IAAI,CAAE,CAAA;EAC9CI,IAAAA,KAAK,CAACN,CAAC,EAAE,CAAC,GAAI,CAACE,QAAQ,GAAG,EAAE,KAAK,CAAC,GAAKC,QAAQ,IAAI,CAAE,CAAA;EACrDG,IAAAA,KAAK,CAACN,CAAC,EAAE,CAAC,GAAI,CAACG,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAKC,QAAQ,GAAG,EAAG,CAAA;EACxD,GAAA;EACA,EAAA,OAAOC,WAAW,CAAA;EACtB,CAAC;;ECxCD,IAAM3C,uBAAqB,GAAG,OAAOC,WAAW,KAAK,UAAU,CAAA;EACxD,IAAM4C,YAAY,GAAG,SAAfA,YAAYA,CAAIC,aAAa,EAAEC,UAAU,EAAK;EACvD,EAAA,IAAI,OAAOD,aAAa,KAAK,QAAQ,EAAE;MACnC,OAAO;EACHrD,MAAAA,IAAI,EAAE,SAAS;EACfC,MAAAA,IAAI,EAAEsD,SAAS,CAACF,aAAa,EAAEC,UAAU,CAAA;OAC5C,CAAA;EACL,GAAA;EACA,EAAA,IAAMtD,IAAI,GAAGqD,aAAa,CAACG,MAAM,CAAC,CAAC,CAAC,CAAA;IACpC,IAAIxD,IAAI,KAAK,GAAG,EAAE;MACd,OAAO;EACHA,MAAAA,IAAI,EAAE,SAAS;QACfC,IAAI,EAAEwD,kBAAkB,CAACJ,aAAa,CAACK,SAAS,CAAC,CAAC,CAAC,EAAEJ,UAAU,CAAA;OAClE,CAAA;EACL,GAAA;EACA,EAAA,IAAMK,UAAU,GAAGhE,oBAAoB,CAACK,IAAI,CAAC,CAAA;IAC7C,IAAI,CAAC2D,UAAU,EAAE;EACb,IAAA,OAAO5D,YAAY,CAAA;EACvB,GAAA;EACA,EAAA,OAAOsD,aAAa,CAACd,MAAM,GAAG,CAAC,GACzB;EACEvC,IAAAA,IAAI,EAAEL,oBAAoB,CAACK,IAAI,CAAC;EAChCC,IAAAA,IAAI,EAAEoD,aAAa,CAACK,SAAS,CAAC,CAAC,CAAA;EACnC,GAAC,GACC;MACE1D,IAAI,EAAEL,oBAAoB,CAACK,IAAI,CAAA;KAClC,CAAA;EACT,CAAC,CAAA;EACD,IAAMyD,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIxD,IAAI,EAAEqD,UAAU,EAAK;EAC7C,EAAA,IAAI/C,uBAAqB,EAAE;EACvB,IAAA,IAAMqD,OAAO,GAAGnB,QAAM,CAACxC,IAAI,CAAC,CAAA;EAC5B,IAAA,OAAOsD,SAAS,CAACK,OAAO,EAAEN,UAAU,CAAC,CAAA;EACzC,GAAC,MACI;MACD,OAAO;EAAEZ,MAAAA,MAAM,EAAE,IAAI;EAAEzC,MAAAA,IAAI,EAAJA,IAAAA;EAAK,KAAC,CAAC;EAClC,GAAA;EACJ,CAAC,CAAA;EACD,IAAMsD,SAAS,GAAG,SAAZA,SAASA,CAAItD,IAAI,EAAEqD,UAAU,EAAK;EACpC,EAAA,QAAQA,UAAU;EACd,IAAA,KAAK,MAAM;QACP,IAAIrD,IAAI,YAAYE,IAAI,EAAE;EACtB;EACA,QAAA,OAAOF,IAAI,CAAA;EACf,OAAC,MACI;EACD;EACA,QAAA,OAAO,IAAIE,IAAI,CAAC,CAACF,IAAI,CAAC,CAAC,CAAA;EAC3B,OAAA;EACJ,IAAA,KAAK,aAAa,CAAA;EAClB,IAAA;QACI,IAAIA,IAAI,YAAYO,WAAW,EAAE;EAC7B;EACA,QAAA,OAAOP,IAAI,CAAA;EACf,OAAC,MACI;EACD;UACA,OAAOA,IAAI,CAACU,MAAM,CAAA;EACtB,OAAA;EACR,GAAA;EACJ,CAAC;;EC1DD,IAAMkD,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,EAAE,CAAC,CAAC;EAC1C,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,OAAO,EAAElD,QAAQ,EAAK;EACzC;EACA,EAAA,IAAMwB,MAAM,GAAG0B,OAAO,CAAC1B,MAAM,CAAA;EAC7B,EAAA,IAAM2B,cAAc,GAAG,IAAIC,KAAK,CAAC5B,MAAM,CAAC,CAAA;IACxC,IAAI6B,KAAK,GAAG,CAAC,CAAA;EACbH,EAAAA,OAAO,CAACpE,OAAO,CAAC,UAACiC,MAAM,EAAEQ,CAAC,EAAK;EAC3B;EACA1B,IAAAA,YAAY,CAACkB,MAAM,EAAE,KAAK,EAAE,UAACuB,aAAa,EAAK;EAC3Ca,MAAAA,cAAc,CAAC5B,CAAC,CAAC,GAAGe,aAAa,CAAA;EACjC,MAAA,IAAI,EAAEe,KAAK,KAAK7B,MAAM,EAAE;EACpBxB,QAAAA,QAAQ,CAACmD,cAAc,CAACG,IAAI,CAACR,SAAS,CAAC,CAAC,CAAA;EAC5C,OAAA;EACJ,KAAC,CAAC,CAAA;EACN,GAAC,CAAC,CAAA;EACN,CAAC,CAAA;EACD,IAAMS,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,cAAc,EAAEjB,UAAU,EAAK;EAClD,EAAA,IAAMY,cAAc,GAAGK,cAAc,CAACjD,KAAK,CAACuC,SAAS,CAAC,CAAA;IACtD,IAAMI,OAAO,GAAG,EAAE,CAAA;EAClB,EAAA,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,cAAc,CAAC3B,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,IAAMkC,aAAa,GAAGpB,YAAY,CAACc,cAAc,CAAC5B,CAAC,CAAC,EAAEgB,UAAU,CAAC,CAAA;EACjEW,IAAAA,OAAO,CAACQ,IAAI,CAACD,aAAa,CAAC,CAAA;EAC3B,IAAA,IAAIA,aAAa,CAACxE,IAAI,KAAK,OAAO,EAAE;EAChC,MAAA,MAAA;EACJ,KAAA;EACJ,GAAA;EACA,EAAA,OAAOiE,OAAO,CAAA;EAClB,CAAC,CAAA;EACM,SAASS,yBAAyBA,GAAG;IACxC,OAAO,IAAIC,eAAe,CAAC;EACvBC,IAAAA,SAAS,EAAAA,SAAAA,SAAAA,CAAC9C,MAAM,EAAE+C,UAAU,EAAE;EAC1BhD,MAAAA,oBAAoB,CAACC,MAAM,EAAE,UAACuB,aAAa,EAAK;EAC5C,QAAA,IAAMyB,aAAa,GAAGzB,aAAa,CAACd,MAAM,CAAA;EAC1C,QAAA,IAAIwC,MAAM,CAAA;EACV;UACA,IAAID,aAAa,GAAG,GAAG,EAAE;EACrBC,UAAAA,MAAM,GAAG,IAAItD,UAAU,CAAC,CAAC,CAAC,CAAA;EAC1B,UAAA,IAAIuD,QAAQ,CAACD,MAAM,CAACpE,MAAM,CAAC,CAACsE,QAAQ,CAAC,CAAC,EAAEH,aAAa,CAAC,CAAA;EAC1D,SAAC,MACI,IAAIA,aAAa,GAAG,KAAK,EAAE;EAC5BC,UAAAA,MAAM,GAAG,IAAItD,UAAU,CAAC,CAAC,CAAC,CAAA;YAC1B,IAAMyD,IAAI,GAAG,IAAIF,QAAQ,CAACD,MAAM,CAACpE,MAAM,CAAC,CAAA;EACxCuE,UAAAA,IAAI,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;EACrBC,UAAAA,IAAI,CAACC,SAAS,CAAC,CAAC,EAAEL,aAAa,CAAC,CAAA;EACpC,SAAC,MACI;EACDC,UAAAA,MAAM,GAAG,IAAItD,UAAU,CAAC,CAAC,CAAC,CAAA;YAC1B,IAAMyD,KAAI,GAAG,IAAIF,QAAQ,CAACD,MAAM,CAACpE,MAAM,CAAC,CAAA;EACxCuE,UAAAA,KAAI,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;YACrBC,KAAI,CAACE,YAAY,CAAC,CAAC,EAAEC,MAAM,CAACP,aAAa,CAAC,CAAC,CAAA;EAC/C,SAAA;EACA;UACA,IAAIhD,MAAM,CAAC7B,IAAI,IAAI,OAAO6B,MAAM,CAAC7B,IAAI,KAAK,QAAQ,EAAE;EAChD8E,UAAAA,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAAA;EACrB,SAAA;EACAF,QAAAA,UAAU,CAACS,OAAO,CAACP,MAAM,CAAC,CAAA;EAC1BF,QAAAA,UAAU,CAACS,OAAO,CAACjC,aAAa,CAAC,CAAA;EACrC,OAAC,CAAC,CAAA;EACN,KAAA;EACJ,GAAC,CAAC,CAAA;EACN,CAAA;EACA,IAAIkC,YAAY,CAAA;EAChB,SAASC,WAAWA,CAACC,MAAM,EAAE;EACzB,EAAA,OAAOA,MAAM,CAACC,MAAM,CAAC,UAACC,GAAG,EAAEC,KAAK,EAAA;EAAA,IAAA,OAAKD,GAAG,GAAGC,KAAK,CAACrD,MAAM,CAAA;EAAA,GAAA,EAAE,CAAC,CAAC,CAAA;EAC/D,CAAA;EACA,SAASsD,YAAYA,CAACJ,MAAM,EAAEK,IAAI,EAAE;IAChC,IAAIL,MAAM,CAAC,CAAC,CAAC,CAAClD,MAAM,KAAKuD,IAAI,EAAE;EAC3B,IAAA,OAAOL,MAAM,CAACM,KAAK,EAAE,CAAA;EACzB,GAAA;EACA,EAAA,IAAMpF,MAAM,GAAG,IAAIc,UAAU,CAACqE,IAAI,CAAC,CAAA;IACnC,IAAIE,CAAC,GAAG,CAAC,CAAA;IACT,KAAK,IAAI1D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwD,IAAI,EAAExD,CAAC,EAAE,EAAE;MAC3B3B,MAAM,CAAC2B,CAAC,CAAC,GAAGmD,MAAM,CAAC,CAAC,CAAC,CAACO,CAAC,EAAE,CAAC,CAAA;MAC1B,IAAIA,CAAC,KAAKP,MAAM,CAAC,CAAC,CAAC,CAAClD,MAAM,EAAE;QACxBkD,MAAM,CAACM,KAAK,EAAE,CAAA;EACdC,MAAAA,CAAC,GAAG,CAAC,CAAA;EACT,KAAA;EACJ,GAAA;EACA,EAAA,IAAIP,MAAM,CAAClD,MAAM,IAAIyD,CAAC,GAAGP,MAAM,CAAC,CAAC,CAAC,CAAClD,MAAM,EAAE;EACvCkD,IAAAA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACD,CAAC,CAAC,CAAA;EAClC,GAAA;EACA,EAAA,OAAOrF,MAAM,CAAA;EACjB,CAAA;EACO,SAASuF,yBAAyBA,CAACC,UAAU,EAAE7C,UAAU,EAAE;IAC9D,IAAI,CAACiC,YAAY,EAAE;EACfA,IAAAA,YAAY,GAAG,IAAIa,WAAW,EAAE,CAAA;EACpC,GAAA;IACA,IAAMX,MAAM,GAAG,EAAE,CAAA;IACjB,IAAIY,KAAK,GAAG,CAAC,yBAAC;IACd,IAAIC,cAAc,GAAG,CAAC,CAAC,CAAA;IACvB,IAAIC,QAAQ,GAAG,KAAK,CAAA;IACpB,OAAO,IAAI5B,eAAe,CAAC;EACvBC,IAAAA,SAAS,EAAAA,SAAAA,SAAAA,CAACgB,KAAK,EAAEf,UAAU,EAAE;EACzBY,MAAAA,MAAM,CAAChB,IAAI,CAACmB,KAAK,CAAC,CAAA;EAClB,MAAA,OAAO,IAAI,EAAE;EACT,QAAA,IAAIS,KAAK,KAAK,CAAC,0BAA0B;EACrC,UAAA,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAG,CAAC,EAAE;EACzB,YAAA,MAAA;EACJ,WAAA;EACA,UAAA,IAAMV,MAAM,GAAGc,YAAY,CAACJ,MAAM,EAAE,CAAC,CAAC,CAAA;YACtCc,QAAQ,GAAG,CAACxB,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,IAAI,CAAA;EACtCuB,UAAAA,cAAc,GAAGvB,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;YACjC,IAAIuB,cAAc,GAAG,GAAG,EAAE;cACtBD,KAAK,GAAG,CAAC,0BAAC;EACd,WAAC,MACI,IAAIC,cAAc,KAAK,GAAG,EAAE;cAC7BD,KAAK,GAAG,CAAC,qCAAC;EACd,WAAC,MACI;cACDA,KAAK,GAAG,CAAC,qCAAC;EACd,WAAA;EACJ,SAAC,MACI,IAAIA,KAAK,KAAK,CAAC,sCAAsC;EACtD,UAAA,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAG,CAAC,EAAE;EACzB,YAAA,MAAA;EACJ,WAAA;EACA,UAAA,IAAMe,WAAW,GAAGX,YAAY,CAACJ,MAAM,EAAE,CAAC,CAAC,CAAA;YAC3Ca,cAAc,GAAG,IAAItB,QAAQ,CAACwB,WAAW,CAAC7F,MAAM,EAAE6F,WAAW,CAAC9E,UAAU,EAAE8E,WAAW,CAACjE,MAAM,CAAC,CAACkE,SAAS,CAAC,CAAC,CAAC,CAAA;YAC1GJ,KAAK,GAAG,CAAC,0BAAC;EACd,SAAC,MACI,IAAIA,KAAK,KAAK,CAAC,sCAAsC;EACtD,UAAA,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAG,CAAC,EAAE;EACzB,YAAA,MAAA;EACJ,WAAA;EACA,UAAA,IAAMe,YAAW,GAAGX,YAAY,CAACJ,MAAM,EAAE,CAAC,CAAC,CAAA;EAC3C,UAAA,IAAMP,IAAI,GAAG,IAAIF,QAAQ,CAACwB,YAAW,CAAC7F,MAAM,EAAE6F,YAAW,CAAC9E,UAAU,EAAE8E,YAAW,CAACjE,MAAM,CAAC,CAAA;EACzF,UAAA,IAAMmE,CAAC,GAAGxB,IAAI,CAACyB,SAAS,CAAC,CAAC,CAAC,CAAA;EAC3B,UAAA,IAAID,CAAC,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE;EAC9B;EACAhC,YAAAA,UAAU,CAACS,OAAO,CAACvF,YAAY,CAAC,CAAA;EAChC,YAAA,MAAA;EACJ,WAAA;EACAuG,UAAAA,cAAc,GAAGI,CAAC,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG3B,IAAI,CAACyB,SAAS,CAAC,CAAC,CAAC,CAAA;YACxDN,KAAK,GAAG,CAAC,0BAAC;EACd,SAAC,MACI;EACD,UAAA,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAGa,cAAc,EAAE;EACtC,YAAA,MAAA;EACJ,WAAA;EACA,UAAA,IAAMrG,IAAI,GAAG4F,YAAY,CAACJ,MAAM,EAAEa,cAAc,CAAC,CAAA;EACjDzB,UAAAA,UAAU,CAACS,OAAO,CAAClC,YAAY,CAACmD,QAAQ,GAAGtG,IAAI,GAAGsF,YAAY,CAAC9C,MAAM,CAACxC,IAAI,CAAC,EAAEqD,UAAU,CAAC,CAAC,CAAA;YACzF+C,KAAK,GAAG,CAAC,yBAAC;EACd,SAAA;EACA,QAAA,IAAIC,cAAc,KAAK,CAAC,IAAIA,cAAc,GAAGH,UAAU,EAAE;EACrDtB,UAAAA,UAAU,CAACS,OAAO,CAACvF,YAAY,CAAC,CAAA;EAChC,UAAA,MAAA;EACJ,SAAA;EACJ,OAAA;EACJ,KAAA;EACJ,GAAC,CAAC,CAAA;EACN,CAAA;EACO,IAAM+G,UAAQ,GAAG,CAAC;;EC1JzB;EACA;EACA;EACA;EACA;;EAEO,SAASC,OAAOA,CAACrG,GAAG,EAAE;EAC3B,EAAA,IAAIA,GAAG,EAAE,OAAOsG,KAAK,CAACtG,GAAG,CAAC,CAAA;EAC5B,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,SAASsG,KAAKA,CAACtG,GAAG,EAAE;EAClB,EAAA,KAAK,IAAIZ,GAAG,IAAIiH,OAAO,CAAC3G,SAAS,EAAE;MACjCM,GAAG,CAACZ,GAAG,CAAC,GAAGiH,OAAO,CAAC3G,SAAS,CAACN,GAAG,CAAC,CAAA;EACnC,GAAA;EACA,EAAA,OAAOY,GAAG,CAAA;EACZ,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAqG,OAAO,CAAC3G,SAAS,CAAC6G,EAAE,GACpBF,OAAO,CAAC3G,SAAS,CAAC8G,gBAAgB,GAAG,UAASC,KAAK,EAAEC,EAAE,EAAC;IACtD,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,EAAE,CAAA;IACvC,CAAC,IAAI,CAACA,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,GAAG,IAAI,CAACE,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,IAAI,EAAE,EAC/D1C,IAAI,CAAC2C,EAAE,CAAC,CAAA;EACX,EAAA,OAAO,IAAI,CAAA;EACb,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAL,OAAO,CAAC3G,SAAS,CAACkH,IAAI,GAAG,UAASH,KAAK,EAAEC,EAAE,EAAC;IAC1C,SAASH,EAAEA,GAAG;EACZ,IAAA,IAAI,CAACM,GAAG,CAACJ,KAAK,EAAEF,EAAE,CAAC,CAAA;EACnBG,IAAAA,EAAE,CAACI,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAA;EAC3B,GAAA;IAEAR,EAAE,CAACG,EAAE,GAAGA,EAAE,CAAA;EACV,EAAA,IAAI,CAACH,EAAE,CAACE,KAAK,EAAEF,EAAE,CAAC,CAAA;EAClB,EAAA,OAAO,IAAI,CAAA;EACb,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAF,OAAO,CAAC3G,SAAS,CAACmH,GAAG,GACrBR,OAAO,CAAC3G,SAAS,CAACsH,cAAc,GAChCX,OAAO,CAAC3G,SAAS,CAACuH,kBAAkB,GACpCZ,OAAO,CAAC3G,SAAS,CAACwH,mBAAmB,GAAG,UAAST,KAAK,EAAEC,EAAE,EAAC;IACzD,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,EAAE,CAAA;;EAEvC;EACA,EAAA,IAAI,CAAC,IAAII,SAAS,CAAClF,MAAM,EAAE;EACzB,IAAA,IAAI,CAAC8E,UAAU,GAAG,EAAE,CAAA;EACpB,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;;EAEA;IACA,IAAIQ,SAAS,GAAG,IAAI,CAACR,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,CAAA;EAC5C,EAAA,IAAI,CAACU,SAAS,EAAE,OAAO,IAAI,CAAA;;EAE3B;EACA,EAAA,IAAI,CAAC,IAAIJ,SAAS,CAAClF,MAAM,EAAE;EACzB,IAAA,OAAO,IAAI,CAAC8E,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,CAAA;EACnC,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;;EAEA;EACA,EAAA,IAAIW,EAAE,CAAA;EACN,EAAA,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuF,SAAS,CAACtF,MAAM,EAAED,CAAC,EAAE,EAAE;EACzCwF,IAAAA,EAAE,GAAGD,SAAS,CAACvF,CAAC,CAAC,CAAA;MACjB,IAAIwF,EAAE,KAAKV,EAAE,IAAIU,EAAE,CAACV,EAAE,KAAKA,EAAE,EAAE;EAC7BS,MAAAA,SAAS,CAACE,MAAM,CAACzF,CAAC,EAAE,CAAC,CAAC,CAAA;EACtB,MAAA,MAAA;EACF,KAAA;EACF,GAAA;;EAEA;EACA;EACA,EAAA,IAAIuF,SAAS,CAACtF,MAAM,KAAK,CAAC,EAAE;EAC1B,IAAA,OAAO,IAAI,CAAC8E,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,CAAA;EACrC,GAAA;EAEA,EAAA,OAAO,IAAI,CAAA;EACb,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAJ,OAAO,CAAC3G,SAAS,CAAC4H,IAAI,GAAG,UAASb,KAAK,EAAC;IACtC,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,EAAE,CAAA;IAEvC,IAAIY,IAAI,GAAG,IAAI9D,KAAK,CAACsD,SAAS,CAAClF,MAAM,GAAG,CAAC,CAAC;MACtCsF,SAAS,GAAG,IAAI,CAACR,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,CAAA;EAE5C,EAAA,KAAK,IAAI7E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmF,SAAS,CAAClF,MAAM,EAAED,CAAC,EAAE,EAAE;MACzC2F,IAAI,CAAC3F,CAAC,GAAG,CAAC,CAAC,GAAGmF,SAAS,CAACnF,CAAC,CAAC,CAAA;EAC5B,GAAA;EAEA,EAAA,IAAIuF,SAAS,EAAE;EACbA,IAAAA,SAAS,GAAGA,SAAS,CAAC5B,KAAK,CAAC,CAAC,CAAC,CAAA;EAC9B,IAAA,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEM,GAAG,GAAGiF,SAAS,CAACtF,MAAM,EAAED,CAAC,GAAGM,GAAG,EAAE,EAAEN,CAAC,EAAE;QACpDuF,SAAS,CAACvF,CAAC,CAAC,CAACkF,KAAK,CAAC,IAAI,EAAES,IAAI,CAAC,CAAA;EAChC,KAAA;EACF,GAAA;EAEA,EAAA,OAAO,IAAI,CAAA;EACb,CAAC,CAAA;;EAED;EACAlB,OAAO,CAAC3G,SAAS,CAAC8H,YAAY,GAAGnB,OAAO,CAAC3G,SAAS,CAAC4H,IAAI,CAAA;;EAEvD;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAjB,OAAO,CAAC3G,SAAS,CAAC+H,SAAS,GAAG,UAAShB,KAAK,EAAC;IAC3C,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,EAAE,CAAA;IACvC,OAAO,IAAI,CAACA,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,IAAI,EAAE,CAAA;EAC3C,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAJ,OAAO,CAAC3G,SAAS,CAACgI,YAAY,GAAG,UAASjB,KAAK,EAAC;IAC9C,OAAO,CAAC,CAAE,IAAI,CAACgB,SAAS,CAAChB,KAAK,CAAC,CAAC5E,MAAM,CAAA;EACxC,CAAC;;ECxKM,IAAM8F,QAAQ,GAAI,YAAM;EAC3B,EAAA,IAAMC,kBAAkB,GAAG,OAAOC,OAAO,KAAK,UAAU,IAAI,OAAOA,OAAO,CAACC,OAAO,KAAK,UAAU,CAAA;EACjG,EAAA,IAAIF,kBAAkB,EAAE;EACpB,IAAA,OAAO,UAACR,EAAE,EAAA;QAAA,OAAKS,OAAO,CAACC,OAAO,EAAE,CAACxG,IAAI,CAAC8F,EAAE,CAAC,CAAA;EAAA,KAAA,CAAA;EAC7C,GAAC,MACI;MACD,OAAO,UAACA,EAAE,EAAEW,YAAY,EAAA;EAAA,MAAA,OAAKA,YAAY,CAACX,EAAE,EAAE,CAAC,CAAC,CAAA;EAAA,KAAA,CAAA;EACpD,GAAA;EACJ,CAAC,EAAG,CAAA;EACG,IAAMY,cAAc,GAAI,YAAM;EACjC,EAAA,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;EAC7B,IAAA,OAAOA,IAAI,CAAA;EACf,GAAC,MACI,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;EACpC,IAAA,OAAOA,MAAM,CAAA;EACjB,GAAC,MACI;EACD,IAAA,OAAOC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAA;EACpC,GAAA;EACJ,CAAC,EAAG,CAAA;EACG,IAAMC,iBAAiB,GAAG,aAAa,CAAA;EACvC,SAASC,eAAeA,GAAG;;ECpB3B,SAASC,IAAIA,CAACtI,GAAG,EAAW;IAAA,KAAAuI,IAAAA,IAAA,GAAAxB,SAAA,CAAAlF,MAAA,EAAN2G,IAAI,OAAA/E,KAAA,CAAA8E,IAAA,GAAAA,CAAAA,GAAAA,IAAA,WAAAE,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA,EAAA,EAAA;EAAJD,IAAAA,IAAI,CAAAC,IAAA,GAAA1B,CAAAA,CAAAA,GAAAA,SAAA,CAAA0B,IAAA,CAAA,CAAA;EAAA,GAAA;IAC7B,OAAOD,IAAI,CAACxD,MAAM,CAAC,UAACC,GAAG,EAAEyD,CAAC,EAAK;EAC3B,IAAA,IAAI1I,GAAG,CAAC2I,cAAc,CAACD,CAAC,CAAC,EAAE;EACvBzD,MAAAA,GAAG,CAACyD,CAAC,CAAC,GAAG1I,GAAG,CAAC0I,CAAC,CAAC,CAAA;EACnB,KAAA;EACA,IAAA,OAAOzD,GAAG,CAAA;KACb,EAAE,EAAE,CAAC,CAAA;EACV,CAAA;EACA;EACA,IAAM2D,kBAAkB,GAAGC,cAAU,CAACC,UAAU,CAAA;EAChD,IAAMC,oBAAoB,GAAGF,cAAU,CAACG,YAAY,CAAA;EAC7C,SAASC,qBAAqBA,CAACjJ,GAAG,EAAEkJ,IAAI,EAAE;IAC7C,IAAIA,IAAI,CAACC,eAAe,EAAE;MACtBnJ,GAAG,CAAC+H,YAAY,GAAGa,kBAAkB,CAACQ,IAAI,CAACP,cAAU,CAAC,CAAA;MACtD7I,GAAG,CAACqJ,cAAc,GAAGN,oBAAoB,CAACK,IAAI,CAACP,cAAU,CAAC,CAAA;EAC9D,GAAC,MACI;MACD7I,GAAG,CAAC+H,YAAY,GAAGc,cAAU,CAACC,UAAU,CAACM,IAAI,CAACP,cAAU,CAAC,CAAA;MACzD7I,GAAG,CAACqJ,cAAc,GAAGR,cAAU,CAACG,YAAY,CAACI,IAAI,CAACP,cAAU,CAAC,CAAA;EACjE,GAAA;EACJ,CAAA;EACA;EACA,IAAMS,eAAe,GAAG,IAAI,CAAA;EAC5B;EACO,SAASrI,UAAUA,CAACjB,GAAG,EAAE;EAC5B,EAAA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MACzB,OAAOuJ,UAAU,CAACvJ,GAAG,CAAC,CAAA;EAC1B,GAAA;EACA;EACA,EAAA,OAAOkG,IAAI,CAACsD,IAAI,CAAC,CAACxJ,GAAG,CAACiB,UAAU,IAAIjB,GAAG,CAACoF,IAAI,IAAIkE,eAAe,CAAC,CAAA;EACpE,CAAA;EACA,SAASC,UAAUA,CAACE,GAAG,EAAE;IACrB,IAAIC,CAAC,GAAG,CAAC;EAAE7H,IAAAA,MAAM,GAAG,CAAC,CAAA;EACrB,EAAA,KAAK,IAAID,CAAC,GAAG,CAAC,EAAE+H,CAAC,GAAGF,GAAG,CAAC5H,MAAM,EAAED,CAAC,GAAG+H,CAAC,EAAE/H,CAAC,EAAE,EAAE;EACxC8H,IAAAA,CAAC,GAAGD,GAAG,CAAC3H,UAAU,CAACF,CAAC,CAAC,CAAA;MACrB,IAAI8H,CAAC,GAAG,IAAI,EAAE;EACV7H,MAAAA,MAAM,IAAI,CAAC,CAAA;EACf,KAAC,MACI,IAAI6H,CAAC,GAAG,KAAK,EAAE;EAChB7H,MAAAA,MAAM,IAAI,CAAC,CAAA;OACd,MACI,IAAI6H,CAAC,GAAG,MAAM,IAAIA,CAAC,IAAI,MAAM,EAAE;EAChC7H,MAAAA,MAAM,IAAI,CAAC,CAAA;EACf,KAAC,MACI;EACDD,MAAAA,CAAC,EAAE,CAAA;EACHC,MAAAA,MAAM,IAAI,CAAC,CAAA;EACf,KAAA;EACJ,GAAA;EACA,EAAA,OAAOA,MAAM,CAAA;EACjB,CAAA;EACA;EACA;EACA;EACO,SAAS+H,YAAYA,GAAG;EAC3B,EAAA,OAAQC,IAAI,CAACC,GAAG,EAAE,CAACnK,QAAQ,CAAC,EAAE,CAAC,CAACqD,SAAS,CAAC,CAAC,CAAC,GACxCkD,IAAI,CAAC6D,MAAM,EAAE,CAACpK,QAAQ,CAAC,EAAE,CAAC,CAACqD,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;EAClD;;EC1DA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASvB,MAAMA,CAACzB,GAAG,EAAE;IACxB,IAAIyJ,GAAG,GAAG,EAAE,CAAA;EACZ,EAAA,KAAK,IAAI7H,CAAC,IAAI5B,GAAG,EAAE;EACf,IAAA,IAAIA,GAAG,CAAC2I,cAAc,CAAC/G,CAAC,CAAC,EAAE;EACvB,MAAA,IAAI6H,GAAG,CAAC5H,MAAM,EACV4H,GAAG,IAAI,GAAG,CAAA;EACdA,MAAAA,GAAG,IAAIO,kBAAkB,CAACpI,CAAC,CAAC,GAAG,GAAG,GAAGoI,kBAAkB,CAAChK,GAAG,CAAC4B,CAAC,CAAC,CAAC,CAAA;EACnE,KAAA;EACJ,GAAA;EACA,EAAA,OAAO6H,GAAG,CAAA;EACd,CAAA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAAS1H,MAAMA,CAACkI,EAAE,EAAE;IACvB,IAAIC,GAAG,GAAG,EAAE,CAAA;EACZ,EAAA,IAAIC,KAAK,GAAGF,EAAE,CAACrJ,KAAK,CAAC,GAAG,CAAC,CAAA;EACzB,EAAA,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAE+H,CAAC,GAAGQ,KAAK,CAACtI,MAAM,EAAED,CAAC,GAAG+H,CAAC,EAAE/H,CAAC,EAAE,EAAE;MAC1C,IAAIwI,IAAI,GAAGD,KAAK,CAACvI,CAAC,CAAC,CAAChB,KAAK,CAAC,GAAG,CAAC,CAAA;EAC9BsJ,IAAAA,GAAG,CAACG,kBAAkB,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,kBAAkB,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;EAClE,GAAA;EACA,EAAA,OAAOF,GAAG,CAAA;EACd;;EC7BaI,IAAAA,cAAc,0BAAAC,MAAA,EAAA;EACvB,EAAA,SAAAD,eAAYE,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAAA,IAAA,IAAAC,KAAA,CAAA;EACtCA,IAAAA,KAAA,GAAAJ,MAAA,CAAA3K,IAAA,CAAA,IAAA,EAAM4K,MAAM,CAAC,IAAA,IAAA,CAAA;MACbG,KAAA,CAAKF,WAAW,GAAGA,WAAW,CAAA;MAC9BE,KAAA,CAAKD,OAAO,GAAGA,OAAO,CAAA;MACtBC,KAAA,CAAKrL,IAAI,GAAG,gBAAgB,CAAA;EAAC,IAAA,OAAAqL,KAAA,CAAA;EACjC,GAAA;IAACC,cAAA,CAAAN,cAAA,EAAAC,MAAA,CAAA,CAAA;EAAA,EAAA,OAAAD,cAAA,CAAA;EAAA,CAAAO,eAAAA,gBAAA,CAN+BC,KAAK,CAAA,CAAA,CAAA;EAQ5BC,IAAAA,SAAS,0BAAAC,QAAA,EAAA;EAClB;EACJ;EACA;EACA;EACA;EACA;IACI,SAAAD,SAAAA,CAAY7B,IAAI,EAAE;EAAA,IAAA,IAAA+B,MAAA,CAAA;EACdA,IAAAA,MAAA,GAAAD,QAAA,CAAApL,IAAA,KAAM,CAAC,IAAA,IAAA,CAAA;MACPqL,MAAA,CAAKC,QAAQ,GAAG,KAAK,CAAA;EACrBjC,IAAAA,qBAAqB,CAAAgC,MAAA,EAAO/B,IAAI,CAAC,CAAA;MACjC+B,MAAA,CAAK/B,IAAI,GAAGA,IAAI,CAAA;EAChB+B,IAAAA,MAAA,CAAKE,KAAK,GAAGjC,IAAI,CAACiC,KAAK,CAAA;EACvBF,IAAAA,MAAA,CAAKG,MAAM,GAAGlC,IAAI,CAACkC,MAAM,CAAA;EACzBH,IAAAA,MAAA,CAAK7K,cAAc,GAAG,CAAC8I,IAAI,CAACmC,WAAW,CAAA;EAAC,IAAA,OAAAJ,MAAA,CAAA;EAC5C,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IARIL,cAAA,CAAAG,SAAA,EAAAC,QAAA,CAAA,CAAA;EAAA,EAAA,IAAAM,MAAA,GAAAP,SAAA,CAAArL,SAAA,CAAA;IAAA4L,MAAA,CASAC,OAAO,GAAP,SAAAA,OAAAA,CAAQf,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAClCM,IAAAA,QAAA,CAAAtL,SAAA,CAAM8H,YAAY,CAAA5H,IAAA,CAAC,IAAA,EAAA,OAAO,EAAE,IAAI0K,cAAc,CAACE,MAAM,EAAEC,WAAW,EAAEC,OAAO,CAAC,CAAA,CAAA;EAC5E,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA,MAFI;EAAAY,EAAAA,MAAA,CAGAE,IAAI,GAAJ,SAAAA,OAAO;MACH,IAAI,CAACC,UAAU,GAAG,SAAS,CAAA;MAC3B,IAAI,CAACC,MAAM,EAAE,CAAA;EACb,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA,MAFI;EAAAJ,EAAAA,MAAA,CAGAK,KAAK,GAAL,SAAAA,QAAQ;MACJ,IAAI,IAAI,CAACF,UAAU,KAAK,SAAS,IAAI,IAAI,CAACA,UAAU,KAAK,MAAM,EAAE;QAC7D,IAAI,CAACG,OAAO,EAAE,CAAA;QACd,IAAI,CAACC,OAAO,EAAE,CAAA;EAClB,KAAA;EACA,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAP,EAAAA,MAAA,CAKAQ,IAAI,GAAJ,SAAAA,IAAAA,CAAKvI,OAAO,EAAE;EACV,IAAA,IAAI,IAAI,CAACkI,UAAU,KAAK,MAAM,EAAE;EAC5B,MAAA,IAAI,CAACM,KAAK,CAACxI,OAAO,CAAC,CAAA;EACvB,KAEI;EAER,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAA+H,EAAAA,MAAA,CAKAU,MAAM,GAAN,SAAAA,SAAS;MACL,IAAI,CAACP,UAAU,GAAG,MAAM,CAAA;MACxB,IAAI,CAACP,QAAQ,GAAG,IAAI,CAAA;EACpBF,IAAAA,QAAA,CAAAtL,SAAA,CAAM8H,YAAY,CAAA5H,IAAA,OAAC,MAAM,CAAA,CAAA;EAC7B,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAA0L,EAAAA,MAAA,CAMAW,MAAM,GAAN,SAAAA,MAAAA,CAAO1M,IAAI,EAAE;MACT,IAAM6B,MAAM,GAAGsB,YAAY,CAACnD,IAAI,EAAE,IAAI,CAAC6L,MAAM,CAACxI,UAAU,CAAC,CAAA;EACzD,IAAA,IAAI,CAACsJ,QAAQ,CAAC9K,MAAM,CAAC,CAAA;EACzB,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAkK,EAAAA,MAAA,CAKAY,QAAQ,GAAR,SAAAA,QAAAA,CAAS9K,MAAM,EAAE;MACb4J,QAAA,CAAAtL,SAAA,CAAM8H,YAAY,CAAA5H,IAAA,CAAA,IAAA,EAAC,QAAQ,EAAEwB,MAAM,CAAA,CAAA;EACvC,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAkK,EAAAA,MAAA,CAKAO,OAAO,GAAP,SAAAA,OAAAA,CAAQM,OAAO,EAAE;MACb,IAAI,CAACV,UAAU,GAAG,QAAQ,CAAA;MAC1BT,QAAA,CAAAtL,SAAA,CAAM8H,YAAY,CAAA5H,IAAA,CAAA,IAAA,EAAC,OAAO,EAAEuM,OAAO,CAAA,CAAA;EACvC,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;IAAAb,MAAA,CAKAc,KAAK,GAAL,SAAAA,MAAMC,OAAO,EAAE,EAAG,CAAA;EAAAf,EAAAA,MAAA,CAClBgB,SAAS,GAAT,SAAAA,SAAAA,CAAUC,MAAM,EAAc;EAAA,IAAA,IAAZpB,KAAK,GAAApE,SAAA,CAAAlF,MAAA,GAAA,CAAA,IAAAkF,SAAA,CAAA,CAAA,CAAA,KAAAyF,SAAA,GAAAzF,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;MACxB,OAAQwF,MAAM,GACV,KAAK,GACL,IAAI,CAACE,SAAS,EAAE,GAChB,IAAI,CAACC,KAAK,EAAE,GACZ,IAAI,CAACxD,IAAI,CAACyD,IAAI,GACd,IAAI,CAACC,MAAM,CAACzB,KAAK,CAAC,CAAA;KACzB,CAAA;EAAAG,EAAAA,MAAA,CACDmB,SAAS,GAAT,SAAAA,YAAY;EACR,IAAA,IAAMI,QAAQ,GAAG,IAAI,CAAC3D,IAAI,CAAC2D,QAAQ,CAAA;EACnC,IAAA,OAAOA,QAAQ,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAGD,QAAQ,GAAG,GAAG,GAAGA,QAAQ,GAAG,GAAG,CAAA;KACxE,CAAA;EAAAvB,EAAAA,MAAA,CACDoB,KAAK,GAAL,SAAAA,QAAQ;EACJ,IAAA,IAAI,IAAI,CAACxD,IAAI,CAAC6D,IAAI,KACZ,IAAI,CAAC7D,IAAI,CAAC8D,MAAM,IAAIC,MAAM,CAAC,IAAI,CAAC/D,IAAI,CAAC6D,IAAI,KAAK,GAAG,CAAC,IAC/C,CAAC,IAAI,CAAC7D,IAAI,CAAC8D,MAAM,IAAIC,MAAM,CAAC,IAAI,CAAC/D,IAAI,CAAC6D,IAAI,CAAC,KAAK,EAAG,CAAC,EAAE;EAC3D,MAAA,OAAO,GAAG,GAAG,IAAI,CAAC7D,IAAI,CAAC6D,IAAI,CAAA;EAC/B,KAAC,MACI;EACD,MAAA,OAAO,EAAE,CAAA;EACb,KAAA;KACH,CAAA;EAAAzB,EAAAA,MAAA,CACDsB,MAAM,GAAN,SAAAA,MAAAA,CAAOzB,KAAK,EAAE;EACV,IAAA,IAAM+B,YAAY,GAAGzL,MAAM,CAAC0J,KAAK,CAAC,CAAA;MAClC,OAAO+B,YAAY,CAACrL,MAAM,GAAG,GAAG,GAAGqL,YAAY,GAAG,EAAE,CAAA;KACvD,CAAA;EAAA,EAAA,OAAAnC,SAAA,CAAA;EAAA,CAAA,CAhI0B1E,OAAO,CAAA;;ECTzB8G,IAAAA,OAAO,0BAAAC,UAAA,EAAA;EAChB,EAAA,SAAAD,UAAc;EAAA,IAAA,IAAAxC,KAAA,CAAA;EACVA,IAAAA,KAAA,GAAAyC,UAAA,CAAAtG,KAAA,CAAA,IAAA,EAASC,SAAS,CAAC,IAAA,IAAA,CAAA;MACnB4D,KAAA,CAAK0C,QAAQ,GAAG,KAAK,CAAA;EAAC,IAAA,OAAA1C,KAAA,CAAA;EAC1B,GAAA;IAACC,cAAA,CAAAuC,OAAA,EAAAC,UAAA,CAAA,CAAA;EAAA,EAAA,IAAA9B,MAAA,GAAA6B,OAAA,CAAAzN,SAAA,CAAA;EAID;EACJ;EACA;EACA;EACA;EACA;EALI4L,EAAAA,MAAA,CAMAI,MAAM,GAAN,SAAAA,SAAS;MACL,IAAI,CAAC4B,KAAK,EAAE,CAAA;EAChB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAAhC,EAAAA,MAAA,CAMAc,KAAK,GAAL,SAAAA,KAAAA,CAAMC,OAAO,EAAE;EAAA,IAAA,IAAApB,MAAA,GAAA,IAAA,CAAA;MACX,IAAI,CAACQ,UAAU,GAAG,SAAS,CAAA;EAC3B,IAAA,IAAMW,KAAK,GAAG,SAARA,KAAKA,GAAS;QAChBnB,MAAI,CAACQ,UAAU,GAAG,QAAQ,CAAA;EAC1BY,MAAAA,OAAO,EAAE,CAAA;OACZ,CAAA;MACD,IAAI,IAAI,CAACgB,QAAQ,IAAI,CAAC,IAAI,CAACnC,QAAQ,EAAE;QACjC,IAAIqC,KAAK,GAAG,CAAC,CAAA;QACb,IAAI,IAAI,CAACF,QAAQ,EAAE;EACfE,QAAAA,KAAK,EAAE,CAAA;EACP,QAAA,IAAI,CAAC3G,IAAI,CAAC,cAAc,EAAE,YAAY;EAClC,UAAA,EAAE2G,KAAK,IAAInB,KAAK,EAAE,CAAA;EACtB,SAAC,CAAC,CAAA;EACN,OAAA;EACA,MAAA,IAAI,CAAC,IAAI,CAAClB,QAAQ,EAAE;EAChBqC,QAAAA,KAAK,EAAE,CAAA;EACP,QAAA,IAAI,CAAC3G,IAAI,CAAC,OAAO,EAAE,YAAY;EAC3B,UAAA,EAAE2G,KAAK,IAAInB,KAAK,EAAE,CAAA;EACtB,SAAC,CAAC,CAAA;EACN,OAAA;EACJ,KAAC,MACI;EACDA,MAAAA,KAAK,EAAE,CAAA;EACX,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAd,EAAAA,MAAA,CAKAgC,KAAK,GAAL,SAAAA,QAAQ;MACJ,IAAI,CAACD,QAAQ,GAAG,IAAI,CAAA;MACpB,IAAI,CAACG,MAAM,EAAE,CAAA;EACb,IAAA,IAAI,CAAChG,YAAY,CAAC,MAAM,CAAC,CAAA;EAC7B,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAA8D,EAAAA,MAAA,CAKAW,MAAM,GAAN,SAAAA,MAAAA,CAAO1M,IAAI,EAAE;EAAA,IAAA,IAAAkO,MAAA,GAAA,IAAA,CAAA;EACT,IAAA,IAAMpN,QAAQ,GAAG,SAAXA,QAAQA,CAAIe,MAAM,EAAK;EACzB;QACA,IAAI,SAAS,KAAKqM,MAAI,CAAChC,UAAU,IAAIrK,MAAM,CAAC9B,IAAI,KAAK,MAAM,EAAE;UACzDmO,MAAI,CAACzB,MAAM,EAAE,CAAA;EACjB,OAAA;EACA;EACA,MAAA,IAAI,OAAO,KAAK5K,MAAM,CAAC9B,IAAI,EAAE;UACzBmO,MAAI,CAAC5B,OAAO,CAAC;EAAEpB,UAAAA,WAAW,EAAE,gCAAA;EAAiC,SAAC,CAAC,CAAA;EAC/D,QAAA,OAAO,KAAK,CAAA;EAChB,OAAA;EACA;EACAgD,MAAAA,MAAI,CAACvB,QAAQ,CAAC9K,MAAM,CAAC,CAAA;OACxB,CAAA;EACD;EACAwC,IAAAA,aAAa,CAACrE,IAAI,EAAE,IAAI,CAAC6L,MAAM,CAACxI,UAAU,CAAC,CAACzD,OAAO,CAACkB,QAAQ,CAAC,CAAA;EAC7D;EACA,IAAA,IAAI,QAAQ,KAAK,IAAI,CAACoL,UAAU,EAAE;EAC9B;QACA,IAAI,CAAC4B,QAAQ,GAAG,KAAK,CAAA;EACrB,MAAA,IAAI,CAAC7F,YAAY,CAAC,cAAc,CAAC,CAAA;EACjC,MAAA,IAAI,MAAM,KAAK,IAAI,CAACiE,UAAU,EAAE;UAC5B,IAAI,CAAC6B,KAAK,EAAE,CAAA;EAChB,OAEA;EACJ,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAhC,EAAAA,MAAA,CAKAM,OAAO,GAAP,SAAAA,UAAU;EAAA,IAAA,IAAA8B,MAAA,GAAA,IAAA,CAAA;EACN,IAAA,IAAM/B,KAAK,GAAG,SAARA,KAAKA,GAAS;QAChB+B,MAAI,CAAC3B,KAAK,CAAC,CAAC;EAAEzM,QAAAA,IAAI,EAAE,OAAA;EAAQ,OAAC,CAAC,CAAC,CAAA;OAClC,CAAA;EACD,IAAA,IAAI,MAAM,KAAK,IAAI,CAACmM,UAAU,EAAE;EAC5BE,MAAAA,KAAK,EAAE,CAAA;EACX,KAAC,MACI;EACD;EACA;EACA,MAAA,IAAI,CAAC/E,IAAI,CAAC,MAAM,EAAE+E,KAAK,CAAC,CAAA;EAC5B,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAAL,EAAAA,MAAA,CAMAS,KAAK,GAAL,SAAAA,KAAAA,CAAMxI,OAAO,EAAE;EAAA,IAAA,IAAAoK,MAAA,GAAA,IAAA,CAAA;MACX,IAAI,CAACzC,QAAQ,GAAG,KAAK,CAAA;EACrB5H,IAAAA,aAAa,CAACC,OAAO,EAAE,UAAChE,IAAI,EAAK;EAC7BoO,MAAAA,MAAI,CAACC,OAAO,CAACrO,IAAI,EAAE,YAAM;UACrBoO,MAAI,CAACzC,QAAQ,GAAG,IAAI,CAAA;EACpByC,QAAAA,MAAI,CAACnG,YAAY,CAAC,OAAO,CAAC,CAAA;EAC9B,OAAC,CAAC,CAAA;EACN,KAAC,CAAC,CAAA;EACN,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAA8D,EAAAA,MAAA,CAKAuC,GAAG,GAAH,SAAAA,MAAM;MACF,IAAMtB,MAAM,GAAG,IAAI,CAACrD,IAAI,CAAC8D,MAAM,GAAG,OAAO,GAAG,MAAM,CAAA;EAClD,IAAA,IAAM7B,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,EAAE,CAAA;EAC9B;EACA,IAAA,IAAI,KAAK,KAAK,IAAI,CAACjC,IAAI,CAAC4E,iBAAiB,EAAE;QACvC3C,KAAK,CAAC,IAAI,CAACjC,IAAI,CAAC6E,cAAc,CAAC,GAAGnE,YAAY,EAAE,CAAA;EACpD,KAAA;MACA,IAAI,CAAC,IAAI,CAACxJ,cAAc,IAAI,CAAC+K,KAAK,CAAC6C,GAAG,EAAE;QACpC7C,KAAK,CAAC8C,GAAG,GAAG,CAAC,CAAA;EACjB,KAAA;EACA,IAAA,OAAO,IAAI,CAAC3B,SAAS,CAACC,MAAM,EAAEpB,KAAK,CAAC,CAAA;KACvC,CAAA;IAAA,OAAA+C,YAAA,CAAAf,OAAA,EAAA,CAAA;MAAA/N,GAAA,EAAA,MAAA;MAAA+O,GAAA,EAvID,SAAAA,GAAAA,GAAW;EACP,MAAA,OAAO,SAAS,CAAA;EACpB,KAAA;EAAC,GAAA,CAAA,CAAA,CAAA;EAAA,CAAA,CAPwBpD,SAAS,CAAA;;ECHtC;EACA,IAAIqD,KAAK,GAAG,KAAK,CAAA;EACjB,IAAI;IACAA,KAAK,GAAG,OAAOC,cAAc,KAAK,WAAW,IACzC,iBAAiB,IAAI,IAAIA,cAAc,EAAE,CAAA;EACjD,CAAC,CACD,OAAOC,GAAG,EAAE;EACR;EACA;EAAA,CAAA;EAEG,IAAMC,OAAO,GAAGH,KAAK;;ECL5B,SAASI,KAAKA,GAAG,EAAE;EACNC,IAAAA,OAAO,0BAAAC,QAAA,EAAA;EAChB;EACJ;EACA;EACA;EACA;EACA;IACI,SAAAD,OAAAA,CAAYvF,IAAI,EAAE;EAAA,IAAA,IAAAyB,KAAA,CAAA;EACdA,IAAAA,KAAA,GAAA+D,QAAA,CAAA9O,IAAA,CAAA,IAAA,EAAMsJ,IAAI,CAAC,IAAA,IAAA,CAAA;EACX,IAAA,IAAI,OAAOyF,QAAQ,KAAK,WAAW,EAAE;EACjC,MAAA,IAAMC,KAAK,GAAG,QAAQ,KAAKD,QAAQ,CAACvI,QAAQ,CAAA;EAC5C,MAAA,IAAI2G,IAAI,GAAG4B,QAAQ,CAAC5B,IAAI,CAAA;EACxB;QACA,IAAI,CAACA,IAAI,EAAE;EACPA,QAAAA,IAAI,GAAG6B,KAAK,GAAG,KAAK,GAAG,IAAI,CAAA;EAC/B,OAAA;QACAjE,KAAA,CAAKkE,EAAE,GACF,OAAOF,QAAQ,KAAK,WAAW,IAC5BzF,IAAI,CAAC2D,QAAQ,KAAK8B,QAAQ,CAAC9B,QAAQ,IACnCE,IAAI,KAAK7D,IAAI,CAAC6D,IAAI,CAAA;EAC9B,KAAA;EAAC,IAAA,OAAApC,KAAA,CAAA;EACL,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;IANIC,cAAA,CAAA6D,OAAA,EAAAC,QAAA,CAAA,CAAA;EAAA,EAAA,IAAApD,MAAA,GAAAmD,OAAA,CAAA/O,SAAA,CAAA;IAAA4L,MAAA,CAOAsC,OAAO,GAAP,SAAAA,QAAQrO,IAAI,EAAEmH,EAAE,EAAE;EAAA,IAAA,IAAAuE,MAAA,GAAA,IAAA,CAAA;EACd,IAAA,IAAM6D,GAAG,GAAG,IAAI,CAACC,OAAO,CAAC;EACrBC,MAAAA,MAAM,EAAE,MAAM;EACdzP,MAAAA,IAAI,EAAEA,IAAAA;EACV,KAAC,CAAC,CAAA;EACFuP,IAAAA,GAAG,CAACvI,EAAE,CAAC,SAAS,EAAEG,EAAE,CAAC,CAAA;MACrBoI,GAAG,CAACvI,EAAE,CAAC,OAAO,EAAE,UAAC0I,SAAS,EAAEvE,OAAO,EAAK;QACpCO,MAAI,CAACM,OAAO,CAAC,gBAAgB,EAAE0D,SAAS,EAAEvE,OAAO,CAAC,CAAA;EACtD,KAAC,CAAC,CAAA;EACN,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAY,EAAAA,MAAA,CAKAkC,MAAM,GAAN,SAAAA,SAAS;EAAA,IAAA,IAAAC,MAAA,GAAA,IAAA,CAAA;EACL,IAAA,IAAMqB,GAAG,GAAG,IAAI,CAACC,OAAO,EAAE,CAAA;EAC1BD,IAAAA,GAAG,CAACvI,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC0F,MAAM,CAAC7C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;MACtC0F,GAAG,CAACvI,EAAE,CAAC,OAAO,EAAE,UAAC0I,SAAS,EAAEvE,OAAO,EAAK;QACpC+C,MAAI,CAAClC,OAAO,CAAC,gBAAgB,EAAE0D,SAAS,EAAEvE,OAAO,CAAC,CAAA;EACtD,KAAC,CAAC,CAAA;MACF,IAAI,CAACwE,OAAO,GAAGJ,GAAG,CAAA;KACrB,CAAA;EAAA,EAAA,OAAAL,OAAA,CAAA;EAAA,CAAA,CAnDwBtB,OAAO,CAAA,CAAA;EAqDvBgC,IAAAA,OAAO,0BAAAnE,QAAA,EAAA;EAChB;EACJ;EACA;EACA;EACA;EACA;EACI,EAAA,SAAAmE,QAAYC,aAAa,EAAEvB,GAAG,EAAE3E,IAAI,EAAE;EAAA,IAAA,IAAAwE,MAAA,CAAA;EAClCA,IAAAA,MAAA,GAAA1C,QAAA,CAAApL,IAAA,KAAM,CAAC,IAAA,IAAA,CAAA;MACP8N,MAAA,CAAK0B,aAAa,GAAGA,aAAa,CAAA;EAClCnG,IAAAA,qBAAqB,CAAAyE,MAAA,EAAOxE,IAAI,CAAC,CAAA;MACjCwE,MAAA,CAAK2B,KAAK,GAAGnG,IAAI,CAAA;EACjBwE,IAAAA,MAAA,CAAK4B,OAAO,GAAGpG,IAAI,CAAC8F,MAAM,IAAI,KAAK,CAAA;MACnCtB,MAAA,CAAK6B,IAAI,GAAG1B,GAAG,CAAA;EACfH,IAAAA,MAAA,CAAK8B,KAAK,GAAGhD,SAAS,KAAKtD,IAAI,CAAC3J,IAAI,GAAG2J,IAAI,CAAC3J,IAAI,GAAG,IAAI,CAAA;MACvDmO,MAAA,CAAK+B,OAAO,EAAE,CAAA;EAAC,IAAA,OAAA/B,MAAA,CAAA;EACnB,GAAA;EACA;EACJ;EACA;EACA;EACA;IAJI9C,cAAA,CAAAuE,OAAA,EAAAnE,QAAA,CAAA,CAAA;EAAA,EAAA,IAAA0E,OAAA,GAAAP,OAAA,CAAAzP,SAAA,CAAA;EAAAgQ,EAAAA,OAAA,CAKAD,OAAO,GAAP,SAAAA,UAAU;EAAA,IAAA,IAAA9B,MAAA,GAAA,IAAA,CAAA;EACN,IAAA,IAAIgC,EAAE,CAAA;MACN,IAAMzG,IAAI,GAAGZ,IAAI,CAAC,IAAI,CAAC+G,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,oBAAoB,EAAE,WAAW,CAAC,CAAA;MAC9HnG,IAAI,CAAC0G,OAAO,GAAG,CAAC,CAAC,IAAI,CAACP,KAAK,CAACR,EAAE,CAAA;MAC9B,IAAMgB,GAAG,GAAI,IAAI,CAACC,IAAI,GAAG,IAAI,CAACV,aAAa,CAAClG,IAAI,CAAE,CAAA;MAClD,IAAI;EACA2G,MAAAA,GAAG,CAACrE,IAAI,CAAC,IAAI,CAAC8D,OAAO,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAAC,CAAA;QACvC,IAAI;EACA,QAAA,IAAI,IAAI,CAACF,KAAK,CAACU,YAAY,EAAE;EACzB;YACAF,GAAG,CAACG,qBAAqB,IAAIH,GAAG,CAACG,qBAAqB,CAAC,IAAI,CAAC,CAAA;YAC5D,KAAK,IAAIpO,CAAC,IAAI,IAAI,CAACyN,KAAK,CAACU,YAAY,EAAE;cACnC,IAAI,IAAI,CAACV,KAAK,CAACU,YAAY,CAACpH,cAAc,CAAC/G,CAAC,CAAC,EAAE;EAC3CiO,cAAAA,GAAG,CAACI,gBAAgB,CAACrO,CAAC,EAAE,IAAI,CAACyN,KAAK,CAACU,YAAY,CAACnO,CAAC,CAAC,CAAC,CAAA;EACvD,aAAA;EACJ,WAAA;EACJ,SAAA;EACJ,OAAC,CACD,OAAOsO,CAAC,EAAE,EAAE;EACZ,MAAA,IAAI,MAAM,KAAK,IAAI,CAACZ,OAAO,EAAE;UACzB,IAAI;EACAO,UAAAA,GAAG,CAACI,gBAAgB,CAAC,cAAc,EAAE,0BAA0B,CAAC,CAAA;EACpE,SAAC,CACD,OAAOC,CAAC,EAAE,EAAE;EAChB,OAAA;QACA,IAAI;EACAL,QAAAA,GAAG,CAACI,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;EACzC,OAAC,CACD,OAAOC,CAAC,EAAE,EAAE;QACZ,CAACP,EAAE,GAAG,IAAI,CAACN,KAAK,CAACc,SAAS,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACS,UAAU,CAACP,GAAG,CAAC,CAAA;EACnF;QACA,IAAI,iBAAiB,IAAIA,GAAG,EAAE;EAC1BA,QAAAA,GAAG,CAACQ,eAAe,GAAG,IAAI,CAAChB,KAAK,CAACgB,eAAe,CAAA;EACpD,OAAA;EACA,MAAA,IAAI,IAAI,CAAChB,KAAK,CAACiB,cAAc,EAAE;EAC3BT,QAAAA,GAAG,CAACU,OAAO,GAAG,IAAI,CAAClB,KAAK,CAACiB,cAAc,CAAA;EAC3C,OAAA;QACAT,GAAG,CAACW,kBAAkB,GAAG,YAAM;EAC3B,QAAA,IAAIb,EAAE,CAAA;EACN,QAAA,IAAIE,GAAG,CAACpE,UAAU,KAAK,CAAC,EAAE;YACtB,CAACkE,EAAE,GAAGhC,MAAI,CAAC0B,KAAK,CAACc,SAAS,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,YAAY;EAChF;EACAZ,UAAAA,GAAG,CAACa,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAA;EACxC,SAAA;EACA,QAAA,IAAI,CAAC,KAAKb,GAAG,CAACpE,UAAU,EACpB,OAAA;UACJ,IAAI,GAAG,KAAKoE,GAAG,CAACc,MAAM,IAAI,IAAI,KAAKd,GAAG,CAACc,MAAM,EAAE;YAC3ChD,MAAI,CAACiD,OAAO,EAAE,CAAA;EAClB,SAAC,MACI;EACD;EACA;YACAjD,MAAI,CAAC5F,YAAY,CAAC,YAAM;EACpB4F,YAAAA,MAAI,CAACkD,QAAQ,CAAC,OAAOhB,GAAG,CAACc,MAAM,KAAK,QAAQ,GAAGd,GAAG,CAACc,MAAM,GAAG,CAAC,CAAC,CAAA;aACjE,EAAE,CAAC,CAAC,CAAA;EACT,SAAA;SACH,CAAA;EACDd,MAAAA,GAAG,CAAC/D,IAAI,CAAC,IAAI,CAAC0D,KAAK,CAAC,CAAA;OACvB,CACD,OAAOU,CAAC,EAAE;EACN;EACA;EACA;QACA,IAAI,CAACnI,YAAY,CAAC,YAAM;EACpB4F,QAAAA,MAAI,CAACkD,QAAQ,CAACX,CAAC,CAAC,CAAA;SACnB,EAAE,CAAC,CAAC,CAAA;EACL,MAAA,OAAA;EACJ,KAAA;EACA,IAAA,IAAI,OAAOY,QAAQ,KAAK,WAAW,EAAE;EACjC,MAAA,IAAI,CAACC,MAAM,GAAG5B,OAAO,CAAC6B,aAAa,EAAE,CAAA;QACrC7B,OAAO,CAAC8B,QAAQ,CAAC,IAAI,CAACF,MAAM,CAAC,GAAG,IAAI,CAAA;EACxC,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAArB,EAAAA,OAAA,CAKAmB,QAAQ,GAAR,SAAAA,QAAAA,CAASvC,GAAG,EAAE;MACV,IAAI,CAAC9G,YAAY,CAAC,OAAO,EAAE8G,GAAG,EAAE,IAAI,CAACwB,IAAI,CAAC,CAAA;EAC1C,IAAA,IAAI,CAACoB,QAAQ,CAAC,IAAI,CAAC,CAAA;EACvB,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAxB,EAAAA,OAAA,CAKAwB,QAAQ,GAAR,SAAAA,QAAAA,CAASC,SAAS,EAAE;EAChB,IAAA,IAAI,WAAW,KAAK,OAAO,IAAI,CAACrB,IAAI,IAAI,IAAI,KAAK,IAAI,CAACA,IAAI,EAAE;EACxD,MAAA,OAAA;EACJ,KAAA;EACA,IAAA,IAAI,CAACA,IAAI,CAACU,kBAAkB,GAAGhC,KAAK,CAAA;EACpC,IAAA,IAAI2C,SAAS,EAAE;QACX,IAAI;EACA,QAAA,IAAI,CAACrB,IAAI,CAACsB,KAAK,EAAE,CAAA;EACrB,OAAC,CACD,OAAOlB,CAAC,EAAE,EAAE;EAChB,KAAA;EACA,IAAA,IAAI,OAAOY,QAAQ,KAAK,WAAW,EAAE;EACjC,MAAA,OAAO3B,OAAO,CAAC8B,QAAQ,CAAC,IAAI,CAACF,MAAM,CAAC,CAAA;EACxC,KAAA;MACA,IAAI,CAACjB,IAAI,GAAG,IAAI,CAAA;EACpB,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAJ,EAAAA,OAAA,CAKAkB,OAAO,GAAP,SAAAA,UAAU;EACN,IAAA,IAAMrR,IAAI,GAAG,IAAI,CAACuQ,IAAI,CAACuB,YAAY,CAAA;MACnC,IAAI9R,IAAI,KAAK,IAAI,EAAE;EACf,MAAA,IAAI,CAACiI,YAAY,CAAC,MAAM,EAAEjI,IAAI,CAAC,CAAA;EAC/B,MAAA,IAAI,CAACiI,YAAY,CAAC,SAAS,CAAC,CAAA;QAC5B,IAAI,CAAC0J,QAAQ,EAAE,CAAA;EACnB,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAxB,EAAAA,OAAA,CAKA0B,KAAK,GAAL,SAAAA,QAAQ;MACJ,IAAI,CAACF,QAAQ,EAAE,CAAA;KAClB,CAAA;EAAA,EAAA,OAAA/B,OAAA,CAAA;EAAA,CAAA,CAjJwB9I,OAAO,CAAA,CAAA;EAmJpC8I,OAAO,CAAC6B,aAAa,GAAG,CAAC,CAAA;EACzB7B,OAAO,CAAC8B,QAAQ,GAAG,EAAE,CAAA;EACrB;EACA;EACA;EACA;EACA;EACA,IAAI,OAAOH,QAAQ,KAAK,WAAW,EAAE;EACjC;EACA,EAAA,IAAI,OAAOQ,WAAW,KAAK,UAAU,EAAE;EACnC;EACAA,IAAAA,WAAW,CAAC,UAAU,EAAEC,aAAa,CAAC,CAAA;EAC1C,GAAC,MACI,IAAI,OAAO/K,gBAAgB,KAAK,UAAU,EAAE;MAC7C,IAAMgL,gBAAgB,GAAG,YAAY,IAAI3I,cAAU,GAAG,UAAU,GAAG,QAAQ,CAAA;EAC3ErC,IAAAA,gBAAgB,CAACgL,gBAAgB,EAAED,aAAa,EAAE,KAAK,CAAC,CAAA;EAC5D,GAAA;EACJ,CAAA;EACA,SAASA,aAAaA,GAAG;EACrB,EAAA,KAAK,IAAI3P,CAAC,IAAIuN,OAAO,CAAC8B,QAAQ,EAAE;MAC5B,IAAI9B,OAAO,CAAC8B,QAAQ,CAACtI,cAAc,CAAC/G,CAAC,CAAC,EAAE;QACpCuN,OAAO,CAAC8B,QAAQ,CAACrP,CAAC,CAAC,CAACwP,KAAK,EAAE,CAAA;EAC/B,KAAA;EACJ,GAAA;EACJ,CAAA;EACA,IAAMK,OAAO,GAAI,YAAY;IACzB,IAAM5B,GAAG,GAAG6B,UAAU,CAAC;EACnB9B,IAAAA,OAAO,EAAE,KAAA;EACb,GAAC,CAAC,CAAA;EACF,EAAA,OAAOC,GAAG,IAAIA,GAAG,CAAC8B,YAAY,KAAK,IAAI,CAAA;EAC3C,CAAC,EAAG,CAAA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACaC,IAAAA,GAAG,0BAAAC,QAAA,EAAA;IACZ,SAAAD,GAAAA,CAAY1I,IAAI,EAAE;EAAA,IAAA,IAAA4I,MAAA,CAAA;EACdA,IAAAA,MAAA,GAAAD,QAAA,CAAAjS,IAAA,CAAA,IAAA,EAAMsJ,IAAI,CAAC,IAAA,IAAA,CAAA;EACX,IAAA,IAAMmC,WAAW,GAAGnC,IAAI,IAAIA,IAAI,CAACmC,WAAW,CAAA;EAC5CyG,IAAAA,MAAA,CAAK1R,cAAc,GAAGqR,OAAO,IAAI,CAACpG,WAAW,CAAA;EAAC,IAAA,OAAAyG,MAAA,CAAA;EAClD,GAAA;IAAClH,cAAA,CAAAgH,GAAA,EAAAC,QAAA,CAAA,CAAA;EAAA,EAAA,IAAAE,OAAA,GAAAH,GAAA,CAAAlS,SAAA,CAAA;EAAAqS,EAAAA,OAAA,CACDhD,OAAO,GAAP,SAAAA,UAAmB;EAAA,IAAA,IAAX7F,IAAI,GAAAnC,SAAA,CAAAlF,MAAA,GAAA,CAAA,IAAAkF,SAAA,CAAA,CAAA,CAAA,KAAAyF,SAAA,GAAAzF,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;MACbiL,QAAA,CAAc9I,IAAI,EAAE;QAAE2F,EAAE,EAAE,IAAI,CAACA,EAAAA;EAAG,KAAC,EAAE,IAAI,CAAC3F,IAAI,CAAC,CAAA;EAC/C,IAAA,OAAO,IAAIiG,OAAO,CAACuC,UAAU,EAAE,IAAI,CAAC7D,GAAG,EAAE,EAAE3E,IAAI,CAAC,CAAA;KACnD,CAAA;EAAA,EAAA,OAAA0I,GAAA,CAAA;EAAA,CAAA,CAToBnD,OAAO,CAAA,CAAA;EAWhC,SAASiD,UAAUA,CAACxI,IAAI,EAAE;EACtB,EAAA,IAAM0G,OAAO,GAAG1G,IAAI,CAAC0G,OAAO,CAAA;EAC5B;IACA,IAAI;MACA,IAAI,WAAW,KAAK,OAAOvB,cAAc,KAAK,CAACuB,OAAO,IAAIrB,OAAO,CAAC,EAAE;QAChE,OAAO,IAAIF,cAAc,EAAE,CAAA;EAC/B,KAAA;EACJ,GAAC,CACD,OAAO6B,CAAC,EAAE,EAAE;IACZ,IAAI,CAACN,OAAO,EAAE;MACV,IAAI;EACA,MAAA,OAAO,IAAI/G,cAAU,CAAC,CAAC,QAAQ,CAAC,CAACoJ,MAAM,CAAC,QAAQ,CAAC,CAACtO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAA;EACrF,KAAC,CACD,OAAOuM,CAAC,EAAE,EAAE;EAChB,GAAA;EACJ;;EC1QA;EACA,IAAMgC,aAAa,GAAG,OAAOC,SAAS,KAAK,WAAW,IAClD,OAAOA,SAAS,CAACC,OAAO,KAAK,QAAQ,IACrCD,SAAS,CAACC,OAAO,CAACC,WAAW,EAAE,KAAK,aAAa,CAAA;EACxCC,IAAAA,MAAM,0BAAAlF,UAAA,EAAA;EAAA,EAAA,SAAAkF,MAAA,GAAA;EAAA,IAAA,OAAAlF,UAAA,CAAAtG,KAAA,CAAA,IAAA,EAAAC,SAAA,CAAA,IAAA,IAAA,CAAA;EAAA,GAAA;IAAA6D,cAAA,CAAA0H,MAAA,EAAAlF,UAAA,CAAA,CAAA;EAAA,EAAA,IAAA9B,MAAA,GAAAgH,MAAA,CAAA5S,SAAA,CAAA;EAAA4L,EAAAA,MAAA,CAIfI,MAAM,GAAN,SAAAA,SAAS;EACL,IAAA,IAAMmC,GAAG,GAAG,IAAI,CAACA,GAAG,EAAE,CAAA;EACtB,IAAA,IAAM0E,SAAS,GAAG,IAAI,CAACrJ,IAAI,CAACqJ,SAAS,CAAA;EACrC;EACA,IAAA,IAAMrJ,IAAI,GAAGgJ,aAAa,GACpB,EAAE,GACF5J,IAAI,CAAC,IAAI,CAACY,IAAI,EAAE,OAAO,EAAE,mBAAmB,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,oBAAoB,EAAE,cAAc,EAAE,iBAAiB,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAAA;EAC1N,IAAA,IAAI,IAAI,CAACA,IAAI,CAAC6G,YAAY,EAAE;EACxB7G,MAAAA,IAAI,CAACsJ,OAAO,GAAG,IAAI,CAACtJ,IAAI,CAAC6G,YAAY,CAAA;EACzC,KAAA;MACA,IAAI;EACA,MAAA,IAAI,CAAC0C,EAAE,GAAG,IAAI,CAACC,YAAY,CAAC7E,GAAG,EAAE0E,SAAS,EAAErJ,IAAI,CAAC,CAAA;OACpD,CACD,OAAOoF,GAAG,EAAE;EACR,MAAA,OAAO,IAAI,CAAC9G,YAAY,CAAC,OAAO,EAAE8G,GAAG,CAAC,CAAA;EAC1C,KAAA;MACA,IAAI,CAACmE,EAAE,CAAC7P,UAAU,GAAG,IAAI,CAACwI,MAAM,CAACxI,UAAU,CAAA;MAC3C,IAAI,CAAC+P,iBAAiB,EAAE,CAAA;EAC5B,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAArH,EAAAA,MAAA,CAKAqH,iBAAiB,GAAjB,SAAAA,oBAAoB;EAAA,IAAA,IAAAhI,KAAA,GAAA,IAAA,CAAA;EAChB,IAAA,IAAI,CAAC8H,EAAE,CAACG,MAAM,GAAG,YAAM;EACnB,MAAA,IAAIjI,KAAI,CAACzB,IAAI,CAAC2J,SAAS,EAAE;EACrBlI,QAAAA,KAAI,CAAC8H,EAAE,CAACK,OAAO,CAACC,KAAK,EAAE,CAAA;EAC3B,OAAA;QACApI,KAAI,CAACqB,MAAM,EAAE,CAAA;OAChB,CAAA;EACD,IAAA,IAAI,CAACyG,EAAE,CAACO,OAAO,GAAG,UAACC,UAAU,EAAA;QAAA,OAAKtI,KAAI,CAACkB,OAAO,CAAC;EAC3CpB,QAAAA,WAAW,EAAE,6BAA6B;EAC1CC,QAAAA,OAAO,EAAEuI,UAAAA;EACb,OAAC,CAAC,CAAA;EAAA,KAAA,CAAA;EACF,IAAA,IAAI,CAACR,EAAE,CAACS,SAAS,GAAG,UAACC,EAAE,EAAA;EAAA,MAAA,OAAKxI,KAAI,CAACsB,MAAM,CAACkH,EAAE,CAAC5T,IAAI,CAAC,CAAA;EAAA,KAAA,CAAA;EAChD,IAAA,IAAI,CAACkT,EAAE,CAACW,OAAO,GAAG,UAAClD,CAAC,EAAA;EAAA,MAAA,OAAKvF,KAAI,CAACY,OAAO,CAAC,iBAAiB,EAAE2E,CAAC,CAAC,CAAA;EAAA,KAAA,CAAA;KAC9D,CAAA;EAAA5E,EAAAA,MAAA,CACDS,KAAK,GAAL,SAAAA,KAAAA,CAAMxI,OAAO,EAAE;EAAA,IAAA,IAAA0H,MAAA,GAAA,IAAA,CAAA;MACX,IAAI,CAACC,QAAQ,GAAG,KAAK,CAAA;EACrB;EACA;MAAA,IAAAmI,KAAA,GAAAA,SAAAA,KAAAA,GACyC;EACrC,MAAA,IAAMjS,MAAM,GAAGmC,OAAO,CAAC3B,CAAC,CAAC,CAAA;QACzB,IAAM0R,UAAU,GAAG1R,CAAC,KAAK2B,OAAO,CAAC1B,MAAM,GAAG,CAAC,CAAA;QAC3C3B,YAAY,CAACkB,MAAM,EAAE6J,MAAI,CAAC7K,cAAc,EAAE,UAACb,IAAI,EAAK;EAChD;EACA;EACA;UACA,IAAI;EACA0L,UAAAA,MAAI,CAAC2C,OAAO,CAACxM,MAAM,EAAE7B,IAAI,CAAC,CAAA;EAC9B,SAAC,CACD,OAAO2Q,CAAC,EAAE,EACV;EACA,QAAA,IAAIoD,UAAU,EAAE;EACZ;EACA;EACA3L,UAAAA,QAAQ,CAAC,YAAM;cACXsD,MAAI,CAACC,QAAQ,GAAG,IAAI,CAAA;EACpBD,YAAAA,MAAI,CAACzD,YAAY,CAAC,OAAO,CAAC,CAAA;EAC9B,WAAC,EAAEyD,MAAI,CAAClD,YAAY,CAAC,CAAA;EACzB,SAAA;EACJ,OAAC,CAAC,CAAA;OACL,CAAA;EArBD,IAAA,KAAK,IAAInG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,OAAO,CAAC1B,MAAM,EAAED,CAAC,EAAE,EAAA;QAAAyR,KAAA,EAAA,CAAA;EAAA,KAAA;KAsB1C,CAAA;EAAA/H,EAAAA,MAAA,CACDM,OAAO,GAAP,SAAAA,UAAU;EACN,IAAA,IAAI,OAAO,IAAI,CAAC6G,EAAE,KAAK,WAAW,EAAE;EAChC,MAAA,IAAI,CAACA,EAAE,CAACW,OAAO,GAAG,YAAM,EAAG,CAAA;EAC3B,MAAA,IAAI,CAACX,EAAE,CAAC9G,KAAK,EAAE,CAAA;QACf,IAAI,CAAC8G,EAAE,GAAG,IAAI,CAAA;EAClB,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAnH,EAAAA,MAAA,CAKAuC,GAAG,GAAH,SAAAA,MAAM;MACF,IAAMtB,MAAM,GAAG,IAAI,CAACrD,IAAI,CAAC8D,MAAM,GAAG,KAAK,GAAG,IAAI,CAAA;EAC9C,IAAA,IAAM7B,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,EAAE,CAAA;EAC9B;EACA,IAAA,IAAI,IAAI,CAACjC,IAAI,CAAC4E,iBAAiB,EAAE;QAC7B3C,KAAK,CAAC,IAAI,CAACjC,IAAI,CAAC6E,cAAc,CAAC,GAAGnE,YAAY,EAAE,CAAA;EACpD,KAAA;EACA;EACA,IAAA,IAAI,CAAC,IAAI,CAACxJ,cAAc,EAAE;QACtB+K,KAAK,CAAC8C,GAAG,GAAG,CAAC,CAAA;EACjB,KAAA;EACA,IAAA,OAAO,IAAI,CAAC3B,SAAS,CAACC,MAAM,EAAEpB,KAAK,CAAC,CAAA;KACvC,CAAA;IAAA,OAAA+C,YAAA,CAAAoE,MAAA,EAAA,CAAA;MAAAlT,GAAA,EAAA,MAAA;MAAA+O,GAAA,EA5FD,SAAAA,GAAAA,GAAW;EACP,MAAA,OAAO,WAAW,CAAA;EACtB,KAAA;EAAC,GAAA,CAAA,CAAA,CAAA;EAAA,CAAA,CAHuBpD,SAAS,CAAA,CAAA;EA+FrC,IAAMwI,aAAa,GAAG1K,cAAU,CAAC2K,SAAS,IAAI3K,cAAU,CAAC4K,YAAY,CAAA;EACrE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACaC,IAAAA,EAAE,0BAAAC,OAAA,EAAA;EAAA,EAAA,SAAAD,EAAA,GAAA;EAAA,IAAA,OAAAC,OAAA,CAAA7M,KAAA,CAAA,IAAA,EAAAC,SAAA,CAAA,IAAA,IAAA,CAAA;EAAA,GAAA;IAAA6D,cAAA,CAAA8I,EAAA,EAAAC,OAAA,CAAA,CAAA;EAAA,EAAA,IAAAjE,OAAA,GAAAgE,EAAA,CAAAhU,SAAA,CAAA;IAAAgQ,OAAA,CACXgD,YAAY,GAAZ,SAAAA,YAAAA,CAAa7E,GAAG,EAAE0E,SAAS,EAAErJ,IAAI,EAAE;MAC/B,OAAO,CAACgJ,aAAa,GACfK,SAAS,GACL,IAAIgB,aAAa,CAAC1F,GAAG,EAAE0E,SAAS,CAAC,GACjC,IAAIgB,aAAa,CAAC1F,GAAG,CAAC,GAC1B,IAAI0F,aAAa,CAAC1F,GAAG,EAAE0E,SAAS,EAAErJ,IAAI,CAAC,CAAA;KAChD,CAAA;IAAAwG,OAAA,CACD9B,OAAO,GAAP,SAAAA,QAAQgG,OAAO,EAAErU,IAAI,EAAE;EACnB,IAAA,IAAI,CAACkT,EAAE,CAAC3G,IAAI,CAACvM,IAAI,CAAC,CAAA;KACrB,CAAA;EAAA,EAAA,OAAAmU,EAAA,CAAA;EAAA,CAAA,CAVmBpB,MAAM,CAAA;;EC9G9B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACauB,IAAAA,EAAE,0BAAAzG,UAAA,EAAA;EAAA,EAAA,SAAAyG,EAAA,GAAA;EAAA,IAAA,OAAAzG,UAAA,CAAAtG,KAAA,CAAA,IAAA,EAAAC,SAAA,CAAA,IAAA,IAAA,CAAA;EAAA,GAAA;IAAA6D,cAAA,CAAAiJ,EAAA,EAAAzG,UAAA,CAAA,CAAA;EAAA,EAAA,IAAA9B,MAAA,GAAAuI,EAAA,CAAAnU,SAAA,CAAA;EAAA4L,EAAAA,MAAA,CAIXI,MAAM,GAAN,SAAAA,SAAS;EAAA,IAAA,IAAAf,KAAA,GAAA,IAAA,CAAA;MACL,IAAI;EACA;QACA,IAAI,CAACmJ,UAAU,GAAG,IAAIC,YAAY,CAAC,IAAI,CAACzH,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,CAACpD,IAAI,CAAC8K,gBAAgB,CAAC,IAAI,CAACC,IAAI,CAAC,CAAC,CAAA;OACrG,CACD,OAAO3F,GAAG,EAAE;EACR,MAAA,OAAO,IAAI,CAAC9G,YAAY,CAAC,OAAO,EAAE8G,GAAG,CAAC,CAAA;EAC1C,KAAA;EACA,IAAA,IAAI,CAACwF,UAAU,CAACI,MAAM,CACjB5S,IAAI,CAAC,YAAM;QACZqJ,KAAI,CAACkB,OAAO,EAAE,CAAA;EAClB,KAAC,CAAC,CAAA,OAAA,CACQ,CAAC,UAACyC,GAAG,EAAK;EAChB3D,MAAAA,KAAI,CAACY,OAAO,CAAC,oBAAoB,EAAE+C,GAAG,CAAC,CAAA;EAC3C,KAAC,CAAC,CAAA;EACF;EACA,IAAA,IAAI,CAACwF,UAAU,CAACK,KAAK,CAAC7S,IAAI,CAAC,YAAM;QAC7BqJ,KAAI,CAACmJ,UAAU,CAACM,yBAAyB,EAAE,CAAC9S,IAAI,CAAC,UAAC+S,MAAM,EAAK;EACzD,QAAA,IAAMC,aAAa,GAAG9O,yBAAyB,CAACyH,MAAM,CAACsH,gBAAgB,EAAE5J,KAAI,CAACS,MAAM,CAACxI,UAAU,CAAC,CAAA;EAChG,QAAA,IAAM4R,MAAM,GAAGH,MAAM,CAACI,QAAQ,CAACC,WAAW,CAACJ,aAAa,CAAC,CAACK,SAAS,EAAE,CAAA;EACrE,QAAA,IAAMC,aAAa,GAAG5Q,yBAAyB,EAAE,CAAA;UACjD4Q,aAAa,CAACH,QAAQ,CAACI,MAAM,CAACR,MAAM,CAACnJ,QAAQ,CAAC,CAAA;UAC9CP,KAAI,CAACmK,OAAO,GAAGF,aAAa,CAAC1J,QAAQ,CAAC6J,SAAS,EAAE,CAAA;EACjD,QAAA,IAAMC,IAAI,GAAG,SAAPA,IAAIA,GAAS;YACfR,MAAM,CACDQ,IAAI,EAAE,CACN1T,IAAI,CAAC,UAAAnB,IAAA,EAAqB;EAAA,YAAA,IAAlB8U,IAAI,GAAA9U,IAAA,CAAJ8U,IAAI;gBAAE7G,KAAK,GAAAjO,IAAA,CAALiO,KAAK,CAAA;EACpB,YAAA,IAAI6G,IAAI,EAAE;EACN,cAAA,OAAA;EACJ,aAAA;EACAtK,YAAAA,KAAI,CAACuB,QAAQ,CAACkC,KAAK,CAAC,CAAA;EACpB4G,YAAAA,IAAI,EAAE,CAAA;aACT,CAAC,SACQ,CAAC,UAAC1G,GAAG,EAAK,EACnB,CAAC,CAAA;WACL,CAAA;EACD0G,QAAAA,IAAI,EAAE,CAAA;EACN,QAAA,IAAM5T,MAAM,GAAG;EAAE9B,UAAAA,IAAI,EAAE,MAAA;WAAQ,CAAA;EAC/B,QAAA,IAAIqL,KAAI,CAACQ,KAAK,CAAC6C,GAAG,EAAE;YAChB5M,MAAM,CAAC7B,IAAI,GAAA,aAAA,CAAA0S,MAAA,CAActH,KAAI,CAACQ,KAAK,CAAC6C,GAAG,EAAI,KAAA,CAAA,CAAA;EAC/C,SAAA;UACArD,KAAI,CAACmK,OAAO,CAAC/I,KAAK,CAAC3K,MAAM,CAAC,CAACE,IAAI,CAAC,YAAA;EAAA,UAAA,OAAMqJ,KAAI,CAACqB,MAAM,EAAE,CAAA;WAAC,CAAA,CAAA;EACxD,OAAC,CAAC,CAAA;EACN,KAAC,CAAC,CAAA;KACL,CAAA;EAAAV,EAAAA,MAAA,CACDS,KAAK,GAAL,SAAAA,KAAAA,CAAMxI,OAAO,EAAE;EAAA,IAAA,IAAA0H,MAAA,GAAA,IAAA,CAAA;MACX,IAAI,CAACC,QAAQ,GAAG,KAAK,CAAA;MAAC,IAAAmI,KAAA,GAAAA,SAAAA,KAAAA,GACmB;EACrC,MAAA,IAAMjS,MAAM,GAAGmC,OAAO,CAAC3B,CAAC,CAAC,CAAA;QACzB,IAAM0R,UAAU,GAAG1R,CAAC,KAAK2B,OAAO,CAAC1B,MAAM,GAAG,CAAC,CAAA;QAC3CoJ,MAAI,CAAC6J,OAAO,CAAC/I,KAAK,CAAC3K,MAAM,CAAC,CAACE,IAAI,CAAC,YAAM;EAClC,QAAA,IAAIgS,UAAU,EAAE;EACZ3L,UAAAA,QAAQ,CAAC,YAAM;cACXsD,MAAI,CAACC,QAAQ,GAAG,IAAI,CAAA;EACpBD,YAAAA,MAAI,CAACzD,YAAY,CAAC,OAAO,CAAC,CAAA;EAC9B,WAAC,EAAEyD,MAAI,CAAClD,YAAY,CAAC,CAAA;EACzB,SAAA;EACJ,OAAC,CAAC,CAAA;OACL,CAAA;EAXD,IAAA,KAAK,IAAInG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,OAAO,CAAC1B,MAAM,EAAED,CAAC,EAAE,EAAA;QAAAyR,KAAA,EAAA,CAAA;EAAA,KAAA;KAY1C,CAAA;EAAA/H,EAAAA,MAAA,CACDM,OAAO,GAAP,SAAAA,UAAU;EACN,IAAA,IAAI+D,EAAE,CAAA;MACN,CAACA,EAAE,GAAG,IAAI,CAACmE,UAAU,MAAM,IAAI,IAAInE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAChE,KAAK,EAAE,CAAA;KACzE,CAAA;IAAA,OAAAuC,YAAA,CAAA2F,EAAA,EAAA,CAAA;MAAAzU,GAAA,EAAA,MAAA;MAAA+O,GAAA,EAlED,SAAAA,GAAAA,GAAW;EACP,MAAA,OAAO,cAAc,CAAA;EACzB,KAAA;EAAC,GAAA,CAAA,CAAA,CAAA;EAAA,CAAA,CAHmBpD,SAAS,CAAA;;ECR1B,IAAMmK,UAAU,GAAG;EACtBC,EAAAA,SAAS,EAAEzB,EAAE;EACb0B,EAAAA,YAAY,EAAEvB,EAAE;EAChBwB,EAAAA,OAAO,EAAEzD,GAAAA;EACb,CAAC;;ECPD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAM0D,EAAE,GAAG,qPAAqP,CAAA;EAChQ,IAAMC,KAAK,GAAG,CACV,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAChJ,CAAA;EACM,SAASC,KAAKA,CAAC/L,GAAG,EAAE;EACvB,EAAA,IAAIA,GAAG,CAAC5H,MAAM,GAAG,IAAI,EAAE;EACnB,IAAA,MAAM,cAAc,CAAA;EACxB,GAAA;IACA,IAAM4T,GAAG,GAAGhM,GAAG;EAAEiM,IAAAA,CAAC,GAAGjM,GAAG,CAACqD,OAAO,CAAC,GAAG,CAAC;EAAEoD,IAAAA,CAAC,GAAGzG,GAAG,CAACqD,OAAO,CAAC,GAAG,CAAC,CAAA;IAC3D,IAAI4I,CAAC,IAAI,CAAC,CAAC,IAAIxF,CAAC,IAAI,CAAC,CAAC,EAAE;EACpBzG,IAAAA,GAAG,GAAGA,GAAG,CAACzG,SAAS,CAAC,CAAC,EAAE0S,CAAC,CAAC,GAAGjM,GAAG,CAACzG,SAAS,CAAC0S,CAAC,EAAExF,CAAC,CAAC,CAACyF,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,GAAGlM,GAAG,CAACzG,SAAS,CAACkN,CAAC,EAAEzG,GAAG,CAAC5H,MAAM,CAAC,CAAA;EACrG,GAAA;IACA,IAAI+T,CAAC,GAAGN,EAAE,CAACO,IAAI,CAACpM,GAAG,IAAI,EAAE,CAAC;MAAEoE,GAAG,GAAG,EAAE;EAAEjM,IAAAA,CAAC,GAAG,EAAE,CAAA;IAC5C,OAAOA,CAAC,EAAE,EAAE;EACRiM,IAAAA,GAAG,CAAC0H,KAAK,CAAC3T,CAAC,CAAC,CAAC,GAAGgU,CAAC,CAAChU,CAAC,CAAC,IAAI,EAAE,CAAA;EAC9B,GAAA;IACA,IAAI8T,CAAC,IAAI,CAAC,CAAC,IAAIxF,CAAC,IAAI,CAAC,CAAC,EAAE;MACpBrC,GAAG,CAACiI,MAAM,GAAGL,GAAG,CAAA;MAChB5H,GAAG,CAACkI,IAAI,GAAGlI,GAAG,CAACkI,IAAI,CAAC/S,SAAS,CAAC,CAAC,EAAE6K,GAAG,CAACkI,IAAI,CAAClU,MAAM,GAAG,CAAC,CAAC,CAAC8T,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;MACxE9H,GAAG,CAACmI,SAAS,GAAGnI,GAAG,CAACmI,SAAS,CAACL,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;MAClF9H,GAAG,CAACoI,OAAO,GAAG,IAAI,CAAA;EACtB,GAAA;IACApI,GAAG,CAACqI,SAAS,GAAGA,SAAS,CAACrI,GAAG,EAAEA,GAAG,CAAC,MAAM,CAAC,CAAC,CAAA;IAC3CA,GAAG,CAACsI,QAAQ,GAAGA,QAAQ,CAACtI,GAAG,EAAEA,GAAG,CAAC,OAAO,CAAC,CAAC,CAAA;EAC1C,EAAA,OAAOA,GAAG,CAAA;EACd,CAAA;EACA,SAASqI,SAASA,CAAClW,GAAG,EAAE2M,IAAI,EAAE;IAC1B,IAAMyJ,IAAI,GAAG,UAAU;EAAEC,IAAAA,KAAK,GAAG1J,IAAI,CAACgJ,OAAO,CAACS,IAAI,EAAE,GAAG,CAAC,CAACxV,KAAK,CAAC,GAAG,CAAC,CAAA;EACnE,EAAA,IAAI+L,IAAI,CAACpH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,IAAIoH,IAAI,CAAC9K,MAAM,KAAK,CAAC,EAAE;EAC9CwU,IAAAA,KAAK,CAAChP,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;EACtB,GAAA;IACA,IAAIsF,IAAI,CAACpH,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;MACvB8Q,KAAK,CAAChP,MAAM,CAACgP,KAAK,CAACxU,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;EACrC,GAAA;EACA,EAAA,OAAOwU,KAAK,CAAA;EAChB,CAAA;EACA,SAASF,QAAQA,CAACtI,GAAG,EAAE1C,KAAK,EAAE;IAC1B,IAAM5L,IAAI,GAAG,EAAE,CAAA;IACf4L,KAAK,CAACwK,OAAO,CAAC,2BAA2B,EAAE,UAAUW,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC7D,IAAA,IAAID,EAAE,EAAE;EACJhX,MAAAA,IAAI,CAACgX,EAAE,CAAC,GAAGC,EAAE,CAAA;EACjB,KAAA;EACJ,GAAC,CAAC,CAAA;EACF,EAAA,OAAOjX,IAAI,CAAA;EACf;;ECxDA,IAAMkX,kBAAkB,GAAG,OAAOjQ,gBAAgB,KAAK,UAAU,IAC7D,OAAOU,mBAAmB,KAAK,UAAU,CAAA;EAC7C,IAAMwP,uBAAuB,GAAG,EAAE,CAAA;EAClC,IAAID,kBAAkB,EAAE;EACpB;EACA;IACAjQ,gBAAgB,CAAC,SAAS,EAAE,YAAM;EAC9BkQ,IAAAA,uBAAuB,CAACvX,OAAO,CAAC,UAACwX,QAAQ,EAAA;QAAA,OAAKA,QAAQ,EAAE,CAAA;OAAC,CAAA,CAAA;KAC5D,EAAE,KAAK,CAAC,CAAA;EACb,CAAA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACaC,IAAAA,oBAAoB,0BAAA5L,QAAA,EAAA;EAC7B;EACJ;EACA;EACA;EACA;EACA;EACI,EAAA,SAAA4L,oBAAY/I,CAAAA,GAAG,EAAE3E,IAAI,EAAE;EAAA,IAAA,IAAAyB,KAAA,CAAA;EACnBA,IAAAA,KAAA,GAAAK,QAAA,CAAApL,IAAA,KAAM,CAAC,IAAA,IAAA,CAAA;MACP+K,KAAA,CAAK/H,UAAU,GAAGwF,iBAAiB,CAAA;MACnCuC,KAAA,CAAKkM,WAAW,GAAG,EAAE,CAAA;MACrBlM,KAAA,CAAKmM,cAAc,GAAG,CAAC,CAAA;EACvBnM,IAAAA,KAAA,CAAKoM,aAAa,GAAG,CAAC,CAAC,CAAA;EACvBpM,IAAAA,KAAA,CAAKqM,YAAY,GAAG,CAAC,CAAC,CAAA;EACtBrM,IAAAA,KAAA,CAAKsM,WAAW,GAAG,CAAC,CAAC,CAAA;EACrB;EACR;EACA;EACA;MACQtM,KAAA,CAAKuM,gBAAgB,GAAGC,QAAQ,CAAA;EAChC,IAAA,IAAItJ,GAAG,IAAI,QAAQ,KAAAuJ,OAAA,CAAYvJ,GAAG,CAAE,EAAA;EAChC3E,MAAAA,IAAI,GAAG2E,GAAG,CAAA;EACVA,MAAAA,GAAG,GAAG,IAAI,CAAA;EACd,KAAA;EACA,IAAA,IAAIA,GAAG,EAAE;EACL,MAAA,IAAMwJ,SAAS,GAAG7B,KAAK,CAAC3H,GAAG,CAAC,CAAA;EAC5B3E,MAAAA,IAAI,CAAC2D,QAAQ,GAAGwK,SAAS,CAACtB,IAAI,CAAA;EAC9B7M,MAAAA,IAAI,CAAC8D,MAAM,GACPqK,SAAS,CAACjR,QAAQ,KAAK,OAAO,IAAIiR,SAAS,CAACjR,QAAQ,KAAK,KAAK,CAAA;EAClE8C,MAAAA,IAAI,CAAC6D,IAAI,GAAGsK,SAAS,CAACtK,IAAI,CAAA;QAC1B,IAAIsK,SAAS,CAAClM,KAAK,EACfjC,IAAI,CAACiC,KAAK,GAAGkM,SAAS,CAAClM,KAAK,CAAA;EACpC,KAAC,MACI,IAAIjC,IAAI,CAAC6M,IAAI,EAAE;QAChB7M,IAAI,CAAC2D,QAAQ,GAAG2I,KAAK,CAACtM,IAAI,CAAC6M,IAAI,CAAC,CAACA,IAAI,CAAA;EACzC,KAAA;EACA9M,IAAAA,qBAAqB,CAAA0B,KAAA,EAAOzB,IAAI,CAAC,CAAA;MACjCyB,KAAA,CAAKqC,MAAM,GACP,IAAI,IAAI9D,IAAI,CAAC8D,MAAM,GACb9D,IAAI,CAAC8D,MAAM,GACX,OAAO2B,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAKA,QAAQ,CAACvI,QAAQ,CAAA;MAC3E,IAAI8C,IAAI,CAAC2D,QAAQ,IAAI,CAAC3D,IAAI,CAAC6D,IAAI,EAAE;EAC7B;QACA7D,IAAI,CAAC6D,IAAI,GAAGpC,KAAA,CAAKqC,MAAM,GAAG,KAAK,GAAG,IAAI,CAAA;EAC1C,KAAA;EACArC,IAAAA,KAAA,CAAKkC,QAAQ,GACT3D,IAAI,CAAC2D,QAAQ,KACR,OAAO8B,QAAQ,KAAK,WAAW,GAAGA,QAAQ,CAAC9B,QAAQ,GAAG,WAAW,CAAC,CAAA;MAC3ElC,KAAA,CAAKoC,IAAI,GACL7D,IAAI,CAAC6D,IAAI,KACJ,OAAO4B,QAAQ,KAAK,WAAW,IAAIA,QAAQ,CAAC5B,IAAI,GAC3C4B,QAAQ,CAAC5B,IAAI,GACbpC,KAAA,CAAKqC,MAAM,GACP,KAAK,GACL,IAAI,CAAC,CAAA;MACvBrC,KAAA,CAAKuK,UAAU,GAAG,EAAE,CAAA;EACpBvK,IAAAA,KAAA,CAAK2M,iBAAiB,GAAG,EAAE,CAAA;EAC3BpO,IAAAA,IAAI,CAACgM,UAAU,CAAC/V,OAAO,CAAC,UAACoY,CAAC,EAAK;EAC3B,MAAA,IAAMC,aAAa,GAAGD,CAAC,CAAC7X,SAAS,CAACuU,IAAI,CAAA;EACtCtJ,MAAAA,KAAA,CAAKuK,UAAU,CAACnR,IAAI,CAACyT,aAAa,CAAC,CAAA;EACnC7M,MAAAA,KAAA,CAAK2M,iBAAiB,CAACE,aAAa,CAAC,GAAGD,CAAC,CAAA;EAC7C,KAAC,CAAC,CAAA;EACF5M,IAAAA,KAAA,CAAKzB,IAAI,GAAG8I,QAAA,CAAc;EACtBrF,MAAAA,IAAI,EAAE,YAAY;EAClB8K,MAAAA,KAAK,EAAE,KAAK;EACZpH,MAAAA,eAAe,EAAE,KAAK;EACtBqH,MAAAA,OAAO,EAAE,IAAI;EACb3J,MAAAA,cAAc,EAAE,GAAG;EACnB4J,MAAAA,eAAe,EAAE,KAAK;EACtBC,MAAAA,gBAAgB,EAAE,IAAI;EACtBC,MAAAA,kBAAkB,EAAE,IAAI;EACxBC,MAAAA,iBAAiB,EAAE;EACfC,QAAAA,SAAS,EAAE,IAAA;SACd;QACD/D,gBAAgB,EAAE,EAAE;EACpBgE,MAAAA,mBAAmB,EAAE,KAAA;OACxB,EAAE9O,IAAI,CAAC,CAAA;MACRyB,KAAA,CAAKzB,IAAI,CAACyD,IAAI,GACVhC,KAAA,CAAKzB,IAAI,CAACyD,IAAI,CAACgJ,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAC5BhL,KAAA,CAAKzB,IAAI,CAAC0O,gBAAgB,GAAG,GAAG,GAAG,EAAE,CAAC,CAAA;MAC/C,IAAI,OAAOjN,KAAA,CAAKzB,IAAI,CAACiC,KAAK,KAAK,QAAQ,EAAE;EACrCR,MAAAA,KAAA,CAAKzB,IAAI,CAACiC,KAAK,GAAGpJ,MAAM,CAAC4I,KAAA,CAAKzB,IAAI,CAACiC,KAAK,CAAC,CAAA;EAC7C,KAAA;EACA,IAAA,IAAIsL,kBAAkB,EAAE;EACpB,MAAA,IAAI9L,KAAA,CAAKzB,IAAI,CAAC8O,mBAAmB,EAAE;EAC/B;EACA;EACA;UACArN,KAAA,CAAKsN,0BAA0B,GAAG,YAAM;YACpC,IAAItN,KAAA,CAAKuN,SAAS,EAAE;EAChB;EACAvN,YAAAA,KAAA,CAAKuN,SAAS,CAACjR,kBAAkB,EAAE,CAAA;EACnC0D,YAAAA,KAAA,CAAKuN,SAAS,CAACvM,KAAK,EAAE,CAAA;EAC1B,WAAA;WACH,CAAA;UACDnF,gBAAgB,CAAC,cAAc,EAAEmE,KAAA,CAAKsN,0BAA0B,EAAE,KAAK,CAAC,CAAA;EAC5E,OAAA;EACA,MAAA,IAAItN,KAAA,CAAKkC,QAAQ,KAAK,WAAW,EAAE;UAC/BlC,KAAA,CAAKwN,qBAAqB,GAAG,YAAM;EAC/BxN,UAAAA,KAAA,CAAKyN,QAAQ,CAAC,iBAAiB,EAAE;EAC7B3N,YAAAA,WAAW,EAAE,yBAAA;EACjB,WAAC,CAAC,CAAA;WACL,CAAA;EACDiM,QAAAA,uBAAuB,CAAC3S,IAAI,CAAC4G,KAAA,CAAKwN,qBAAqB,CAAC,CAAA;EAC5D,OAAA;EACJ,KAAA;EACA,IAAA,IAAIxN,KAAA,CAAKzB,IAAI,CAACmH,eAAe,EAAE;EAC3B1F,MAAAA,KAAA,CAAK0N,UAAU,GAAGhQ,eAAe,EAAE,CAAA;EACvC,KAAA;MACAsC,KAAA,CAAK2N,KAAK,EAAE,CAAA;EAAC,IAAA,OAAA3N,KAAA,CAAA;EACjB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;IANIC,cAAA,CAAAgM,oBAAA,EAAA5L,QAAA,CAAA,CAAA;EAAA,EAAA,IAAAM,MAAA,GAAAsL,oBAAA,CAAAlX,SAAA,CAAA;EAAA4L,EAAAA,MAAA,CAOAiN,eAAe,GAAf,SAAAA,eAAAA,CAAgBtE,IAAI,EAAE;EAClB,IAAA,IAAM9I,KAAK,GAAG6G,QAAA,CAAc,EAAE,EAAE,IAAI,CAAC9I,IAAI,CAACiC,KAAK,CAAC,CAAA;EAChD;MACAA,KAAK,CAACqN,GAAG,GAAGpS,UAAQ,CAAA;EACpB;MACA+E,KAAK,CAAC+M,SAAS,GAAGjE,IAAI,CAAA;EACtB;MACA,IAAI,IAAI,CAACwE,EAAE,EACPtN,KAAK,CAAC6C,GAAG,GAAG,IAAI,CAACyK,EAAE,CAAA;MACvB,IAAMvP,IAAI,GAAG8I,QAAA,CAAc,EAAE,EAAE,IAAI,CAAC9I,IAAI,EAAE;EACtCiC,MAAAA,KAAK,EAALA,KAAK;EACLC,MAAAA,MAAM,EAAE,IAAI;QACZyB,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBG,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBD,IAAI,EAAE,IAAI,CAACA,IAAAA;OACd,EAAE,IAAI,CAAC7D,IAAI,CAAC8K,gBAAgB,CAACC,IAAI,CAAC,CAAC,CAAA;MACpC,OAAO,IAAI,IAAI,CAACqD,iBAAiB,CAACrD,IAAI,CAAC,CAAC/K,IAAI,CAAC,CAAA;EACjD,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAoC,EAAAA,MAAA,CAKAgN,KAAK,GAAL,SAAAA,QAAQ;EAAA,IAAA,IAAArN,MAAA,GAAA,IAAA,CAAA;EACJ,IAAA,IAAI,IAAI,CAACiK,UAAU,CAACrT,MAAM,KAAK,CAAC,EAAE;EAC9B;QACA,IAAI,CAACkG,YAAY,CAAC,YAAM;EACpBkD,QAAAA,MAAI,CAACzD,YAAY,CAAC,OAAO,EAAE,yBAAyB,CAAC,CAAA;SACxD,EAAE,CAAC,CAAC,CAAA;EACL,MAAA,OAAA;EACJ,KAAA;EACA,IAAA,IAAMgQ,aAAa,GAAG,IAAI,CAACtO,IAAI,CAACyO,eAAe,IAC3Cf,oBAAoB,CAAC8B,qBAAqB,IAC1C,IAAI,CAACxD,UAAU,CAACpI,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,GACzC,WAAW,GACX,IAAI,CAACoI,UAAU,CAAC,CAAC,CAAC,CAAA;MACxB,IAAI,CAACzJ,UAAU,GAAG,SAAS,CAAA;EAC3B,IAAA,IAAMyM,SAAS,GAAG,IAAI,CAACK,eAAe,CAACf,aAAa,CAAC,CAAA;MACrDU,SAAS,CAAC1M,IAAI,EAAE,CAAA;EAChB,IAAA,IAAI,CAACmN,YAAY,CAACT,SAAS,CAAC,CAAA;EAChC,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAA5M,EAAAA,MAAA,CAKAqN,YAAY,GAAZ,SAAAA,YAAAA,CAAaT,SAAS,EAAE;EAAA,IAAA,IAAAzK,MAAA,GAAA,IAAA,CAAA;MACpB,IAAI,IAAI,CAACyK,SAAS,EAAE;EAChB,MAAA,IAAI,CAACA,SAAS,CAACjR,kBAAkB,EAAE,CAAA;EACvC,KAAA;EACA;MACA,IAAI,CAACiR,SAAS,GAAGA,SAAS,CAAA;EAC1B;MACAA,SAAS,CACJ3R,EAAE,CAAC,OAAO,EAAE,IAAI,CAACqS,QAAQ,CAACxP,IAAI,CAAC,IAAI,CAAC,CAAC,CACrC7C,EAAE,CAAC,QAAQ,EAAE,IAAI,CAACsS,SAAS,CAACzP,IAAI,CAAC,IAAI,CAAC,CAAC,CACvC7C,EAAE,CAAC,OAAO,EAAE,IAAI,CAACsK,QAAQ,CAACzH,IAAI,CAAC,IAAI,CAAC,CAAC,CACrC7C,EAAE,CAAC,OAAO,EAAE,UAACiE,MAAM,EAAA;EAAA,MAAA,OAAKiD,MAAI,CAAC2K,QAAQ,CAAC,iBAAiB,EAAE5N,MAAM,CAAC,CAAA;OAAC,CAAA,CAAA;EAC1E,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAc,EAAAA,MAAA,CAKAU,MAAM,GAAN,SAAAA,SAAS;MACL,IAAI,CAACP,UAAU,GAAG,MAAM,CAAA;MACxBmL,oBAAoB,CAAC8B,qBAAqB,GACtC,WAAW,KAAK,IAAI,CAACR,SAAS,CAACjE,IAAI,CAAA;EACvC,IAAA,IAAI,CAACzM,YAAY,CAAC,MAAM,CAAC,CAAA;MACzB,IAAI,CAACsR,KAAK,EAAE,CAAA;EAChB,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAxN,EAAAA,MAAA,CAKAuN,SAAS,GAAT,SAAAA,SAAAA,CAAUzX,MAAM,EAAE;EACd,IAAA,IAAI,SAAS,KAAK,IAAI,CAACqK,UAAU,IAC7B,MAAM,KAAK,IAAI,CAACA,UAAU,IAC1B,SAAS,KAAK,IAAI,CAACA,UAAU,EAAE;EAC/B,MAAA,IAAI,CAACjE,YAAY,CAAC,QAAQ,EAAEpG,MAAM,CAAC,CAAA;EACnC;EACA,MAAA,IAAI,CAACoG,YAAY,CAAC,WAAW,CAAC,CAAA;QAC9B,QAAQpG,MAAM,CAAC9B,IAAI;EACf,QAAA,KAAK,MAAM;YACP,IAAI,CAACyZ,WAAW,CAACC,IAAI,CAACxD,KAAK,CAACpU,MAAM,CAAC7B,IAAI,CAAC,CAAC,CAAA;EACzC,UAAA,MAAA;EACJ,QAAA,KAAK,MAAM;EACP,UAAA,IAAI,CAAC0Z,WAAW,CAAC,MAAM,CAAC,CAAA;EACxB,UAAA,IAAI,CAACzR,YAAY,CAAC,MAAM,CAAC,CAAA;EACzB,UAAA,IAAI,CAACA,YAAY,CAAC,MAAM,CAAC,CAAA;YACzB,IAAI,CAAC0R,iBAAiB,EAAE,CAAA;EACxB,UAAA,MAAA;EACJ,QAAA,KAAK,OAAO;EACR,UAAA,IAAM5K,GAAG,GAAG,IAAIxD,KAAK,CAAC,cAAc,CAAC,CAAA;EACrC;EACAwD,UAAAA,GAAG,CAAC6K,IAAI,GAAG/X,MAAM,CAAC7B,IAAI,CAAA;EACtB,UAAA,IAAI,CAACsR,QAAQ,CAACvC,GAAG,CAAC,CAAA;EAClB,UAAA,MAAA;EACJ,QAAA,KAAK,SAAS;YACV,IAAI,CAAC9G,YAAY,CAAC,MAAM,EAAEpG,MAAM,CAAC7B,IAAI,CAAC,CAAA;YACtC,IAAI,CAACiI,YAAY,CAAC,SAAS,EAAEpG,MAAM,CAAC7B,IAAI,CAAC,CAAA;EACzC,UAAA,MAAA;EACR,OAAA;EACJ,KAEA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAA+L,EAAAA,MAAA,CAMAyN,WAAW,GAAX,SAAAA,WAAAA,CAAYxZ,IAAI,EAAE;EACd,IAAA,IAAI,CAACiI,YAAY,CAAC,WAAW,EAAEjI,IAAI,CAAC,CAAA;EACpC,IAAA,IAAI,CAACkZ,EAAE,GAAGlZ,IAAI,CAACyO,GAAG,CAAA;MAClB,IAAI,CAACkK,SAAS,CAAC/M,KAAK,CAAC6C,GAAG,GAAGzO,IAAI,CAACyO,GAAG,CAAA;EACnC,IAAA,IAAI,CAAC+I,aAAa,GAAGxX,IAAI,CAAC6Z,YAAY,CAAA;EACtC,IAAA,IAAI,CAACpC,YAAY,GAAGzX,IAAI,CAAC8Z,WAAW,CAAA;EACpC,IAAA,IAAI,CAACpC,WAAW,GAAG1X,IAAI,CAACkG,UAAU,CAAA;MAClC,IAAI,CAACuG,MAAM,EAAE,CAAA;EACb;EACA,IAAA,IAAI,QAAQ,KAAK,IAAI,CAACP,UAAU,EAC5B,OAAA;MACJ,IAAI,CAACyN,iBAAiB,EAAE,CAAA;EAC5B,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAA5N,EAAAA,MAAA,CAKA4N,iBAAiB,GAAjB,SAAAA,oBAAoB;EAAA,IAAA,IAAAxL,MAAA,GAAA,IAAA,CAAA;EAChB,IAAA,IAAI,CAACrE,cAAc,CAAC,IAAI,CAACiQ,iBAAiB,CAAC,CAAA;MAC3C,IAAMC,KAAK,GAAG,IAAI,CAACxC,aAAa,GAAG,IAAI,CAACC,YAAY,CAAA;MACpD,IAAI,CAACE,gBAAgB,GAAGrN,IAAI,CAACC,GAAG,EAAE,GAAGyP,KAAK,CAAA;EAC1C,IAAA,IAAI,CAACD,iBAAiB,GAAG,IAAI,CAACvR,YAAY,CAAC,YAAM;EAC7C2F,MAAAA,MAAI,CAAC0K,QAAQ,CAAC,cAAc,CAAC,CAAA;OAChC,EAAEmB,KAAK,CAAC,CAAA;EACT,IAAA,IAAI,IAAI,CAACrQ,IAAI,CAAC2J,SAAS,EAAE;EACrB,MAAA,IAAI,CAACyG,iBAAiB,CAACvG,KAAK,EAAE,CAAA;EAClC,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAzH,EAAAA,MAAA,CAKAsN,QAAQ,GAAR,SAAAA,WAAW;MACP,IAAI,CAAC/B,WAAW,CAACxP,MAAM,CAAC,CAAC,EAAE,IAAI,CAACyP,cAAc,CAAC,CAAA;EAC/C;EACA;EACA;MACA,IAAI,CAACA,cAAc,GAAG,CAAC,CAAA;EACvB,IAAA,IAAI,CAAC,KAAK,IAAI,CAACD,WAAW,CAAChV,MAAM,EAAE;EAC/B,MAAA,IAAI,CAAC2F,YAAY,CAAC,OAAO,CAAC,CAAA;EAC9B,KAAC,MACI;QACD,IAAI,CAACsR,KAAK,EAAE,CAAA;EAChB,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAxN,EAAAA,MAAA,CAKAwN,KAAK,GAAL,SAAAA,QAAQ;MACJ,IAAI,QAAQ,KAAK,IAAI,CAACrN,UAAU,IAC5B,IAAI,CAACyM,SAAS,CAAChN,QAAQ,IACvB,CAAC,IAAI,CAACsO,SAAS,IACf,IAAI,CAAC3C,WAAW,CAAChV,MAAM,EAAE;EACzB,MAAA,IAAM0B,OAAO,GAAG,IAAI,CAACkW,mBAAmB,EAAE,CAAA;EAC1C,MAAA,IAAI,CAACvB,SAAS,CAACpM,IAAI,CAACvI,OAAO,CAAC,CAAA;EAC5B;EACA;EACA,MAAA,IAAI,CAACuT,cAAc,GAAGvT,OAAO,CAAC1B,MAAM,CAAA;EACpC,MAAA,IAAI,CAAC2F,YAAY,CAAC,OAAO,CAAC,CAAA;EAC9B,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAA8D,EAAAA,MAAA,CAMAmO,mBAAmB,GAAnB,SAAAA,sBAAsB;MAClB,IAAMC,sBAAsB,GAAG,IAAI,CAACzC,WAAW,IAC3C,IAAI,CAACiB,SAAS,CAACjE,IAAI,KAAK,SAAS,IACjC,IAAI,CAAC4C,WAAW,CAAChV,MAAM,GAAG,CAAC,CAAA;MAC/B,IAAI,CAAC6X,sBAAsB,EAAE;QACzB,OAAO,IAAI,CAAC7C,WAAW,CAAA;EAC3B,KAAA;EACA,IAAA,IAAI8C,WAAW,GAAG,CAAC,CAAC;EACpB,IAAA,KAAK,IAAI/X,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACiV,WAAW,CAAChV,MAAM,EAAED,CAAC,EAAE,EAAE;QAC9C,IAAMrC,IAAI,GAAG,IAAI,CAACsX,WAAW,CAACjV,CAAC,CAAC,CAACrC,IAAI,CAAA;EACrC,MAAA,IAAIA,IAAI,EAAE;EACNoa,QAAAA,WAAW,IAAI1Y,UAAU,CAAC1B,IAAI,CAAC,CAAA;EACnC,OAAA;QACA,IAAIqC,CAAC,GAAG,CAAC,IAAI+X,WAAW,GAAG,IAAI,CAAC1C,WAAW,EAAE;UACzC,OAAO,IAAI,CAACJ,WAAW,CAACtR,KAAK,CAAC,CAAC,EAAE3D,CAAC,CAAC,CAAA;EACvC,OAAA;QACA+X,WAAW,IAAI,CAAC,CAAC;EACrB,KAAA;MACA,OAAO,IAAI,CAAC9C,WAAW,CAAA;EAC3B,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACI,gBAAA;EAAAvL,EAAAA,MAAA,CAAcsO,eAAe,GAAf,SAAAA,kBAAkB;EAAA,IAAA,IAAAjM,MAAA,GAAA,IAAA,CAAA;EAC5B,IAAA,IAAI,CAAC,IAAI,CAACuJ,gBAAgB,EACtB,OAAO,IAAI,CAAA;MACf,IAAM2C,UAAU,GAAGhQ,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAACoN,gBAAgB,CAAA;EACrD,IAAA,IAAI2C,UAAU,EAAE;QACZ,IAAI,CAAC3C,gBAAgB,GAAG,CAAC,CAAA;EACzBvP,MAAAA,QAAQ,CAAC,YAAM;EACXgG,QAAAA,MAAI,CAACyK,QAAQ,CAAC,cAAc,CAAC,CAAA;EACjC,OAAC,EAAE,IAAI,CAACrQ,YAAY,CAAC,CAAA;EACzB,KAAA;EACA,IAAA,OAAO8R,UAAU,CAAA;EACrB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA,MAPI;IAAAvO,MAAA,CAQAS,KAAK,GAAL,SAAAA,KAAAA,CAAM+N,GAAG,EAAEC,OAAO,EAAErT,EAAE,EAAE;MACpB,IAAI,CAACuS,WAAW,CAAC,SAAS,EAAEa,GAAG,EAAEC,OAAO,EAAErT,EAAE,CAAC,CAAA;EAC7C,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA,MAPI;IAAA4E,MAAA,CAQAQ,IAAI,GAAJ,SAAAA,IAAAA,CAAKgO,GAAG,EAAEC,OAAO,EAAErT,EAAE,EAAE;MACnB,IAAI,CAACuS,WAAW,CAAC,SAAS,EAAEa,GAAG,EAAEC,OAAO,EAAErT,EAAE,CAAC,CAAA;EAC7C,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MARI;EAAA4E,EAAAA,MAAA,CASA2N,WAAW,GAAX,SAAAA,WAAY3Z,CAAAA,IAAI,EAAEC,IAAI,EAAEwa,OAAO,EAAErT,EAAE,EAAE;EACjC,IAAA,IAAI,UAAU,KAAK,OAAOnH,IAAI,EAAE;EAC5BmH,MAAAA,EAAE,GAAGnH,IAAI,CAAA;EACTA,MAAAA,IAAI,GAAGiN,SAAS,CAAA;EACpB,KAAA;EACA,IAAA,IAAI,UAAU,KAAK,OAAOuN,OAAO,EAAE;EAC/BrT,MAAAA,EAAE,GAAGqT,OAAO,CAAA;EACZA,MAAAA,OAAO,GAAG,IAAI,CAAA;EAClB,KAAA;MACA,IAAI,SAAS,KAAK,IAAI,CAACtO,UAAU,IAAI,QAAQ,KAAK,IAAI,CAACA,UAAU,EAAE;EAC/D,MAAA,OAAA;EACJ,KAAA;EACAsO,IAAAA,OAAO,GAAGA,OAAO,IAAI,EAAE,CAAA;EACvBA,IAAAA,OAAO,CAACC,QAAQ,GAAG,KAAK,KAAKD,OAAO,CAACC,QAAQ,CAAA;EAC7C,IAAA,IAAM5Y,MAAM,GAAG;EACX9B,MAAAA,IAAI,EAAEA,IAAI;EACVC,MAAAA,IAAI,EAAEA,IAAI;EACVwa,MAAAA,OAAO,EAAEA,OAAAA;OACZ,CAAA;EACD,IAAA,IAAI,CAACvS,YAAY,CAAC,cAAc,EAAEpG,MAAM,CAAC,CAAA;EACzC,IAAA,IAAI,CAACyV,WAAW,CAAC9S,IAAI,CAAC3C,MAAM,CAAC,CAAA;MAC7B,IAAIsF,EAAE,EACF,IAAI,CAACE,IAAI,CAAC,OAAO,EAAEF,EAAE,CAAC,CAAA;MAC1B,IAAI,CAACoS,KAAK,EAAE,CAAA;EAChB,GAAA;EACA;EACJ;EACA,MAFI;EAAAxN,EAAAA,MAAA,CAGAK,KAAK,GAAL,SAAAA,QAAQ;EAAA,IAAA,IAAAmG,MAAA,GAAA,IAAA,CAAA;EACJ,IAAA,IAAMnG,KAAK,GAAG,SAARA,KAAKA,GAAS;EAChBmG,MAAAA,MAAI,CAACsG,QAAQ,CAAC,cAAc,CAAC,CAAA;EAC7BtG,MAAAA,MAAI,CAACoG,SAAS,CAACvM,KAAK,EAAE,CAAA;OACzB,CAAA;EACD,IAAA,IAAMsO,eAAe,GAAG,SAAlBA,eAAeA,GAAS;EAC1BnI,MAAAA,MAAI,CAACjL,GAAG,CAAC,SAAS,EAAEoT,eAAe,CAAC,CAAA;EACpCnI,MAAAA,MAAI,CAACjL,GAAG,CAAC,cAAc,EAAEoT,eAAe,CAAC,CAAA;EACzCtO,MAAAA,KAAK,EAAE,CAAA;OACV,CAAA;EACD,IAAA,IAAMuO,cAAc,GAAG,SAAjBA,cAAcA,GAAS;EACzB;EACApI,MAAAA,MAAI,CAAClL,IAAI,CAAC,SAAS,EAAEqT,eAAe,CAAC,CAAA;EACrCnI,MAAAA,MAAI,CAAClL,IAAI,CAAC,cAAc,EAAEqT,eAAe,CAAC,CAAA;OAC7C,CAAA;MACD,IAAI,SAAS,KAAK,IAAI,CAACxO,UAAU,IAAI,MAAM,KAAK,IAAI,CAACA,UAAU,EAAE;QAC7D,IAAI,CAACA,UAAU,GAAG,SAAS,CAAA;EAC3B,MAAA,IAAI,IAAI,CAACoL,WAAW,CAAChV,MAAM,EAAE;EACzB,QAAA,IAAI,CAAC+E,IAAI,CAAC,OAAO,EAAE,YAAM;YACrB,IAAIkL,MAAI,CAAC0H,SAAS,EAAE;EAChBU,YAAAA,cAAc,EAAE,CAAA;EACpB,WAAC,MACI;EACDvO,YAAAA,KAAK,EAAE,CAAA;EACX,WAAA;EACJ,SAAC,CAAC,CAAA;EACN,OAAC,MACI,IAAI,IAAI,CAAC6N,SAAS,EAAE;EACrBU,QAAAA,cAAc,EAAE,CAAA;EACpB,OAAC,MACI;EACDvO,QAAAA,KAAK,EAAE,CAAA;EACX,OAAA;EACJ,KAAA;EACA,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAL,EAAAA,MAAA,CAKAuF,QAAQ,GAAR,SAAAA,QAAAA,CAASvC,GAAG,EAAE;MACVsI,oBAAoB,CAAC8B,qBAAqB,GAAG,KAAK,CAAA;EAClD,IAAA,IAAI,IAAI,CAACxP,IAAI,CAACiR,gBAAgB,IAC1B,IAAI,CAACjF,UAAU,CAACrT,MAAM,GAAG,CAAC,IAC1B,IAAI,CAAC4J,UAAU,KAAK,SAAS,EAAE;EAC/B,MAAA,IAAI,CAACyJ,UAAU,CAAC7P,KAAK,EAAE,CAAA;EACvB,MAAA,OAAO,IAAI,CAACiT,KAAK,EAAE,CAAA;EACvB,KAAA;EACA,IAAA,IAAI,CAAC9Q,YAAY,CAAC,OAAO,EAAE8G,GAAG,CAAC,CAAA;EAC/B,IAAA,IAAI,CAAC8J,QAAQ,CAAC,iBAAiB,EAAE9J,GAAG,CAAC,CAAA;EACzC,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;IAAAhD,MAAA,CAKA8M,QAAQ,GAAR,SAAAA,SAAS5N,MAAM,EAAEC,WAAW,EAAE;EAC1B,IAAA,IAAI,SAAS,KAAK,IAAI,CAACgB,UAAU,IAC7B,MAAM,KAAK,IAAI,CAACA,UAAU,IAC1B,SAAS,KAAK,IAAI,CAACA,UAAU,EAAE;EAC/B;EACA,MAAA,IAAI,CAACpC,cAAc,CAAC,IAAI,CAACiQ,iBAAiB,CAAC,CAAA;EAC3C;EACA,MAAA,IAAI,CAACpB,SAAS,CAACjR,kBAAkB,CAAC,OAAO,CAAC,CAAA;EAC1C;EACA,MAAA,IAAI,CAACiR,SAAS,CAACvM,KAAK,EAAE,CAAA;EACtB;EACA,MAAA,IAAI,CAACuM,SAAS,CAACjR,kBAAkB,EAAE,CAAA;EACnC,MAAA,IAAIwP,kBAAkB,EAAE;UACpB,IAAI,IAAI,CAACwB,0BAA0B,EAAE;YACjC/Q,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAAC+Q,0BAA0B,EAAE,KAAK,CAAC,CAAA;EAC/E,SAAA;UACA,IAAI,IAAI,CAACE,qBAAqB,EAAE;YAC5B,IAAMvW,CAAC,GAAG8U,uBAAuB,CAAC5J,OAAO,CAAC,IAAI,CAACqL,qBAAqB,CAAC,CAAA;EACrE,UAAA,IAAIvW,CAAC,KAAK,CAAC,CAAC,EAAE;EACV8U,YAAAA,uBAAuB,CAACrP,MAAM,CAACzF,CAAC,EAAE,CAAC,CAAC,CAAA;EACxC,WAAA;EACJ,SAAA;EACJ,OAAA;EACA;QACA,IAAI,CAAC6J,UAAU,GAAG,QAAQ,CAAA;EAC1B;QACA,IAAI,CAACgN,EAAE,GAAG,IAAI,CAAA;EACd;QACA,IAAI,CAACjR,YAAY,CAAC,OAAO,EAAEgD,MAAM,EAAEC,WAAW,CAAC,CAAA;EAC/C;EACA;QACA,IAAI,CAACoM,WAAW,GAAG,EAAE,CAAA;QACrB,IAAI,CAACC,cAAc,GAAG,CAAC,CAAA;EAC3B,KAAA;KACH,CAAA;EAAA,EAAA,OAAAF,oBAAA,CAAA;EAAA,CAAA,CAhfqCvQ,OAAO,CAAA,CAAA;EAkfjDuQ,oBAAoB,CAACxQ,QAAQ,GAAGA,UAAQ,CAAA;EACxC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACagU,IAAAA,iBAAiB,0BAAAC,qBAAA,EAAA;EAC1B,EAAA,SAAAD,oBAAc;EAAA,IAAA,IAAAE,MAAA,CAAA;EACVA,IAAAA,MAAA,GAAAD,qBAAA,CAAAvT,KAAA,CAAA,IAAA,EAASC,SAAS,CAAC,IAAA,IAAA,CAAA;MACnBuT,MAAA,CAAKC,SAAS,GAAG,EAAE,CAAA;EAAC,IAAA,OAAAD,MAAA,CAAA;EACxB,GAAA;IAAC1P,cAAA,CAAAwP,iBAAA,EAAAC,qBAAA,CAAA,CAAA;EAAA,EAAA,IAAA3K,OAAA,GAAA0K,iBAAA,CAAA1a,SAAA,CAAA;EAAAgQ,EAAAA,OAAA,CACD1D,MAAM,GAAN,SAAAA,SAAS;EACLqO,IAAAA,qBAAA,CAAA3a,SAAA,CAAMsM,MAAM,CAAApM,IAAA,CAAA,IAAA,CAAA,CAAA;MACZ,IAAI,MAAM,KAAK,IAAI,CAAC6L,UAAU,IAAI,IAAI,CAACvC,IAAI,CAACwO,OAAO,EAAE;EACjD,MAAA,KAAK,IAAI9V,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC2Y,SAAS,CAAC1Y,MAAM,EAAED,CAAC,EAAE,EAAE;UAC5C,IAAI,CAAC4Y,MAAM,CAAC,IAAI,CAACD,SAAS,CAAC3Y,CAAC,CAAC,CAAC,CAAA;EAClC,OAAA;EACJ,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAA8N,EAAAA,OAAA,CAMA8K,MAAM,GAAN,SAAAA,MAAAA,CAAOvG,IAAI,EAAE;EAAA,IAAA,IAAAwG,MAAA,GAAA,IAAA,CAAA;EACT,IAAA,IAAIvC,SAAS,GAAG,IAAI,CAACK,eAAe,CAACtE,IAAI,CAAC,CAAA;MAC1C,IAAIyG,MAAM,GAAG,KAAK,CAAA;MAClB9D,oBAAoB,CAAC8B,qBAAqB,GAAG,KAAK,CAAA;EAClD,IAAA,IAAMiC,eAAe,GAAG,SAAlBA,eAAeA,GAAS;EAC1B,MAAA,IAAID,MAAM,EACN,OAAA;QACJxC,SAAS,CAACpM,IAAI,CAAC,CAAC;EAAExM,QAAAA,IAAI,EAAE,MAAM;EAAEC,QAAAA,IAAI,EAAE,OAAA;EAAQ,OAAC,CAAC,CAAC,CAAA;EACjD2Y,MAAAA,SAAS,CAACtR,IAAI,CAAC,QAAQ,EAAE,UAACkT,GAAG,EAAK;EAC9B,QAAA,IAAIY,MAAM,EACN,OAAA;UACJ,IAAI,MAAM,KAAKZ,GAAG,CAACxa,IAAI,IAAI,OAAO,KAAKwa,GAAG,CAACva,IAAI,EAAE;YAC7Ckb,MAAI,CAACjB,SAAS,GAAG,IAAI,CAAA;EACrBiB,UAAAA,MAAI,CAACjT,YAAY,CAAC,WAAW,EAAE0Q,SAAS,CAAC,CAAA;YACzC,IAAI,CAACA,SAAS,EACV,OAAA;EACJtB,UAAAA,oBAAoB,CAAC8B,qBAAqB,GACtC,WAAW,KAAKR,SAAS,CAACjE,IAAI,CAAA;EAClCwG,UAAAA,MAAI,CAACvC,SAAS,CAAC9L,KAAK,CAAC,YAAM;EACvB,YAAA,IAAIsO,MAAM,EACN,OAAA;EACJ,YAAA,IAAI,QAAQ,KAAKD,MAAI,CAAChP,UAAU,EAC5B,OAAA;EACJmP,YAAAA,OAAO,EAAE,CAAA;EACTH,YAAAA,MAAI,CAAC9B,YAAY,CAACT,SAAS,CAAC,CAAA;cAC5BA,SAAS,CAACpM,IAAI,CAAC,CAAC;EAAExM,cAAAA,IAAI,EAAE,SAAA;EAAU,aAAC,CAAC,CAAC,CAAA;EACrCmb,YAAAA,MAAI,CAACjT,YAAY,CAAC,SAAS,EAAE0Q,SAAS,CAAC,CAAA;EACvCA,YAAAA,SAAS,GAAG,IAAI,CAAA;cAChBuC,MAAI,CAACjB,SAAS,GAAG,KAAK,CAAA;cACtBiB,MAAI,CAAC3B,KAAK,EAAE,CAAA;EAChB,WAAC,CAAC,CAAA;EACN,SAAC,MACI;EACD,UAAA,IAAMxK,GAAG,GAAG,IAAIxD,KAAK,CAAC,aAAa,CAAC,CAAA;EACpC;EACAwD,UAAAA,GAAG,CAAC4J,SAAS,GAAGA,SAAS,CAACjE,IAAI,CAAA;EAC9BwG,UAAAA,MAAI,CAACjT,YAAY,CAAC,cAAc,EAAE8G,GAAG,CAAC,CAAA;EAC1C,SAAA;EACJ,OAAC,CAAC,CAAA;OACL,CAAA;MACD,SAASuM,eAAeA,GAAG;EACvB,MAAA,IAAIH,MAAM,EACN,OAAA;EACJ;EACAA,MAAAA,MAAM,GAAG,IAAI,CAAA;EACbE,MAAAA,OAAO,EAAE,CAAA;QACT1C,SAAS,CAACvM,KAAK,EAAE,CAAA;EACjBuM,MAAAA,SAAS,GAAG,IAAI,CAAA;EACpB,KAAA;EACA;EACA,IAAA,IAAM9E,OAAO,GAAG,SAAVA,OAAOA,CAAI9E,GAAG,EAAK;QACrB,IAAMwM,KAAK,GAAG,IAAIhQ,KAAK,CAAC,eAAe,GAAGwD,GAAG,CAAC,CAAA;EAC9C;EACAwM,MAAAA,KAAK,CAAC5C,SAAS,GAAGA,SAAS,CAACjE,IAAI,CAAA;EAChC4G,MAAAA,eAAe,EAAE,CAAA;EACjBJ,MAAAA,MAAI,CAACjT,YAAY,CAAC,cAAc,EAAEsT,KAAK,CAAC,CAAA;OAC3C,CAAA;MACD,SAASC,gBAAgBA,GAAG;QACxB3H,OAAO,CAAC,kBAAkB,CAAC,CAAA;EAC/B,KAAA;EACA;MACA,SAASJ,OAAOA,GAAG;QACfI,OAAO,CAAC,eAAe,CAAC,CAAA;EAC5B,KAAA;EACA;MACA,SAAS4H,SAASA,CAACC,EAAE,EAAE;QACnB,IAAI/C,SAAS,IAAI+C,EAAE,CAAChH,IAAI,KAAKiE,SAAS,CAACjE,IAAI,EAAE;EACzC4G,QAAAA,eAAe,EAAE,CAAA;EACrB,OAAA;EACJ,KAAA;EACA;EACA,IAAA,IAAMD,OAAO,GAAG,SAAVA,OAAOA,GAAS;EAClB1C,MAAAA,SAAS,CAAClR,cAAc,CAAC,MAAM,EAAE2T,eAAe,CAAC,CAAA;EACjDzC,MAAAA,SAAS,CAAClR,cAAc,CAAC,OAAO,EAAEoM,OAAO,CAAC,CAAA;EAC1C8E,MAAAA,SAAS,CAAClR,cAAc,CAAC,OAAO,EAAE+T,gBAAgB,CAAC,CAAA;EACnDN,MAAAA,MAAI,CAAC5T,GAAG,CAAC,OAAO,EAAEmM,OAAO,CAAC,CAAA;EAC1ByH,MAAAA,MAAI,CAAC5T,GAAG,CAAC,WAAW,EAAEmU,SAAS,CAAC,CAAA;OACnC,CAAA;EACD9C,IAAAA,SAAS,CAACtR,IAAI,CAAC,MAAM,EAAE+T,eAAe,CAAC,CAAA;EACvCzC,IAAAA,SAAS,CAACtR,IAAI,CAAC,OAAO,EAAEwM,OAAO,CAAC,CAAA;EAChC8E,IAAAA,SAAS,CAACtR,IAAI,CAAC,OAAO,EAAEmU,gBAAgB,CAAC,CAAA;EACzC,IAAA,IAAI,CAACnU,IAAI,CAAC,OAAO,EAAEoM,OAAO,CAAC,CAAA;EAC3B,IAAA,IAAI,CAACpM,IAAI,CAAC,WAAW,EAAEoU,SAAS,CAAC,CAAA;EACjC,IAAA,IAAI,IAAI,CAACT,SAAS,CAACzN,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,IAC7CmH,IAAI,KAAK,cAAc,EAAE;EACzB;QACA,IAAI,CAAClM,YAAY,CAAC,YAAM;UACpB,IAAI,CAAC2S,MAAM,EAAE;YACTxC,SAAS,CAAC1M,IAAI,EAAE,CAAA;EACpB,SAAA;SACH,EAAE,GAAG,CAAC,CAAA;EACX,KAAC,MACI;QACD0M,SAAS,CAAC1M,IAAI,EAAE,CAAA;EACpB,KAAA;KACH,CAAA;EAAAkE,EAAAA,OAAA,CACDqJ,WAAW,GAAX,SAAAA,WAAAA,CAAYxZ,IAAI,EAAE;MACd,IAAI,CAACgb,SAAS,GAAG,IAAI,CAACW,eAAe,CAAC3b,IAAI,CAAC4b,QAAQ,CAAC,CAAA;EACpDd,IAAAA,qBAAA,CAAA3a,SAAA,CAAMqZ,WAAW,CAAAnZ,IAAA,OAACL,IAAI,CAAA,CAAA;EAC1B,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAAmQ,EAAAA,OAAA,CAMAwL,eAAe,GAAf,SAAAA,eAAAA,CAAgBC,QAAQ,EAAE;MACtB,IAAMC,gBAAgB,GAAG,EAAE,CAAA;EAC3B,IAAA,KAAK,IAAIxZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuZ,QAAQ,CAACtZ,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,IAAI,CAAC,IAAI,CAACsT,UAAU,CAACpI,OAAO,CAACqO,QAAQ,CAACvZ,CAAC,CAAC,CAAC,EACrCwZ,gBAAgB,CAACrX,IAAI,CAACoX,QAAQ,CAACvZ,CAAC,CAAC,CAAC,CAAA;EAC1C,KAAA;EACA,IAAA,OAAOwZ,gBAAgB,CAAA;KAC1B,CAAA;EAAA,EAAA,OAAAhB,iBAAA,CAAA;EAAA,CAAA,CApIkCxD,oBAAoB,CAAA,CAAA;EAsI3D;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACayE,IAAAA,QAAM,0BAAAC,kBAAA,EAAA;IACf,SAAAD,MAAAA,CAAYxN,GAAG,EAAa;EAAA,IAAA,IAAX3E,IAAI,GAAAnC,SAAA,CAAAlF,MAAA,GAAA,CAAA,IAAAkF,SAAA,CAAA,CAAA,CAAA,KAAAyF,SAAA,GAAAzF,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;MACtB,IAAMwU,CAAC,GAAGnE,OAAA,CAAOvJ,GAAG,MAAK,QAAQ,GAAGA,GAAG,GAAG3E,IAAI,CAAA;EAC9C,IAAA,IAAI,CAACqS,CAAC,CAACrG,UAAU,IACZqG,CAAC,CAACrG,UAAU,IAAI,OAAOqG,CAAC,CAACrG,UAAU,CAAC,CAAC,CAAC,KAAK,QAAS,EAAE;EACvDqG,MAAAA,CAAC,CAACrG,UAAU,GAAG,CAACqG,CAAC,CAACrG,UAAU,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,cAAc,CAAC,EACnEsG,GAAG,CAAC,UAAChE,aAAa,EAAA;UAAA,OAAKiE,UAAkB,CAACjE,aAAa,CAAC,CAAA;EAAA,OAAA,CAAC,CACzDkE,MAAM,CAAC,UAACnE,CAAC,EAAA;UAAA,OAAK,CAAC,CAACA,CAAC,CAAA;SAAC,CAAA,CAAA;EAC3B,KAAA;EAAC,IAAA,OACD+D,kBAAA,CAAA1b,IAAA,OAAMiO,GAAG,EAAE0N,CAAC,CAAC,IAAA,IAAA,CAAA;EACjB,GAAA;IAAC3Q,cAAA,CAAAyQ,MAAA,EAAAC,kBAAA,CAAA,CAAA;EAAA,EAAA,OAAAD,MAAA,CAAA;EAAA,CAAA,CAVuBjB,iBAAiB,CAAA;;ACxsBrBiB,UAAM,CAACjV;;;;;;;;;;;;;ICC/B,IAAIuV,CAAC,GAAG,IAAI,CAAA;EACZ,EAAA,IAAI/F,CAAC,GAAG+F,CAAC,GAAG,EAAE,CAAA;EACd,EAAA,IAAIC,CAAC,GAAGhG,CAAC,GAAG,EAAE,CAAA;EACd,EAAA,IAAIiG,CAAC,GAAGD,CAAC,GAAG,EAAE,CAAA;EACd,EAAA,IAAIE,CAAC,GAAGD,CAAC,GAAG,CAAC,CAAA;EACb,EAAA,IAAIE,CAAC,GAAGF,CAAC,GAAG,MAAM,CAAA;;EAElB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAG,EAAAA,EAAc,GAAG,SAAAA,EAAAA,CAASC,GAAG,EAAElC,OAAO,EAAE;EACtCA,IAAAA,OAAO,GAAGA,OAAO,IAAI,EAAE,CAAA;EACvB,IAAA,IAAIza,IAAI,GAAA8X,OAAA,CAAU6E,GAAG,CAAA,CAAA;MACrB,IAAI3c,IAAI,KAAK,QAAQ,IAAI2c,GAAG,CAACpa,MAAM,GAAG,CAAC,EAAE;QACvC,OAAO2T,KAAK,CAACyG,GAAG,CAAC,CAAA;OAClB,MAAM,IAAI3c,IAAI,KAAK,QAAQ,IAAI4c,QAAQ,CAACD,GAAG,CAAC,EAAE;QAC7C,OAAOlC,OAAO,CAAK,MAAA,CAAA,GAAGoC,OAAO,CAACF,GAAG,CAAC,GAAGG,QAAQ,CAACH,GAAG,CAAC,CAAA;EACnD,KAAA;MACD,MAAM,IAAInR,KAAK,CACb,uDAAuD,GACrDkO,IAAI,CAACqD,SAAS,CAACJ,GAAG,CACxB,CAAG,CAAA;KACF,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;;IAEA,SAASzG,KAAKA,CAAC/L,GAAG,EAAE;EAClBA,IAAAA,GAAG,GAAGrG,MAAM,CAACqG,GAAG,CAAC,CAAA;EACjB,IAAA,IAAIA,GAAG,CAAC5H,MAAM,GAAG,GAAG,EAAE;EACpB,MAAA,OAAA;EACD,KAAA;EACD,IAAA,IAAIya,KAAK,GAAG,kIAAkI,CAACzG,IAAI,CACjJpM,GACJ,CAAG,CAAA;MACD,IAAI,CAAC6S,KAAK,EAAE;EACV,MAAA,OAAA;EACD,KAAA;MACD,IAAItW,CAAC,GAAGuW,UAAU,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;EAC5B,IAAA,IAAIhd,IAAI,GAAG,CAACgd,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,EAAEjK,WAAW,EAAE,CAAA;EAC3C,IAAA,QAAQ/S,IAAI;EACV,MAAA,KAAK,OAAO,CAAA;EACZ,MAAA,KAAK,MAAM,CAAA;EACX,MAAA,KAAK,KAAK,CAAA;EACV,MAAA,KAAK,IAAI,CAAA;EACT,MAAA,KAAK,GAAG;UACN,OAAO0G,CAAC,GAAG+V,CAAC,CAAA;EACd,MAAA,KAAK,OAAO,CAAA;EACZ,MAAA,KAAK,MAAM,CAAA;EACX,MAAA,KAAK,GAAG;UACN,OAAO/V,CAAC,GAAG8V,CAAC,CAAA;EACd,MAAA,KAAK,MAAM,CAAA;EACX,MAAA,KAAK,KAAK,CAAA;EACV,MAAA,KAAK,GAAG;UACN,OAAO9V,CAAC,GAAG6V,CAAC,CAAA;EACd,MAAA,KAAK,OAAO,CAAA;EACZ,MAAA,KAAK,MAAM,CAAA;EACX,MAAA,KAAK,KAAK,CAAA;EACV,MAAA,KAAK,IAAI,CAAA;EACT,MAAA,KAAK,GAAG;UACN,OAAO7V,CAAC,GAAG4V,CAAC,CAAA;EACd,MAAA,KAAK,SAAS,CAAA;EACd,MAAA,KAAK,QAAQ,CAAA;EACb,MAAA,KAAK,MAAM,CAAA;EACX,MAAA,KAAK,KAAK,CAAA;EACV,MAAA,KAAK,GAAG;UACN,OAAO5V,CAAC,GAAG4P,CAAC,CAAA;EACd,MAAA,KAAK,SAAS,CAAA;EACd,MAAA,KAAK,QAAQ,CAAA;EACb,MAAA,KAAK,MAAM,CAAA;EACX,MAAA,KAAK,KAAK,CAAA;EACV,MAAA,KAAK,GAAG;UACN,OAAO5P,CAAC,GAAG2V,CAAC,CAAA;EACd,MAAA,KAAK,cAAc,CAAA;EACnB,MAAA,KAAK,aAAa,CAAA;EAClB,MAAA,KAAK,OAAO,CAAA;EACZ,MAAA,KAAK,MAAM,CAAA;EACX,MAAA,KAAK,IAAI;EACP,QAAA,OAAO3V,CAAC,CAAA;EACV,MAAA;EACE,QAAA,OAAOwG,SAAS,CAAA;EACnB,KAAA;EACH,GAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;IAEA,SAAS4P,QAAQA,CAACJ,EAAE,EAAE;EACpB,IAAA,IAAIQ,KAAK,GAAGtW,IAAI,CAACuW,GAAG,CAACT,EAAE,CAAC,CAAA;MACxB,IAAIQ,KAAK,IAAIX,CAAC,EAAE;QACd,OAAO3V,IAAI,CAACwW,KAAK,CAACV,EAAE,GAAGH,CAAC,CAAC,GAAG,GAAG,CAAA;EAChC,KAAA;MACD,IAAIW,KAAK,IAAIZ,CAAC,EAAE;QACd,OAAO1V,IAAI,CAACwW,KAAK,CAACV,EAAE,GAAGJ,CAAC,CAAC,GAAG,GAAG,CAAA;EAChC,KAAA;MACD,IAAIY,KAAK,IAAI5G,CAAC,EAAE;QACd,OAAO1P,IAAI,CAACwW,KAAK,CAACV,EAAE,GAAGpG,CAAC,CAAC,GAAG,GAAG,CAAA;EAChC,KAAA;MACD,IAAI4G,KAAK,IAAIb,CAAC,EAAE;QACd,OAAOzV,IAAI,CAACwW,KAAK,CAACV,EAAE,GAAGL,CAAC,CAAC,GAAG,GAAG,CAAA;EAChC,KAAA;MACD,OAAOK,EAAE,GAAG,IAAI,CAAA;EAClB,GAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;IAEA,SAASG,OAAOA,CAACH,EAAE,EAAE;EACnB,IAAA,IAAIQ,KAAK,GAAGtW,IAAI,CAACuW,GAAG,CAACT,EAAE,CAAC,CAAA;MACxB,IAAIQ,KAAK,IAAIX,CAAC,EAAE;QACd,OAAOc,MAAM,CAACX,EAAE,EAAEQ,KAAK,EAAEX,CAAC,EAAE,KAAK,CAAC,CAAA;EACnC,KAAA;MACD,IAAIW,KAAK,IAAIZ,CAAC,EAAE;QACd,OAAOe,MAAM,CAACX,EAAE,EAAEQ,KAAK,EAAEZ,CAAC,EAAE,MAAM,CAAC,CAAA;EACpC,KAAA;MACD,IAAIY,KAAK,IAAI5G,CAAC,EAAE;QACd,OAAO+G,MAAM,CAACX,EAAE,EAAEQ,KAAK,EAAE5G,CAAC,EAAE,QAAQ,CAAC,CAAA;EACtC,KAAA;MACD,IAAI4G,KAAK,IAAIb,CAAC,EAAE;QACd,OAAOgB,MAAM,CAACX,EAAE,EAAEQ,KAAK,EAAEb,CAAC,EAAE,QAAQ,CAAC,CAAA;EACtC,KAAA;MACD,OAAOK,EAAE,GAAG,KAAK,CAAA;EACnB,GAAA;;EAEA;EACA;EACA;;IAEA,SAASW,MAAMA,CAACX,EAAE,EAAEQ,KAAK,EAAExW,CAAC,EAAEiO,IAAI,EAAE;EAClC,IAAA,IAAI2I,QAAQ,GAAGJ,KAAK,IAAIxW,CAAC,GAAG,GAAG,CAAA;EAC/B,IAAA,OAAOE,IAAI,CAACwW,KAAK,CAACV,EAAE,GAAGhW,CAAC,CAAC,GAAG,GAAG,GAAGiO,IAAI,IAAI2I,QAAQ,GAAG,GAAG,GAAG,EAAE,CAAC,CAAA;EAChE,GAAA;;;;EChKA;EACA;EACA;EACA;;EAEA,SAASC,KAAKA,CAACC,GAAG,EAAE;IACnBC,WAAW,CAACC,KAAK,GAAGD,WAAW,CAAA;IAC/BA,WAAW,CAAA,SAAA,CAAQ,GAAGA,WAAW,CAAA;IACjCA,WAAW,CAACE,MAAM,GAAGA,MAAM,CAAA;IAC3BF,WAAW,CAACG,OAAO,GAAGA,OAAO,CAAA;IAC7BH,WAAW,CAACI,MAAM,GAAGA,MAAM,CAAA;IAC3BJ,WAAW,CAACK,OAAO,GAAGA,OAAO,CAAA;EAC7BL,EAAAA,WAAW,CAACM,QAAQ,GAAGC,WAAa,CAAA;IACpCP,WAAW,CAACQ,OAAO,GAAGA,OAAO,CAAA;IAE7Bxe,MAAM,CAACG,IAAI,CAAC4d,GAAG,CAAC,CAAC3d,OAAO,CAAC,UAAAC,GAAG,EAAI;EAC/B2d,IAAAA,WAAW,CAAC3d,GAAG,CAAC,GAAG0d,GAAG,CAAC1d,GAAG,CAAC,CAAA;EAC7B,GAAE,CAAC,CAAA;;EAEH;EACA;EACA;;IAEC2d,WAAW,CAAC1G,KAAK,GAAG,EAAE,CAAA;IACtB0G,WAAW,CAACS,KAAK,GAAG,EAAE,CAAA;;EAEvB;EACA;EACA;EACA;EACA;EACCT,EAAAA,WAAW,CAACU,UAAU,GAAG,EAAE,CAAA;;EAE5B;EACA;EACA;EACA;EACA;EACA;IACC,SAASC,WAAWA,CAACC,SAAS,EAAE;MAC/B,IAAIC,IAAI,GAAG,CAAC,CAAA;EAEZ,IAAA,KAAK,IAAIhc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+b,SAAS,CAAC9b,MAAM,EAAED,CAAC,EAAE,EAAE;EAC1Cgc,MAAAA,IAAI,GAAI,CAACA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAID,SAAS,CAAC7b,UAAU,CAACF,CAAC,CAAC,CAAA;QACrDgc,IAAI,IAAI,CAAC,CAAC;EACV,KAAA;EAED,IAAA,OAAOb,WAAW,CAACc,MAAM,CAAC3X,IAAI,CAACuW,GAAG,CAACmB,IAAI,CAAC,GAAGb,WAAW,CAACc,MAAM,CAAChc,MAAM,CAAC,CAAA;EACrE,GAAA;IACDkb,WAAW,CAACW,WAAW,GAAGA,WAAW,CAAA;;EAEtC;EACA;EACA;EACA;EACA;EACA;EACA;IACC,SAASX,WAAWA,CAACY,SAAS,EAAE;EAC/B,IAAA,IAAIG,QAAQ,CAAA;MACZ,IAAIC,cAAc,GAAG,IAAI,CAAA;EACzB,IAAA,IAAIC,eAAe,CAAA;EACnB,IAAA,IAAIC,YAAY,CAAA;MAEhB,SAASjB,KAAKA,GAAU;EAAA,MAAA,KAAA,IAAAzU,IAAA,GAAAxB,SAAA,CAAAlF,MAAA,EAAN0F,IAAI,GAAA9D,IAAAA,KAAA,CAAA8E,IAAA,GAAAE,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA,EAAA,EAAA;EAAJlB,QAAAA,IAAI,CAAAkB,IAAA,CAAA1B,GAAAA,SAAA,CAAA0B,IAAA,CAAA,CAAA;EAAA,OAAA;EACxB;EACG,MAAA,IAAI,CAACuU,KAAK,CAACI,OAAO,EAAE;EACnB,QAAA,OAAA;EACA,OAAA;QAED,IAAMnV,IAAI,GAAG+U,KAAK,CAAA;;EAErB;QACG,IAAMkB,IAAI,GAAGjR,MAAM,CAAC,IAAIpD,IAAI,EAAE,CAAC,CAAA;EAC/B,MAAA,IAAMmS,EAAE,GAAGkC,IAAI,IAAIJ,QAAQ,IAAII,IAAI,CAAC,CAAA;QACpCjW,IAAI,CAACkW,IAAI,GAAGnC,EAAE,CAAA;QACd/T,IAAI,CAACmW,IAAI,GAAGN,QAAQ,CAAA;QACpB7V,IAAI,CAACiW,IAAI,GAAGA,IAAI,CAAA;EAChBJ,MAAAA,QAAQ,GAAGI,IAAI,CAAA;EAEf3W,MAAAA,IAAI,CAAC,CAAC,CAAC,GAAGwV,WAAW,CAACE,MAAM,CAAC1V,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;EAErC,MAAA,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;EACpC;EACIA,QAAAA,IAAI,CAAC8W,OAAO,CAAC,IAAI,CAAC,CAAA;EAClB,OAAA;;EAEJ;QACG,IAAIC,KAAK,GAAG,CAAC,CAAA;EACb/W,MAAAA,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,CAACoO,OAAO,CAAC,eAAe,EAAE,UAAC2G,KAAK,EAAEiC,MAAM,EAAK;EACjE;UACI,IAAIjC,KAAK,KAAK,IAAI,EAAE;EACnB,UAAA,OAAO,GAAG,CAAA;EACV,SAAA;EACDgC,QAAAA,KAAK,EAAE,CAAA;EACP,QAAA,IAAME,SAAS,GAAGzB,WAAW,CAACU,UAAU,CAACc,MAAM,CAAC,CAAA;EAChD,QAAA,IAAI,OAAOC,SAAS,KAAK,UAAU,EAAE;EACpC,UAAA,IAAMvC,GAAG,GAAG1U,IAAI,CAAC+W,KAAK,CAAC,CAAA;YACvBhC,KAAK,GAAGkC,SAAS,CAAC5e,IAAI,CAACqI,IAAI,EAAEgU,GAAG,CAAC,CAAA;;EAEtC;EACK1U,UAAAA,IAAI,CAACF,MAAM,CAACiX,KAAK,EAAE,CAAC,CAAC,CAAA;EACrBA,UAAAA,KAAK,EAAE,CAAA;EACP,SAAA;EACD,QAAA,OAAOhC,KAAK,CAAA;EAChB,OAAI,CAAC,CAAA;;EAEL;QACGS,WAAW,CAAC0B,UAAU,CAAC7e,IAAI,CAACqI,IAAI,EAAEV,IAAI,CAAC,CAAA;QAEvC,IAAMmX,KAAK,GAAGzW,IAAI,CAAC0W,GAAG,IAAI5B,WAAW,CAAC4B,GAAG,CAAA;EACzCD,MAAAA,KAAK,CAAC5X,KAAK,CAACmB,IAAI,EAAEV,IAAI,CAAC,CAAA;EACvB,KAAA;MAEDyV,KAAK,CAACW,SAAS,GAAGA,SAAS,CAAA;EAC3BX,IAAAA,KAAK,CAAC4B,SAAS,GAAG7B,WAAW,CAAC6B,SAAS,EAAE,CAAA;MACzC5B,KAAK,CAAC6B,KAAK,GAAG9B,WAAW,CAACW,WAAW,CAACC,SAAS,CAAC,CAAA;MAChDX,KAAK,CAAC8B,MAAM,GAAGA,MAAM,CAAA;EACrB9B,IAAAA,KAAK,CAACO,OAAO,GAAGR,WAAW,CAACQ,OAAO,CAAC;;EAEpCxe,IAAAA,MAAM,CAACggB,cAAc,CAAC/B,KAAK,EAAE,SAAS,EAAE;EACvCgC,MAAAA,UAAU,EAAE,IAAI;EAChBC,MAAAA,YAAY,EAAE,KAAK;QACnB9Q,GAAG,EAAE,SAAAA,GAAAA,GAAM;UACV,IAAI4P,cAAc,KAAK,IAAI,EAAE;EAC5B,UAAA,OAAOA,cAAc,CAAA;EACrB,SAAA;EACD,QAAA,IAAIC,eAAe,KAAKjB,WAAW,CAACmC,UAAU,EAAE;YAC/ClB,eAAe,GAAGjB,WAAW,CAACmC,UAAU,CAAA;EACxCjB,UAAAA,YAAY,GAAGlB,WAAW,CAACK,OAAO,CAACO,SAAS,CAAC,CAAA;EAC7C,SAAA;EAED,QAAA,OAAOM,YAAY,CAAA;SACnB;EACDkB,MAAAA,GAAG,EAAE,SAAAA,GAAAC,CAAAA,CAAC,EAAI;EACTrB,QAAAA,cAAc,GAAGqB,CAAC,CAAA;EAClB,OAAA;EACJ,KAAG,CAAC,CAAA;;EAEJ;EACE,IAAA,IAAI,OAAOrC,WAAW,CAACsC,IAAI,KAAK,UAAU,EAAE;EAC3CtC,MAAAA,WAAW,CAACsC,IAAI,CAACrC,KAAK,CAAC,CAAA;EACvB,KAAA;EAED,IAAA,OAAOA,KAAK,CAAA;EACZ,GAAA;EAED,EAAA,SAAS8B,MAAMA,CAACnB,SAAS,EAAE2B,SAAS,EAAE;EACrC,IAAA,IAAMC,QAAQ,GAAGxC,WAAW,CAAC,IAAI,CAACY,SAAS,IAAI,OAAO2B,SAAS,KAAK,WAAW,GAAG,GAAG,GAAGA,SAAS,CAAC,GAAG3B,SAAS,CAAC,CAAA;EAC/G4B,IAAAA,QAAQ,CAACZ,GAAG,GAAG,IAAI,CAACA,GAAG,CAAA;EACvB,IAAA,OAAOY,QAAQ,CAAA;EACf,GAAA;;EAEF;EACA;EACA;EACA;EACA;EACA;EACA;IACC,SAASpC,MAAMA,CAAC+B,UAAU,EAAE;EAC3BnC,IAAAA,WAAW,CAACyC,IAAI,CAACN,UAAU,CAAC,CAAA;MAC5BnC,WAAW,CAACmC,UAAU,GAAGA,UAAU,CAAA;MAEnCnC,WAAW,CAAC1G,KAAK,GAAG,EAAE,CAAA;MACtB0G,WAAW,CAACS,KAAK,GAAG,EAAE,CAAA;EAEtB,IAAA,IAAI5b,CAAC,CAAA;EACL,IAAA,IAAMhB,KAAK,GAAG,CAAC,OAAOse,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAG,EAAE,EAAEte,KAAK,CAAC,QAAQ,CAAC,CAAA;EAChF,IAAA,IAAMsB,GAAG,GAAGtB,KAAK,CAACiB,MAAM,CAAA;MAExB,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,GAAG,EAAEN,CAAC,EAAE,EAAE;EACzB,MAAA,IAAI,CAAChB,KAAK,CAACgB,CAAC,CAAC,EAAE;EAClB;EACI,QAAA,SAAA;EACA,OAAA;QAEDsd,UAAU,GAAGte,KAAK,CAACgB,CAAC,CAAC,CAAC+T,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;EAE3C,MAAA,IAAIuJ,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;EAC1BnC,QAAAA,WAAW,CAACS,KAAK,CAACzZ,IAAI,CAAC,IAAI0b,MAAM,CAAC,GAAG,GAAGP,UAAU,CAAC3Z,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAA;EACvE,OAAI,MAAM;EACNwX,QAAAA,WAAW,CAAC1G,KAAK,CAACtS,IAAI,CAAC,IAAI0b,MAAM,CAAC,GAAG,GAAGP,UAAU,GAAG,GAAG,CAAC,CAAC,CAAA;EAC1D,OAAA;EACD,KAAA;EACD,GAAA;;EAEF;EACA;EACA;EACA;EACA;EACA;IACC,SAAShC,OAAOA,GAAG;EAClB,IAAA,IAAMgC,UAAU,GAAG,EAAAjN,CAAAA,MAAA,CAAAyN,kBAAA,CACf3C,WAAW,CAAC1G,KAAK,CAACmF,GAAG,CAACmE,WAAW,CAAC,CAAAD,EAAAA,kBAAA,CAClC3C,WAAW,CAACS,KAAK,CAAChC,GAAG,CAACmE,WAAW,CAAC,CAACnE,GAAG,CAAC,UAAAmC,SAAS,EAAA;QAAA,OAAI,GAAG,GAAGA,SAAS,CAAA;EAAA,KAAA,CAAC,CACtEha,CAAAA,CAAAA,IAAI,CAAC,GAAG,CAAC,CAAA;EACXoZ,IAAAA,WAAW,CAACI,MAAM,CAAC,EAAE,CAAC,CAAA;EACtB,IAAA,OAAO+B,UAAU,CAAA;EACjB,GAAA;;EAEF;EACA;EACA;EACA;EACA;EACA;EACA;IACC,SAAS9B,OAAOA,CAACnJ,IAAI,EAAE;MACtB,IAAIA,IAAI,CAACA,IAAI,CAACpS,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;EAClC,MAAA,OAAO,IAAI,CAAA;EACX,KAAA;EAED,IAAA,IAAID,CAAC,CAAA;EACL,IAAA,IAAIM,GAAG,CAAA;EAEP,IAAA,KAAKN,CAAC,GAAG,CAAC,EAAEM,GAAG,GAAG6a,WAAW,CAACS,KAAK,CAAC3b,MAAM,EAAED,CAAC,GAAGM,GAAG,EAAEN,CAAC,EAAE,EAAE;QACzD,IAAImb,WAAW,CAACS,KAAK,CAAC5b,CAAC,CAAC,CAACge,IAAI,CAAC3L,IAAI,CAAC,EAAE;EACpC,QAAA,OAAO,KAAK,CAAA;EACZ,OAAA;EACD,KAAA;EAED,IAAA,KAAKrS,CAAC,GAAG,CAAC,EAAEM,GAAG,GAAG6a,WAAW,CAAC1G,KAAK,CAACxU,MAAM,EAAED,CAAC,GAAGM,GAAG,EAAEN,CAAC,EAAE,EAAE;QACzD,IAAImb,WAAW,CAAC1G,KAAK,CAACzU,CAAC,CAAC,CAACge,IAAI,CAAC3L,IAAI,CAAC,EAAE;EACpC,QAAA,OAAO,IAAI,CAAA;EACX,OAAA;EACD,KAAA;EAED,IAAA,OAAO,KAAK,CAAA;EACZ,GAAA;;EAEF;EACA;EACA;EACA;EACA;EACA;EACA;IACC,SAAS0L,WAAWA,CAACE,MAAM,EAAE;MAC5B,OAAOA,MAAM,CAAClgB,QAAQ,EAAE,CACtBqD,SAAS,CAAC,CAAC,EAAE6c,MAAM,CAAClgB,QAAQ,EAAE,CAACkC,MAAM,GAAG,CAAC,CAAC,CAC1C8T,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAA;EACzB,GAAA;;EAEF;EACA;EACA;EACA;EACA;EACA;EACA;IACC,SAASsH,MAAMA,CAAChB,GAAG,EAAE;MACpB,IAAIA,GAAG,YAAYnR,KAAK,EAAE;EACzB,MAAA,OAAOmR,GAAG,CAAC6D,KAAK,IAAI7D,GAAG,CAAC8D,OAAO,CAAA;EAC/B,KAAA;EACD,IAAA,OAAO9D,GAAG,CAAA;EACV,GAAA;;EAEF;EACA;EACA;EACA;IACC,SAASsB,OAAOA,GAAG;EAClByC,IAAAA,OAAO,CAACC,IAAI,CAAC,uIAAuI,CAAC,CAAA;EACrJ,GAAA;IAEDlD,WAAW,CAACI,MAAM,CAACJ,WAAW,CAACmD,IAAI,EAAE,CAAC,CAAA;EAEtC,EAAA,OAAOnD,WAAW,CAAA;EACnB,CAAA;EAEA,IAAAoD,MAAc,GAAGtD,KAAK;;;;;EC/QtB;EACA;EACA;;IAEAuD,OAAA,CAAA3B,UAAA,GAAqBA,UAAU,CAAA;IAC/B2B,OAAA,CAAAZ,IAAA,GAAeA,IAAI,CAAA;IACnBY,OAAA,CAAAF,IAAA,GAAeA,IAAI,CAAA;IACnBE,OAAA,CAAAxB,SAAA,GAAoBA,SAAS,CAAA;EAC7BwB,EAAAA,OAAkB,CAAAC,OAAA,GAAAC,YAAY,EAAE,CAAA;IAChCF,OAAA,CAAA7C,OAAA,GAAmB,YAAM;MACxB,IAAIgD,MAAM,GAAG,KAAK,CAAA;EAElB,IAAA,OAAO,YAAM;QACZ,IAAI,CAACA,MAAM,EAAE;EACZA,QAAAA,MAAM,GAAG,IAAI,CAAA;EACbP,QAAAA,OAAO,CAACC,IAAI,CAAC,uIAAuI,CAAC,CAAA;EACrJ,OAAA;OACD,CAAA;EACF,GAAC,EAAG,CAAA;;EAEJ;EACA;EACA;;EAEAG,EAAAA,OAAiB,CAAAvC,MAAA,GAAA,CAChB,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACT,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;IACA,SAASe,SAASA,GAAG;EACrB;EACA;EACA;MACC,IAAI,OAAO1W,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACsY,OAAO,KAAKtY,MAAM,CAACsY,OAAO,CAAClhB,IAAI,KAAK,UAAU,IAAI4I,MAAM,CAACsY,OAAO,CAACC,MAAM,CAAC,EAAE;EACrH,MAAA,OAAO,IAAI,CAAA;EACX,KAAA;;EAEF;MACC,IAAI,OAAOtO,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACuO,SAAS,IAAIvO,SAAS,CAACuO,SAAS,CAACrO,WAAW,EAAE,CAACiK,KAAK,CAAC,uBAAuB,CAAC,EAAE;EAChI,MAAA,OAAO,KAAK,CAAA;EACZ,KAAA;;EAEF;EACA;MACC,OAAQ,OAAOxL,QAAQ,KAAK,WAAW,IAAIA,QAAQ,CAAC6P,eAAe,IAAI7P,QAAQ,CAAC6P,eAAe,CAACC,KAAK,IAAI9P,QAAQ,CAAC6P,eAAe,CAACC,KAAK,CAACC,gBAAgB;EACzJ;MACG,OAAO3Y,MAAM,KAAK,WAAW,IAAIA,MAAM,CAAC8X,OAAO,KAAK9X,MAAM,CAAC8X,OAAO,CAACc,OAAO,IAAK5Y,MAAM,CAAC8X,OAAO,CAACe,SAAS,IAAI7Y,MAAM,CAAC8X,OAAO,CAACgB,KAAM,CAAE;EACrI;EACA;EACG,IAAA,OAAO7O,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACuO,SAAS,IAAIvO,SAAS,CAACuO,SAAS,CAACrO,WAAW,EAAE,CAACiK,KAAK,CAAC,gBAAgB,CAAC,IAAI2E,QAAQ,CAACxB,MAAM,CAAClJ,EAAE,EAAE,EAAE,CAAC,IAAI,EAAG;EACzJ;EACG,IAAA,OAAOpE,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACuO,SAAS,IAAIvO,SAAS,CAACuO,SAAS,CAACrO,WAAW,EAAE,CAACiK,KAAK,CAAC,oBAAoB,CAAE,CAAA;EAC5H,GAAA;;EAEA;EACA;EACA;EACA;EACA;;IAEA,SAASmC,UAAUA,CAAClX,IAAI,EAAE;MACzBA,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAACqX,SAAS,GAAG,IAAI,GAAG,EAAE,IACpC,IAAI,CAACjB,SAAS,IACb,IAAI,CAACiB,SAAS,GAAG,KAAK,GAAG,GAAG,CAAC,GAC9BrX,IAAI,CAAC,CAAC,CAAC,IACN,IAAI,CAACqX,SAAS,GAAG,KAAK,GAAG,GAAG,CAAC,GAC9B,GAAG,GAAGsC,MAAM,CAACd,OAAO,CAAC/C,QAAQ,CAAC,IAAI,CAACc,IAAI,CAAC,CAAA;EAEzC,IAAA,IAAI,CAAC,IAAI,CAACS,SAAS,EAAE;EACpB,MAAA,OAAA;EACA,KAAA;EAED,IAAA,IAAMlV,CAAC,GAAG,SAAS,GAAG,IAAI,CAACmV,KAAK,CAAA;MAChCtX,IAAI,CAACF,MAAM,CAAC,CAAC,EAAE,CAAC,EAAEqC,CAAC,EAAE,gBAAgB,CAAC,CAAA;;EAEvC;EACA;EACA;MACC,IAAI4U,KAAK,GAAG,CAAC,CAAA;MACb,IAAI6C,KAAK,GAAG,CAAC,CAAA;MACb5Z,IAAI,CAAC,CAAC,CAAC,CAACoO,OAAO,CAAC,aAAa,EAAE,UAAA2G,KAAK,EAAI;QACvC,IAAIA,KAAK,KAAK,IAAI,EAAE;EACnB,QAAA,OAAA;EACA,OAAA;EACDgC,MAAAA,KAAK,EAAE,CAAA;QACP,IAAIhC,KAAK,KAAK,IAAI,EAAE;EACtB;EACA;EACG6E,QAAAA,KAAK,GAAG7C,KAAK,CAAA;EACb,OAAA;EACH,KAAE,CAAC,CAAA;MAEF/W,IAAI,CAACF,MAAM,CAAC8Z,KAAK,EAAE,CAAC,EAAEzX,CAAC,CAAC,CAAA;EACzB,GAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA0W,EAAAA,OAAc,CAAAzB,GAAA,GAAAqB,OAAO,CAAChD,KAAK,IAAIgD,OAAO,CAACrB,GAAG,IAAK,YAAM,EAAG,CAAA;;EAExD;EACA;EACA;EACA;EACA;EACA;IACA,SAASa,IAAIA,CAACN,UAAU,EAAE;MACzB,IAAI;EACH,MAAA,IAAIA,UAAU,EAAE;UACfkB,OAAO,CAACC,OAAO,CAACe,OAAO,CAAC,OAAO,EAAElC,UAAU,CAAC,CAAA;EAC/C,OAAG,MAAM;EACNkB,QAAAA,OAAO,CAACC,OAAO,CAACgB,UAAU,CAAC,OAAO,CAAC,CAAA;EACnC,OAAA;OACD,CAAC,OAAOvG,KAAK,EAAE;EACjB;EACA;EAAA,KAAA;EAEA,GAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;IACA,SAASoF,IAAIA,GAAG;EACf,IAAA,IAAIoB,CAAC,CAAA;MACL,IAAI;QACHA,CAAC,GAAGlB,OAAO,CAACC,OAAO,CAACkB,OAAO,CAAC,OAAO,CAAC,CAAA;OACpC,CAAC,OAAOzG,KAAK,EAAE;EACjB;EACA;EAAA,KAAA;;EAGA;MACC,IAAI,CAACwG,CAAC,IAAI,OAAOd,OAAO,KAAK,WAAW,IAAI,KAAK,IAAIA,OAAO,EAAE;EAC7Dc,MAAAA,CAAC,GAAGd,OAAO,CAAC1D,GAAG,CAAC0E,KAAK,CAAA;EACrB,KAAA;EAED,IAAA,OAAOF,CAAC,CAAA;EACT,GAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;IAEA,SAAShB,YAAYA,GAAG;MACvB,IAAI;EACL;EACA;EACE,MAAA,OAAOmB,YAAY,CAAA;OACnB,CAAC,OAAO3G,KAAK,EAAE;EACjB;EACA;EAAA,KAAA;EAEA,GAAA;EAEAoG,EAAAA,MAAA,CAAAd,OAAA,GAAiB9C,MAAmB,CAAC8C,OAAO,CAAC,CAAA;EAE7C,EAAA,IAAO3C,UAAU,GAAIyD,MAAM,CAACd,OAAO,CAA5B3C,UAAU,CAAA;;EAEjB;EACA;EACA;;EAEAA,EAAAA,UAAU,CAACnY,CAAC,GAAG,UAAU8Z,CAAC,EAAE;MAC3B,IAAI;EACH,MAAA,OAAOpG,IAAI,CAACqD,SAAS,CAAC+C,CAAC,CAAC,CAAA;OACxB,CAAC,OAAOtE,KAAK,EAAE;EACf,MAAA,OAAO,8BAA8B,GAAGA,KAAK,CAACiF,OAAO,CAAA;EACrD,KAAA;KACD,CAAA;;;;;EC1QD,IAAM/C,OAAK,GAAG0E,WAAW,CAAC,sBAAsB,CAAC,CAAC;EAClD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASC,GAAGA,CAAC9T,GAAG,EAAkB;EAAA,EAAA,IAAhBlB,IAAI,GAAA5F,SAAA,CAAAlF,MAAA,GAAA,CAAA,IAAAkF,SAAA,CAAA,CAAA,CAAA,KAAAyF,SAAA,GAAAzF,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;IAAA,IAAE6a,GAAG,GAAA7a,SAAA,CAAAlF,MAAA,GAAAkF,CAAAA,GAAAA,SAAA,MAAAyF,SAAA,CAAA;IACnC,IAAIxM,GAAG,GAAG6N,GAAG,CAAA;EACb;IACA+T,GAAG,GAAGA,GAAG,IAAK,OAAOjT,QAAQ,KAAK,WAAW,IAAIA,QAAS,CAAA;EAC1D,EAAA,IAAI,IAAI,IAAId,GAAG,EACXA,GAAG,GAAG+T,GAAG,CAACxb,QAAQ,GAAG,IAAI,GAAGwb,GAAG,CAAC7L,IAAI,CAAA;EACxC;EACA,EAAA,IAAI,OAAOlI,GAAG,KAAK,QAAQ,EAAE;MACzB,IAAI,GAAG,KAAKA,GAAG,CAAC/K,MAAM,CAAC,CAAC,CAAC,EAAE;QACvB,IAAI,GAAG,KAAK+K,GAAG,CAAC/K,MAAM,CAAC,CAAC,CAAC,EAAE;EACvB+K,QAAAA,GAAG,GAAG+T,GAAG,CAACxb,QAAQ,GAAGyH,GAAG,CAAA;EAC5B,OAAC,MACI;EACDA,QAAAA,GAAG,GAAG+T,GAAG,CAAC7L,IAAI,GAAGlI,GAAG,CAAA;EACxB,OAAA;EACJ,KAAA;EACA,IAAA,IAAI,CAAC,qBAAqB,CAAC+R,IAAI,CAAC/R,GAAG,CAAC,EAAE;EAClCmP,MAAAA,OAAK,CAAC,sBAAsB,EAAEnP,GAAG,CAAC,CAAA;EAClC,MAAA,IAAI,WAAW,KAAK,OAAO+T,GAAG,EAAE;EAC5B/T,QAAAA,GAAG,GAAG+T,GAAG,CAACxb,QAAQ,GAAG,IAAI,GAAGyH,GAAG,CAAA;EACnC,OAAC,MACI;UACDA,GAAG,GAAG,UAAU,GAAGA,GAAG,CAAA;EAC1B,OAAA;EACJ,KAAA;EACA;EACAmP,IAAAA,OAAK,CAAC,UAAU,EAAEnP,GAAG,CAAC,CAAA;EACtB7N,IAAAA,GAAG,GAAGwV,KAAK,CAAC3H,GAAG,CAAC,CAAA;EACpB,GAAA;EACA;EACA,EAAA,IAAI,CAAC7N,GAAG,CAAC+M,IAAI,EAAE;MACX,IAAI,aAAa,CAAC6S,IAAI,CAAC5f,GAAG,CAACoG,QAAQ,CAAC,EAAE;QAClCpG,GAAG,CAAC+M,IAAI,GAAG,IAAI,CAAA;OAClB,MACI,IAAI,cAAc,CAAC6S,IAAI,CAAC5f,GAAG,CAACoG,QAAQ,CAAC,EAAE;QACxCpG,GAAG,CAAC+M,IAAI,GAAG,KAAK,CAAA;EACpB,KAAA;EACJ,GAAA;EACA/M,EAAAA,GAAG,CAAC2M,IAAI,GAAG3M,GAAG,CAAC2M,IAAI,IAAI,GAAG,CAAA;EAC1B,EAAA,IAAMkV,IAAI,GAAG7hB,GAAG,CAAC+V,IAAI,CAACjJ,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAA;EACzC,EAAA,IAAMiJ,IAAI,GAAG8L,IAAI,GAAG,GAAG,GAAG7hB,GAAG,CAAC+V,IAAI,GAAG,GAAG,GAAG/V,GAAG,CAAC+V,IAAI,CAAA;EACnD;EACA/V,EAAAA,GAAG,CAACyY,EAAE,GAAGzY,GAAG,CAACoG,QAAQ,GAAG,KAAK,GAAG2P,IAAI,GAAG,GAAG,GAAG/V,GAAG,CAAC+M,IAAI,GAAGJ,IAAI,CAAA;EAC5D;EACA3M,EAAAA,GAAG,CAAC8hB,IAAI,GACJ9hB,GAAG,CAACoG,QAAQ,GACR,KAAK,GACL2P,IAAI,IACH6L,GAAG,IAAIA,GAAG,CAAC7U,IAAI,KAAK/M,GAAG,CAAC+M,IAAI,GAAG,EAAE,GAAG,GAAG,GAAG/M,GAAG,CAAC+M,IAAI,CAAC,CAAA;EAC5D,EAAA,OAAO/M,GAAG,CAAA;EACd;;EC9DA,IAAMH,qBAAqB,GAAG,OAAOC,WAAW,KAAK,UAAU,CAAA;EAC/D,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAIC,GAAG,EAAK;EACpB,EAAA,OAAO,OAAOF,WAAW,CAACC,MAAM,KAAK,UAAU,GACzCD,WAAW,CAACC,MAAM,CAACC,GAAG,CAAC,GACvBA,GAAG,CAACC,MAAM,YAAYH,WAAW,CAAA;EAC3C,CAAC,CAAA;EACD,IAAMH,QAAQ,GAAGZ,MAAM,CAACW,SAAS,CAACC,QAAQ,CAAA;EAC1C,IAAMH,cAAc,GAAG,OAAOC,IAAI,KAAK,UAAU,IAC5C,OAAOA,IAAI,KAAK,WAAW,IACxBE,QAAQ,CAACC,IAAI,CAACH,IAAI,CAAC,KAAK,0BAA2B,CAAA;EAC3D,IAAMsiB,cAAc,GAAG,OAAOC,IAAI,KAAK,UAAU,IAC5C,OAAOA,IAAI,KAAK,WAAW,IACxBriB,QAAQ,CAACC,IAAI,CAACoiB,IAAI,CAAC,KAAK,0BAA2B,CAAA;EAC3D;EACA;EACA;EACA;EACA;EACO,SAASnc,QAAQA,CAAC7F,GAAG,EAAE;IAC1B,OAASH,qBAAqB,KAAKG,GAAG,YAAYF,WAAW,IAAIC,MAAM,CAACC,GAAG,CAAC,CAAC,IACxER,cAAc,IAAIQ,GAAG,YAAYP,IAAK,IACtCsiB,cAAc,IAAI/hB,GAAG,YAAYgiB,IAAK,CAAA;EAC/C,CAAA;EACO,SAASC,SAASA,CAACjiB,GAAG,EAAEkiB,MAAM,EAAE;IACnC,IAAI,CAACliB,GAAG,IAAIoX,OAAA,CAAOpX,GAAG,CAAA,KAAK,QAAQ,EAAE;EACjC,IAAA,OAAO,KAAK,CAAA;EAChB,GAAA;EACA,EAAA,IAAIyD,KAAK,CAAC0e,OAAO,CAACniB,GAAG,CAAC,EAAE;EACpB,IAAA,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAE+H,CAAC,GAAG3J,GAAG,CAAC6B,MAAM,EAAED,CAAC,GAAG+H,CAAC,EAAE/H,CAAC,EAAE,EAAE;EACxC,MAAA,IAAIqgB,SAAS,CAACjiB,GAAG,CAAC4B,CAAC,CAAC,CAAC,EAAE;EACnB,QAAA,OAAO,IAAI,CAAA;EACf,OAAA;EACJ,KAAA;EACA,IAAA,OAAO,KAAK,CAAA;EAChB,GAAA;EACA,EAAA,IAAIiE,QAAQ,CAAC7F,GAAG,CAAC,EAAE;EACf,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA,EAAA,IAAIA,GAAG,CAACkiB,MAAM,IACV,OAAOliB,GAAG,CAACkiB,MAAM,KAAK,UAAU,IAChCnb,SAAS,CAAClF,MAAM,KAAK,CAAC,EAAE;MACxB,OAAOogB,SAAS,CAACjiB,GAAG,CAACkiB,MAAM,EAAE,EAAE,IAAI,CAAC,CAAA;EACxC,GAAA;EACA,EAAA,KAAK,IAAM9iB,GAAG,IAAIY,GAAG,EAAE;MACnB,IAAIjB,MAAM,CAACW,SAAS,CAACiJ,cAAc,CAAC/I,IAAI,CAACI,GAAG,EAAEZ,GAAG,CAAC,IAAI6iB,SAAS,CAACjiB,GAAG,CAACZ,GAAG,CAAC,CAAC,EAAE;EACvE,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACJ,GAAA;EACA,EAAA,OAAO,KAAK,CAAA;EAChB;;EChDA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASgjB,iBAAiBA,CAAChhB,MAAM,EAAE;IACtC,IAAMihB,OAAO,GAAG,EAAE,CAAA;EAClB,EAAA,IAAMC,UAAU,GAAGlhB,MAAM,CAAC7B,IAAI,CAAA;IAC9B,IAAMgjB,IAAI,GAAGnhB,MAAM,CAAA;IACnBmhB,IAAI,CAAChjB,IAAI,GAAGijB,kBAAkB,CAACF,UAAU,EAAED,OAAO,CAAC,CAAA;EACnDE,EAAAA,IAAI,CAACE,WAAW,GAAGJ,OAAO,CAACxgB,MAAM,CAAC;IAClC,OAAO;EAAET,IAAAA,MAAM,EAAEmhB,IAAI;EAAEF,IAAAA,OAAO,EAAEA,OAAAA;KAAS,CAAA;EAC7C,CAAA;EACA,SAASG,kBAAkBA,CAACjjB,IAAI,EAAE8iB,OAAO,EAAE;EACvC,EAAA,IAAI,CAAC9iB,IAAI,EACL,OAAOA,IAAI,CAAA;EACf,EAAA,IAAIsG,QAAQ,CAACtG,IAAI,CAAC,EAAE;EAChB,IAAA,IAAMmjB,WAAW,GAAG;EAAEC,MAAAA,YAAY,EAAE,IAAI;QAAEC,GAAG,EAAEP,OAAO,CAACxgB,MAAAA;OAAQ,CAAA;EAC/DwgB,IAAAA,OAAO,CAACte,IAAI,CAACxE,IAAI,CAAC,CAAA;EAClB,IAAA,OAAOmjB,WAAW,CAAA;KACrB,MACI,IAAIjf,KAAK,CAAC0e,OAAO,CAAC5iB,IAAI,CAAC,EAAE;MAC1B,IAAMsjB,OAAO,GAAG,IAAIpf,KAAK,CAAClE,IAAI,CAACsC,MAAM,CAAC,CAAA;EACtC,IAAA,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrC,IAAI,CAACsC,MAAM,EAAED,CAAC,EAAE,EAAE;EAClCihB,MAAAA,OAAO,CAACjhB,CAAC,CAAC,GAAG4gB,kBAAkB,CAACjjB,IAAI,CAACqC,CAAC,CAAC,EAAEygB,OAAO,CAAC,CAAA;EACrD,KAAA;EACA,IAAA,OAAOQ,OAAO,CAAA;EAClB,GAAC,MACI,IAAIzL,OAAA,CAAO7X,IAAI,CAAA,KAAK,QAAQ,IAAI,EAAEA,IAAI,YAAYsK,IAAI,CAAC,EAAE;MAC1D,IAAMgZ,QAAO,GAAG,EAAE,CAAA;EAClB,IAAA,KAAK,IAAMzjB,GAAG,IAAIG,IAAI,EAAE;EACpB,MAAA,IAAIR,MAAM,CAACW,SAAS,CAACiJ,cAAc,CAAC/I,IAAI,CAACL,IAAI,EAAEH,GAAG,CAAC,EAAE;EACjDyjB,QAAAA,QAAO,CAACzjB,GAAG,CAAC,GAAGojB,kBAAkB,CAACjjB,IAAI,CAACH,GAAG,CAAC,EAAEijB,OAAO,CAAC,CAAA;EACzD,OAAA;EACJ,KAAA;EACA,IAAA,OAAOQ,QAAO,CAAA;EAClB,GAAA;EACA,EAAA,OAAOtjB,IAAI,CAAA;EACf,CAAA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASujB,iBAAiBA,CAAC1hB,MAAM,EAAEihB,OAAO,EAAE;IAC/CjhB,MAAM,CAAC7B,IAAI,GAAGwjB,kBAAkB,CAAC3hB,MAAM,CAAC7B,IAAI,EAAE8iB,OAAO,CAAC,CAAA;EACtD,EAAA,OAAOjhB,MAAM,CAACqhB,WAAW,CAAC;EAC1B,EAAA,OAAOrhB,MAAM,CAAA;EACjB,CAAA;EACA,SAAS2hB,kBAAkBA,CAACxjB,IAAI,EAAE8iB,OAAO,EAAE;EACvC,EAAA,IAAI,CAAC9iB,IAAI,EACL,OAAOA,IAAI,CAAA;EACf,EAAA,IAAIA,IAAI,IAAIA,IAAI,CAACojB,YAAY,KAAK,IAAI,EAAE;MACpC,IAAMK,YAAY,GAAG,OAAOzjB,IAAI,CAACqjB,GAAG,KAAK,QAAQ,IAC7CrjB,IAAI,CAACqjB,GAAG,IAAI,CAAC,IACbrjB,IAAI,CAACqjB,GAAG,GAAGP,OAAO,CAACxgB,MAAM,CAAA;EAC7B,IAAA,IAAImhB,YAAY,EAAE;EACd,MAAA,OAAOX,OAAO,CAAC9iB,IAAI,CAACqjB,GAAG,CAAC,CAAC;EAC7B,KAAC,MACI;EACD,MAAA,MAAM,IAAI9X,KAAK,CAAC,qBAAqB,CAAC,CAAA;EAC1C,KAAA;KACH,MACI,IAAIrH,KAAK,CAAC0e,OAAO,CAAC5iB,IAAI,CAAC,EAAE;EAC1B,IAAA,KAAK,IAAIqC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrC,IAAI,CAACsC,MAAM,EAAED,CAAC,EAAE,EAAE;EAClCrC,MAAAA,IAAI,CAACqC,CAAC,CAAC,GAAGmhB,kBAAkB,CAACxjB,IAAI,CAACqC,CAAC,CAAC,EAAEygB,OAAO,CAAC,CAAA;EAClD,KAAA;EACJ,GAAC,MACI,IAAIjL,OAAA,CAAO7X,IAAI,CAAA,KAAK,QAAQ,EAAE;EAC/B,IAAA,KAAK,IAAMH,GAAG,IAAIG,IAAI,EAAE;EACpB,MAAA,IAAIR,MAAM,CAACW,SAAS,CAACiJ,cAAc,CAAC/I,IAAI,CAACL,IAAI,EAAEH,GAAG,CAAC,EAAE;EACjDG,QAAAA,IAAI,CAACH,GAAG,CAAC,GAAG2jB,kBAAkB,CAACxjB,IAAI,CAACH,GAAG,CAAC,EAAEijB,OAAO,CAAC,CAAA;EACtD,OAAA;EACJ,KAAA;EACJ,GAAA;EACA,EAAA,OAAO9iB,IAAI,CAAA;EACf;;EC/EA;EACA;EACA;EACA,IAAM0jB,iBAAe,GAAG,CACpB,SAAS;EAAE;EACX,eAAe;EAAE;EACjB,YAAY;EAAE;EACd,eAAe;EAAE;EACjB,aAAa;EAAE;EACf,gBAAgB;EAAE,CACrB,CAAA;EACD;EACA;EACA;EACA;EACA;EACO,IAAM7c,QAAQ,GAAG,CAAC,CAAA;EAClB,IAAI8c,UAAU,CAAA;EACrB,CAAC,UAAUA,UAAU,EAAE;IACnBA,UAAU,CAACA,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAA;IACjDA,UAAU,CAACA,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,CAAA;IACvDA,UAAU,CAACA,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAA;IAC7CA,UAAU,CAACA,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAA;IACzCA,UAAU,CAACA,UAAU,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe,CAAA;IAC7DA,UAAU,CAACA,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,CAAA;IAC3DA,UAAU,CAACA,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,CAAA;EAC3D,CAAC,EAAEA,UAAU,KAAKA,UAAU,GAAG,EAAE,CAAC,CAAC,CAAA;EACnC;EACA;EACA;EACA,IAAaC,OAAO,gBAAA,YAAA;EAChB;EACJ;EACA;EACA;EACA;IACI,SAAAA,OAAAA,CAAYC,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAAA;EAC5B,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,EAAA,IAAA9X,MAAA,GAAA6X,OAAA,CAAAzjB,SAAA,CAAA;EAAA4L,EAAAA,MAAA,CAMA7J,MAAM,GAAN,SAAAA,MAAAA,CAAOzB,GAAG,EAAE;EACR,IAAA,IAAIA,GAAG,CAACV,IAAI,KAAK4jB,UAAU,CAACG,KAAK,IAAIrjB,GAAG,CAACV,IAAI,KAAK4jB,UAAU,CAACI,GAAG,EAAE;EAC9D,MAAA,IAAIrB,SAAS,CAACjiB,GAAG,CAAC,EAAE;UAChB,OAAO,IAAI,CAACujB,cAAc,CAAC;EACvBjkB,UAAAA,IAAI,EAAEU,GAAG,CAACV,IAAI,KAAK4jB,UAAU,CAACG,KAAK,GAC7BH,UAAU,CAACM,YAAY,GACvBN,UAAU,CAACO,UAAU;YAC3BC,GAAG,EAAE1jB,GAAG,CAAC0jB,GAAG;YACZnkB,IAAI,EAAES,GAAG,CAACT,IAAI;YACdkZ,EAAE,EAAEzY,GAAG,CAACyY,EAAAA;EACZ,SAAC,CAAC,CAAA;EACN,OAAA;EACJ,KAAA;EACA,IAAA,OAAO,CAAC,IAAI,CAACkL,cAAc,CAAC3jB,GAAG,CAAC,CAAC,CAAA;EACrC,GAAA;EACA;EACJ;EACA,MAFI;EAAAsL,EAAAA,MAAA,CAGAqY,cAAc,GAAd,SAAAA,cAAAA,CAAe3jB,GAAG,EAAE;EAChB;EACA,IAAA,IAAIyJ,GAAG,GAAG,EAAE,GAAGzJ,GAAG,CAACV,IAAI,CAAA;EACvB;EACA,IAAA,IAAIU,GAAG,CAACV,IAAI,KAAK4jB,UAAU,CAACM,YAAY,IACpCxjB,GAAG,CAACV,IAAI,KAAK4jB,UAAU,CAACO,UAAU,EAAE;EACpCha,MAAAA,GAAG,IAAIzJ,GAAG,CAACyiB,WAAW,GAAG,GAAG,CAAA;EAChC,KAAA;EACA;EACA;MACA,IAAIziB,GAAG,CAAC0jB,GAAG,IAAI,GAAG,KAAK1jB,GAAG,CAAC0jB,GAAG,EAAE;EAC5Bja,MAAAA,GAAG,IAAIzJ,GAAG,CAAC0jB,GAAG,GAAG,GAAG,CAAA;EACxB,KAAA;EACA;EACA,IAAA,IAAI,IAAI,IAAI1jB,GAAG,CAACyY,EAAE,EAAE;QAChBhP,GAAG,IAAIzJ,GAAG,CAACyY,EAAE,CAAA;EACjB,KAAA;EACA;EACA,IAAA,IAAI,IAAI,IAAIzY,GAAG,CAACT,IAAI,EAAE;EAClBkK,MAAAA,GAAG,IAAIuP,IAAI,CAACqD,SAAS,CAACrc,GAAG,CAACT,IAAI,EAAE,IAAI,CAAC6jB,QAAQ,CAAC,CAAA;EAClD,KAAA;EACA,IAAA,OAAO3Z,GAAG,CAAA;EACd,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAA6B,EAAAA,MAAA,CAKAiY,cAAc,GAAd,SAAAA,cAAAA,CAAevjB,GAAG,EAAE;EAChB,IAAA,IAAM4jB,cAAc,GAAGxB,iBAAiB,CAACpiB,GAAG,CAAC,CAAA;MAC7C,IAAMuiB,IAAI,GAAG,IAAI,CAACoB,cAAc,CAACC,cAAc,CAACxiB,MAAM,CAAC,CAAA;EACvD,IAAA,IAAMihB,OAAO,GAAGuB,cAAc,CAACvB,OAAO,CAAA;EACtCA,IAAAA,OAAO,CAAChE,OAAO,CAACkE,IAAI,CAAC,CAAC;MACtB,OAAOF,OAAO,CAAC;KAClB,CAAA;EAAA,EAAA,OAAAc,OAAA,CAAA;EAAA,CAAA,EAAA,CAAA;EAEL;EACA;EACA;EACA;EACA;EACaU,IAAAA,OAAO,0BAAA7Y,QAAA,EAAA;EAChB;EACJ;EACA;EACA;EACA;IACI,SAAA6Y,OAAAA,CAAYC,OAAO,EAAE;EAAA,IAAA,IAAAnZ,KAAA,CAAA;EACjBA,IAAAA,KAAA,GAAAK,QAAA,CAAApL,IAAA,KAAM,CAAC,IAAA,IAAA,CAAA;MACP+K,KAAA,CAAKmZ,OAAO,GAAGA,OAAO,CAAA;EAAC,IAAA,OAAAnZ,KAAA,CAAA;EAC3B,GAAA;EACA;EACJ;EACA;EACA;EACA;IAJIC,cAAA,CAAAiZ,OAAA,EAAA7Y,QAAA,CAAA,CAAA;EAAA,EAAA,IAAA0E,OAAA,GAAAmU,OAAA,CAAAnkB,SAAA,CAAA;EAAAgQ,EAAAA,OAAA,CAKAqU,GAAG,GAAH,SAAAA,GAAAA,CAAI/jB,GAAG,EAAE;EACL,IAAA,IAAIoB,MAAM,CAAA;EACV,IAAA,IAAI,OAAOpB,GAAG,KAAK,QAAQ,EAAE;QACzB,IAAI,IAAI,CAACgkB,aAAa,EAAE;EACpB,QAAA,MAAM,IAAIlZ,KAAK,CAAC,iDAAiD,CAAC,CAAA;EACtE,OAAA;EACA1J,MAAAA,MAAM,GAAG,IAAI,CAAC6iB,YAAY,CAACjkB,GAAG,CAAC,CAAA;QAC/B,IAAMkkB,aAAa,GAAG9iB,MAAM,CAAC9B,IAAI,KAAK4jB,UAAU,CAACM,YAAY,CAAA;QAC7D,IAAIU,aAAa,IAAI9iB,MAAM,CAAC9B,IAAI,KAAK4jB,UAAU,CAACO,UAAU,EAAE;UACxDriB,MAAM,CAAC9B,IAAI,GAAG4kB,aAAa,GAAGhB,UAAU,CAACG,KAAK,GAAGH,UAAU,CAACI,GAAG,CAAA;EAC/D;EACA,QAAA,IAAI,CAACU,aAAa,GAAG,IAAIG,mBAAmB,CAAC/iB,MAAM,CAAC,CAAA;EACpD;EACA,QAAA,IAAIA,MAAM,CAACqhB,WAAW,KAAK,CAAC,EAAE;YAC1BzX,QAAA,CAAAtL,SAAA,CAAM8H,YAAY,CAAA5H,IAAA,CAAA,IAAA,EAAC,SAAS,EAAEwB,MAAM,CAAA,CAAA;EACxC,SAAA;EACJ,OAAC,MACI;EACD;UACA4J,QAAA,CAAAtL,SAAA,CAAM8H,YAAY,CAAA5H,IAAA,CAAA,IAAA,EAAC,SAAS,EAAEwB,MAAM,CAAA,CAAA;EACxC,OAAA;OACH,MACI,IAAIyE,QAAQ,CAAC7F,GAAG,CAAC,IAAIA,GAAG,CAACgC,MAAM,EAAE;EAClC;EACA,MAAA,IAAI,CAAC,IAAI,CAACgiB,aAAa,EAAE;EACrB,QAAA,MAAM,IAAIlZ,KAAK,CAAC,kDAAkD,CAAC,CAAA;EACvE,OAAC,MACI;UACD1J,MAAM,GAAG,IAAI,CAAC4iB,aAAa,CAACI,cAAc,CAACpkB,GAAG,CAAC,CAAA;EAC/C,QAAA,IAAIoB,MAAM,EAAE;EACR;YACA,IAAI,CAAC4iB,aAAa,GAAG,IAAI,CAAA;YACzBhZ,QAAA,CAAAtL,SAAA,CAAM8H,YAAY,CAAA5H,IAAA,CAAA,IAAA,EAAC,SAAS,EAAEwB,MAAM,CAAA,CAAA;EACxC,SAAA;EACJ,OAAA;EACJ,KAAC,MACI;EACD,MAAA,MAAM,IAAI0J,KAAK,CAAC,gBAAgB,GAAG9K,GAAG,CAAC,CAAA;EAC3C,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAA0P,EAAAA,OAAA,CAMAuU,YAAY,GAAZ,SAAAA,YAAAA,CAAaxa,GAAG,EAAE;MACd,IAAI7H,CAAC,GAAG,CAAC,CAAA;EACT;EACA,IAAA,IAAMO,CAAC,GAAG;QACN7C,IAAI,EAAE2N,MAAM,CAACxD,GAAG,CAAC3G,MAAM,CAAC,CAAC,CAAC,CAAA;OAC7B,CAAA;MACD,IAAIogB,UAAU,CAAC/gB,CAAC,CAAC7C,IAAI,CAAC,KAAKkN,SAAS,EAAE;QAClC,MAAM,IAAI1B,KAAK,CAAC,sBAAsB,GAAG3I,CAAC,CAAC7C,IAAI,CAAC,CAAA;EACpD,KAAA;EACA;EACA,IAAA,IAAI6C,CAAC,CAAC7C,IAAI,KAAK4jB,UAAU,CAACM,YAAY,IAClCrhB,CAAC,CAAC7C,IAAI,KAAK4jB,UAAU,CAACO,UAAU,EAAE;EAClC,MAAA,IAAMY,KAAK,GAAGziB,CAAC,GAAG,CAAC,CAAA;EACnB,MAAA,OAAO6H,GAAG,CAAC3G,MAAM,CAAC,EAAElB,CAAC,CAAC,KAAK,GAAG,IAAIA,CAAC,IAAI6H,GAAG,CAAC5H,MAAM,EAAE,EAAE;QACrD,IAAMyiB,GAAG,GAAG7a,GAAG,CAACzG,SAAS,CAACqhB,KAAK,EAAEziB,CAAC,CAAC,CAAA;EACnC,MAAA,IAAI0iB,GAAG,IAAIrX,MAAM,CAACqX,GAAG,CAAC,IAAI7a,GAAG,CAAC3G,MAAM,CAAClB,CAAC,CAAC,KAAK,GAAG,EAAE;EAC7C,QAAA,MAAM,IAAIkJ,KAAK,CAAC,qBAAqB,CAAC,CAAA;EAC1C,OAAA;EACA3I,MAAAA,CAAC,CAACsgB,WAAW,GAAGxV,MAAM,CAACqX,GAAG,CAAC,CAAA;EAC/B,KAAA;EACA;MACA,IAAI,GAAG,KAAK7a,GAAG,CAAC3G,MAAM,CAAClB,CAAC,GAAG,CAAC,CAAC,EAAE;EAC3B,MAAA,IAAMyiB,MAAK,GAAGziB,CAAC,GAAG,CAAC,CAAA;QACnB,OAAO,EAAEA,CAAC,EAAE;EACR,QAAA,IAAM8H,CAAC,GAAGD,GAAG,CAAC3G,MAAM,CAAClB,CAAC,CAAC,CAAA;UACvB,IAAI,GAAG,KAAK8H,CAAC,EACT,MAAA;EACJ,QAAA,IAAI9H,CAAC,KAAK6H,GAAG,CAAC5H,MAAM,EAChB,MAAA;EACR,OAAA;QACAM,CAAC,CAACuhB,GAAG,GAAGja,GAAG,CAACzG,SAAS,CAACqhB,MAAK,EAAEziB,CAAC,CAAC,CAAA;EACnC,KAAC,MACI;QACDO,CAAC,CAACuhB,GAAG,GAAG,GAAG,CAAA;EACf,KAAA;EACA;MACA,IAAMa,IAAI,GAAG9a,GAAG,CAAC3G,MAAM,CAAClB,CAAC,GAAG,CAAC,CAAC,CAAA;MAC9B,IAAI,EAAE,KAAK2iB,IAAI,IAAItX,MAAM,CAACsX,IAAI,CAAC,IAAIA,IAAI,EAAE;EACrC,MAAA,IAAMF,OAAK,GAAGziB,CAAC,GAAG,CAAC,CAAA;QACnB,OAAO,EAAEA,CAAC,EAAE;EACR,QAAA,IAAM8H,EAAC,GAAGD,GAAG,CAAC3G,MAAM,CAAClB,CAAC,CAAC,CAAA;UACvB,IAAI,IAAI,IAAI8H,EAAC,IAAIuD,MAAM,CAACvD,EAAC,CAAC,IAAIA,EAAC,EAAE;EAC7B,UAAA,EAAE9H,CAAC,CAAA;EACH,UAAA,MAAA;EACJ,SAAA;EACA,QAAA,IAAIA,CAAC,KAAK6H,GAAG,CAAC5H,MAAM,EAChB,MAAA;EACR,OAAA;EACAM,MAAAA,CAAC,CAACsW,EAAE,GAAGxL,MAAM,CAACxD,GAAG,CAACzG,SAAS,CAACqhB,OAAK,EAAEziB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;EAC9C,KAAA;EACA;EACA,IAAA,IAAI6H,GAAG,CAAC3G,MAAM,CAAC,EAAElB,CAAC,CAAC,EAAE;EACjB,MAAA,IAAM4iB,OAAO,GAAG,IAAI,CAACC,QAAQ,CAAChb,GAAG,CAACib,MAAM,CAAC9iB,CAAC,CAAC,CAAC,CAAA;QAC5C,IAAIiiB,OAAO,CAACc,cAAc,CAACxiB,CAAC,CAAC7C,IAAI,EAAEklB,OAAO,CAAC,EAAE;UACzCriB,CAAC,CAAC5C,IAAI,GAAGilB,OAAO,CAAA;EACpB,OAAC,MACI;EACD,QAAA,MAAM,IAAI1Z,KAAK,CAAC,iBAAiB,CAAC,CAAA;EACtC,OAAA;EACJ,KAAA;EACA,IAAA,OAAO3I,CAAC,CAAA;KACX,CAAA;EAAAuN,EAAAA,OAAA,CACD+U,QAAQ,GAAR,SAAAA,QAAAA,CAAShb,GAAG,EAAE;MACV,IAAI;QACA,OAAOuP,IAAI,CAACxD,KAAK,CAAC/L,GAAG,EAAE,IAAI,CAACqa,OAAO,CAAC,CAAA;OACvC,CACD,OAAO5T,CAAC,EAAE;EACN,MAAA,OAAO,KAAK,CAAA;EAChB,KAAA;KACH,CAAA;IAAA2T,OAAA,CACMc,cAAc,GAArB,SAAAA,eAAsBrlB,IAAI,EAAEklB,OAAO,EAAE;EACjC,IAAA,QAAQllB,IAAI;QACR,KAAK4jB,UAAU,CAAC0B,OAAO;UACnB,OAAOC,QAAQ,CAACL,OAAO,CAAC,CAAA;QAC5B,KAAKtB,UAAU,CAAC4B,UAAU;UACtB,OAAON,OAAO,KAAKhY,SAAS,CAAA;QAChC,KAAK0W,UAAU,CAAC6B,aAAa;UACzB,OAAO,OAAOP,OAAO,KAAK,QAAQ,IAAIK,QAAQ,CAACL,OAAO,CAAC,CAAA;QAC3D,KAAKtB,UAAU,CAACG,KAAK,CAAA;QACrB,KAAKH,UAAU,CAACM,YAAY;EACxB,QAAA,OAAQ/f,KAAK,CAAC0e,OAAO,CAACqC,OAAO,CAAC,KACzB,OAAOA,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,IAC1B,OAAOA,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,IAC3BvB,iBAAe,CAACnW,OAAO,CAAC0X,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAE,CAAC,CAAA;QAC5D,KAAKtB,UAAU,CAACI,GAAG,CAAA;QACnB,KAAKJ,UAAU,CAACO,UAAU;EACtB,QAAA,OAAOhgB,KAAK,CAAC0e,OAAO,CAACqC,OAAO,CAAC,CAAA;EACrC,KAAA;EACJ,GAAA;EACA;EACJ;EACA,MAFI;EAAA9U,EAAAA,OAAA,CAGA6N,OAAO,GAAP,SAAAA,UAAU;MACN,IAAI,IAAI,CAACyG,aAAa,EAAE;EACpB,MAAA,IAAI,CAACA,aAAa,CAACgB,sBAAsB,EAAE,CAAA;QAC3C,IAAI,CAAChB,aAAa,GAAG,IAAI,CAAA;EAC7B,KAAA;KACH,CAAA;EAAA,EAAA,OAAAH,OAAA,CAAA;EAAA,CAAA,CA9JwBxd,OAAO,CAAA,CAAA;EAgKpC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAPA,IAQM8d,mBAAmB,gBAAA,YAAA;IACrB,SAAAA,mBAAAA,CAAY/iB,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAGA,MAAM,CAAA;MACpB,IAAI,CAACihB,OAAO,GAAG,EAAE,CAAA;MACjB,IAAI,CAAC4C,SAAS,GAAG7jB,MAAM,CAAA;EAC3B,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EAPI,EAAA,IAAA2Q,OAAA,GAAAoS,mBAAA,CAAAzkB,SAAA,CAAA;EAAAqS,EAAAA,OAAA,CAQAqS,cAAc,GAAd,SAAAA,cAAAA,CAAec,OAAO,EAAE;EACpB,IAAA,IAAI,CAAC7C,OAAO,CAACte,IAAI,CAACmhB,OAAO,CAAC,CAAA;MAC1B,IAAI,IAAI,CAAC7C,OAAO,CAACxgB,MAAM,KAAK,IAAI,CAACojB,SAAS,CAACxC,WAAW,EAAE;EACpD;QACA,IAAMrhB,MAAM,GAAG0hB,iBAAiB,CAAC,IAAI,CAACmC,SAAS,EAAE,IAAI,CAAC5C,OAAO,CAAC,CAAA;QAC9D,IAAI,CAAC2C,sBAAsB,EAAE,CAAA;EAC7B,MAAA,OAAO5jB,MAAM,CAAA;EACjB,KAAA;EACA,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA,MAFI;EAAA2Q,EAAAA,OAAA,CAGAiT,sBAAsB,GAAtB,SAAAA,yBAAyB;MACrB,IAAI,CAACC,SAAS,GAAG,IAAI,CAAA;MACrB,IAAI,CAAC5C,OAAO,GAAG,EAAE,CAAA;KACpB,CAAA;EAAA,EAAA,OAAA8B,mBAAA,CAAA;EAAA,CAAA,EAAA,CAAA;EAEL,SAASgB,gBAAgBA,CAACzB,GAAG,EAAE;IAC3B,OAAO,OAAOA,GAAG,KAAK,QAAQ,CAAA;EAClC,CAAA;EACA;EACA,IAAM0B,SAAS,GAAGnY,MAAM,CAACmY,SAAS,IAC9B,UAAUhX,KAAK,EAAE;EACb,EAAA,OAAQ,OAAOA,KAAK,KAAK,QAAQ,IAC7B8N,QAAQ,CAAC9N,KAAK,CAAC,IACflI,IAAI,CAACmf,KAAK,CAACjX,KAAK,CAAC,KAAKA,KAAK,CAAA;EACnC,CAAC,CAAA;EACL,SAASkX,YAAYA,CAAC7M,EAAE,EAAE;EACtB,EAAA,OAAOA,EAAE,KAAKjM,SAAS,IAAI4Y,SAAS,CAAC3M,EAAE,CAAC,CAAA;EAC5C,CAAA;EACA;EACA,SAASoM,QAAQA,CAACzW,KAAK,EAAE;IACrB,OAAOrP,MAAM,CAACW,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACwO,KAAK,CAAC,KAAK,iBAAiB,CAAA;EACtE,CAAA;EACA,SAASmX,WAAWA,CAACjmB,IAAI,EAAEklB,OAAO,EAAE;EAChC,EAAA,QAAQllB,IAAI;MACR,KAAK4jB,UAAU,CAAC0B,OAAO;EACnB,MAAA,OAAOJ,OAAO,KAAKhY,SAAS,IAAIqY,QAAQ,CAACL,OAAO,CAAC,CAAA;MACrD,KAAKtB,UAAU,CAAC4B,UAAU;QACtB,OAAON,OAAO,KAAKhY,SAAS,CAAA;MAChC,KAAK0W,UAAU,CAACG,KAAK;EACjB,MAAA,OAAQ5f,KAAK,CAAC0e,OAAO,CAACqC,OAAO,CAAC,KACzB,OAAOA,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,IAC1B,OAAOA,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,IAC3BvB,iBAAe,CAACnW,OAAO,CAAC0X,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAE,CAAC,CAAA;MAC5D,KAAKtB,UAAU,CAACI,GAAG;EACf,MAAA,OAAO7f,KAAK,CAAC0e,OAAO,CAACqC,OAAO,CAAC,CAAA;MACjC,KAAKtB,UAAU,CAAC6B,aAAa;QACzB,OAAO,OAAOP,OAAO,KAAK,QAAQ,IAAIK,QAAQ,CAACL,OAAO,CAAC,CAAA;EAC3D,IAAA;EACI,MAAA,OAAO,KAAK,CAAA;EACpB,GAAA;EACJ,CAAA;EACO,SAASgB,aAAaA,CAACpkB,MAAM,EAAE;IAClC,OAAQ+jB,gBAAgB,CAAC/jB,MAAM,CAACsiB,GAAG,CAAC,IAChC4B,YAAY,CAAClkB,MAAM,CAACqX,EAAE,CAAC,IACvB8M,WAAW,CAACnkB,MAAM,CAAC9B,IAAI,EAAE8B,MAAM,CAAC7B,IAAI,CAAC,CAAA;EAC7C;;;;;;;;;;;EC3VO,SAASgH,EAAEA,CAACvG,GAAG,EAAEmT,EAAE,EAAEzM,EAAE,EAAE;EAC5B1G,EAAAA,GAAG,CAACuG,EAAE,CAAC4M,EAAE,EAAEzM,EAAE,CAAC,CAAA;IACd,OAAO,SAAS+e,UAAUA,GAAG;EACzBzlB,IAAAA,GAAG,CAAC6G,GAAG,CAACsM,EAAE,EAAEzM,EAAE,CAAC,CAAA;KAClB,CAAA;EACL;;ECDA,IAAMsW,OAAK,GAAG0E,WAAW,CAAC,yBAAyB,CAAC,CAAC;EACrD;EACA;EACA;EACA;EACA,IAAMuB,eAAe,GAAGlkB,MAAM,CAAC2mB,MAAM,CAAC;EAClCC,EAAAA,OAAO,EAAE,CAAC;EACVC,EAAAA,aAAa,EAAE,CAAC;EAChBC,EAAAA,UAAU,EAAE,CAAC;EACbC,EAAAA,aAAa,EAAE,CAAC;EAChB;EACAC,EAAAA,WAAW,EAAE,CAAC;EACd/e,EAAAA,cAAc,EAAE,CAAA;EACpB,CAAC,CAAC,CAAA;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACaqU,IAAAA,MAAM,0BAAArQ,QAAA,EAAA;EACf;EACJ;EACA;EACI,EAAA,SAAAqQ,OAAY2K,EAAE,EAAEtC,GAAG,EAAExa,IAAI,EAAE;EAAA,IAAA,IAAAyB,KAAA,CAAA;EACvBA,IAAAA,KAAA,GAAAK,QAAA,CAAApL,IAAA,KAAM,CAAC,IAAA,IAAA,CAAA;EACP;EACR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;MACQ+K,KAAA,CAAKsb,SAAS,GAAG,KAAK,CAAA;EACtB;EACR;EACA;EACA;MACQtb,KAAA,CAAKub,SAAS,GAAG,KAAK,CAAA;EACtB;EACR;EACA;MACQvb,KAAA,CAAKwb,aAAa,GAAG,EAAE,CAAA;EACvB;EACR;EACA;MACQxb,KAAA,CAAKyb,UAAU,GAAG,EAAE,CAAA;EACpB;EACR;EACA;EACA;EACA;EACA;MACQzb,KAAA,CAAK0b,MAAM,GAAG,EAAE,CAAA;EAChB;EACR;EACA;EACA;MACQ1b,KAAA,CAAK2b,SAAS,GAAG,CAAC,CAAA;MAClB3b,KAAA,CAAK4b,GAAG,GAAG,CAAC,CAAA;EACZ;EACR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACQ5b,IAAAA,KAAA,CAAK6b,IAAI,GAAG,EAAE,CAAA;EACd7b,IAAAA,KAAA,CAAK8b,KAAK,GAAG,EAAE,CAAA;MACf9b,KAAA,CAAKqb,EAAE,GAAGA,EAAE,CAAA;MACZrb,KAAA,CAAK+Y,GAAG,GAAGA,GAAG,CAAA;EACd,IAAA,IAAIxa,IAAI,IAAIA,IAAI,CAACwd,IAAI,EAAE;EACnB/b,MAAAA,KAAA,CAAK+b,IAAI,GAAGxd,IAAI,CAACwd,IAAI,CAAA;EACzB,KAAA;MACA/b,KAAA,CAAK0E,KAAK,GAAG2C,QAAA,CAAc,EAAE,EAAE9I,IAAI,CAAC,CAAA;MACpC,IAAIyB,KAAA,CAAKqb,EAAE,CAACW,YAAY,EACpBhc,KAAA,CAAKa,IAAI,EAAE,CAAA;EAAC,IAAA,OAAAb,KAAA,CAAA;EACpB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IAbIC,cAAA,CAAAyQ,MAAA,EAAArQ,QAAA,CAAA,CAAA;EAAA,EAAA,IAAAM,MAAA,GAAA+P,MAAA,CAAA3b,SAAA,CAAA;EAiBA;EACJ;EACA;EACA;EACA;EAJI4L,EAAAA,MAAA,CAKAsb,SAAS,GAAT,SAAAA,YAAY;MACR,IAAI,IAAI,CAACC,IAAI,EACT,OAAA;EACJ,IAAA,IAAMb,EAAE,GAAG,IAAI,CAACA,EAAE,CAAA;EAClB,IAAA,IAAI,CAACa,IAAI,GAAG,CACRtgB,EAAE,CAACyf,EAAE,EAAE,MAAM,EAAE,IAAI,CAACpT,MAAM,CAACxJ,IAAI,CAAC,IAAI,CAAC,CAAC,EACtC7C,EAAE,CAACyf,EAAE,EAAE,QAAQ,EAAE,IAAI,CAACc,QAAQ,CAAC1d,IAAI,CAAC,IAAI,CAAC,CAAC,EAC1C7C,EAAE,CAACyf,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC5S,OAAO,CAAChK,IAAI,CAAC,IAAI,CAAC,CAAC,EACxC7C,EAAE,CAACyf,EAAE,EAAE,OAAO,EAAE,IAAI,CAAChT,OAAO,CAAC5J,IAAI,CAAC,IAAI,CAAC,CAAC,CAC3C,CAAA;EACL,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAhBI;EAoBA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EATIkC,EAAAA,MAAA,CAUAqa,OAAO,GAAP,SAAAA,UAAU;EACN,IAAA,IAAI,IAAI,CAACM,SAAS,EACd,OAAO,IAAI,CAAA;MACf,IAAI,CAACW,SAAS,EAAE,CAAA;EAChB,IAAA,IAAI,CAAC,IAAI,CAACZ,EAAE,CAAC,eAAe,CAAC,EACzB,IAAI,CAACA,EAAE,CAACxa,IAAI,EAAE,CAAC;EACnB,IAAA,IAAI,MAAM,KAAK,IAAI,CAACwa,EAAE,CAACe,WAAW,EAC9B,IAAI,CAACnU,MAAM,EAAE,CAAA;EACjB,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA,MAFI;EAAAtH,EAAAA,MAAA,CAGAE,IAAI,GAAJ,SAAAA,OAAO;EACH,IAAA,OAAO,IAAI,CAACma,OAAO,EAAE,CAAA;EACzB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAdI;EAAAra,EAAAA,MAAA,CAeAQ,IAAI,GAAJ,SAAAA,OAAc;EAAA,IAAA,KAAA,IAAAvD,IAAA,GAAAxB,SAAA,CAAAlF,MAAA,EAAN0F,IAAI,GAAA9D,IAAAA,KAAA,CAAA8E,IAAA,GAAAE,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA,EAAA,EAAA;EAAJlB,MAAAA,IAAI,CAAAkB,IAAA,CAAA1B,GAAAA,SAAA,CAAA0B,IAAA,CAAA,CAAA;EAAA,KAAA;EACRlB,IAAAA,IAAI,CAAC8W,OAAO,CAAC,SAAS,CAAC,CAAA;MACvB,IAAI,CAAC/W,IAAI,CAACR,KAAK,CAAC,IAAI,EAAES,IAAI,CAAC,CAAA;EAC3B,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAhBI;EAAA+D,EAAAA,MAAA,CAiBAhE,IAAI,GAAJ,SAAAA,IAAAA,CAAK6L,EAAE,EAAW;EACd,IAAA,IAAIxD,EAAE,EAAEqX,EAAE,EAAEC,EAAE,CAAA;EACd,IAAA,IAAIhE,eAAe,CAACta,cAAc,CAACwK,EAAE,CAAC,EAAE;EACpC,MAAA,MAAM,IAAIrI,KAAK,CAAC,GAAG,GAAGqI,EAAE,CAACxT,QAAQ,EAAE,GAAG,4BAA4B,CAAC,CAAA;EACvE,KAAA;MAAC,KAAAunB,IAAAA,KAAA,GAAAngB,SAAA,CAAAlF,MAAA,EAJO0F,IAAI,OAAA9D,KAAA,CAAAyjB,KAAA,GAAAA,CAAAA,GAAAA,KAAA,WAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,EAAA;EAAJ5f,MAAAA,IAAI,CAAA4f,KAAA,GAAApgB,CAAAA,CAAAA,GAAAA,SAAA,CAAAogB,KAAA,CAAA,CAAA;EAAA,KAAA;EAKZ5f,IAAAA,IAAI,CAAC8W,OAAO,CAAClL,EAAE,CAAC,CAAA;EAChB,IAAA,IAAI,IAAI,CAAC9D,KAAK,CAAC+X,OAAO,IAAI,CAAC,IAAI,CAACX,KAAK,CAACY,SAAS,IAAI,CAAC,IAAI,CAACZ,KAAK,YAAS,EAAE;EACrE,MAAA,IAAI,CAACa,WAAW,CAAC/f,IAAI,CAAC,CAAA;EACtB,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA,IAAA,IAAMnG,MAAM,GAAG;QACX9B,IAAI,EAAE4jB,UAAU,CAACG,KAAK;EACtB9jB,MAAAA,IAAI,EAAEgI,IAAAA;OACT,CAAA;EACDnG,IAAAA,MAAM,CAAC2Y,OAAO,GAAG,EAAE,CAAA;MACnB3Y,MAAM,CAAC2Y,OAAO,CAACC,QAAQ,GAAG,IAAI,CAACyM,KAAK,CAACzM,QAAQ,KAAK,KAAK,CAAA;EACvD;MACA,IAAI,UAAU,KAAK,OAAOzS,IAAI,CAACA,IAAI,CAAC1F,MAAM,GAAG,CAAC,CAAC,EAAE;EAC7C,MAAA,IAAM4W,EAAE,GAAG,IAAI,CAAC8N,GAAG,EAAE,CAAA;EACrBvJ,MAAAA,OAAK,CAAC,gCAAgC,EAAEvE,EAAE,CAAC,CAAA;EAC3C,MAAA,IAAM8O,GAAG,GAAGhgB,IAAI,CAACigB,GAAG,EAAE,CAAA;EACtB,MAAA,IAAI,CAACC,oBAAoB,CAAChP,EAAE,EAAE8O,GAAG,CAAC,CAAA;QAClCnmB,MAAM,CAACqX,EAAE,GAAGA,EAAE,CAAA;EAClB,KAAA;EACA,IAAA,IAAMiP,mBAAmB,GAAG,CAACV,EAAE,GAAG,CAACrX,EAAE,GAAG,IAAI,CAACqW,EAAE,CAAC2B,MAAM,MAAM,IAAI,IAAIhY,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuI,SAAS,MAAM,IAAI,IAAI8O,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC9b,QAAQ,CAAA;EAC3J,IAAA,IAAM0c,WAAW,GAAG,IAAI,CAAC3B,SAAS,IAAI,EAAE,CAACgB,EAAE,GAAG,IAAI,CAACjB,EAAE,CAAC2B,MAAM,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACrN,eAAe,EAAE,CAAC,CAAA;MACxH,IAAMiO,aAAa,GAAG,IAAI,CAACpB,KAAK,CAAS,UAAA,CAAA,IAAI,CAACiB,mBAAmB,CAAA;EACjE,IAAA,IAAIG,aAAa,EAAE;QACf7K,OAAK,CAAC,2DAA2D,CAAC,CAAA;OACrE,MACI,IAAI4K,WAAW,EAAE;EAClB,MAAA,IAAI,CAACE,uBAAuB,CAAC1mB,MAAM,CAAC,CAAA;EACpC,MAAA,IAAI,CAACA,MAAM,CAACA,MAAM,CAAC,CAAA;EACvB,KAAC,MACI;EACD,MAAA,IAAI,CAACglB,UAAU,CAACriB,IAAI,CAAC3C,MAAM,CAAC,CAAA;EAChC,KAAA;EACA,IAAA,IAAI,CAACqlB,KAAK,GAAG,EAAE,CAAA;EACf,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA,MAFI;IAAAnb,MAAA,CAGAmc,oBAAoB,GAApB,SAAAA,qBAAqBhP,EAAE,EAAE8O,GAAG,EAAE;EAAA,IAAA,IAAAtc,MAAA,GAAA,IAAA,CAAA;EAC1B,IAAA,IAAI0E,EAAE,CAAA;MACN,IAAMY,OAAO,GAAG,CAACZ,EAAE,GAAG,IAAI,CAAC8W,KAAK,CAAClW,OAAO,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAACN,KAAK,CAAC0Y,UAAU,CAAA;MAChG,IAAIxX,OAAO,KAAK/D,SAAS,EAAE;EACvB,MAAA,IAAI,CAACga,IAAI,CAAC/N,EAAE,CAAC,GAAG8O,GAAG,CAAA;EACnB,MAAA,OAAA;EACJ,KAAA;EACA;MACA,IAAMS,KAAK,GAAG,IAAI,CAAChC,EAAE,CAACje,YAAY,CAAC,YAAM;EACrC,MAAA,OAAOkD,MAAI,CAACub,IAAI,CAAC/N,EAAE,CAAC,CAAA;EACpB,MAAA,KAAK,IAAI7W,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqJ,MAAI,CAACmb,UAAU,CAACvkB,MAAM,EAAED,CAAC,EAAE,EAAE;UAC7C,IAAIqJ,MAAI,CAACmb,UAAU,CAACxkB,CAAC,CAAC,CAAC6W,EAAE,KAAKA,EAAE,EAAE;EAC9BuE,UAAAA,OAAK,CAAC,gDAAgD,EAAEvE,EAAE,CAAC,CAAA;YAC3DxN,MAAI,CAACmb,UAAU,CAAC/e,MAAM,CAACzF,CAAC,EAAE,CAAC,CAAC,CAAA;EAChC,SAAA;EACJ,OAAA;EACAob,MAAAA,OAAK,CAAC,gDAAgD,EAAEvE,EAAE,EAAElI,OAAO,CAAC,CAAA;QACpEgX,GAAG,CAAC3nB,IAAI,CAACqL,MAAI,EAAE,IAAIH,KAAK,CAAC,yBAAyB,CAAC,CAAC,CAAA;OACvD,EAAEyF,OAAO,CAAC,CAAA;EACX,IAAA,IAAM7J,EAAE,GAAG,SAALA,EAAEA,GAAgB;EACpB;EACAuE,MAAAA,MAAI,CAAC+a,EAAE,CAAC3c,cAAc,CAAC2e,KAAK,CAAC,CAAA;EAAC,MAAA,KAAA,IAAAC,KAAA,GAAAlhB,SAAA,CAAAlF,MAAA,EAFnB0F,IAAI,GAAA9D,IAAAA,KAAA,CAAAwkB,KAAA,GAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,EAAA;EAAJ3gB,QAAAA,IAAI,CAAA2gB,KAAA,CAAAnhB,GAAAA,SAAA,CAAAmhB,KAAA,CAAA,CAAA;EAAA,OAAA;EAGfX,MAAAA,GAAG,CAACzgB,KAAK,CAACmE,MAAI,EAAE1D,IAAI,CAAC,CAAA;OACxB,CAAA;MACDb,EAAE,CAACyhB,SAAS,GAAG,IAAI,CAAA;EACnB,IAAA,IAAI,CAAC3B,IAAI,CAAC/N,EAAE,CAAC,GAAG/R,EAAE,CAAA;EACtB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAfI;EAAA4E,EAAAA,MAAA,CAgBA8c,WAAW,GAAX,SAAAA,WAAAA,CAAYjV,EAAE,EAAW;EAAA,IAAA,IAAA1F,MAAA,GAAA,IAAA,CAAA;MAAA,KAAA4a,IAAAA,KAAA,GAAAthB,SAAA,CAAAlF,MAAA,EAAN0F,IAAI,OAAA9D,KAAA,CAAA4kB,KAAA,GAAAA,CAAAA,GAAAA,KAAA,WAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,EAAA;EAAJ/gB,MAAAA,IAAI,CAAA+gB,KAAA,GAAAvhB,CAAAA,CAAAA,GAAAA,SAAA,CAAAuhB,KAAA,CAAA,CAAA;EAAA,KAAA;EACnB,IAAA,OAAO,IAAIzgB,OAAO,CAAC,UAACC,OAAO,EAAEygB,MAAM,EAAK;QACpC,IAAM7hB,EAAE,GAAG,SAALA,EAAEA,CAAI8hB,IAAI,EAAEC,IAAI,EAAK;UACvB,OAAOD,IAAI,GAAGD,MAAM,CAACC,IAAI,CAAC,GAAG1gB,OAAO,CAAC2gB,IAAI,CAAC,CAAA;SAC7C,CAAA;QACD/hB,EAAE,CAACyhB,SAAS,GAAG,IAAI,CAAA;EACnB5gB,MAAAA,IAAI,CAACxD,IAAI,CAAC2C,EAAE,CAAC,CAAA;EACb+G,MAAAA,MAAI,CAACnG,IAAI,CAAAR,KAAA,CAAT2G,MAAI,EAAM0F,CAAAA,EAAE,CAAAlB,CAAAA,MAAA,CAAK1K,IAAI,CAAC,CAAA,CAAA;EAC1B,KAAC,CAAC,CAAA;EACN,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAA+D,EAAAA,MAAA,CAKAgc,WAAW,GAAX,SAAAA,WAAAA,CAAY/f,IAAI,EAAE;EAAA,IAAA,IAAAmG,MAAA,GAAA,IAAA,CAAA;EACd,IAAA,IAAI6Z,GAAG,CAAA;MACP,IAAI,OAAOhgB,IAAI,CAACA,IAAI,CAAC1F,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;EAC7C0lB,MAAAA,GAAG,GAAGhgB,IAAI,CAACigB,GAAG,EAAE,CAAA;EACpB,KAAA;EACA,IAAA,IAAMpmB,MAAM,GAAG;EACXqX,MAAAA,EAAE,EAAE,IAAI,CAAC6N,SAAS,EAAE;EACpBoC,MAAAA,QAAQ,EAAE,CAAC;EACXC,MAAAA,OAAO,EAAE,KAAK;EACdphB,MAAAA,IAAI,EAAJA,IAAI;QACJkf,KAAK,EAAEzU,QAAA,CAAc;EAAEqV,QAAAA,SAAS,EAAE,IAAA;SAAM,EAAE,IAAI,CAACZ,KAAK,CAAA;OACvD,CAAA;EACDlf,IAAAA,IAAI,CAACxD,IAAI,CAAC,UAACuK,GAAG,EAAsB;QAChC,IAAIlN,MAAM,KAAKsM,MAAI,CAAC2Y,MAAM,CAAC,CAAC,CAAC,EAAE;EAC3B;EACA,QAAA,OAAA;EACJ,OAAA;EACA,MAAA,IAAMuC,QAAQ,GAAGta,GAAG,KAAK,IAAI,CAAA;EAC7B,MAAA,IAAIsa,QAAQ,EAAE;UACV,IAAIxnB,MAAM,CAACsnB,QAAQ,GAAGhb,MAAI,CAAC2B,KAAK,CAAC+X,OAAO,EAAE;YACtCpK,OAAK,CAAC,yCAAyC,EAAE5b,MAAM,CAACqX,EAAE,EAAErX,MAAM,CAACsnB,QAAQ,CAAC,CAAA;EAC5Ehb,UAAAA,MAAI,CAAC2Y,MAAM,CAAChhB,KAAK,EAAE,CAAA;EACnB,UAAA,IAAIkiB,GAAG,EAAE;cACLA,GAAG,CAACjZ,GAAG,CAAC,CAAA;EACZ,WAAA;EACJ,SAAA;EACJ,OAAC,MACI;EACD0O,QAAAA,OAAK,CAAC,mCAAmC,EAAE5b,MAAM,CAACqX,EAAE,CAAC,CAAA;EACrD/K,QAAAA,MAAI,CAAC2Y,MAAM,CAAChhB,KAAK,EAAE,CAAA;EACnB,QAAA,IAAIkiB,GAAG,EAAE;YAAA,KAAAsB,IAAAA,KAAA,GAAA9hB,SAAA,CAAAlF,MAAA,EAlBEinB,YAAY,OAAArlB,KAAA,CAAAolB,KAAA,GAAAA,CAAAA,GAAAA,KAAA,WAAAE,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA,EAAA,EAAA;EAAZD,YAAAA,YAAY,CAAAC,KAAA,GAAAhiB,CAAAA,CAAAA,GAAAA,SAAA,CAAAgiB,KAAA,CAAA,CAAA;EAAA,WAAA;YAmBnBxB,GAAG,CAAAzgB,KAAA,CAAC,KAAA,CAAA,EAAA,CAAA,IAAI,EAAAmL,MAAA,CAAK6W,YAAY,CAAC,CAAA,CAAA;EAC9B,SAAA;EACJ,OAAA;QACA1nB,MAAM,CAACunB,OAAO,GAAG,KAAK,CAAA;EACtB,MAAA,OAAOjb,MAAI,CAACsb,WAAW,EAAE,CAAA;EAC7B,KAAC,CAAC,CAAA;EACF,IAAA,IAAI,CAAC3C,MAAM,CAACtiB,IAAI,CAAC3C,MAAM,CAAC,CAAA;MACxB,IAAI,CAAC4nB,WAAW,EAAE,CAAA;EACtB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAA1d,EAAAA,MAAA,CAMA0d,WAAW,GAAX,SAAAA,cAA2B;EAAA,IAAA,IAAfC,KAAK,GAAAliB,SAAA,CAAAlF,MAAA,GAAA,CAAA,IAAAkF,SAAA,CAAA,CAAA,CAAA,KAAAyF,SAAA,GAAAzF,SAAA,CAAA,CAAA,CAAA,GAAG,KAAK,CAAA;MACrBiW,OAAK,CAAC,gBAAgB,CAAC,CAAA;EACvB,IAAA,IAAI,CAAC,IAAI,CAACiJ,SAAS,IAAI,IAAI,CAACI,MAAM,CAACxkB,MAAM,KAAK,CAAC,EAAE;EAC7C,MAAA,OAAA;EACJ,KAAA;EACA,IAAA,IAAMT,MAAM,GAAG,IAAI,CAACilB,MAAM,CAAC,CAAC,CAAC,CAAA;EAC7B,IAAA,IAAIjlB,MAAM,CAACunB,OAAO,IAAI,CAACM,KAAK,EAAE;EAC1BjM,MAAAA,OAAK,CAAC,6DAA6D,EAAE5b,MAAM,CAACqX,EAAE,CAAC,CAAA;EAC/E,MAAA,OAAA;EACJ,KAAA;MACArX,MAAM,CAACunB,OAAO,GAAG,IAAI,CAAA;MACrBvnB,MAAM,CAACsnB,QAAQ,EAAE,CAAA;MACjB1L,OAAK,CAAC,gCAAgC,EAAE5b,MAAM,CAACqX,EAAE,EAAErX,MAAM,CAACsnB,QAAQ,CAAC,CAAA;EACnE,IAAA,IAAI,CAACjC,KAAK,GAAGrlB,MAAM,CAACqlB,KAAK,CAAA;MACzB,IAAI,CAACnf,IAAI,CAACR,KAAK,CAAC,IAAI,EAAE1F,MAAM,CAACmG,IAAI,CAAC,CAAA;EACtC,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAA+D,EAAAA,MAAA,CAMAlK,MAAM,GAAN,SAAAA,MAAAA,CAAOA,OAAM,EAAE;EACXA,IAAAA,OAAM,CAACsiB,GAAG,GAAG,IAAI,CAACA,GAAG,CAAA;EACrB,IAAA,IAAI,CAACsC,EAAE,CAACpS,OAAO,CAACxS,OAAM,CAAC,CAAA;EAC3B,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAkK,EAAAA,MAAA,CAKAsH,MAAM,GAAN,SAAAA,SAAS;EAAA,IAAA,IAAAjF,MAAA,GAAA,IAAA,CAAA;MACLqP,OAAK,CAAC,gCAAgC,CAAC,CAAA;EACvC,IAAA,IAAI,OAAO,IAAI,CAAC0J,IAAI,IAAI,UAAU,EAAE;EAChC,MAAA,IAAI,CAACA,IAAI,CAAC,UAACnnB,IAAI,EAAK;EAChBoO,QAAAA,MAAI,CAACub,kBAAkB,CAAC3pB,IAAI,CAAC,CAAA;EACjC,OAAC,CAAC,CAAA;EACN,KAAC,MACI;EACD,MAAA,IAAI,CAAC2pB,kBAAkB,CAAC,IAAI,CAACxC,IAAI,CAAC,CAAA;EACtC,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAApb,EAAAA,MAAA,CAMA4d,kBAAkB,GAAlB,SAAAA,kBAAAA,CAAmB3pB,IAAI,EAAE;MACrB,IAAI,CAAC6B,MAAM,CAAC;QACR9B,IAAI,EAAE4jB,UAAU,CAAC0B,OAAO;EACxBrlB,MAAAA,IAAI,EAAE,IAAI,CAAC4pB,IAAI,GACTnX,QAAA,CAAc;UAAEoX,GAAG,EAAE,IAAI,CAACD,IAAI;UAAEE,MAAM,EAAE,IAAI,CAACC,WAAAA;SAAa,EAAE/pB,IAAI,CAAC,GACjEA,IAAAA;EACV,KAAC,CAAC,CAAA;EACN,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAA+L,EAAAA,MAAA,CAMA8H,OAAO,GAAP,SAAAA,OAAAA,CAAQ9E,GAAG,EAAE;EACT,IAAA,IAAI,CAAC,IAAI,CAAC2X,SAAS,EAAE;EACjB,MAAA,IAAI,CAACze,YAAY,CAAC,eAAe,EAAE8G,GAAG,CAAC,CAAA;EAC3C,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA,MANI;IAAAhD,MAAA,CAOA0H,OAAO,GAAP,SAAAA,QAAQxI,MAAM,EAAEC,WAAW,EAAE;EACzBuS,IAAAA,OAAK,CAAC,YAAY,EAAExS,MAAM,CAAC,CAAA;MAC3B,IAAI,CAACyb,SAAS,GAAG,KAAK,CAAA;MACtB,OAAO,IAAI,CAACxN,EAAE,CAAA;MACd,IAAI,CAACjR,YAAY,CAAC,YAAY,EAAEgD,MAAM,EAAEC,WAAW,CAAC,CAAA;MACpD,IAAI,CAAC8e,UAAU,EAAE,CAAA;EACrB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAAje,EAAAA,MAAA,CAMAie,UAAU,GAAV,SAAAA,aAAa;EAAA,IAAA,IAAAzX,MAAA,GAAA,IAAA,CAAA;EACT/S,IAAAA,MAAM,CAACG,IAAI,CAAC,IAAI,CAACsnB,IAAI,CAAC,CAACrnB,OAAO,CAAC,UAACsZ,EAAE,EAAK;QACnC,IAAM+Q,UAAU,GAAG1X,MAAI,CAACsU,UAAU,CAACqD,IAAI,CAAC,UAACroB,MAAM,EAAA;EAAA,QAAA,OAAKgC,MAAM,CAAChC,MAAM,CAACqX,EAAE,CAAC,KAAKA,EAAE,CAAA;SAAC,CAAA,CAAA;QAC7E,IAAI,CAAC+Q,UAAU,EAAE;EACb;EACA,QAAA,IAAMjC,GAAG,GAAGzV,MAAI,CAAC0U,IAAI,CAAC/N,EAAE,CAAC,CAAA;EACzB,QAAA,OAAO3G,MAAI,CAAC0U,IAAI,CAAC/N,EAAE,CAAC,CAAA;UACpB,IAAI8O,GAAG,CAACY,SAAS,EAAE;YACfZ,GAAG,CAAC3nB,IAAI,CAACkS,MAAI,EAAE,IAAIhH,KAAK,CAAC,8BAA8B,CAAC,CAAC,CAAA;EAC7D,SAAA;EACJ,OAAA;EACJ,KAAC,CAAC,CAAA;EACN,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAAQ,EAAAA,MAAA,CAMAwb,QAAQ,GAAR,SAAAA,QAAAA,CAAS1lB,MAAM,EAAE;MACb,IAAMsoB,aAAa,GAAGtoB,MAAM,CAACsiB,GAAG,KAAK,IAAI,CAACA,GAAG,CAAA;MAC7C,IAAI,CAACgG,aAAa,EACd,OAAA;MACJ,QAAQtoB,MAAM,CAAC9B,IAAI;QACf,KAAK4jB,UAAU,CAAC0B,OAAO;UACnB,IAAIxjB,MAAM,CAAC7B,IAAI,IAAI6B,MAAM,CAAC7B,IAAI,CAACyO,GAAG,EAAE;EAChC,UAAA,IAAI,CAAC2b,SAAS,CAACvoB,MAAM,CAAC7B,IAAI,CAACyO,GAAG,EAAE5M,MAAM,CAAC7B,IAAI,CAAC6pB,GAAG,CAAC,CAAA;EACpD,SAAC,MACI;YACD,IAAI,CAAC5hB,YAAY,CAAC,eAAe,EAAE,IAAIsD,KAAK,CAAC,2LAA2L,CAAC,CAAC,CAAA;EAC9O,SAAA;EACA,QAAA,MAAA;QACJ,KAAKoY,UAAU,CAACG,KAAK,CAAA;QACrB,KAAKH,UAAU,CAACM,YAAY;EACxB,QAAA,IAAI,CAACoG,OAAO,CAACxoB,MAAM,CAAC,CAAA;EACpB,QAAA,MAAA;QACJ,KAAK8hB,UAAU,CAACI,GAAG,CAAA;QACnB,KAAKJ,UAAU,CAACO,UAAU;EACtB,QAAA,IAAI,CAACoG,KAAK,CAACzoB,MAAM,CAAC,CAAA;EAClB,QAAA,MAAA;QACJ,KAAK8hB,UAAU,CAAC4B,UAAU;UACtB,IAAI,CAACgF,YAAY,EAAE,CAAA;EACnB,QAAA,MAAA;QACJ,KAAK5G,UAAU,CAAC6B,aAAa;UACzB,IAAI,CAACxH,OAAO,EAAE,CAAA;UACd,IAAMjP,GAAG,GAAG,IAAIxD,KAAK,CAAC1J,MAAM,CAAC7B,IAAI,CAACwgB,OAAO,CAAC,CAAA;EAC1C;EACAzR,QAAAA,GAAG,CAAC/O,IAAI,GAAG6B,MAAM,CAAC7B,IAAI,CAACA,IAAI,CAAA;EAC3B,QAAA,IAAI,CAACiI,YAAY,CAAC,eAAe,EAAE8G,GAAG,CAAC,CAAA;EACvC,QAAA,MAAA;EACR,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAAhD,EAAAA,MAAA,CAMAse,OAAO,GAAP,SAAAA,OAAAA,CAAQxoB,MAAM,EAAE;EACZ,IAAA,IAAMmG,IAAI,GAAGnG,MAAM,CAAC7B,IAAI,IAAI,EAAE,CAAA;EAC9Byd,IAAAA,OAAK,CAAC,mBAAmB,EAAEzV,IAAI,CAAC,CAAA;EAChC,IAAA,IAAI,IAAI,IAAInG,MAAM,CAACqX,EAAE,EAAE;QACnBuE,OAAK,CAAC,iCAAiC,CAAC,CAAA;QACxCzV,IAAI,CAACxD,IAAI,CAAC,IAAI,CAACwjB,GAAG,CAACnmB,MAAM,CAACqX,EAAE,CAAC,CAAC,CAAA;EAClC,KAAA;MACA,IAAI,IAAI,CAACwN,SAAS,EAAE;EAChB,MAAA,IAAI,CAAC8D,SAAS,CAACxiB,IAAI,CAAC,CAAA;EACxB,KAAC,MACI;QACD,IAAI,CAAC4e,aAAa,CAACpiB,IAAI,CAAChF,MAAM,CAAC2mB,MAAM,CAACne,IAAI,CAAC,CAAC,CAAA;EAChD,KAAA;KACH,CAAA;EAAA+D,EAAAA,MAAA,CACDye,SAAS,GAAT,SAAAA,SAAAA,CAAUxiB,IAAI,EAAE;MACZ,IAAI,IAAI,CAACyiB,aAAa,IAAI,IAAI,CAACA,aAAa,CAACnoB,MAAM,EAAE;QACjD,IAAM4F,SAAS,GAAG,IAAI,CAACuiB,aAAa,CAACzkB,KAAK,EAAE,CAAA;EAAC,MAAA,IAAA0kB,SAAA,GAAAC,0BAAA,CACtBziB,SAAS,CAAA;UAAA0iB,KAAA,CAAA;EAAA,MAAA,IAAA;UAAhC,KAAAF,SAAA,CAAAtO,CAAA,EAAAwO,EAAAA,CAAAA,CAAAA,KAAA,GAAAF,SAAA,CAAAjkB,CAAA,EAAAiP,EAAAA,IAAA,GAAkC;EAAA,UAAA,IAAvB0B,QAAQ,GAAAwT,KAAA,CAAA/b,KAAA,CAAA;EACfuI,UAAAA,QAAQ,CAAC7P,KAAK,CAAC,IAAI,EAAES,IAAI,CAAC,CAAA;EAC9B,SAAA;EAAC,OAAA,CAAA,OAAA+G,GAAA,EAAA;UAAA2b,SAAA,CAAA/Z,CAAA,CAAA5B,GAAA,CAAA,CAAA;EAAA,OAAA,SAAA;EAAA2b,QAAAA,SAAA,CAAAG,CAAA,EAAA,CAAA;EAAA,OAAA;EACL,KAAA;MACApf,QAAA,CAAAtL,SAAA,CAAM4H,IAAI,CAACR,KAAK,CAAC,IAAI,EAAES,IAAI,CAAC,CAAA;EAC5B,IAAA,IAAI,IAAI,CAAC4hB,IAAI,IAAI5hB,IAAI,CAAC1F,MAAM,IAAI,OAAO0F,IAAI,CAACA,IAAI,CAAC1F,MAAM,GAAG,CAAC,CAAC,KAAK,QAAQ,EAAE;QACvE,IAAI,CAACynB,WAAW,GAAG/hB,IAAI,CAACA,IAAI,CAAC1F,MAAM,GAAG,CAAC,CAAC,CAAA;EAC5C,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAyJ,EAAAA,MAAA,CAKAic,GAAG,GAAH,SAAAA,GAAAA,CAAI9O,EAAE,EAAE;MACJ,IAAMxQ,IAAI,GAAG,IAAI,CAAA;MACjB,IAAIoiB,IAAI,GAAG,KAAK,CAAA;EAChB,IAAA,OAAO,YAAmB;EACtB;EACA,MAAA,IAAIA,IAAI,EACJ,OAAA;EACJA,MAAAA,IAAI,GAAG,IAAI,CAAA;EAAC,MAAA,KAAA,IAAAC,KAAA,GAAAvjB,SAAA,CAAAlF,MAAA,EAJI0F,IAAI,GAAA9D,IAAAA,KAAA,CAAA6mB,KAAA,GAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,EAAA;EAAJhjB,QAAAA,IAAI,CAAAgjB,KAAA,CAAAxjB,GAAAA,SAAA,CAAAwjB,KAAA,CAAA,CAAA;EAAA,OAAA;EAKpBvN,MAAAA,OAAK,CAAC,gBAAgB,EAAEzV,IAAI,CAAC,CAAA;QAC7BU,IAAI,CAAC7G,MAAM,CAAC;UACR9B,IAAI,EAAE4jB,UAAU,CAACI,GAAG;EACpB7K,QAAAA,EAAE,EAAEA,EAAE;EACNlZ,QAAAA,IAAI,EAAEgI,IAAAA;EACV,OAAC,CAAC,CAAA;OACL,CAAA;EACL,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAA+D,EAAAA,MAAA,CAMAue,KAAK,GAAL,SAAAA,KAAAA,CAAMzoB,MAAM,EAAE;MACV,IAAMmmB,GAAG,GAAG,IAAI,CAACf,IAAI,CAACplB,MAAM,CAACqX,EAAE,CAAC,CAAA;EAChC,IAAA,IAAI,OAAO8O,GAAG,KAAK,UAAU,EAAE;EAC3BvK,MAAAA,OAAK,CAAC,YAAY,EAAE5b,MAAM,CAACqX,EAAE,CAAC,CAAA;EAC9B,MAAA,OAAA;EACJ,KAAA;EACA,IAAA,OAAO,IAAI,CAAC+N,IAAI,CAACplB,MAAM,CAACqX,EAAE,CAAC,CAAA;MAC3BuE,OAAK,CAAC,wBAAwB,EAAE5b,MAAM,CAACqX,EAAE,EAAErX,MAAM,CAAC7B,IAAI,CAAC,CAAA;EACvD;MACA,IAAIgoB,GAAG,CAACY,SAAS,EAAE;EACf/mB,MAAAA,MAAM,CAAC7B,IAAI,CAAC8e,OAAO,CAAC,IAAI,CAAC,CAAA;EAC7B,KAAA;EACA;MACAkJ,GAAG,CAACzgB,KAAK,CAAC,IAAI,EAAE1F,MAAM,CAAC7B,IAAI,CAAC,CAAA;EAChC,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;IAAA+L,MAAA,CAKAqe,SAAS,GAAT,SAAAA,UAAUlR,EAAE,EAAE2Q,GAAG,EAAE;EACfpM,IAAAA,OAAK,CAAC,6BAA6B,EAAEvE,EAAE,CAAC,CAAA;MACxC,IAAI,CAACA,EAAE,GAAGA,EAAE,CAAA;MACZ,IAAI,CAACyN,SAAS,GAAGkD,GAAG,IAAI,IAAI,CAACD,IAAI,KAAKC,GAAG,CAAA;EACzC,IAAA,IAAI,CAACD,IAAI,GAAGC,GAAG,CAAC;MAChB,IAAI,CAACnD,SAAS,GAAG,IAAI,CAAA;MACrB,IAAI,CAACuE,YAAY,EAAE,CAAA;EACnB,IAAA,IAAI,CAAChjB,YAAY,CAAC,SAAS,CAAC,CAAA;EAC5B,IAAA,IAAI,CAACwhB,WAAW,CAAC,IAAI,CAAC,CAAA;EAC1B,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAA1d,EAAAA,MAAA,CAKAkf,YAAY,GAAZ,SAAAA,eAAe;EAAA,IAAA,IAAAlQ,MAAA,GAAA,IAAA,CAAA;EACX,IAAA,IAAI,CAAC6L,aAAa,CAAChnB,OAAO,CAAC,UAACoI,IAAI,EAAA;EAAA,MAAA,OAAK+S,MAAI,CAACyP,SAAS,CAACxiB,IAAI,CAAC,CAAA;OAAC,CAAA,CAAA;MAC1D,IAAI,CAAC4e,aAAa,GAAG,EAAE,CAAA;EACvB,IAAA,IAAI,CAACC,UAAU,CAACjnB,OAAO,CAAC,UAACiC,MAAM,EAAK;EAChCkZ,MAAAA,MAAI,CAACwN,uBAAuB,CAAC1mB,MAAM,CAAC,CAAA;EACpCkZ,MAAAA,MAAI,CAAClZ,MAAM,CAACA,MAAM,CAAC,CAAA;EACvB,KAAC,CAAC,CAAA;MACF,IAAI,CAACglB,UAAU,GAAG,EAAE,CAAA;EACxB,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAA9a,EAAAA,MAAA,CAKAwe,YAAY,GAAZ,SAAAA,eAAe;EACX9M,IAAAA,OAAK,CAAC,wBAAwB,EAAE,IAAI,CAAC0G,GAAG,CAAC,CAAA;MACzC,IAAI,CAACnG,OAAO,EAAE,CAAA;EACd,IAAA,IAAI,CAACvK,OAAO,CAAC,sBAAsB,CAAC,CAAA;EACxC,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA,MANI;EAAA1H,EAAAA,MAAA,CAOAiS,OAAO,GAAP,SAAAA,UAAU;MACN,IAAI,IAAI,CAACsJ,IAAI,EAAE;EACX;EACA,MAAA,IAAI,CAACA,IAAI,CAAC1nB,OAAO,CAAC,UAACsmB,UAAU,EAAA;UAAA,OAAKA,UAAU,EAAE,CAAA;SAAC,CAAA,CAAA;QAC/C,IAAI,CAACoB,IAAI,GAAGra,SAAS,CAAA;EACzB,KAAA;EACA,IAAA,IAAI,CAACwZ,EAAE,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAA;EAC7B,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAfI;EAAA1a,EAAAA,MAAA,CAgBAua,UAAU,GAAV,SAAAA,aAAa;MACT,IAAI,IAAI,CAACI,SAAS,EAAE;EAChBjJ,MAAAA,OAAK,CAAC,4BAA4B,EAAE,IAAI,CAAC0G,GAAG,CAAC,CAAA;QAC7C,IAAI,CAACtiB,MAAM,CAAC;UAAE9B,IAAI,EAAE4jB,UAAU,CAAC4B,UAAAA;EAAW,OAAC,CAAC,CAAA;EAChD,KAAA;EACA;MACA,IAAI,CAACvH,OAAO,EAAE,CAAA;MACd,IAAI,IAAI,CAAC0I,SAAS,EAAE;EAChB;EACA,MAAA,IAAI,CAACjT,OAAO,CAAC,sBAAsB,CAAC,CAAA;EACxC,KAAA;EACA,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAA1H,EAAAA,MAAA,CAKAK,KAAK,GAAL,SAAAA,QAAQ;EACJ,IAAA,OAAO,IAAI,CAACka,UAAU,EAAE,CAAA;EAC5B,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MARI;EAAAva,EAAAA,MAAA,CASA0O,QAAQ,GAAR,SAAAA,QAAAA,CAASA,SAAQ,EAAE;EACf,IAAA,IAAI,CAACyM,KAAK,CAACzM,QAAQ,GAAGA,SAAQ,CAAA;EAC9B,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MARI;EAaA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAZI1O,EAAAA,MAAA,CAaAiF,OAAO,GAAP,SAAAA,OAAAA,CAAQA,QAAO,EAAE;EACb,IAAA,IAAI,CAACkW,KAAK,CAAClW,OAAO,GAAGA,QAAO,CAAA;EAC5B,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAVI;EAAAjF,EAAAA,MAAA,CAWAmf,KAAK,GAAL,SAAAA,KAAAA,CAAM9T,QAAQ,EAAE;EACZ,IAAA,IAAI,CAACqT,aAAa,GAAG,IAAI,CAACA,aAAa,IAAI,EAAE,CAAA;EAC7C,IAAA,IAAI,CAACA,aAAa,CAACjmB,IAAI,CAAC4S,QAAQ,CAAC,CAAA;EACjC,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAVI;EAAArL,EAAAA,MAAA,CAWAof,UAAU,GAAV,SAAAA,UAAAA,CAAW/T,QAAQ,EAAE;EACjB,IAAA,IAAI,CAACqT,aAAa,GAAG,IAAI,CAACA,aAAa,IAAI,EAAE,CAAA;EAC7C,IAAA,IAAI,CAACA,aAAa,CAAC3L,OAAO,CAAC1H,QAAQ,CAAC,CAAA;EACpC,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAjBI;EAAArL,EAAAA,MAAA,CAkBAqf,MAAM,GAAN,SAAAA,MAAAA,CAAOhU,QAAQ,EAAE;EACb,IAAA,IAAI,CAAC,IAAI,CAACqT,aAAa,EAAE;EACrB,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA,IAAA,IAAIrT,QAAQ,EAAE;EACV,MAAA,IAAMlP,SAAS,GAAG,IAAI,CAACuiB,aAAa,CAAA;EACpC,MAAA,KAAK,IAAIpoB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6F,SAAS,CAAC5F,MAAM,EAAED,CAAC,EAAE,EAAE;EACvC,QAAA,IAAI+U,QAAQ,KAAKlP,SAAS,CAAC7F,CAAC,CAAC,EAAE;EAC3B6F,UAAAA,SAAS,CAACJ,MAAM,CAACzF,CAAC,EAAE,CAAC,CAAC,CAAA;EACtB,UAAA,OAAO,IAAI,CAAA;EACf,SAAA;EACJ,OAAA;EACJ,KAAC,MACI;QACD,IAAI,CAACooB,aAAa,GAAG,EAAE,CAAA;EAC3B,KAAA;EACA,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA,MAHI;EAAA1e,EAAAA,MAAA,CAIAsf,YAAY,GAAZ,SAAAA,eAAe;EACX,IAAA,OAAO,IAAI,CAACZ,aAAa,IAAI,EAAE,CAAA;EACnC,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAZI;EAAA1e,EAAAA,MAAA,CAaAuf,aAAa,GAAb,SAAAA,aAAAA,CAAclU,QAAQ,EAAE;EACpB,IAAA,IAAI,CAACmU,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,IAAI,EAAE,CAAA;EAC7D,IAAA,IAAI,CAACA,qBAAqB,CAAC/mB,IAAI,CAAC4S,QAAQ,CAAC,CAAA;EACzC,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAZI;EAAArL,EAAAA,MAAA,CAaAyf,kBAAkB,GAAlB,SAAAA,kBAAAA,CAAmBpU,QAAQ,EAAE;EACzB,IAAA,IAAI,CAACmU,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,IAAI,EAAE,CAAA;EAC7D,IAAA,IAAI,CAACA,qBAAqB,CAACzM,OAAO,CAAC1H,QAAQ,CAAC,CAAA;EAC5C,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAjBI;EAAArL,EAAAA,MAAA,CAkBA0f,cAAc,GAAd,SAAAA,cAAAA,CAAerU,QAAQ,EAAE;EACrB,IAAA,IAAI,CAAC,IAAI,CAACmU,qBAAqB,EAAE;EAC7B,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA,IAAA,IAAInU,QAAQ,EAAE;EACV,MAAA,IAAMlP,SAAS,GAAG,IAAI,CAACqjB,qBAAqB,CAAA;EAC5C,MAAA,KAAK,IAAIlpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6F,SAAS,CAAC5F,MAAM,EAAED,CAAC,EAAE,EAAE;EACvC,QAAA,IAAI+U,QAAQ,KAAKlP,SAAS,CAAC7F,CAAC,CAAC,EAAE;EAC3B6F,UAAAA,SAAS,CAACJ,MAAM,CAACzF,CAAC,EAAE,CAAC,CAAC,CAAA;EACtB,UAAA,OAAO,IAAI,CAAA;EACf,SAAA;EACJ,OAAA;EACJ,KAAC,MACI;QACD,IAAI,CAACkpB,qBAAqB,GAAG,EAAE,CAAA;EACnC,KAAA;EACA,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA,MAHI;EAAAxf,EAAAA,MAAA,CAIA2f,oBAAoB,GAApB,SAAAA,uBAAuB;EACnB,IAAA,OAAO,IAAI,CAACH,qBAAqB,IAAI,EAAE,CAAA;EAC3C,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA,MANI;EAAAxf,EAAAA,MAAA,CAOAwc,uBAAuB,GAAvB,SAAAA,uBAAAA,CAAwB1mB,MAAM,EAAE;MAC5B,IAAI,IAAI,CAAC0pB,qBAAqB,IAAI,IAAI,CAACA,qBAAqB,CAACjpB,MAAM,EAAE;QACjE,IAAM4F,SAAS,GAAG,IAAI,CAACqjB,qBAAqB,CAACvlB,KAAK,EAAE,CAAA;EAAC,MAAA,IAAA2lB,UAAA,GAAAhB,0BAAA,CAC9BziB,SAAS,CAAA;UAAA0jB,MAAA,CAAA;EAAA,MAAA,IAAA;UAAhC,KAAAD,UAAA,CAAAvP,CAAA,EAAAwP,EAAAA,CAAAA,CAAAA,MAAA,GAAAD,UAAA,CAAAllB,CAAA,EAAAiP,EAAAA,IAAA,GAAkC;EAAA,UAAA,IAAvB0B,QAAQ,GAAAwU,MAAA,CAAA/c,KAAA,CAAA;YACfuI,QAAQ,CAAC7P,KAAK,CAAC,IAAI,EAAE1F,MAAM,CAAC7B,IAAI,CAAC,CAAA;EACrC,SAAA;EAAC,OAAA,CAAA,OAAA+O,GAAA,EAAA;UAAA4c,UAAA,CAAAhb,CAAA,CAAA5B,GAAA,CAAA,CAAA;EAAA,OAAA,SAAA;EAAA4c,QAAAA,UAAA,CAAAd,CAAA,EAAA,CAAA;EAAA,OAAA;EACL,KAAA;KACH,CAAA;IAAA,OAAAlc,YAAA,CAAAmN,MAAA,EAAA,CAAA;MAAAjc,GAAA,EAAA,cAAA;MAAA+O,GAAA,EA5vBD,SAAAA,GAAAA,GAAmB;QACf,OAAO,CAAC,IAAI,CAAC8X,SAAS,CAAA;EAC1B,KAAA;EAAC,GAAA,EAAA;MAAA7mB,GAAA,EAAA,QAAA;MAAA+O,GAAA,EAkCD,SAAAA,GAAAA,GAAa;EACT,MAAA,OAAO,CAAC,CAAC,IAAI,CAAC0Y,IAAI,CAAA;EACtB,KAAA;EAAC,GAAA,EAAA;MAAAznB,GAAA,EAAA,UAAA;MAAA+O,GAAA,EAyhBD,SAAAA,GAAAA,GAAe;EACX,MAAA,IAAI,CAACsY,KAAK,CAAS,UAAA,CAAA,GAAG,IAAI,CAAA;EAC1B,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EAAC,GAAA,CAAA,CAAA,CAAA;EAAA,CAAA,CAjqBuBpgB,OAAO,CAAA;;EC1CnC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAAS+kB,OAAOA,CAACliB,IAAI,EAAE;EAC1BA,EAAAA,IAAI,GAAGA,IAAI,IAAI,EAAE,CAAA;EACjB,EAAA,IAAI,CAAC8S,EAAE,GAAG9S,IAAI,CAACmiB,GAAG,IAAI,GAAG,CAAA;EACzB,EAAA,IAAI,CAACC,GAAG,GAAGpiB,IAAI,CAACoiB,GAAG,IAAI,KAAK,CAAA;EAC5B,EAAA,IAAI,CAACC,MAAM,GAAGriB,IAAI,CAACqiB,MAAM,IAAI,CAAC,CAAA;EAC9B,EAAA,IAAI,CAACC,MAAM,GAAGtiB,IAAI,CAACsiB,MAAM,GAAG,CAAC,IAAItiB,IAAI,CAACsiB,MAAM,IAAI,CAAC,GAAGtiB,IAAI,CAACsiB,MAAM,GAAG,CAAC,CAAA;IACnE,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAA;EACrB,CAAA;EACA;EACA;EACA;EACA;EACA;EACA;EACAL,OAAO,CAAC1rB,SAAS,CAACgsB,QAAQ,GAAG,YAAY;EACrC,EAAA,IAAI1P,EAAE,GAAG,IAAI,CAACA,EAAE,GAAG9V,IAAI,CAACC,GAAG,CAAC,IAAI,CAAColB,MAAM,EAAE,IAAI,CAACE,QAAQ,EAAE,CAAC,CAAA;IACzD,IAAI,IAAI,CAACD,MAAM,EAAE;EACb,IAAA,IAAIG,IAAI,GAAGzlB,IAAI,CAAC6D,MAAM,EAAE,CAAA;EACxB,IAAA,IAAI6hB,SAAS,GAAG1lB,IAAI,CAACmf,KAAK,CAACsG,IAAI,GAAG,IAAI,CAACH,MAAM,GAAGxP,EAAE,CAAC,CAAA;MACnDA,EAAE,GAAG,CAAC9V,IAAI,CAACmf,KAAK,CAACsG,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG3P,EAAE,GAAG4P,SAAS,GAAG5P,EAAE,GAAG4P,SAAS,CAAA;EAC3E,GAAA;IACA,OAAO1lB,IAAI,CAACmlB,GAAG,CAACrP,EAAE,EAAE,IAAI,CAACsP,GAAG,CAAC,GAAG,CAAC,CAAA;EACrC,CAAC,CAAA;EACD;EACA;EACA;EACA;EACA;EACAF,OAAO,CAAC1rB,SAAS,CAACmsB,KAAK,GAAG,YAAY;IAClC,IAAI,CAACJ,QAAQ,GAAG,CAAC,CAAA;EACrB,CAAC,CAAA;EACD;EACA;EACA;EACA;EACA;EACAL,OAAO,CAAC1rB,SAAS,CAACosB,MAAM,GAAG,UAAUT,GAAG,EAAE;IACtC,IAAI,CAACrP,EAAE,GAAGqP,GAAG,CAAA;EACjB,CAAC,CAAA;EACD;EACA;EACA;EACA;EACA;EACAD,OAAO,CAAC1rB,SAAS,CAACqsB,MAAM,GAAG,UAAUT,GAAG,EAAE;IACtC,IAAI,CAACA,GAAG,GAAGA,GAAG,CAAA;EAClB,CAAC,CAAA;EACD;EACA;EACA;EACA;EACA;EACAF,OAAO,CAAC1rB,SAAS,CAACssB,SAAS,GAAG,UAAUR,MAAM,EAAE;IAC5C,IAAI,CAACA,MAAM,GAAGA,MAAM,CAAA;EACxB,CAAC;;EC1DD,IAAMxO,OAAK,GAAG0E,WAAW,CAAC,0BAA0B,CAAC,CAAC;EACzCuK,IAAAA,OAAO,0BAAAjhB,QAAA,EAAA;EAChB,EAAA,SAAAihB,OAAYpe,CAAAA,GAAG,EAAE3E,IAAI,EAAE;EAAA,IAAA,IAAAyB,KAAA,CAAA;EACnB,IAAA,IAAIgF,EAAE,CAAA;EACNhF,IAAAA,KAAA,GAAAK,QAAA,CAAApL,IAAA,KAAM,CAAC,IAAA,IAAA,CAAA;EACP+K,IAAAA,KAAA,CAAKuhB,IAAI,GAAG,EAAE,CAAA;MACdvhB,KAAA,CAAKkc,IAAI,GAAG,EAAE,CAAA;EACd,IAAA,IAAIhZ,GAAG,IAAI,QAAQ,KAAAuJ,OAAA,CAAYvJ,GAAG,CAAE,EAAA;EAChC3E,MAAAA,IAAI,GAAG2E,GAAG,CAAA;EACVA,MAAAA,GAAG,GAAGrB,SAAS,CAAA;EACnB,KAAA;EACAtD,IAAAA,IAAI,GAAGA,IAAI,IAAI,EAAE,CAAA;EACjBA,IAAAA,IAAI,CAACyD,IAAI,GAAGzD,IAAI,CAACyD,IAAI,IAAI,YAAY,CAAA;MACrChC,KAAA,CAAKzB,IAAI,GAAGA,IAAI,CAAA;EAChBD,IAAAA,qBAAqB,CAAA0B,KAAA,EAAOzB,IAAI,CAAC,CAAA;MACjCyB,KAAA,CAAKwhB,YAAY,CAACjjB,IAAI,CAACijB,YAAY,KAAK,KAAK,CAAC,CAAA;MAC9CxhB,KAAA,CAAKyhB,oBAAoB,CAACljB,IAAI,CAACkjB,oBAAoB,IAAIjV,QAAQ,CAAC,CAAA;MAChExM,KAAA,CAAK0hB,iBAAiB,CAACnjB,IAAI,CAACmjB,iBAAiB,IAAI,IAAI,CAAC,CAAA;MACtD1hB,KAAA,CAAK2hB,oBAAoB,CAACpjB,IAAI,CAACojB,oBAAoB,IAAI,IAAI,CAAC,CAAA;MAC5D3hB,KAAA,CAAK4hB,mBAAmB,CAAC,CAAC5c,EAAE,GAAGzG,IAAI,CAACqjB,mBAAmB,MAAM,IAAI,IAAI5c,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG,CAAC,CAAA;EAC9FhF,IAAAA,KAAA,CAAK6hB,OAAO,GAAG,IAAIpB,OAAO,CAAC;EACvBC,MAAAA,GAAG,EAAE1gB,KAAA,CAAK0hB,iBAAiB,EAAE;EAC7Bf,MAAAA,GAAG,EAAE3gB,KAAA,CAAK2hB,oBAAoB,EAAE;EAChCd,MAAAA,MAAM,EAAE7gB,KAAA,CAAK4hB,mBAAmB,EAAC;EACrC,KAAC,CAAC,CAAA;EACF5hB,IAAAA,KAAA,CAAK4F,OAAO,CAAC,IAAI,IAAIrH,IAAI,CAACqH,OAAO,GAAG,KAAK,GAAGrH,IAAI,CAACqH,OAAO,CAAC,CAAA;MACzD5F,KAAA,CAAKoc,WAAW,GAAG,QAAQ,CAAA;MAC3Bpc,KAAA,CAAKkD,GAAG,GAAGA,GAAG,CAAA;EACd,IAAA,IAAM4e,OAAO,GAAGvjB,IAAI,CAACwjB,MAAM,IAAIA,MAAM,CAAA;MACrC/hB,KAAA,CAAKgiB,OAAO,GAAG,IAAIF,OAAO,CAACtJ,OAAO,EAAE,CAAA;MACpCxY,KAAA,CAAKiiB,OAAO,GAAG,IAAIH,OAAO,CAAC5I,OAAO,EAAE,CAAA;EACpClZ,IAAAA,KAAA,CAAKgc,YAAY,GAAGzd,IAAI,CAAC2jB,WAAW,KAAK,KAAK,CAAA;MAC9C,IAAIliB,KAAA,CAAKgc,YAAY,EACjBhc,KAAA,CAAKa,IAAI,EAAE,CAAA;EAAC,IAAA,OAAAb,KAAA,CAAA;EACpB,GAAA;IAACC,cAAA,CAAAqhB,OAAA,EAAAjhB,QAAA,CAAA,CAAA;EAAA,EAAA,IAAAM,MAAA,GAAA2gB,OAAA,CAAAvsB,SAAA,CAAA;EAAA4L,EAAAA,MAAA,CACD6gB,YAAY,GAAZ,SAAAA,YAAAA,CAAa/M,CAAC,EAAE;MACZ,IAAI,CAACrY,SAAS,CAAClF,MAAM,EACjB,OAAO,IAAI,CAACirB,aAAa,CAAA;EAC7B,IAAA,IAAI,CAACA,aAAa,GAAG,CAAC,CAAC1N,CAAC,CAAA;MACxB,IAAI,CAACA,CAAC,EAAE;QACJ,IAAI,CAAC2N,aAAa,GAAG,IAAI,CAAA;EAC7B,KAAA;EACA,IAAA,OAAO,IAAI,CAAA;KACd,CAAA;EAAAzhB,EAAAA,MAAA,CACD8gB,oBAAoB,GAApB,SAAAA,oBAAAA,CAAqBhN,CAAC,EAAE;EACpB,IAAA,IAAIA,CAAC,KAAK5S,SAAS,EACf,OAAO,IAAI,CAACwgB,qBAAqB,CAAA;MACrC,IAAI,CAACA,qBAAqB,GAAG5N,CAAC,CAAA;EAC9B,IAAA,OAAO,IAAI,CAAA;KACd,CAAA;EAAA9T,EAAAA,MAAA,CACD+gB,iBAAiB,GAAjB,SAAAA,iBAAAA,CAAkBjN,CAAC,EAAE;EACjB,IAAA,IAAIzP,EAAE,CAAA;EACN,IAAA,IAAIyP,CAAC,KAAK5S,SAAS,EACf,OAAO,IAAI,CAACygB,kBAAkB,CAAA;MAClC,IAAI,CAACA,kBAAkB,GAAG7N,CAAC,CAAA;MAC3B,CAACzP,EAAE,GAAG,IAAI,CAAC6c,OAAO,MAAM,IAAI,IAAI7c,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmc,MAAM,CAAC1M,CAAC,CAAC,CAAA;EACrE,IAAA,OAAO,IAAI,CAAA;KACd,CAAA;EAAA9T,EAAAA,MAAA,CACDihB,mBAAmB,GAAnB,SAAAA,mBAAAA,CAAoBnN,CAAC,EAAE;EACnB,IAAA,IAAIzP,EAAE,CAAA;EACN,IAAA,IAAIyP,CAAC,KAAK5S,SAAS,EACf,OAAO,IAAI,CAAC0gB,oBAAoB,CAAA;MACpC,IAAI,CAACA,oBAAoB,GAAG9N,CAAC,CAAA;MAC7B,CAACzP,EAAE,GAAG,IAAI,CAAC6c,OAAO,MAAM,IAAI,IAAI7c,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqc,SAAS,CAAC5M,CAAC,CAAC,CAAA;EACxE,IAAA,OAAO,IAAI,CAAA;KACd,CAAA;EAAA9T,EAAAA,MAAA,CACDghB,oBAAoB,GAApB,SAAAA,oBAAAA,CAAqBlN,CAAC,EAAE;EACpB,IAAA,IAAIzP,EAAE,CAAA;EACN,IAAA,IAAIyP,CAAC,KAAK5S,SAAS,EACf,OAAO,IAAI,CAAC2gB,qBAAqB,CAAA;MACrC,IAAI,CAACA,qBAAqB,GAAG/N,CAAC,CAAA;MAC9B,CAACzP,EAAE,GAAG,IAAI,CAAC6c,OAAO,MAAM,IAAI,IAAI7c,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACoc,MAAM,CAAC3M,CAAC,CAAC,CAAA;EACrE,IAAA,OAAO,IAAI,CAAA;KACd,CAAA;EAAA9T,EAAAA,MAAA,CACDiF,OAAO,GAAP,SAAAA,OAAAA,CAAQ6O,CAAC,EAAE;MACP,IAAI,CAACrY,SAAS,CAAClF,MAAM,EACjB,OAAO,IAAI,CAACurB,QAAQ,CAAA;MACxB,IAAI,CAACA,QAAQ,GAAGhO,CAAC,CAAA;EACjB,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAA9T,EAAAA,MAAA,CAMA+hB,oBAAoB,GAApB,SAAAA,uBAAuB;EACnB;EACA,IAAA,IAAI,CAAC,IAAI,CAACC,aAAa,IACnB,IAAI,CAACR,aAAa,IAClB,IAAI,CAACN,OAAO,CAACf,QAAQ,KAAK,CAAC,EAAE;EAC7B;QACA,IAAI,CAAC8B,SAAS,EAAE,CAAA;EACpB,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA,MANI;EAAAjiB,EAAAA,MAAA,CAOAE,IAAI,GAAJ,SAAAA,IAAAA,CAAK9E,EAAE,EAAE;EAAA,IAAA,IAAAuE,MAAA,GAAA,IAAA,CAAA;EACL+R,IAAAA,OAAK,CAAC,eAAe,EAAE,IAAI,CAAC+J,WAAW,CAAC,CAAA;MACxC,IAAI,CAAC,IAAI,CAACA,WAAW,CAACja,OAAO,CAAC,MAAM,CAAC,EACjC,OAAO,IAAI,CAAA;EACfkQ,IAAAA,OAAK,CAAC,YAAY,EAAE,IAAI,CAACnP,GAAG,CAAC,CAAA;EAC7B,IAAA,IAAI,CAAC8Z,MAAM,GAAG,IAAI6F,QAAM,CAAC,IAAI,CAAC3f,GAAG,EAAE,IAAI,CAAC3E,IAAI,CAAC,CAAA;EAC7C,IAAA,IAAMkC,MAAM,GAAG,IAAI,CAACuc,MAAM,CAAA;MAC1B,IAAM1f,IAAI,GAAG,IAAI,CAAA;MACjB,IAAI,CAAC8e,WAAW,GAAG,SAAS,CAAA;MAC5B,IAAI,CAACgG,aAAa,GAAG,KAAK,CAAA;EAC1B;MACA,IAAMU,cAAc,GAAGlnB,EAAE,CAAC6E,MAAM,EAAE,MAAM,EAAE,YAAY;QAClDnD,IAAI,CAAC2K,MAAM,EAAE,CAAA;QACblM,EAAE,IAAIA,EAAE,EAAE,CAAA;EACd,KAAC,CAAC,CAAA;EACF,IAAA,IAAM6E,OAAO,GAAG,SAAVA,OAAOA,CAAI+C,GAAG,EAAK;QACrB0O,OAAK,CAAC,OAAO,CAAC,CAAA;QACd/R,MAAI,CAAC2P,OAAO,EAAE,CAAA;QACd3P,MAAI,CAAC8b,WAAW,GAAG,QAAQ,CAAA;EAC3B9b,MAAAA,MAAI,CAACzD,YAAY,CAAC,OAAO,EAAE8G,GAAG,CAAC,CAAA;EAC/B,MAAA,IAAI5H,EAAE,EAAE;UACJA,EAAE,CAAC4H,GAAG,CAAC,CAAA;EACX,OAAC,MACI;EACD;UACArD,MAAI,CAACoiB,oBAAoB,EAAE,CAAA;EAC/B,OAAA;OACH,CAAA;EACD;MACA,IAAMK,QAAQ,GAAGnnB,EAAE,CAAC6E,MAAM,EAAE,OAAO,EAAEG,OAAO,CAAC,CAAA;EAC7C,IAAA,IAAI,KAAK,KAAK,IAAI,CAAC6hB,QAAQ,EAAE;EACzB,MAAA,IAAM7c,OAAO,GAAG,IAAI,CAAC6c,QAAQ,CAAA;EAC7BpQ,MAAAA,OAAK,CAAC,uCAAuC,EAAEzM,OAAO,CAAC,CAAA;EACvD;EACA,MAAA,IAAMyX,KAAK,GAAG,IAAI,CAACjgB,YAAY,CAAC,YAAM;EAClCiV,QAAAA,OAAK,CAAC,oCAAoC,EAAEzM,OAAO,CAAC,CAAA;EACpDkd,QAAAA,cAAc,EAAE,CAAA;EAChBliB,QAAAA,OAAO,CAAC,IAAIT,KAAK,CAAC,SAAS,CAAC,CAAC,CAAA;UAC7BM,MAAM,CAACO,KAAK,EAAE,CAAA;SACjB,EAAE4E,OAAO,CAAC,CAAA;EACX,MAAA,IAAI,IAAI,CAACrH,IAAI,CAAC2J,SAAS,EAAE;UACrBmV,KAAK,CAACjV,KAAK,EAAE,CAAA;EACjB,OAAA;EACA,MAAA,IAAI,CAAC8T,IAAI,CAAC9iB,IAAI,CAAC,YAAM;EACjBkH,QAAAA,MAAI,CAAC5B,cAAc,CAAC2e,KAAK,CAAC,CAAA;EAC9B,OAAC,CAAC,CAAA;EACN,KAAA;EACA,IAAA,IAAI,CAACnB,IAAI,CAAC9iB,IAAI,CAAC0pB,cAAc,CAAC,CAAA;EAC9B,IAAA,IAAI,CAAC5G,IAAI,CAAC9iB,IAAI,CAAC2pB,QAAQ,CAAC,CAAA;EACxB,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAApiB,EAAAA,MAAA,CAMAqa,OAAO,GAAP,SAAAA,OAAAA,CAAQjf,EAAE,EAAE;EACR,IAAA,OAAO,IAAI,CAAC8E,IAAI,CAAC9E,EAAE,CAAC,CAAA;EACxB,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAA4E,EAAAA,MAAA,CAKAsH,MAAM,GAAN,SAAAA,SAAS;MACLoK,OAAK,CAAC,MAAM,CAAC,CAAA;EACb;MACA,IAAI,CAACpC,OAAO,EAAE,CAAA;EACd;MACA,IAAI,CAACmM,WAAW,GAAG,MAAM,CAAA;EACzB,IAAA,IAAI,CAACvf,YAAY,CAAC,MAAM,CAAC,CAAA;EACzB;EACA,IAAA,IAAM4D,MAAM,GAAG,IAAI,CAACuc,MAAM,CAAA;EAC1B,IAAA,IAAI,CAACd,IAAI,CAAC9iB,IAAI,CAACwC,EAAE,CAAC6E,MAAM,EAAE,MAAM,EAAE,IAAI,CAACuiB,MAAM,CAACvkB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE7C,EAAE,CAAC6E,MAAM,EAAE,MAAM,EAAE,IAAI,CAACwiB,MAAM,CAACxkB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE7C,EAAE,CAAC6E,MAAM,EAAE,OAAO,EAAE,IAAI,CAACgI,OAAO,CAAChK,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE7C,EAAE,CAAC6E,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC4H,OAAO,CAAC5J,IAAI,CAAC,IAAI,CAAC,CAAC;EACjM;EACA7C,IAAAA,EAAE,CAAC,IAAI,CAACqmB,OAAO,EAAE,SAAS,EAAE,IAAI,CAACiB,SAAS,CAACzkB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;EAC3D,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAkC,EAAAA,MAAA,CAKAqiB,MAAM,GAAN,SAAAA,SAAS;EACL,IAAA,IAAI,CAACnmB,YAAY,CAAC,MAAM,CAAC,CAAA;EAC7B,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAA8D,EAAAA,MAAA,CAKAsiB,MAAM,GAAN,SAAAA,MAAAA,CAAOruB,IAAI,EAAE;MACT,IAAI;EACA,MAAA,IAAI,CAACqtB,OAAO,CAAC7I,GAAG,CAACxkB,IAAI,CAAC,CAAA;OACzB,CACD,OAAO2Q,CAAC,EAAE;EACN,MAAA,IAAI,CAAC8C,OAAO,CAAC,aAAa,EAAE9C,CAAC,CAAC,CAAA;EAClC,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAA5E,EAAAA,MAAA,CAKAuiB,SAAS,GAAT,SAAAA,SAAAA,CAAUzsB,MAAM,EAAE;EAAA,IAAA,IAAAqM,MAAA,GAAA,IAAA,CAAA;EACd;EACA9F,IAAAA,QAAQ,CAAC,YAAM;EACX8F,MAAAA,MAAI,CAACjG,YAAY,CAAC,QAAQ,EAAEpG,MAAM,CAAC,CAAA;EACvC,KAAC,EAAE,IAAI,CAAC2G,YAAY,CAAC,CAAA;EACzB,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAuD,EAAAA,MAAA,CAKA8H,OAAO,GAAP,SAAAA,OAAAA,CAAQ9E,GAAG,EAAE;EACT0O,IAAAA,OAAK,CAAC,OAAO,EAAE1O,GAAG,CAAC,CAAA;EACnB,IAAA,IAAI,CAAC9G,YAAY,CAAC,OAAO,EAAE8G,GAAG,CAAC,CAAA;EACnC,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;IAAAhD,MAAA,CAMAF,MAAM,GAAN,SAAAA,OAAOsY,GAAG,EAAExa,IAAI,EAAE;EACd,IAAA,IAAIkC,MAAM,GAAG,IAAI,CAAC8gB,IAAI,CAACxI,GAAG,CAAC,CAAA;MAC3B,IAAI,CAACtY,MAAM,EAAE;QACTA,MAAM,GAAG,IAAIiQ,MAAM,CAAC,IAAI,EAAEqI,GAAG,EAAExa,IAAI,CAAC,CAAA;EACpC,MAAA,IAAI,CAACgjB,IAAI,CAACxI,GAAG,CAAC,GAAGtY,MAAM,CAAA;OAC1B,MACI,IAAI,IAAI,CAACub,YAAY,IAAI,CAACvb,MAAM,CAAC0iB,MAAM,EAAE;QAC1C1iB,MAAM,CAACua,OAAO,EAAE,CAAA;EACpB,KAAA;EACA,IAAA,OAAOva,MAAM,CAAA;EACjB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAAE,EAAAA,MAAA,CAMAyiB,QAAQ,GAAR,SAAAA,QAAAA,CAAS3iB,MAAM,EAAE;MACb,IAAM8gB,IAAI,GAAGntB,MAAM,CAACG,IAAI,CAAC,IAAI,CAACgtB,IAAI,CAAC,CAAA;EACnC,IAAA,KAAA,IAAA8B,EAAA,GAAA,CAAA,EAAAC,KAAA,GAAkB/B,IAAI,EAAA8B,EAAA,GAAAC,KAAA,CAAApsB,MAAA,EAAAmsB,EAAA,EAAE,EAAA;EAAnB,MAAA,IAAMtK,GAAG,GAAAuK,KAAA,CAAAD,EAAA,CAAA,CAAA;EACV,MAAA,IAAM5iB,OAAM,GAAG,IAAI,CAAC8gB,IAAI,CAACxI,GAAG,CAAC,CAAA;QAC7B,IAAItY,OAAM,CAAC0iB,MAAM,EAAE;EACf9Q,QAAAA,OAAK,CAAC,2CAA2C,EAAE0G,GAAG,CAAC,CAAA;EACvD,QAAA,OAAA;EACJ,OAAA;EACJ,KAAA;MACA,IAAI,CAACwK,MAAM,EAAE,CAAA;EACjB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAA5iB,EAAAA,MAAA,CAMAsI,OAAO,GAAP,SAAAA,OAAAA,CAAQxS,MAAM,EAAE;EACZ4b,IAAAA,OAAK,CAAC,mBAAmB,EAAE5b,MAAM,CAAC,CAAA;MAClC,IAAMoC,cAAc,GAAG,IAAI,CAACmpB,OAAO,CAAClrB,MAAM,CAACL,MAAM,CAAC,CAAA;EAClD,IAAA,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,cAAc,CAAC3B,MAAM,EAAED,CAAC,EAAE,EAAE;EAC5C,MAAA,IAAI,CAAC+lB,MAAM,CAAC5b,KAAK,CAACvI,cAAc,CAAC5B,CAAC,CAAC,EAAER,MAAM,CAAC2Y,OAAO,CAAC,CAAA;EACxD,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAzO,EAAAA,MAAA,CAKAsP,OAAO,GAAP,SAAAA,UAAU;MACNoC,OAAK,CAAC,SAAS,CAAC,CAAA;EAChB,IAAA,IAAI,CAAC6J,IAAI,CAAC1nB,OAAO,CAAC,UAACsmB,UAAU,EAAA;QAAA,OAAKA,UAAU,EAAE,CAAA;OAAC,CAAA,CAAA;EAC/C,IAAA,IAAI,CAACoB,IAAI,CAAChlB,MAAM,GAAG,CAAC,CAAA;EACpB,IAAA,IAAI,CAAC+qB,OAAO,CAACrP,OAAO,EAAE,CAAA;EAC1B,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAjS,EAAAA,MAAA,CAKA4iB,MAAM,GAAN,SAAAA,SAAS;MACLlR,OAAK,CAAC,YAAY,CAAC,CAAA;MACnB,IAAI,CAAC+P,aAAa,GAAG,IAAI,CAAA;MACzB,IAAI,CAACO,aAAa,GAAG,KAAK,CAAA;EAC1B,IAAA,IAAI,CAACta,OAAO,CAAC,cAAc,CAAC,CAAA;EAChC,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAA1H,EAAAA,MAAA,CAKAua,UAAU,GAAV,SAAAA,aAAa;EACT,IAAA,OAAO,IAAI,CAACqI,MAAM,EAAE,CAAA;EACxB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MARI;IAAA5iB,MAAA,CASA0H,OAAO,GAAP,SAAAA,QAAQxI,MAAM,EAAEC,WAAW,EAAE;EACzB,IAAA,IAAIkF,EAAE,CAAA;EACNqN,IAAAA,OAAK,CAAC,kBAAkB,EAAExS,MAAM,CAAC,CAAA;MACjC,IAAI,CAACoQ,OAAO,EAAE,CAAA;MACd,CAACjL,EAAE,GAAG,IAAI,CAACgY,MAAM,MAAM,IAAI,IAAIhY,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAChE,KAAK,EAAE,CAAA;EAClE,IAAA,IAAI,CAAC6gB,OAAO,CAACX,KAAK,EAAE,CAAA;MACpB,IAAI,CAAC9E,WAAW,GAAG,QAAQ,CAAA;MAC3B,IAAI,CAACvf,YAAY,CAAC,OAAO,EAAEgD,MAAM,EAAEC,WAAW,CAAC,CAAA;MAC/C,IAAI,IAAI,CAACqiB,aAAa,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE;QAC3C,IAAI,CAACQ,SAAS,EAAE,CAAA;EACpB,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAjiB,EAAAA,MAAA,CAKAiiB,SAAS,GAAT,SAAAA,YAAY;EAAA,IAAA,IAAA7f,MAAA,GAAA,IAAA,CAAA;MACR,IAAI,IAAI,CAAC4f,aAAa,IAAI,IAAI,CAACP,aAAa,EACxC,OAAO,IAAI,CAAA;MACf,IAAM9kB,IAAI,GAAG,IAAI,CAAA;MACjB,IAAI,IAAI,CAACukB,OAAO,CAACf,QAAQ,IAAI,IAAI,CAACuB,qBAAqB,EAAE;QACrDhQ,OAAK,CAAC,kBAAkB,CAAC,CAAA;EACzB,MAAA,IAAI,CAACwP,OAAO,CAACX,KAAK,EAAE,CAAA;EACpB,MAAA,IAAI,CAACrkB,YAAY,CAAC,kBAAkB,CAAC,CAAA;QACrC,IAAI,CAAC8lB,aAAa,GAAG,KAAK,CAAA;EAC9B,KAAC,MACI;QACD,IAAM/T,KAAK,GAAG,IAAI,CAACiT,OAAO,CAACd,QAAQ,EAAE,CAAA;EACrC1O,MAAAA,OAAK,CAAC,yCAAyC,EAAEzD,KAAK,CAAC,CAAA;QACvD,IAAI,CAAC+T,aAAa,GAAG,IAAI,CAAA;EACzB,MAAA,IAAMtF,KAAK,GAAG,IAAI,CAACjgB,YAAY,CAAC,YAAM;UAClC,IAAIE,IAAI,CAAC8kB,aAAa,EAClB,OAAA;UACJ/P,OAAK,CAAC,sBAAsB,CAAC,CAAA;UAC7BtP,MAAI,CAAClG,YAAY,CAAC,mBAAmB,EAAES,IAAI,CAACukB,OAAO,CAACf,QAAQ,CAAC,CAAA;EAC7D;UACA,IAAIxjB,IAAI,CAAC8kB,aAAa,EAClB,OAAA;EACJ9kB,QAAAA,IAAI,CAACuD,IAAI,CAAC,UAAC8C,GAAG,EAAK;EACf,UAAA,IAAIA,GAAG,EAAE;cACL0O,OAAK,CAAC,yBAAyB,CAAC,CAAA;cAChC/U,IAAI,CAACqlB,aAAa,GAAG,KAAK,CAAA;cAC1BrlB,IAAI,CAACslB,SAAS,EAAE,CAAA;EAChB7f,YAAAA,MAAI,CAAClG,YAAY,CAAC,iBAAiB,EAAE8G,GAAG,CAAC,CAAA;EAC7C,WAAC,MACI;cACD0O,OAAK,CAAC,mBAAmB,CAAC,CAAA;cAC1B/U,IAAI,CAACkmB,WAAW,EAAE,CAAA;EACtB,WAAA;EACJ,SAAC,CAAC,CAAA;SACL,EAAE5U,KAAK,CAAC,CAAA;EACT,MAAA,IAAI,IAAI,CAACrQ,IAAI,CAAC2J,SAAS,EAAE;UACrBmV,KAAK,CAACjV,KAAK,EAAE,CAAA;EACjB,OAAA;EACA,MAAA,IAAI,CAAC8T,IAAI,CAAC9iB,IAAI,CAAC,YAAM;EACjB2J,QAAAA,MAAI,CAACrE,cAAc,CAAC2e,KAAK,CAAC,CAAA;EAC9B,OAAC,CAAC,CAAA;EACN,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAA1c,EAAAA,MAAA,CAKA6iB,WAAW,GAAX,SAAAA,cAAc;EACV,IAAA,IAAMC,OAAO,GAAG,IAAI,CAAC5B,OAAO,CAACf,QAAQ,CAAA;MACrC,IAAI,CAAC6B,aAAa,GAAG,KAAK,CAAA;EAC1B,IAAA,IAAI,CAACd,OAAO,CAACX,KAAK,EAAE,CAAA;EACpB,IAAA,IAAI,CAACrkB,YAAY,CAAC,WAAW,EAAE4mB,OAAO,CAAC,CAAA;KAC1C,CAAA;EAAA,EAAA,OAAAnC,OAAA,CAAA;EAAA,CAAA,CAxXwB5lB,OAAO,CAAA;;ECJpC,IAAM2W,KAAK,GAAG0E,WAAW,CAAC,kBAAkB,CAAC,CAAC;EAC9C;EACA;EACA;EACA,IAAM2M,KAAK,GAAG,EAAE,CAAA;EAChB,SAAS1sB,MAAMA,CAACkM,GAAG,EAAE3E,IAAI,EAAE;EACvB,EAAA,IAAIkO,OAAA,CAAOvJ,GAAG,CAAA,KAAK,QAAQ,EAAE;EACzB3E,IAAAA,IAAI,GAAG2E,GAAG,CAAA;EACVA,IAAAA,GAAG,GAAGrB,SAAS,CAAA;EACnB,GAAA;EACAtD,EAAAA,IAAI,GAAGA,IAAI,IAAI,EAAE,CAAA;IACjB,IAAMolB,MAAM,GAAG3M,GAAG,CAAC9T,GAAG,EAAE3E,IAAI,CAACyD,IAAI,IAAI,YAAY,CAAC,CAAA;EAClD,EAAA,IAAMmJ,MAAM,GAAGwY,MAAM,CAACxY,MAAM,CAAA;EAC5B,EAAA,IAAM2C,EAAE,GAAG6V,MAAM,CAAC7V,EAAE,CAAA;EACpB,EAAA,IAAM9L,IAAI,GAAG2hB,MAAM,CAAC3hB,IAAI,CAAA;EACxB,EAAA,IAAM+c,aAAa,GAAG2E,KAAK,CAAC5V,EAAE,CAAC,IAAI9L,IAAI,IAAI0hB,KAAK,CAAC5V,EAAE,CAAC,CAAC,MAAM,CAAC,CAAA;EAC5D,EAAA,IAAM8V,aAAa,GAAGrlB,IAAI,CAACslB,QAAQ,IAC/BtlB,IAAI,CAAC,sBAAsB,CAAC,IAC5B,KAAK,KAAKA,IAAI,CAACulB,SAAS,IACxB/E,aAAa,CAAA;EACjB,EAAA,IAAI1D,EAAE,CAAA;EACN,EAAA,IAAIuI,aAAa,EAAE;EACfvR,IAAAA,KAAK,CAAC,8BAA8B,EAAElH,MAAM,CAAC,CAAA;EAC7CkQ,IAAAA,EAAE,GAAG,IAAIiG,OAAO,CAACnW,MAAM,EAAE5M,IAAI,CAAC,CAAA;EAClC,GAAC,MACI;EACD,IAAA,IAAI,CAACmlB,KAAK,CAAC5V,EAAE,CAAC,EAAE;EACZuE,MAAAA,KAAK,CAAC,wBAAwB,EAAElH,MAAM,CAAC,CAAA;QACvCuY,KAAK,CAAC5V,EAAE,CAAC,GAAG,IAAIwT,OAAO,CAACnW,MAAM,EAAE5M,IAAI,CAAC,CAAA;EACzC,KAAA;EACA8c,IAAAA,EAAE,GAAGqI,KAAK,CAAC5V,EAAE,CAAC,CAAA;EAClB,GAAA;IACA,IAAI6V,MAAM,CAACnjB,KAAK,IAAI,CAACjC,IAAI,CAACiC,KAAK,EAAE;EAC7BjC,IAAAA,IAAI,CAACiC,KAAK,GAAGmjB,MAAM,CAACnY,QAAQ,CAAA;EAChC,GAAA;IACA,OAAO6P,EAAE,CAAC5a,MAAM,CAACkjB,MAAM,CAAC3hB,IAAI,EAAEzD,IAAI,CAAC,CAAA;EACvC,CAAA;EACA;EACA;EACA8I,QAAA,CAAcrQ,MAAM,EAAE;EAClBsqB,EAAAA,OAAO,EAAPA,OAAO;EACP5Q,EAAAA,MAAM,EAANA,MAAM;EACN2K,EAAAA,EAAE,EAAErkB,MAAM;EACVgkB,EAAAA,OAAO,EAAEhkB,MAAAA;EACb,CAAC,CAAC;;;;;;;;"}