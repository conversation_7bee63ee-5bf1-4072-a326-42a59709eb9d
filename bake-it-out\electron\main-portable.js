const { app, BrowserWindow, <PERSON>u, shell, dialog } = require('electron')
const path = require('path')
const isDev = require('electron-is-dev')
const { spawn } = require('child_process')
const http = require('http')
const fs = require('fs')
const url = require('url')

let mainWindow
let serverProcess
let staticServer

function createStaticServer() {
  const appDir = path.join(__dirname, '../app')
  const port = 3002

  staticServer = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url)
    let pathname = parsedUrl.pathname

    // Default to index.html
    if (pathname === '/') {
      pathname = '/index.html'
    }

    const filePath = path.join(appDir, pathname)

    // Security check - ensure file is within app directory
    if (!filePath.startsWith(appDir)) {
      res.writeHead(403)
      res.end('Forbidden')
      return
    }

    fs.readFile(filePath, (err, data) => {
      if (err) {
        res.writeHead(404)
        res.end('Not Found')
        return
      }

      // Set content type based on file extension
      const ext = path.extname(filePath).toLowerCase()
      const contentTypes = {
        '.html': 'text/html',
        '.js': 'application/javascript',
        '.css': 'text/css',
        '.json': 'application/json',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.ico': 'image/x-icon'
      }

      const contentType = contentTypes[ext] || 'application/octet-stream'
      res.writeHead(200, { 'Content-Type': contentType })
      res.end(data)
    })
  })

  staticServer.listen(port, () => {
    console.log(`Static server running on port ${port}`)
  })

  return port
}

function createWindow(staticPort) {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets', 'icon.png'),
    show: false,
    titleBarStyle: 'default'
  })

  // Load the app from the static server
  const startUrl = `http://localhost:${staticPort}`

  mainWindow.loadURL(startUrl)

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()
    
    if (isDev) {
      mainWindow.webContents.openDevTools()
    }
  })

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: 'deny' }
  })
}

function startSocketServer() {
  console.log('Starting Socket.IO server...')
  
  const serverPath = path.join(__dirname, '../server/socket-server.js')
  serverProcess = spawn('node', [serverPath], {
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  })
  
  serverProcess.on('error', (err) => {
    console.error('Failed to start server:', err)
    dialog.showErrorBox('Server Error', 'Failed to start multiplayer server: ' + err.message)
  })
  
  serverProcess.on('exit', (code) => {
    console.log(`Server process exited with code ${code}`)
  })
  
  // Give the server a moment to start
  return new Promise((resolve) => {
    setTimeout(resolve, 2000)
  })
}

// App event handlers
app.whenReady().then(async () => {
  // Start static file server
  const staticPort = createStaticServer()

  // Start Socket.IO server
  await startSocketServer()

  // Create main window
  createWindow(staticPort)

  // Create application menu
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Game',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('new-game')
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: 'Game',
      submenu: [
        {
          label: 'Single Player',
          click: () => {
            mainWindow.webContents.send('single-player')
          }
        },
        {
          label: 'Multiplayer',
          click: () => {
            mainWindow.webContents.send('multiplayer')
          }
        },
        { type: 'separator' },
        {
          label: 'Settings',
          click: () => {
            mainWindow.webContents.send('settings')
          }
        }
      ]
    },
    {
      label: 'Language',
      submenu: [
        {
          label: 'English',
          type: 'radio',
          checked: true,
          click: () => {
            mainWindow.webContents.send('language-change', 'en')
          }
        },
        {
          label: 'Čeština',
          type: 'radio',
          click: () => {
            mainWindow.webContents.send('language-change', 'cs')
          }
        }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About Bake It Out',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About Bake It Out',
              message: 'Bake It Out',
              detail: 'A multiplayer bakery management game\nVersion 1.0.0\n\nDeveloped with ❤️ using Electron, Next.js, and Socket.IO'
            })
          }
        },
        {
          label: 'Learn More',
          click: () => {
            shell.openExternal('https://github.com/your-repo/bake-it-out')
          }
        }
      ]
    }
  ]

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('before-quit', () => {
  // Clean up server processes
  if (serverProcess) {
    console.log('Stopping server process...')
    serverProcess.kill()
  }
  if (staticServer) {
    console.log('Stopping static server...')
    staticServer.close()
  }
})

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault()
    shell.openExternal(navigationUrl)
  })
})
