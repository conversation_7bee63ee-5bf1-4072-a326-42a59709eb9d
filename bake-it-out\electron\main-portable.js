const { app, BrowserWindow, <PERSON>u, shell, dialog, ipcMain } = require('electron')
const path = require('path')
const isDev = require('electron-is-dev')
const { spawn } = require('child_process')
const http = require('http')
const fs = require('fs')
const url = require('url')

let mainWindow
let serverProcess
let staticServer

function createStaticServer() {
  const appDir = path.join(__dirname, '../app')
  const port = 3002

  staticServer = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url)
    let pathname = parsedUrl.pathname

    // Default to index.html
    if (pathname === '/') {
      pathname = '/index.html'
    }

    const filePath = path.join(appDir, pathname)

    // Security check - ensure file is within app directory
    if (!filePath.startsWith(appDir)) {
      res.writeHead(403)
      res.end('Forbidden')
      return
    }

    fs.readFile(filePath, (err, data) => {
      if (err) {
        console.log(`404 Not Found: ${pathname}`)
        res.writeHead(404)
        res.end('Not Found')
        return
      }

      // Set content type based on file extension
      const ext = path.extname(filePath).toLowerCase()
      const contentTypes = {
        '.html': 'text/html',
        '.js': 'application/javascript',
        '.css': 'text/css',
        '.json': 'application/json',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.ico': 'image/x-icon'
      }

      const contentType = contentTypes[ext] || 'application/octet-stream'
      res.writeHead(200, { 'Content-Type': contentType })
      res.end(data)
    })
  })

  staticServer.listen(port, () => {
    console.log(`Static server running on port ${port}`)
  })

  return port
}

function createWindow(staticPort) {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets', 'icon.png'),
    show: false,
    titleBarStyle: 'default'
  })

  // Load the app from the static server
  const startUrl = `http://localhost:${staticPort}`

  mainWindow.loadURL(startUrl)

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()
    
    if (isDev) {
      mainWindow.webContents.openDevTools()
    }
  })

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: 'deny' }
  })
}

function startSocketServer() {
  console.log('Starting Socket.IO server...')
  
  const serverPath = path.join(__dirname, '../server/socket-server.js')
  serverProcess = spawn('node', [serverPath], {
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  })
  
  serverProcess.on('error', (err) => {
    console.error('Failed to start server:', err)
    dialog.showErrorBox('Server Error', 'Failed to start multiplayer server: ' + err.message)
  })
  
  serverProcess.on('exit', (code) => {
    console.log(`Server process exited with code ${code}`)
  })
  
  // Give the server a moment to start
  return new Promise((resolve) => {
    setTimeout(resolve, 2000)
  })
}

// App event handlers
app.whenReady().then(async () => {
  // Start static file server
  const staticPort = createStaticServer()

  // Start Socket.IO server
  await startSocketServer()

  // Create main window
  createWindow(staticPort)

  // Create application menu
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Game',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('new-game')
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: 'Game',
      submenu: [
        {
          label: 'Single Player',
          click: () => {
            mainWindow.webContents.send('single-player')
          }
        },
        {
          label: 'Multiplayer',
          click: () => {
            mainWindow.webContents.send('multiplayer')
          }
        },
        { type: 'separator' },
        {
          label: 'Settings',
          click: () => {
            mainWindow.webContents.send('settings')
          }
        }
      ]
    },
    {
      label: 'Language',
      submenu: [
        {
          label: 'English',
          type: 'radio',
          checked: true,
          click: () => {
            mainWindow.webContents.send('language-change', 'en')
          }
        },
        {
          label: 'Čeština',
          type: 'radio',
          click: () => {
            mainWindow.webContents.send('language-change', 'cs')
          }
        }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About Bake It Out',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About Bake It Out',
              message: 'Bake It Out',
              detail: 'A multiplayer bakery management game\nVersion 1.0.0\n\nDeveloped with ❤️ using Electron, Next.js, and Socket.IO'
            })
          }
        },
        {
          label: 'Learn More',
          click: () => {
            shell.openExternal('https://github.com/your-repo/bake-it-out')
          }
        }
      ]
    }
  ]

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('before-quit', () => {
  // Clean up server processes
  if (serverProcess) {
    console.log('Stopping server process...')
    serverProcess.kill()
  }
  if (staticServer) {
    console.log('Stopping static server...')
    staticServer.close()
  }
})

// IPC Handlers for file operations
const fsPromises = require('fs').promises
const os = require('os')

// Get save directory path
ipcMain.handle('get-save-directory', async () => {
  const userDataPath = app.getPath('userData')
  return path.join(userDataPath, 'saves')
})

// Ensure directory exists
ipcMain.handle('ensure-directory', async (event, dirPath) => {
  try {
    await fsPromises.mkdir(dirPath, { recursive: true })
    return true
  } catch (error) {
    console.error('Failed to create directory:', error)
    throw error
  }
})

// Write file
ipcMain.handle('write-file', async (event, filePath, content) => {
  try {
    await fsPromises.writeFile(filePath, content, 'utf8')
    return true
  } catch (error) {
    console.error('Failed to write file:', error)
    throw error
  }
})

// Read file
ipcMain.handle('read-file', async (event, filePath) => {
  try {
    const content = await fsPromises.readFile(filePath, 'utf8')
    return content
  } catch (error) {
    console.error('Failed to read file:', error)
    return null
  }
})

// Delete file
ipcMain.handle('delete-file', async (event, filePath) => {
  try {
    await fsPromises.unlink(filePath)
    return true
  } catch (error) {
    console.error('Failed to delete file:', error)
    throw error
  }
})

// Copy file
ipcMain.handle('copy-file', async (event, source, destination) => {
  try {
    await fsPromises.copyFile(source, destination)
    return true
  } catch (error) {
    console.error('Failed to copy file:', error)
    throw error
  }
})

// List files in directory
ipcMain.handle('list-files', async (event, directory, extension) => {
  try {
    const files = await fsPromises.readdir(directory)
    const filteredFiles = extension
      ? files.filter(file => file.endsWith(extension))
      : files

    const fileStats = await Promise.all(
      filteredFiles.map(async (file) => {
        try {
          const filePath = path.join(directory, file)
          const stats = await fsPromises.stat(filePath)
          return {
            name: file,
            size: stats.size,
            modified: stats.mtime
          }
        } catch (error) {
          return { name: file, size: 0, modified: new Date() }
        }
      })
    )

    return fileStats
  } catch (error) {
    console.error('Failed to list files:', error)
    return []
  }
})

// Show save dialog
ipcMain.handle('show-save-dialog', async (event, defaultName) => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      defaultPath: defaultName,
      filters: [
        { name: 'JSON Files', extensions: ['json'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    })

    return result.canceled ? null : result.filePath
  } catch (error) {
    console.error('Failed to show save dialog:', error)
    return null
  }
})

// Show open dialog
ipcMain.handle('show-open-dialog', async (event, extensions) => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, {
      properties: ['openFile'],
      filters: [
        { name: 'JSON Files', extensions: extensions || ['json'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    })

    return result.canceled ? null : result.filePaths[0]
  } catch (error) {
    console.error('Failed to show open dialog:', error)
    return null
  }
})

// Quit application
ipcMain.handle('quit', () => {
  app.quit()
})

// Discord Rich Presence handlers
let discordClient = null

// Initialize Discord RPC
ipcMain.handle('init-discord-rpc', async (event, clientId) => {
  try {
    // Try to require discord-rpc
    const DiscordRPC = require('discord-rpc')

    discordClient = new DiscordRPC.Client({ transport: 'ipc' })

    discordClient.on('ready', () => {
      console.log('Discord RPC connected successfully')
    })

    discordClient.on('disconnected', () => {
      console.log('Discord RPC disconnected')
    })

    await discordClient.login({ clientId })
    return true
  } catch (error) {
    console.error('Failed to initialize Discord RPC:', error)
    console.log('Discord RPC not available - this is normal if Discord is not installed')
    discordClient = null
    return false
  }
})

// Update Discord RPC activity
ipcMain.handle('update-discord-rpc', async (event, activity) => {
  try {
    if (discordClient) {
      await discordClient.setActivity(activity)
      return true
    }
    return false
  } catch (error) {
    console.error('Failed to update Discord RPC:', error)
    return false
  }
})

// Clear Discord RPC activity
ipcMain.handle('clear-discord-rpc', async () => {
  try {
    if (discordClient) {
      await discordClient.clearActivity()
      return true
    }
    return false
  } catch (error) {
    console.error('Failed to clear Discord RPC:', error)
    return false
  }
})

// Disconnect Discord RPC
ipcMain.handle('disconnect-discord-rpc', async () => {
  try {
    if (discordClient) {
      await discordClient.destroy()
      discordClient = null
      return true
    }
    return false
  } catch (error) {
    console.error('Failed to disconnect Discord RPC:', error)
    return false
  }
})

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault()
    shell.openExternal(navigationUrl)
  })
})
