{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'danger' | 'success'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport const Button: React.FC<ButtonProps> = ({\n  variant = 'primary',\n  size = 'md',\n  className = '',\n  children,\n  ...props\n}) => {\n  const baseClasses = 'font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2'\n\n  const variantClasses = {\n    primary: 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500',\n    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500',\n    danger: 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500',\n    success: 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500',\n  }\n\n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-base',\n    lg: 'px-6 py-3 text-lg',\n  }\n\n  const combinedClasses = [\n    baseClasses,\n    variantClasses[variant],\n    sizeClasses[size],\n    className\n  ].join(' ')\n\n  return (\n    <button\n      className={combinedClasses}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAQO,MAAM,SAAgC;QAAC,EAC5C,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB;QACA,cAAc,CAAC,QAAQ;QACvB,WAAW,CAAC,KAAK;QACjB;KACD,CAAC,IAAI,CAAC;IAEP,qBACE,6LAAC;QACC,WAAW;QACV,GAAG,KAAK;kBAER;;;;;;AAGP;KArCa", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/multiplayer/MultiplayerLobby.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { useLanguage } from '@/contexts/LanguageContext'\nimport { useMultiplayer } from '@/contexts/MultiplayerContext'\n\ninterface MultiplayerLobbyProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport function MultiplayerLobby({ isOpen, onClose }: MultiplayerLobbyProps) {\n  const { t } = useLanguage()\n  const {\n    isConnected,\n    isInRoom,\n    connectionError,\n    currentRoom,\n    currentPlayer,\n    players,\n    gameState,\n    messages,\n    createRoom,\n    joinRoom,\n    leaveRoom,\n    startGame,\n    sendMessage,\n    setPlayerReady\n  } = useMultiplayer()\n\n  const [activeTab, setActiveTab] = useState<'create' | 'join' | 'room'>('create')\n  const [roomName, setRoomName] = useState('')\n  const [playerName, setPlayerName] = useState('')\n  const [roomIdToJoin, setRoomIdToJoin] = useState('')\n  const [gameMode, setGameMode] = useState<'cooperative' | 'competitive'>('cooperative')\n  const [maxPlayers, setMaxPlayers] = useState(4)\n  const [chatMessage, setChatMessage] = useState('')\n  const [isReady, setIsReady] = useState(false)\n\n  if (!isOpen) return null\n\n  const handleCreateRoom = async () => {\n    if (!playerName.trim() || !roomName.trim()) return\n\n    try {\n      await createRoom(\n        {\n          name: roomName,\n          mode: gameMode,\n          maxPlayers,\n          settings: {\n            gameMode,\n            difficulty: 'medium',\n            allowSpectators: true\n          }\n        },\n        {\n          name: playerName,\n          avatar: '👨‍🍳',\n          level: 1\n        }\n      )\n      setActiveTab('room')\n    } catch (error) {\n      console.error('Failed to create room:', error)\n    }\n  }\n\n  const handleJoinRoom = async () => {\n    if (!playerName.trim() || !roomIdToJoin.trim()) return\n\n    try {\n      await joinRoom(roomIdToJoin.toUpperCase(), {\n        name: playerName,\n        avatar: '👨‍🍳',\n        level: 1\n      })\n      setActiveTab('room')\n    } catch (error) {\n      console.error('Failed to join room:', error)\n    }\n  }\n\n  const handleLeaveRoom = () => {\n    leaveRoom()\n    setActiveTab('create')\n    setIsReady(false)\n  }\n\n  const handleStartGame = () => {\n    if (currentPlayer?.isHost) {\n      startGame()\n    }\n  }\n\n  const handleSendMessage = () => {\n    if (chatMessage.trim()) {\n      sendMessage(chatMessage)\n      setChatMessage('')\n    }\n  }\n\n  const handleReadyToggle = () => {\n    const newReady = !isReady\n    setIsReady(newReady)\n    setPlayerReady(newReady)\n  }\n\n  const tabs = [\n    { id: 'create', name: 'Create Room', icon: '🏗️' },\n    { id: 'join', name: 'Join Room', icon: '🚪' },\n    ...(isInRoom ? [{ id: 'room', name: 'Room', icon: '🏠' }] : [])\n  ]\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-2xl font-bold text-orange-800\">\n              👥 Multiplayer Lobby\n            </h2>\n            <div className=\"flex items-center space-x-4\">\n              <div className={`px-3 py-1 rounded-full text-sm ${\n                isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n              }`}>\n                {isConnected ? '🟢 Connected' : '🔴 Disconnected'}\n              </div>\n              <Button variant=\"secondary\" onClick={onClose}>\n                ✕ Close\n              </Button>\n            </div>\n          </div>\n          {connectionError && (\n            <div className=\"mt-2 p-2 bg-red-100 text-red-800 rounded text-sm\">\n              ⚠️ {connectionError}\n            </div>\n          )}\n        </div>\n\n        {/* Tab Navigation */}\n        <div className=\"border-b border-gray-200\">\n          <div className=\"flex space-x-0\">\n            {tabs.map(tab => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id as any)}\n                className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${\n                  activeTab === tab.id\n                    ? 'border-orange-500 text-orange-600 bg-orange-50'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'\n                }`}\n              >\n                {tab.icon} {tab.name}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"p-6 max-h-[60vh] overflow-y-auto\">\n          {/* Create Room Tab */}\n          {activeTab === 'create' && (\n            <div className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Your Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={playerName}\n                    onChange={(e) => setPlayerName(e.target.value)}\n                    placeholder=\"Enter your name\"\n                    className=\"w-full p-3 border rounded-lg\"\n                    maxLength={20}\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Room Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={roomName}\n                    onChange={(e) => setRoomName(e.target.value)}\n                    placeholder=\"Enter room name\"\n                    className=\"w-full p-3 border rounded-lg\"\n                    maxLength={30}\n                  />\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Game Mode\n                  </label>\n                  <select\n                    value={gameMode}\n                    onChange={(e) => setGameMode(e.target.value as any)}\n                    className=\"w-full p-3 border rounded-lg\"\n                  >\n                    <option value=\"cooperative\">🤝 Cooperative</option>\n                    <option value=\"competitive\">⚔️ Competitive</option>\n                  </select>\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Max Players: {maxPlayers}\n                  </label>\n                  <input\n                    type=\"range\"\n                    min=\"2\"\n                    max=\"8\"\n                    value={maxPlayers}\n                    onChange={(e) => setMaxPlayers(parseInt(e.target.value))}\n                    className=\"w-full\"\n                  />\n                </div>\n              </div>\n\n              <Button\n                variant=\"primary\"\n                size=\"lg\"\n                className=\"w-full\"\n                onClick={handleCreateRoom}\n                disabled={!isConnected || !playerName.trim() || !roomName.trim()}\n              >\n                🏗️ Create Room\n              </Button>\n            </div>\n          )}\n\n          {/* Join Room Tab */}\n          {activeTab === 'join' && (\n            <div className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Your Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={playerName}\n                    onChange={(e) => setPlayerName(e.target.value)}\n                    placeholder=\"Enter your name\"\n                    className=\"w-full p-3 border rounded-lg\"\n                    maxLength={20}\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Room ID\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={roomIdToJoin}\n                    onChange={(e) => setRoomIdToJoin(e.target.value.toUpperCase())}\n                    placeholder=\"Enter room ID\"\n                    className=\"w-full p-3 border rounded-lg font-mono\"\n                    maxLength={6}\n                  />\n                </div>\n              </div>\n\n              <Button\n                variant=\"primary\"\n                size=\"lg\"\n                className=\"w-full\"\n                onClick={handleJoinRoom}\n                disabled={!isConnected || !playerName.trim() || !roomIdToJoin.trim()}\n              >\n                🚪 Join Room\n              </Button>\n            </div>\n          )}\n\n          {/* Room Tab */}\n          {activeTab === 'room' && currentRoom && (\n            <div className=\"space-y-6\">\n              {/* Room Info */}\n              <div className=\"bg-blue-50 p-4 rounded-lg\">\n                <div className=\"flex justify-between items-center mb-2\">\n                  <h3 className=\"font-semibold text-blue-800\">{currentRoom.name}</h3>\n                  <div className=\"text-sm text-blue-600\">\n                    Room ID: <span className=\"font-mono font-bold\">{currentRoom.id}</span>\n                  </div>\n                </div>\n                <div className=\"text-sm text-blue-700\">\n                  Mode: {currentRoom.mode} • Players: {currentRoom.currentPlayers}/{currentRoom.maxPlayers}\n                </div>\n              </div>\n\n              {/* Players List */}\n              <div>\n                <h4 className=\"font-medium text-gray-800 mb-3\">Players ({players.length})</h4>\n                <div className=\"space-y-2\">\n                  {players.map(player => (\n                    <div\n                      key={player.id}\n                      className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\"\n                    >\n                      <div className=\"flex items-center space-x-3\">\n                        <span className=\"text-2xl\">{player.avatar}</span>\n                        <div>\n                          <div className=\"font-medium\">\n                            {player.name}\n                            {player.isHost && <span className=\"ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded\">HOST</span>}\n                          </div>\n                          <div className=\"text-sm text-gray-600\">Level {player.level}</div>\n                        </div>\n                      </div>\n                      <div className={`px-2 py-1 rounded text-xs ${\n                        player.isReady ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'\n                      }`}>\n                        {player.isReady ? 'Ready' : 'Not Ready'}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Game Controls */}\n              <div className=\"flex space-x-3\">\n                <Button\n                  variant={isReady ? 'success' : 'secondary'}\n                  onClick={handleReadyToggle}\n                  className=\"flex-1\"\n                >\n                  {isReady ? '✅ Ready' : '⏳ Not Ready'}\n                </Button>\n                {currentPlayer?.isHost && (\n                  <Button\n                    variant=\"primary\"\n                    onClick={handleStartGame}\n                    disabled={!players.every(p => p.isReady) || players.length < 2}\n                  >\n                    🚀 Start Game\n                  </Button>\n                )}\n                <Button variant=\"secondary\" onClick={handleLeaveRoom}>\n                  🚪 Leave\n                </Button>\n              </div>\n\n              {/* Chat */}\n              <div className=\"border-t pt-4\">\n                <h4 className=\"font-medium text-gray-800 mb-3\">Chat</h4>\n                <div className=\"bg-gray-50 p-3 rounded-lg h-32 overflow-y-auto mb-3\">\n                  {messages.map(message => (\n                    <div key={message.id} className=\"text-sm mb-1\">\n                      <span className={`font-medium ${\n                        message.playerId === 'system' ? 'text-blue-600' : 'text-gray-800'\n                      }`}>\n                        {message.playerName}:\n                      </span>\n                      <span className=\"ml-2\">{message.content}</span>\n                    </div>\n                  ))}\n                </div>\n                <div className=\"flex space-x-2\">\n                  <input\n                    type=\"text\"\n                    value={chatMessage}\n                    onChange={(e) => setChatMessage(e.target.value)}\n                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}\n                    placeholder=\"Type a message...\"\n                    className=\"flex-1 p-2 border rounded\"\n                    maxLength={100}\n                  />\n                  <Button size=\"sm\" onClick={handleSendMessage}>\n                    Send\n                  </Button>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAYO,SAAS,iBAAiB,KAA0C;QAA1C,EAAE,MAAM,EAAE,OAAO,EAAyB,GAA1C;;IAC/B,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACxB,MAAM,EACJ,WAAW,EACX,QAAQ,EACR,eAAe,EACf,WAAW,EACX,aAAa,EACb,OAAO,EACP,SAAS,EACT,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,SAAS,EACT,SAAS,EACT,WAAW,EACX,cAAc,EACf,GAAG,CAAA,GAAA,yIAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACvE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiC;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,mBAAmB;QACvB,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,SAAS,IAAI,IAAI;QAE5C,IAAI;YACF,MAAM,WACJ;gBACE,MAAM;gBACN,MAAM;gBACN;gBACA,UAAU;oBACR;oBACA,YAAY;oBACZ,iBAAiB;gBACnB;YACF,GACA;gBACE,MAAM;gBACN,QAAQ;gBACR,OAAO;YACT;YAEF,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,aAAa,IAAI,IAAI;QAEhD,IAAI;YACF,MAAM,SAAS,aAAa,WAAW,IAAI;gBACzC,MAAM;gBACN,QAAQ;gBACR,OAAO;YACT;YACA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,kBAAkB;QACtB;QACA,aAAa;QACb,WAAW;IACb;IAEA,MAAM,kBAAkB;QACtB,IAAI,0BAAA,oCAAA,cAAe,MAAM,EAAE;YACzB;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,YAAY,IAAI,IAAI;YACtB,YAAY;YACZ,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,WAAW,CAAC;QAClB,WAAW;QACX,eAAe;IACjB;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAU,MAAM;YAAe,MAAM;QAAM;QACjD;YAAE,IAAI;YAAQ,MAAM;YAAa,MAAM;QAAK;WACxC,WAAW;YAAC;gBAAE,IAAI;gBAAQ,MAAM;gBAAQ,MAAM;YAAK;SAAE,GAAG,EAAE;KAC/D;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAqC;;;;;;8CAGnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,AAAC,kCAEhB,OADC,cAAc,gCAAgC;sDAE7C,cAAc,iBAAiB;;;;;;sDAElC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAY,SAAS;sDAAS;;;;;;;;;;;;;;;;;;wBAKjD,iCACC,6LAAC;4BAAI,WAAU;;gCAAmD;gCAC5D;;;;;;;;;;;;;8BAMV,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAA,oBACR,6LAAC;gCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;gCAClC,WAAW,AAAC,8DAIX,OAHC,cAAc,IAAI,EAAE,GAChB,mDACA;;oCAGL,IAAI,IAAI;oCAAC;oCAAE,IAAI,IAAI;;+BARf,IAAI,EAAE;;;;;;;;;;;;;;;8BAcnB,6LAAC;oBAAI,WAAU;;wBAEZ,cAAc,0BACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,aAAY;oDACZ,WAAU;oDACV,WAAW;;;;;;;;;;;;sDAGf,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,aAAY;oDACZ,WAAU;oDACV,WAAW;;;;;;;;;;;;;;;;;;8CAKjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;sEAAc;;;;;;sEAC5B,6LAAC;4DAAO,OAAM;sEAAc;;;;;;;;;;;;;;;;;;sDAGhC,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;;wDAA+C;wDAChD;;;;;;;8DAEhB,6LAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,SAAS,EAAE,MAAM,CAAC,KAAK;oDACtD,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;oCACT,UAAU,CAAC,eAAe,CAAC,WAAW,IAAI,MAAM,CAAC,SAAS,IAAI;8CAC/D;;;;;;;;;;;;wBAOJ,cAAc,wBACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,aAAY;oDACZ,WAAU;oDACV,WAAW;;;;;;;;;;;;sDAGf,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW;oDAC3D,aAAY;oDACZ,WAAU;oDACV,WAAW;;;;;;;;;;;;;;;;;;8CAKjB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;oCACT,UAAU,CAAC,eAAe,CAAC,WAAW,IAAI,MAAM,CAAC,aAAa,IAAI;8CACnE;;;;;;;;;;;;wBAOJ,cAAc,UAAU,6BACvB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA+B,YAAY,IAAI;;;;;;8DAC7D,6LAAC;oDAAI,WAAU;;wDAAwB;sEAC5B,6LAAC;4DAAK,WAAU;sEAAuB,YAAY,EAAE;;;;;;;;;;;;;;;;;;sDAGlE,6LAAC;4CAAI,WAAU;;gDAAwB;gDAC9B,YAAY,IAAI;gDAAC;gDAAa,YAAY,cAAc;gDAAC;gDAAE,YAAY,UAAU;;;;;;;;;;;;;8CAK5F,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;;gDAAiC;gDAAU,QAAQ,MAAM;gDAAC;;;;;;;sDACxE,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,GAAG,CAAC,CAAA,uBACX,6LAAC;oDAEC,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAY,OAAO,MAAM;;;;;;8EACzC,6LAAC;;sFACC,6LAAC;4EAAI,WAAU;;gFACZ,OAAO,IAAI;gFACX,OAAO,MAAM,kBAAI,6LAAC;oFAAK,WAAU;8FAA+D;;;;;;;;;;;;sFAEnG,6LAAC;4EAAI,WAAU;;gFAAwB;gFAAO,OAAO,KAAK;;;;;;;;;;;;;;;;;;;sEAG9D,6LAAC;4DAAI,WAAW,AAAC,6BAEhB,OADC,OAAO,OAAO,GAAG,gCAAgC;sEAEhD,OAAO,OAAO,GAAG,UAAU;;;;;;;mDAhBzB,OAAO,EAAE;;;;;;;;;;;;;;;;8CAwBtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,UAAU,YAAY;4CAC/B,SAAS;4CACT,WAAU;sDAET,UAAU,YAAY;;;;;;wCAExB,CAAA,0BAAA,oCAAA,cAAe,MAAM,mBACpB,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;4CACT,UAAU,CAAC,QAAQ,KAAK,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,QAAQ,MAAM,GAAG;sDAC9D;;;;;;sDAIH,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAY,SAAS;sDAAiB;;;;;;;;;;;;8CAMxD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAI,WAAU;sDACZ,SAAS,GAAG,CAAC,CAAA,wBACZ,6LAAC;oDAAqB,WAAU;;sEAC9B,6LAAC;4DAAK,WAAW,AAAC,eAEjB,OADC,QAAQ,QAAQ,KAAK,WAAW,kBAAkB;;gEAEjD,QAAQ,UAAU;gEAAC;;;;;;;sEAEtB,6LAAC;4DAAK,WAAU;sEAAQ,QAAQ,OAAO;;;;;;;mDAN/B,QAAQ,EAAE;;;;;;;;;;sDAUxB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;oDACxC,aAAY;oDACZ,WAAU;oDACV,WAAW;;;;;;8DAEb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,SAAS;8DAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWhE;GAlXgB;;QACA,sIAAA,CAAA,cAAW;QAgBrB,yIAAA,CAAA,iBAAc;;;KAjBJ", "debugId": null}}, {"offset": {"line": 834, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/Equipment.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useLanguage } from '@/contexts/LanguageContext'\n\nexport interface EquipmentData {\n  id: string\n  name: string\n  type: 'oven' | 'mixer' | 'counter' | 'auto_oven' | 'auto_mixer' | 'conveyor'\n  isActive: boolean\n  timeRemaining?: number\n  currentRecipe?: string\n  level: number\n  efficiency: number\n  automationLevel: number\n  isAutomated?: boolean\n  queuedRecipes?: string[]\n}\n\ninterface EquipmentProps {\n  equipment: EquipmentData\n  onClick: (equipmentId: string, equipmentName: string) => void\n}\n\nexport function Equipment({ equipment, onClick }: EquipmentProps) {\n  const { t } = useLanguage()\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const getEquipmentIcon = (type: string) => {\n    switch (type) {\n      case 'oven': return '🔥'\n      case 'mixer': return '🥄'\n      case 'counter': return '🍽️'\n      default: return '⚙️'\n    }\n  }\n\n  const getStatusColor = () => {\n    if (equipment.isActive) {\n      return 'border-green-400 bg-green-50'\n    }\n    return 'border-gray-200 bg-gray-50 hover:border-orange-300 hover:bg-orange-50'\n  }\n\n  return (\n    <div\n      className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${getStatusColor()}`}\n      onClick={() => !equipment.isActive && onClick(equipment.id, equipment.name)}\n    >\n      <div className=\"text-center\">\n        <div className=\"text-3xl mb-2\">\n          {getEquipmentIcon(equipment.type)}\n        </div>\n        <h3 className=\"font-medium text-gray-800\">{equipment.name}</h3>\n        <div className=\"text-xs text-gray-500\">Level {equipment.level}</div>\n        \n        {equipment.isActive && equipment.timeRemaining ? (\n          <div className=\"mt-2\">\n            <div className=\"text-sm text-green-600\">\n              {t('kitchen.making', { recipe: equipment.currentRecipe || '' })}\n            </div>\n            <div className=\"text-lg font-mono text-green-700\">\n              {formatTime(equipment.timeRemaining)}\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-2 mt-2\">\n              <div \n                className=\"bg-green-500 h-2 rounded-full transition-all duration-1000\"\n                style={{ \n                  width: `${100 - (equipment.timeRemaining / 60) * 100}%` \n                }}\n              ></div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"text-sm text-gray-500 mt-2\">\n            {equipment.isActive ? 'Busy' : t('kitchen.clickToUse')}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;;;AAHA;;AAwBO,SAAS,UAAU,KAAsC;QAAtC,EAAE,SAAS,EAAE,OAAO,EAAkB,GAAtC;;IACxB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAExB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,AAAC,GAAU,OAAR,MAAK,KAAoC,OAAjC,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAChD;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,UAAU,QAAQ,EAAE;YACtB,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,WAAW,AAAC,yDAAyE,OAAjB;QACpE,SAAS,IAAM,CAAC,UAAU,QAAQ,IAAI,QAAQ,UAAU,EAAE,EAAE,UAAU,IAAI;kBAE1E,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACZ,iBAAiB,UAAU,IAAI;;;;;;8BAElC,6LAAC;oBAAG,WAAU;8BAA6B,UAAU,IAAI;;;;;;8BACzD,6LAAC;oBAAI,WAAU;;wBAAwB;wBAAO,UAAU,KAAK;;;;;;;gBAE5D,UAAU,QAAQ,IAAI,UAAU,aAAa,iBAC5C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,EAAE,kBAAkB;gCAAE,QAAQ,UAAU,aAAa,IAAI;4BAAG;;;;;;sCAE/D,6LAAC;4BAAI,WAAU;sCACZ,WAAW,UAAU,aAAa;;;;;;sCAErC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO,AAAC,GAA6C,OAA3C,MAAM,AAAC,UAAU,aAAa,GAAG,KAAM,KAAI;gCACvD;;;;;;;;;;;;;;;;yCAKN,6LAAC;oBAAI,WAAU;8BACZ,UAAU,QAAQ,GAAG,SAAS,EAAE;;;;;;;;;;;;;;;;;AAM7C;GA9DgB;;QACA,sIAAA,CAAA,cAAW;;;KADX", "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/Order.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { useLanguage } from '@/contexts/LanguageContext'\n\nexport interface OrderData {\n  id: string\n  customerName: string\n  items: string[]\n  timeLimit: number\n  reward: number\n  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'failed'\n  difficulty: number\n}\n\ninterface OrderProps {\n  order: OrderData\n  onAccept: (orderId: string) => void\n  onDecline: (orderId: string) => void\n  onComplete?: (orderId: string) => void\n}\n\nexport function Order({ order, onAccept, onDecline, onComplete }: OrderProps) {\n  const { t } = useLanguage()\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const getStatusStyle = () => {\n    switch (order.status) {\n      case 'pending':\n        return 'border-yellow-300 bg-yellow-50'\n      case 'accepted':\n      case 'in_progress':\n        return 'border-blue-300 bg-blue-50'\n      case 'completed':\n        return 'border-green-300 bg-green-50'\n      case 'failed':\n        return 'border-red-300 bg-red-50'\n      default:\n        return 'border-gray-300 bg-gray-50'\n    }\n  }\n\n  const getDifficultyStars = () => {\n    return '⭐'.repeat(order.difficulty) + '☆'.repeat(5 - order.difficulty)\n  }\n\n  const getCustomerAvatar = () => {\n    const avatars = ['👩', '👨', '👵', '👴', '👧', '👦']\n    const index = order.customerName.length % avatars.length\n    return avatars[index]\n  }\n\n  return (\n    <div className={`p-4 rounded-lg border ${getStatusStyle()}`}>\n      <div className=\"flex items-center justify-between mb-2\">\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-lg\">{getCustomerAvatar()}</span>\n          <h3 className=\"font-medium text-gray-800\">{order.customerName}</h3>\n        </div>\n        <span className=\"text-sm font-semibold text-green-600\">{t('orders.reward', { amount: order.reward.toString() })}</span>\n      </div>\n      \n      <div className=\"text-sm text-gray-600 mb-2\">\n        {order.items.map((item, index) => (\n          <div key={index} className=\"flex items-center space-x-1\">\n            <span>🧁</span>\n            <span>{item}</span>\n          </div>\n        ))}\n      </div>\n      \n      <div className=\"flex justify-between items-center mb-3\">\n        <div className=\"text-xs text-gray-500\">\n          ⏰ {t('orders.timeLimit', { time: formatTime(order.timeLimit) })}\n        </div>\n        <div className=\"text-xs\" title={`Difficulty: ${order.difficulty}/5`}>\n          {getDifficultyStars()}\n        </div>\n      </div>\n\n      {order.status === 'pending' && (\n        <div className=\"flex space-x-2\">\n          <Button\n            size=\"sm\"\n            variant=\"success\"\n            onClick={() => onAccept(order.id)}\n            className=\"flex-1\"\n          >\n            ✅ {t('orders.accept')}\n          </Button>\n          <Button \n            size=\"sm\" \n            variant=\"danger\" \n            onClick={() => onDecline(order.id)}\n            className=\"flex-1\"\n          >\n            ❌ {t('orders.decline')}\n          </Button>\n        </div>\n      )}\n      \n      {order.status === 'accepted' && (\n        <div className=\"text-center\">\n          <div className=\"text-blue-600 text-sm font-medium mb-2\">\n            📋 Order Accepted\n          </div>\n          <Button\n            size=\"sm\"\n            variant=\"primary\"\n            onClick={() => onComplete && onComplete(order.id)}\n            className=\"w-full\"\n          >\n            🎯 {t('orders.complete')}\n          </Button>\n        </div>\n      )}\n      \n      {order.status === 'in_progress' && (\n        <div className=\"text-center text-orange-600 text-sm font-medium\">\n          🔄 {t('orders.inProgress')}\n        </div>\n      )}\n      \n      {order.status === 'completed' && (\n        <div className=\"text-center text-green-600 text-sm font-medium\">\n          ✅ Completed!\n        </div>\n      )}\n      \n      {order.status === 'failed' && (\n        <div className=\"text-center text-red-600 text-sm font-medium\">\n          ❌ Failed\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAsBO,SAAS,MAAM,KAAsD;QAAtD,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAc,GAAtD;;IACpB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAExB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,AAAC,GAAU,OAAR,MAAK,KAAoC,OAAjC,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAChD;IAEA,MAAM,iBAAiB;QACrB,OAAQ,MAAM,MAAM;YAClB,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;g<PERSON>CH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,OAAO,IAAI,MAAM,CAAC,MAAM,UAAU,IAAI,IAAI,MAAM,CAAC,IAAI,MAAM,UAAU;IACvE;IAEA,MAAM,oBAAoB;QACxB,MAAM,UAAU;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;SAAK;QACpD,MAAM,QAAQ,MAAM,YAAY,CAAC,MAAM,GAAG,QAAQ,MAAM;QACxD,OAAO,OAAO,CAAC,MAAM;IACvB;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,yBAAyC,OAAjB;;0BACvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAW;;;;;;0CAC3B,6LAAC;gCAAG,WAAU;0CAA6B,MAAM,YAAY;;;;;;;;;;;;kCAE/D,6LAAC;wBAAK,WAAU;kCAAwC,EAAE,iBAAiB;4BAAE,QAAQ,MAAM,MAAM,CAAC,QAAQ;wBAAG;;;;;;;;;;;;0BAG/G,6LAAC;gBAAI,WAAU;0BACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC;wBAAgB,WAAU;;0CACzB,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAM;;;;;;;uBAFC;;;;;;;;;;0BAOd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BAAwB;4BAClC,EAAE,oBAAoB;gCAAE,MAAM,WAAW,MAAM,SAAS;4BAAE;;;;;;;kCAE/D,6LAAC;wBAAI,WAAU;wBAAU,OAAO,AAAC,eAA+B,OAAjB,MAAM,UAAU,EAAC;kCAC7D;;;;;;;;;;;;YAIJ,MAAM,MAAM,KAAK,2BAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,SAAS,MAAM,EAAE;wBAChC,WAAU;;4BACX;4BACI,EAAE;;;;;;;kCAEP,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,UAAU,MAAM,EAAE;wBACjC,WAAU;;4BACX;4BACI,EAAE;;;;;;;;;;;;;YAKV,MAAM,MAAM,KAAK,4BAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAyC;;;;;;kCAGxD,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,cAAc,WAAW,MAAM,EAAE;wBAChD,WAAU;;4BACX;4BACK,EAAE;;;;;;;;;;;;;YAKX,MAAM,MAAM,KAAK,+BAChB,6LAAC;gBAAI,WAAU;;oBAAkD;oBAC3D,EAAE;;;;;;;YAIT,MAAM,MAAM,KAAK,6BAChB,6LAAC;gBAAI,WAAU;0BAAiD;;;;;;YAKjE,MAAM,MAAM,KAAK,0BAChB,6LAAC;gBAAI,WAAU;0BAA+C;;;;;;;;;;;;AAMtE;GAvHgB;;QACA,sIAAA,CAAA,cAAW;;;KADX", "debugId": null}}, {"offset": {"line": 1260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/multiplayer/MultiplayerGame.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { useLanguage } from '@/contexts/LanguageContext'\nimport { useMultiplayer } from '@/contexts/MultiplayerContext'\nimport { Equipment } from '@/components/game/Equipment'\nimport { Order } from '@/components/game/Order'\n\ninterface MultiplayerGameProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport function MultiplayerGame({ isOpen, onClose }: MultiplayerGameProps) {\n  const { t } = useLanguage()\n  const {\n    currentRoom,\n    currentPlayer,\n    players,\n    gameState,\n    sharedGameState,\n    sendPlayerAction,\n    leaveRoom\n  } = useMultiplayer()\n\n  const [selectedTab, setSelectedTab] = useState<'game' | 'players' | 'chat'>('game')\n\n  if (!isOpen || !currentRoom || gameState !== 'playing') return null\n\n  const handleEquipmentClick = (equipmentId: string, equipmentName: string) => {\n    sendPlayerAction({\n      type: 'use_equipment',\n      data: {\n        equipmentId,\n        equipmentName,\n        playerId: currentPlayer?.id\n      }\n    })\n  }\n\n  const handleOrderAction = (orderId: string, action: 'accept' | 'decline' | 'complete') => {\n    sendPlayerAction({\n      type: 'order_action',\n      data: {\n        orderId,\n        action,\n        playerId: currentPlayer?.id\n      }\n    })\n  }\n\n  const handleLeaveGame = () => {\n    leaveRoom()\n    onClose()\n  }\n\n  const tabs = [\n    { id: 'game', name: 'Game', icon: '🎮' },\n    { id: 'players', name: 'Players', icon: '👥' },\n    { id: 'chat', name: 'Chat', icon: '💬' }\n  ]\n\n  // Mock shared game data (in real implementation, this would come from sharedGameState)\n  const sharedEquipment = [\n    { id: 'oven1', name: 'Shared Oven', type: 'oven' as const, isActive: false, level: 1, efficiency: 1.0, automationLevel: 0 },\n    { id: 'mixer1', name: 'Shared Mixer', type: 'mixer' as const, isActive: false, level: 1, efficiency: 1.0, automationLevel: 0 },\n    { id: 'counter1', name: 'Shared Counter', type: 'counter' as const, isActive: false, level: 1, efficiency: 1.0, automationLevel: 0 }\n  ]\n\n  const sharedOrders = [\n    {\n      id: '1',\n      customerName: 'Shared Customer',\n      items: ['Chocolate Chip Cookies'],\n      timeLimit: 300,\n      reward: 50,\n      status: 'pending' as const,\n      difficulty: 1\n    }\n  ]\n\n  const sharedInventory = [\n    { name: 'Flour', quantity: 20, cost: 2 },\n    { name: 'Sugar', quantity: 15, cost: 3 },\n    { name: 'Eggs', quantity: 12, cost: 4 }\n  ]\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-orange-800\">\n                🎮 Multiplayer Game - {currentRoom.name}\n              </h2>\n              <p className=\"text-gray-600\">\n                Mode: {currentRoom.mode} • Players: {players.length}\n              </p>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"bg-green-100 px-3 py-1 rounded-full\">\n                <span className=\"text-green-800 text-sm\">🟢 Playing</span>\n              </div>\n              <Button variant=\"secondary\" onClick={handleLeaveGame}>\n                🚪 Leave Game\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Tab Navigation */}\n        <div className=\"border-b border-gray-200\">\n          <div className=\"flex space-x-0\">\n            {tabs.map(tab => (\n              <button\n                key={tab.id}\n                onClick={() => setSelectedTab(tab.id as any)}\n                className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${\n                  selectedTab === tab.id\n                    ? 'border-orange-500 text-orange-600 bg-orange-50'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'\n                }`}\n              >\n                {tab.icon} {tab.name}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"p-6 max-h-[70vh] overflow-y-auto\">\n          {/* Game Tab */}\n          {selectedTab === 'game' && (\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n              {/* Shared Kitchen */}\n              <div className=\"lg:col-span-2\">\n                <div className=\"bg-white rounded-lg shadow-md p-6\">\n                  <h3 className=\"text-xl font-semibold text-orange-800 mb-4\">\n                    🏪 Shared Kitchen\n                  </h3>\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                    {sharedEquipment.map(equipment => (\n                      <Equipment\n                        key={equipment.id}\n                        equipment={equipment}\n                        onClick={handleEquipmentClick}\n                      />\n                    ))}\n                  </div>\n                </div>\n\n                {/* Shared Orders */}\n                <div className=\"bg-white rounded-lg shadow-md p-6 mt-6\">\n                  <h3 className=\"text-xl font-semibold text-orange-800 mb-4\">\n                    📋 Shared Orders\n                  </h3>\n                  <div className=\"space-y-4\">\n                    {sharedOrders.map(order => (\n                      <Order\n                        key={order.id}\n                        order={order}\n                        onAccept={(id) => handleOrderAction(id, 'accept')}\n                        onDecline={(id) => handleOrderAction(id, 'decline')}\n                        onComplete={(id) => handleOrderAction(id, 'complete')}\n                      />\n                    ))}\n                  </div>\n                </div>\n              </div>\n\n              {/* Shared Resources */}\n              <div className=\"space-y-6\">\n                {/* Shared Inventory */}\n                <div className=\"bg-white rounded-lg shadow-md p-6\">\n                  <h3 className=\"text-xl font-semibold text-orange-800 mb-4\">\n                    📦 Shared Inventory\n                  </h3>\n                  <div className=\"space-y-3\">\n                    {sharedInventory.map((ingredient, index) => (\n                      <div key={index} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                        <div>\n                          <div className=\"font-medium text-gray-800\">{ingredient.name}</div>\n                          <div className=\"text-sm text-gray-600\">Qty: {ingredient.quantity}</div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Game Stats */}\n                <div className=\"bg-white rounded-lg shadow-md p-6\">\n                  <h3 className=\"text-xl font-semibold text-orange-800 mb-4\">\n                    📊 Team Stats\n                  </h3>\n                  <div className=\"space-y-2 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span>Orders Completed:</span>\n                      <span className=\"font-medium\">0</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Total Revenue:</span>\n                      <span className=\"font-medium\">$0</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Team Experience:</span>\n                      <span className=\"font-medium\">0 XP</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Players Tab */}\n          {selectedTab === 'players' && (\n            <div className=\"space-y-4\">\n              <h3 className=\"text-xl font-semibold text-orange-800\">\n                👥 Players ({players.length})\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                {players.map(player => (\n                  <div\n                    key={player.id}\n                    className={`p-4 rounded-lg border-2 ${\n                      player.id === currentPlayer?.id\n                        ? 'border-orange-400 bg-orange-50'\n                        : 'border-gray-300 bg-white'\n                    }`}\n                  >\n                    <div className=\"flex items-center space-x-3 mb-3\">\n                      <span className=\"text-3xl\">{player.avatar}</span>\n                      <div>\n                        <h4 className=\"font-semibold text-gray-800\">\n                          {player.name}\n                          {player.id === currentPlayer?.id && <span className=\"ml-2 text-sm text-orange-600\">(You)</span>}\n                        </h4>\n                        <div className=\"text-sm text-gray-600\">\n                          Level {player.level}\n                          {player.isHost && <span className=\"ml-2 bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs\">HOST</span>}\n                        </div>\n                      </div>\n                    </div>\n                    \n                    <div className=\"space-y-2 text-sm\">\n                      <div className=\"flex justify-between\">\n                        <span>Status:</span>\n                        <span className=\"text-green-600\">🟢 Online</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span>Contribution:</span>\n                        <span className=\"font-medium\">0 orders</span>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* Chat Tab */}\n          {selectedTab === 'chat' && (\n            <div className=\"space-y-4\">\n              <h3 className=\"text-xl font-semibold text-orange-800\">💬 Team Chat</h3>\n              <div className=\"bg-gray-50 p-4 rounded-lg h-64 overflow-y-auto\">\n                <div className=\"text-sm text-gray-500 text-center\">\n                  Chat messages will appear here...\n                </div>\n              </div>\n              <div className=\"flex space-x-2\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Type a message...\"\n                  className=\"flex-1 p-3 border rounded-lg\"\n                />\n                <Button>Send</Button>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Game Mode Info */}\n        <div className=\"p-4 bg-blue-50 border-t border-gray-200\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"text-sm text-blue-700\">\n              {currentRoom.mode === 'cooperative' ? (\n                <>🤝 <strong>Cooperative Mode:</strong> Work together to complete orders and grow your shared bakery!</>\n              ) : (\n                <>⚔️ <strong>Competitive Mode:</strong> Compete against other players to complete the most orders!</>\n              )}\n            </div>\n            <div className=\"text-sm text-blue-600\">\n              Game Time: 00:00\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAcO,SAAS,gBAAgB,KAAyC;QAAzC,EAAE,MAAM,EAAE,OAAO,EAAwB,GAAzC;;IAC9B,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACxB,MAAM,EACJ,WAAW,EACX,aAAa,EACb,OAAO,EACP,SAAS,EACT,eAAe,EACf,gBAAgB,EAChB,SAAS,EACV,GAAG,CAAA,GAAA,yIAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+B;IAE5E,IAAI,CAAC,UAAU,CAAC,eAAe,cAAc,WAAW,OAAO;IAE/D,MAAM,uBAAuB,CAAC,aAAqB;QACjD,iBAAiB;YACf,MAAM;YACN,MAAM;gBACJ;gBACA;gBACA,QAAQ,EAAE,0BAAA,oCAAA,cAAe,EAAE;YAC7B;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC,SAAiB;QAC1C,iBAAiB;YACf,MAAM;YACN,MAAM;gBACJ;gBACA;gBACA,QAAQ,EAAE,0BAAA,oCAAA,cAAe,EAAE;YAC7B;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB;QACA;IACF;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAQ,MAAM;YAAQ,MAAM;QAAK;QACvC;YAAE,IAAI;YAAW,MAAM;YAAW,MAAM;QAAK;QAC7C;YAAE,IAAI;YAAQ,MAAM;YAAQ,MAAM;QAAK;KACxC;IAED,uFAAuF;IACvF,MAAM,kBAAkB;QACtB;YAAE,IAAI;YAAS,MAAM;YAAe,MAAM;YAAiB,UAAU;YAAO,OAAO;YAAG,YAAY;YAAK,iBAAiB;QAAE;QAC1H;YAAE,IAAI;YAAU,MAAM;YAAgB,MAAM;YAAkB,UAAU;YAAO,OAAO;YAAG,YAAY;YAAK,iBAAiB;QAAE;QAC7H;YAAE,IAAI;YAAY,MAAM;YAAkB,MAAM;YAAoB,UAAU;YAAO,OAAO;YAAG,YAAY;YAAK,iBAAiB;QAAE;KACpI;IAED,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,cAAc;YACd,OAAO;gBAAC;aAAyB;YACjC,WAAW;YACX,QAAQ;YACR,QAAQ;YACR,YAAY;QACd;KACD;IAED,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAS,UAAU;YAAI,MAAM;QAAE;QACvC;YAAE,MAAM;YAAS,UAAU;YAAI,MAAM;QAAE;QACvC;YAAE,MAAM;YAAQ,UAAU;YAAI,MAAM;QAAE;KACvC;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;4CAAqC;4CAC1B,YAAY,IAAI;;;;;;;kDAEzC,6LAAC;wCAAE,WAAU;;4CAAgB;4CACpB,YAAY,IAAI;4CAAC;4CAAa,QAAQ,MAAM;;;;;;;;;;;;;0CAGvD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;kDAE3C,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAY,SAAS;kDAAiB;;;;;;;;;;;;;;;;;;;;;;;8BAQ5D,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAA,oBACR,6LAAC;gCAEC,SAAS,IAAM,eAAe,IAAI,EAAE;gCACpC,WAAW,AAAC,8DAIX,OAHC,gBAAgB,IAAI,EAAE,GAClB,mDACA;;oCAGL,IAAI,IAAI;oCAAC;oCAAE,IAAI,IAAI;;+BARf,IAAI,EAAE;;;;;;;;;;;;;;;8BAcnB,6LAAC;oBAAI,WAAU;;wBAEZ,gBAAgB,wBACf,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAI,WAAU;8DACZ,gBAAgB,GAAG,CAAC,CAAA,0BACnB,6LAAC,0IAAA,CAAA,YAAS;4DAER,WAAW;4DACX,SAAS;2DAFJ,UAAU,EAAE;;;;;;;;;;;;;;;;sDASzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAI,WAAU;8DACZ,aAAa,GAAG,CAAC,CAAA,sBAChB,6LAAC,sIAAA,CAAA,QAAK;4DAEJ,OAAO;4DACP,UAAU,CAAC,KAAO,kBAAkB,IAAI;4DACxC,WAAW,CAAC,KAAO,kBAAkB,IAAI;4DACzC,YAAY,CAAC,KAAO,kBAAkB,IAAI;2DAJrC,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;8CAYvB,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAI,WAAU;8DACZ,gBAAgB,GAAG,CAAC,CAAC,YAAY,sBAChC,6LAAC;4DAAgB,WAAU;sEACzB,cAAA,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;kFAA6B,WAAW,IAAI;;;;;;kFAC3D,6LAAC;wEAAI,WAAU;;4EAAwB;4EAAM,WAAW,QAAQ;;;;;;;;;;;;;2DAH1D;;;;;;;;;;;;;;;;sDAWhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;oEAAK,WAAU;8EAAc;;;;;;;;;;;;sEAEhC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;oEAAK,WAAU;8EAAc;;;;;;;;;;;;sEAEhC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;oEAAK,WAAU;8EAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASzC,gBAAgB,2BACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAAwC;wCACvC,QAAQ,MAAM;wCAAC;;;;;;;8CAE9B,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,GAAG,CAAC,CAAA,uBACX,6LAAC;4CAEC,WAAW,AAAC,2BAIX,OAHC,OAAO,EAAE,MAAK,0BAAA,oCAAA,cAAe,EAAE,IAC3B,mCACA;;8DAGN,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAY,OAAO,MAAM;;;;;;sEACzC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;;wEACX,OAAO,IAAI;wEACX,OAAO,EAAE,MAAK,0BAAA,oCAAA,cAAe,EAAE,mBAAI,6LAAC;4EAAK,WAAU;sFAA+B;;;;;;;;;;;;8EAErF,6LAAC;oEAAI,WAAU;;wEAAwB;wEAC9B,OAAO,KAAK;wEAClB,OAAO,MAAM,kBAAI,6LAAC;4EAAK,WAAU;sFAA+D;;;;;;;;;;;;;;;;;;;;;;;;8DAKvG,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;oEAAK,WAAU;8EAAiB;;;;;;;;;;;;sEAEnC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;oEAAK,WAAU;8EAAc;;;;;;;;;;;;;;;;;;;2CA5B7B,OAAO,EAAE;;;;;;;;;;;;;;;;wBAsCvB,gBAAgB,wBACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDAAoC;;;;;;;;;;;8CAIrD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6LAAC,qIAAA,CAAA,SAAM;sDAAC;;;;;;;;;;;;;;;;;;;;;;;;8BAOhB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,YAAY,IAAI,KAAK,8BACpB;;wCAAE;sDAAG,6LAAC;sDAAO;;;;;;wCAA0B;;iEAEvC;;wCAAE;sDAAG,6LAAC;sDAAO;;;;;;wCAA0B;;;;;;;;0CAG3C,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD;GA7RgB;;QACA,sIAAA,CAAA,cAAW;QASrB,yIAAA,CAAA,iBAAc;;;KAVJ", "debugId": null}}, {"offset": {"line": 2057, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { useLanguage } from '@/contexts/LanguageContext'\nimport { MultiplayerLobby } from '@/components/multiplayer/MultiplayerLobby'\nimport { MultiplayerGame } from '@/components/multiplayer/MultiplayerGame'\nimport { useMultiplayer } from '@/contexts/MultiplayerContext'\n\nexport default function Home() {\n  const { language, setLanguage, t } = useLanguage()\n  const { gameState } = useMultiplayer()\n  const [showMultiplayerLobby, setShowMultiplayerLobby] = useState(false)\n  const [showMultiplayerGame, setShowMultiplayerGame] = useState(false)\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-orange-100 to-yellow-100 flex items-center justify-center\">\n      <div className=\"text-center space-y-8 p-8\">\n        <div className=\"space-y-4\">\n          <h1 className=\"text-6xl font-bold text-orange-800 mb-4\">\n            🥖 {t('game.title')}\n          </h1>\n          <p className=\"text-xl text-orange-700 max-w-2xl mx-auto\">\n            {t('game.subtitle')}\n          </p>\n        </div>\n\n        <div className=\"space-y-4\">\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button\n              size=\"lg\"\n              className=\"text-lg px-8 py-4\"\n              onClick={() => window.location.href = '/game'}\n            >\n              {t('game.play')}\n            </Button>\n            <Button\n              variant=\"secondary\"\n              size=\"lg\"\n              className=\"text-lg px-8 py-4\"\n              onClick={() => setShowMultiplayerLobby(true)}\n            >\n              {t('game.multiplayer')}\n            </Button>\n          </div>\n\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button\n              variant={language === 'en' ? 'primary' : 'secondary'}\n              size=\"md\"\n              onClick={() => setLanguage('en')}\n            >\n              {t('game.english')}\n            </Button>\n            <Button\n              variant={language === 'cs' ? 'primary' : 'secondary'}\n              size=\"md\"\n              onClick={() => setLanguage('cs')}\n            >\n              {t('game.czech')}\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 max-w-4xl mx-auto\">\n          <div className=\"bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center\">\n            <div className=\"text-3xl mb-3\">🏪</div>\n            <h3 className=\"font-semibold text-orange-800 mb-2\">{t('features.manage.title')}</h3>\n            <p className=\"text-orange-700 text-sm\">\n              {t('features.manage.description')}\n            </p>\n          </div>\n\n          <div className=\"bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center\">\n            <div className=\"text-3xl mb-3\">📈</div>\n            <h3 className=\"font-semibold text-orange-800 mb-2\">{t('features.levelup.title')}</h3>\n            <p className=\"text-orange-700 text-sm\">\n              {t('features.levelup.description')}\n            </p>\n          </div>\n\n          <div className=\"bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center\">\n            <div className=\"text-3xl mb-3\">👥</div>\n            <h3 className=\"font-semibold text-orange-800 mb-2\">{t('features.multiplayer.title')}</h3>\n            <p className=\"text-orange-700 text-sm\">\n              {t('features.multiplayer.description')}\n            </p>\n          </div>\n        </div>\n\n        <div className=\"mt-8 text-sm text-orange-600\">\n          <p>{t('status.development')}</p>\n        </div>\n      </div>\n\n      {/* Multiplayer Components */}\n      <MultiplayerLobby\n        isOpen={showMultiplayerLobby}\n        onClose={() => setShowMultiplayerLobby(false)}\n      />\n      <MultiplayerGame\n        isOpen={showMultiplayerGame || gameState === 'playing'}\n        onClose={() => setShowMultiplayerGame(false)}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAC/C,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,iBAAc,AAAD;IACnC,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAA0C;oCAClD,EAAE;;;;;;;0CAER,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAIP,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;kDAErC,EAAE;;;;;;kDAEL,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,wBAAwB;kDAEtC,EAAE;;;;;;;;;;;;0CAIP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,OAAO,YAAY;wCACzC,MAAK;wCACL,SAAS,IAAM,YAAY;kDAE1B,EAAE;;;;;;kDAEL,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,OAAO,YAAY;wCACzC,MAAK;wCACL,SAAS,IAAM,YAAY;kDAE1B,EAAE;;;;;;;;;;;;;;;;;;kCAKT,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAAsC,EAAE;;;;;;kDACtD,6LAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;;;;;;;0CAIP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAAsC,EAAE;;;;;;kDACtD,6LAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;;;;;;;0CAIP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAAsC,EAAE;;;;;;kDACtD,6LAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;;;;;;;;;;;;;kCAKT,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;sCAAG,EAAE;;;;;;;;;;;;;;;;;0BAKV,6LAAC,wJAAA,CAAA,mBAAgB;gBACf,QAAQ;gBACR,SAAS,IAAM,wBAAwB;;;;;;0BAEzC,6LAAC,uJAAA,CAAA,kBAAe;gBACd,QAAQ,uBAAuB,cAAc;gBAC7C,SAAS,IAAM,uBAAuB;;;;;;;;;;;;AAI9C;GAjGwB;;QACe,sIAAA,CAAA,cAAW;QAC1B,yIAAA,CAAA,iBAAc;;;KAFd", "debugId": null}}]}