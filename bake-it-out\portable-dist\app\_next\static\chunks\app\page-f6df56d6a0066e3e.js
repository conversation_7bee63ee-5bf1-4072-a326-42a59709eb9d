(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{637:(e,t,s)=>{"use strict";s.d(t,{MultiplayerProvider:()=>d,K:()=>m});var a=s(5155),r=s(2115),l=s(4298),n=s(9509);class i{connect(){var e;null!=(e=this.socket)&&e.connected||(this.socket=(0,l.io)(n.env.NEXT_PUBLIC_SOCKET_URL||"http://localhost:3001",{transports:["websocket","polling"],timeout:2e4,forceNew:!0}),this.setupEventListeners())}setupEventListeners(){this.socket&&(this.socket.on("connect",()=>{console.log("Connected to multiplayer server"),this.isConnected=!0,this.reconnectAttempts=0}),this.socket.on("disconnect",e=>{console.log("Disconnected from multiplayer server:",e),this.isConnected=!1,"io server disconnect"===e&&this.handleReconnect()}),this.socket.on("connect_error",e=>{console.error("Connection error:",e),this.handleReconnect()}),this.socket.on("error",e=>{console.error("Socket error:",e)}))}handleReconnect(){this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,console.log("Attempting to reconnect (".concat(this.reconnectAttempts,"/").concat(this.maxReconnectAttempts,")...")),setTimeout(()=>{this.connect()},1e3*Math.pow(2,this.reconnectAttempts))):console.error("Max reconnection attempts reached")}createRoom(e){return new Promise((t,s)=>{var a;if(!(null==(a=this.socket)?void 0:a.connected))return void s(Error("Not connected to server"));this.socket.emit("create_room",e),this.socket.once("room_created",e=>{this.currentRoom=e,t(e)}),this.socket.once("error",e=>{s(Error(e.message))})})}joinRoom(e,t){return new Promise((s,a)=>{var r;if(!(null==(r=this.socket)?void 0:r.connected))return void a(Error("Not connected to server"));this.socket.emit("join_room",e,t),this.socket.once("room_joined",(e,t)=>{this.currentRoom=e,this.currentPlayer=t,s({room:e,player:t})}),this.socket.once("error",e=>{a(Error(e.message))})})}leaveRoom(){var e;(null==(e=this.socket)?void 0:e.connected)&&this.currentRoom&&(this.socket.emit("leave_room",this.currentRoom.id),this.currentRoom=null,this.currentPlayer=null)}sendPlayerAction(e){var t;(null==(t=this.socket)?void 0:t.connected)&&this.currentPlayer&&this.socket.emit("player_action",{...e,playerId:this.currentPlayer.id,timestamp:Date.now()})}sendMessage(e){var t;(null==(t=this.socket)?void 0:t.connected)&&this.currentPlayer&&this.socket.emit("send_message",{playerId:this.currentPlayer.id,playerName:this.currentPlayer.name,content:e,timestamp:Date.now()})}on(e,t){var s;null==(s=this.socket)||s.on(e,t)}off(e,t){var s;null==(s=this.socket)||s.off(e,t)}once(e,t){var s;null==(s=this.socket)||s.once(e,t)}isSocketConnected(){var e;return this.isConnected&&(null==(e=this.socket)?void 0:e.connected)===!0}getCurrentRoom(){return this.currentRoom}getCurrentPlayer(){return this.currentPlayer}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null,this.isConnected=!1,this.currentRoom=null,this.currentPlayer=null)}constructor(){this.socket=null,this.isConnected=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.currentRoom=null,this.currentPlayer=null,this.connect()}}let c=new i,o=(0,r.createContext)(void 0);function d(e){let{children:t}=e,[s,l]=(0,r.useState)(!1),[n,i]=(0,r.useState)(!1),[d,m]=(0,r.useState)(null),[x,u]=(0,r.useState)(null),[h,p]=(0,r.useState)(null),[g,y]=(0,r.useState)([]),[v,j]=(0,r.useState)("waiting"),[f,b]=(0,r.useState)(null),[N,w]=(0,r.useState)([]);(0,r.useEffect)(()=>{let e=()=>{l(!0),m(null)},t=()=>{l(!1),i(!1),u(null),p(null),y([])},s=e=>{m(e.message||"Connection error"),console.error("Multiplayer error:",e)},a=e=>{u(e),y(e.players),i(!0),j(e.gameState);let t=e.players.find(e=>e.isHost);t&&p(t)},r=(e,t)=>{u(e),p(t),y(e.players),i(!0),j(e.gameState)},n=()=>{u(null),p(null),y([]),i(!1),j("waiting"),b(null),w([])},o=e=>{u(e),y(e.players),j(e.gameState)},d=e=>{y(t=>[...t,e]),k("".concat(e.name," joined the room"))},x=e=>{y(t=>{let s=t.find(t=>t.id===e);return s&&k("".concat(s.name," left the room")),t.filter(t=>t.id!==e)})},h=e=>{j("playing"),b(e),k("Game started!")},g=e=>{b(t=>t?{...t,...e}:null)},v=e=>{console.log("Player action received:",e)},f=e=>{let t={id:Date.now().toString()+Math.random().toString(36).substring(2,11),playerId:e.playerId,playerName:e.playerName,content:e.content,timestamp:e.timestamp};w(e=>[...e,t])};return c.on("connect",e),c.on("disconnect",t),c.on("error",s),c.on("room_created",a),c.on("room_joined",r),c.on("room_left",n),c.on("room_updated",o),c.on("player_joined",d),c.on("player_left",x),c.on("game_started",h),c.on("game_state_update",g),c.on("player_action",v),c.on("message_received",f),l(c.isSocketConnected()),()=>{c.off("connect",e),c.off("disconnect",t),c.off("error",s),c.off("room_created",a),c.off("room_joined",r),c.off("room_left",n),c.off("room_updated",o),c.off("player_joined",d),c.off("player_left",x),c.off("game_started",h),c.off("game_state_update",g),c.off("player_action",v),c.off("message_received",f)}},[]);let k=e=>{let t={id:Date.now().toString()+Math.random().toString(36).substring(2,11),playerId:"system",playerName:"System",content:e,timestamp:Date.now()};w(e=>[...e,t])},C=(0,r.useCallback)(async(e,t)=>{try{m(null),await c.createRoom({...e,hostName:t.name,hostAvatar:t.avatar,hostLevel:t.level})}catch(e){throw m(e.message),e}},[]),S=(0,r.useCallback)(async(e,t)=>{try{m(null);let{room:s,player:a}=await c.joinRoom(e,t)}catch(e){throw m(e.message),e}},[]),_=(0,r.useCallback)(()=>{c.leaveRoom()},[]),R=(0,r.useCallback)(()=>{x&&(null==h?void 0:h.isHost)&&c.sendPlayerAction({type:"start_game",data:{roomId:x.id}})},[x,h]),D=(0,r.useCallback)(e=>{c.sendMessage(e)},[]),P=(0,r.useCallback)(e=>{c.sendPlayerAction(e)},[]),A=(0,r.useCallback)(e=>{h&&P({type:"player_ready",data:{ready:e}})},[h,P]),$=(0,r.useCallback)(e=>{(null==h?void 0:h.isHost)&&P({type:"kick_player",data:{playerId:e}})},[h,P]),E=(0,r.useCallback)(e=>{(null==h?void 0:h.isHost)&&P({type:"update_room_settings",data:{settings:e}})},[h,P]);return(0,a.jsx)(o.Provider,{value:{isConnected:s,isInRoom:n,connectionError:d,currentRoom:x,currentPlayer:h,players:g,gameState:v,sharedGameState:f,messages:N,createRoom:C,joinRoom:S,leaveRoom:_,startGame:R,sendMessage:D,sendPlayerAction:P,setPlayerReady:A,kickPlayer:$,updateRoomSettings:E},children:t})}function m(){let e=(0,r.useContext)(o);return void 0===e?(console.warn("useMultiplayer called outside of MultiplayerProvider, using fallback"),{isConnected:!1,connectionStatus:"disconnected",currentRoom:null,gameState:null,createRoom:async()=>{},joinRoom:async()=>{},leaveRoom:()=>{},sendChatMessage:()=>{},updateGameState:()=>{},setPlayerReady:()=>{}}):e}},2163:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var a=s(5155),r=s(3741),l=s(9283);function n(e){let{order:t,onAccept:s,onDecline:n,onComplete:i}=e,{t:c}=(0,l.o)();return(0,a.jsxs)("div",{className:"p-4 rounded-lg border ".concat((()=>{switch(t.status){case"pending":return"border-yellow-300 bg-yellow-50";case"accepted":case"in_progress":return"border-blue-300 bg-blue-50";case"completed":return"border-green-300 bg-green-50";case"failed":return"border-red-300 bg-red-50";default:return"border-gray-300 bg-gray-50"}})()),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-lg",children:(()=>{let e=["\uD83D\uDC69","\uD83D\uDC68","\uD83D\uDC75","\uD83D\uDC74","\uD83D\uDC67","\uD83D\uDC66"],s=t.customerName.length%e.length;return e[s]})()}),(0,a.jsx)("h3",{className:"font-medium text-gray-800",children:t.customerName})]}),(0,a.jsx)("span",{className:"text-sm font-semibold text-green-600",children:c("orders.reward",{amount:t.reward.toString()})})]}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:t.items.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("span",{children:"\uD83E\uDDC1"}),(0,a.jsx)("span",{children:e})]},t))}),(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["⏰ ",c("orders.timeLimit",{time:(e=>{let t=Math.floor(e/60);return"".concat(t,":").concat((e%60).toString().padStart(2,"0"))})(t.timeLimit)})]}),(0,a.jsx)("div",{className:"text-xs",title:"Difficulty: ".concat(t.difficulty,"/5"),children:"⭐".repeat(t.difficulty)+"☆".repeat(5-t.difficulty)})]}),"pending"===t.status&&(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)(r.$,{size:"sm",variant:"success",onClick:()=>s(t.id),className:"flex-1",children:["✅ ",c("orders.accept")]}),(0,a.jsxs)(r.$,{size:"sm",variant:"danger",onClick:()=>n(t.id),className:"flex-1",children:["❌ ",c("orders.decline")]})]}),"accepted"===t.status&&(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-blue-600 text-sm font-medium mb-2",children:"\uD83D\uDCCB Order Accepted"}),(0,a.jsxs)(r.$,{size:"sm",variant:"primary",onClick:()=>i&&i(t.id),className:"w-full",children:["\uD83C\uDFAF ",c("orders.complete")]})]}),"in_progress"===t.status&&(0,a.jsxs)("div",{className:"text-center text-orange-600 text-sm font-medium",children:["\uD83D\uDD04 ",c("orders.inProgress")]}),"completed"===t.status&&(0,a.jsx)("div",{className:"text-center text-green-600 text-sm font-medium",children:"✅ Completed!"}),"failed"===t.status&&(0,a.jsx)("div",{className:"text-center text-red-600 text-sm font-medium",children:"❌ Failed"})]})}},2617:(e,t,s)=>{Promise.resolve().then(s.bind(s,8775))},3741:(e,t,s)=>{"use strict";s.d(t,{$:()=>r});var a=s(5155);s(2115);let r=e=>{let{variant:t="primary",size:s="md",className:r="",children:l,...n}=e,i=["font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2",{primary:"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500",secondary:"bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500",danger:"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500",success:"bg-green-600 hover:bg-green-700 text-white focus:ring-green-500"}[t],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"}[s],r].join(" ");return(0,a.jsx)("button",{className:i,...n,children:l})}},8775:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var a=s(5155),r=s(2115),l=s(3741),n=s(9283),i=s(637);function c(e){let{isOpen:t,onClose:s}=e,{t:c}=(0,n.o)(),{isConnected:o,isInRoom:d,connectionError:m,currentRoom:x,currentPlayer:u,players:h,gameState:p,messages:g,createRoom:y,joinRoom:v,leaveRoom:j,startGame:f,sendMessage:b,setPlayerReady:N}=(0,i.K)(),[w,k]=(0,r.useState)("create"),[C,S]=(0,r.useState)(""),[_,R]=(0,r.useState)(""),[D,P]=(0,r.useState)(""),[A,$]=(0,r.useState)("cooperative"),[E,L]=(0,r.useState)(4),[M,I]=(0,r.useState)(""),[z,H]=(0,r.useState)(!1);if(!t)return null;let O=async()=>{if(_.trim()&&C.trim())try{await y({name:C,mode:A,maxPlayers:E,settings:{gameMode:A,difficulty:"medium",allowSpectators:!0}},{name:_,avatar:"\uD83D\uDC68‍\uD83C\uDF73",level:1}),k("room")}catch(e){console.error("Failed to create room:",e)}},F=async()=>{if(_.trim()&&D.trim())try{await v(D.toUpperCase(),{name:_,avatar:"\uD83D\uDC68‍\uD83C\uDF73",level:1}),k("room")}catch(e){console.error("Failed to join room:",e)}},K=()=>{M.trim()&&(b(M),I(""))},q=[{id:"create",name:c("multiplayer.createRoom"),icon:"\uD83C\uDFD7️"},{id:"join",name:c("multiplayer.joinRoom"),icon:"\uD83D\uDEAA"},...d?[{id:"room",name:c("multiplayer.room"),icon:"\uD83C\uDFE0"}]:[]];return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:c("multiplayer.lobby")}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"px-3 py-1 rounded-full text-sm ".concat(o?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:o?c("multiplayer.connected"):c("multiplayer.disconnected")}),(0,a.jsx)(l.$,{variant:"secondary",onClick:s,children:c("game.close")})]})]}),m&&(0,a.jsx)("div",{className:"mt-2 p-2 bg-red-100 text-red-800 rounded text-sm",children:c("multiplayer.connection.error",{error:m})})]}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("div",{className:"flex space-x-0",children:q.map(e=>(0,a.jsxs)("button",{onClick:()=>k(e.id),className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(w===e.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.icon," ",e.name]},e.id))})}),(0,a.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["create"===w&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("multiplayer.yourName")}),(0,a.jsx)("input",{type:"text",value:_,onChange:e=>R(e.target.value),placeholder:c("multiplayer.enterName"),className:"w-full p-3 border rounded-lg",maxLength:20})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("multiplayer.roomName")}),(0,a.jsx)("input",{type:"text",value:C,onChange:e=>S(e.target.value),placeholder:c("multiplayer.enterRoomName"),className:"w-full p-3 border rounded-lg",maxLength:30})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("multiplayer.gameMode")}),(0,a.jsxs)("select",{value:A,onChange:e=>$(e.target.value),className:"w-full p-3 border rounded-lg",children:[(0,a.jsx)("option",{value:"cooperative",children:c("multiplayer.cooperative")}),(0,a.jsx)("option",{value:"competitive",children:c("multiplayer.competitive")})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("multiplayer.maxPlayers",{count:E.toString()})}),(0,a.jsx)("input",{type:"range",min:"2",max:"8",value:E,onChange:e=>L(parseInt(e.target.value)),className:"w-full"})]})]}),(0,a.jsx)(l.$,{variant:"primary",size:"lg",className:"w-full",onClick:O,disabled:!o||!_.trim()||!C.trim(),children:c("multiplayer.create.title")})]}),"join"===w&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("multiplayer.yourName")}),(0,a.jsx)("input",{type:"text",value:_,onChange:e=>R(e.target.value),placeholder:c("multiplayer.enterName"),className:"w-full p-3 border rounded-lg",maxLength:20})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("multiplayer.roomId")}),(0,a.jsx)("input",{type:"text",value:D,onChange:e=>P(e.target.value.toUpperCase()),placeholder:c("multiplayer.enterRoomId"),className:"w-full p-3 border rounded-lg font-mono",maxLength:6})]})]}),(0,a.jsx)(l.$,{variant:"primary",size:"lg",className:"w-full",onClick:F,disabled:!o||!_.trim()||!D.trim(),children:c("multiplayer.join.title")})]}),"room"===w&&x&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("h3",{className:"font-semibold text-blue-800",children:x.name}),(0,a.jsxs)("div",{className:"text-sm text-blue-600",children:[c("multiplayer.roomId"),": ",(0,a.jsx)("span",{className:"font-mono font-bold",children:x.id})]})]}),(0,a.jsx)("div",{className:"text-sm text-blue-700",children:c("multiplayer.room.info",{mode:x.mode,current:x.currentPlayers.toString(),max:x.maxPlayers.toString()})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:c("multiplayer.players",{count:h.length.toString()})}),(0,a.jsx)("div",{className:"space-y-2",children:h.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-2xl",children:e.avatar}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium",children:[e.name,e.isHost&&(0,a.jsx)("span",{className:"ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded",children:c("multiplayer.host")})]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:c("multiplayer.level",{level:e.level.toString()})})]})]}),(0,a.jsx)("div",{className:"px-2 py-1 rounded text-xs ".concat(e.isReady?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"),children:e.isReady?c("common.ready"):c("common.notReady")})]},e.id))})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(l.$,{variant:z?"success":"secondary",onClick:()=>{let e=!z;H(e),N(e)},className:"flex-1",children:z?c("multiplayer.room.readyUp"):c("multiplayer.room.notReady")}),(null==u?void 0:u.isHost)&&(0,a.jsx)(l.$,{variant:"primary",onClick:()=>{(null==u?void 0:u.isHost)&&f()},disabled:!h.every(e=>e.isReady)||h.length<2,children:c("multiplayer.room.startGame")}),(0,a.jsx)(l.$,{variant:"secondary",onClick:()=>{j(),k("create"),H(!1)},children:c("multiplayer.room.leaveRoom")})]}),(0,a.jsxs)("div",{className:"border-t pt-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:c("multiplayer.chat")}),(0,a.jsx)("div",{className:"bg-gray-50 p-3 rounded-lg h-32 overflow-y-auto mb-3",children:g.map(e=>(0,a.jsxs)("div",{className:"text-sm mb-1",children:[(0,a.jsxs)("span",{className:"font-medium ".concat("system"===e.playerId?"text-blue-600":"text-gray-800"),children:[e.playerName,":"]}),(0,a.jsx)("span",{className:"ml-2",children:e.content})]},e.id))}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("input",{type:"text",value:M,onChange:e=>I(e.target.value),onKeyDown:e=>"Enter"===e.key&&K(),placeholder:c("multiplayer.typeMessage"),className:"flex-1 p-2 border rounded",maxLength:100}),(0,a.jsx)(l.$,{size:"sm",onClick:K,children:c("common.send")})]})]})]})]})]})})}var o=s(9419),d=s(2163);function m(e){let{isOpen:t,onClose:s}=e,{t:c}=(0,n.o)(),{currentRoom:m,currentPlayer:x,players:u,gameState:h,sharedGameState:p,sendPlayerAction:g,leaveRoom:y}=(0,i.K)(),[v,j]=(0,r.useState)("game");if(!t||!m||"playing"!==h)return null;let f=(e,t)=>{g({type:"use_equipment",data:{equipmentId:e,equipmentName:t,playerId:null==x?void 0:x.id}})},b=(e,t)=>{g({type:"order_action",data:{orderId:e,action:t,playerId:null==x?void 0:x.id}})},N=[{id:"game",name:c("multiplayer.game.tabs.game"),icon:"\uD83C\uDFAE"},{id:"players",name:c("multiplayer.game.tabs.players"),icon:"\uD83D\uDC65"},{id:"chat",name:c("multiplayer.game.tabs.chat"),icon:"\uD83D\uDCAC"}];return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:c("multiplayer.game.title",{roomName:m.name})}),(0,a.jsxs)("p",{className:"text-gray-600",children:[c("multiplayer.game.mode",{mode:m.mode})," • ",c("multiplayer.game.playersCount",{count:u.length.toString()})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,a.jsx)("span",{className:"text-green-800 text-sm",children:c("multiplayer.game.playing")})}),(0,a.jsx)(l.$,{variant:"secondary",onClick:()=>{y(),s()},children:c("multiplayer.game.leaveGame")})]})]})}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("div",{className:"flex space-x-0",children:N.map(e=>(0,a.jsxs)("button",{onClick:()=>j(e.id),className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(v===e.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.icon," ",e.name]},e.id))})}),(0,a.jsxs)("div",{className:"p-6 max-h-[70vh] overflow-y-auto",children:["game"===v&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:c("multiplayer.sharedKitchen")}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[{id:"oven1",name:"Shared Oven",type:"oven",isActive:!1,level:1,efficiency:1,automationLevel:0},{id:"mixer1",name:"Shared Mixer",type:"mixer",isActive:!1,level:1,efficiency:1,automationLevel:0},{id:"counter1",name:"Shared Counter",type:"counter",isActive:!1,level:1,efficiency:1,automationLevel:0}].map(e=>(0,a.jsx)(o.$,{equipment:e,onClick:f},e.id))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mt-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:c("multiplayer.sharedOrders")}),(0,a.jsx)("div",{className:"space-y-4",children:[{id:"1",customerName:"Shared Customer",items:["Chocolate Chip Cookies"],timeLimit:300,reward:50,status:"pending",difficulty:1}].map(e=>(0,a.jsx)(d.p,{order:e,onAccept:e=>b(e,"accept"),onDecline:e=>b(e,"decline"),onComplete:e=>b(e,"complete")},e.id))})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:c("multiplayer.sharedInventory")}),(0,a.jsx)("div",{className:"space-y-3",children:[{name:"Flour",quantity:20,cost:2},{name:"Sugar",quantity:15,cost:3},{name:"Eggs",quantity:12,cost:4}].map((e,t)=>(0,a.jsx)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-800",children:e.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Qty: ",e.quantity]})]})},t))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:c("multiplayer.teamStats")}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:c("multiplayer.ordersCompleted")}),(0,a.jsx)("span",{className:"font-medium",children:"0"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:c("multiplayer.totalRevenue")}),(0,a.jsx)("span",{className:"font-medium",children:"$0"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:c("multiplayer.teamExperience")}),(0,a.jsx)("span",{className:"font-medium",children:"0 XP"})]})]})]})]})]}),"players"===v&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-orange-800",children:c("multiplayer.players",{count:u.length.toString()})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:u.map(e=>(0,a.jsxs)("div",{className:"p-4 rounded-lg border-2 ".concat(e.id===(null==x?void 0:x.id)?"border-orange-400 bg-orange-50":"border-gray-300 bg-white"),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,a.jsx)("span",{className:"text-3xl",children:e.avatar}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"font-semibold text-gray-800",children:[e.name,e.id===(null==x?void 0:x.id)&&(0,a.jsx)("span",{className:"ml-2 text-sm text-orange-600",children:c("multiplayer.you")})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[c("multiplayer.level",{level:e.level.toString()}),e.isHost&&(0,a.jsx)("span",{className:"ml-2 bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs",children:c("multiplayer.host")})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:c("multiplayer.status")}),(0,a.jsx)("span",{className:"text-green-600",children:c("multiplayer.online")})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:c("multiplayer.contribution")}),(0,a.jsx)("span",{className:"font-medium",children:"0 orders"})]})]})]},e.id))})]}),"chat"===v&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-orange-800",children:c("multiplayer.teamChat")}),(0,a.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg h-64 overflow-y-auto",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 text-center",children:c("multiplayer.chatPlaceholder")})}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("input",{type:"text",placeholder:c("multiplayer.typeMessage"),className:"flex-1 p-3 border rounded-lg"}),(0,a.jsx)(l.$,{children:c("common.send")})]})]})]}),(0,a.jsx)("div",{className:"p-4 bg-blue-50 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"text-sm text-blue-700",children:"cooperative"===m.mode?c("multiplayer.mode.cooperative.description"):c("multiplayer.mode.competitive.description")}),(0,a.jsx)("div",{className:"text-sm text-blue-600",children:c("multiplayer.gameTime",{time:"00:00"})})]})})]})})}function x(){let{language:e,setLanguage:t,t:s}=(0,n.o)(),{gameState:o}=(0,i.K)(),[d,x]=(0,r.useState)(!1),[u,h]=(0,r.useState)(!1);return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-orange-100 to-yellow-100 flex items-center justify-center",children:[(0,a.jsxs)("div",{className:"text-center space-y-8 p-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h1",{className:"text-6xl font-bold text-orange-800 mb-4",children:["\uD83E\uDD56 ",s("game.title")]}),(0,a.jsx)("p",{className:"text-xl text-orange-700 max-w-2xl mx-auto",children:s("game.subtitle")})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(l.$,{size:"lg",className:"text-lg px-8 py-4",onClick:()=>window.location.href="/game",children:s("game.play")}),(0,a.jsx)(l.$,{variant:"secondary",size:"lg",className:"text-lg px-8 py-4",onClick:()=>x(!0),children:s("game.multiplayer")})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(l.$,{variant:"en"===e?"primary":"secondary",size:"md",onClick:()=>t("en"),children:s("game.english")}),(0,a.jsx)(l.$,{variant:"cs"===e?"primary":"secondary",size:"md",onClick:()=>t("cs"),children:s("game.czech")})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center",children:[(0,a.jsx)("div",{className:"text-3xl mb-3",children:"\uD83C\uDFEA"}),(0,a.jsx)("h3",{className:"font-semibold text-orange-800 mb-2",children:s("features.manage.title")}),(0,a.jsx)("p",{className:"text-orange-700 text-sm",children:s("features.manage.description")})]}),(0,a.jsxs)("div",{className:"bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center",children:[(0,a.jsx)("div",{className:"text-3xl mb-3",children:"\uD83D\uDCC8"}),(0,a.jsx)("h3",{className:"font-semibold text-orange-800 mb-2",children:s("features.levelup.title")}),(0,a.jsx)("p",{className:"text-orange-700 text-sm",children:s("features.levelup.description")})]}),(0,a.jsxs)("div",{className:"bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center",children:[(0,a.jsx)("div",{className:"text-3xl mb-3",children:"\uD83D\uDC65"}),(0,a.jsx)("h3",{className:"font-semibold text-orange-800 mb-2",children:s("features.multiplayer.title")}),(0,a.jsx)("p",{className:"text-orange-700 text-sm",children:s("features.multiplayer.description")})]})]}),(0,a.jsx)("div",{className:"mt-8 text-sm text-orange-600",children:(0,a.jsx)("p",{children:s("status.development")})})]}),(0,a.jsx)(c,{isOpen:d,onClose:()=>x(!1)}),(0,a.jsx)(m,{isOpen:u||"playing"===o,onClose:()=>h(!1)})]})}},9419:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var a=s(5155),r=s(9283);function l(e){let{equipment:t,onClick:s}=e,{t:l}=(0,r.o)();return(0,a.jsx)("div",{className:"p-4 rounded-lg border-2 cursor-pointer transition-all ".concat(t.isActive?"border-green-400 bg-green-50":"border-gray-200 bg-gray-50 hover:border-orange-300 hover:bg-orange-50"),onClick:()=>!t.isActive&&s(t.id,t.name),children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl mb-2",children:(e=>{switch(e){case"oven":return"\uD83D\uDD25";case"mixer":return"\uD83E\uDD44";case"counter":return"\uD83C\uDF7D️";default:return"⚙️"}})(t.type)}),(0,a.jsx)("h3",{className:"font-medium text-gray-800",children:t.name}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["Level ",t.level]}),t.isActive&&t.timeRemaining?(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("div",{className:"text-sm text-green-600",children:l("kitchen.making",{recipe:t.currentRecipe||""})}),(0,a.jsx)("div",{className:"text-lg font-mono text-green-700",children:(e=>{let t=Math.floor(e/60);return"".concat(t,":").concat((e%60).toString().padStart(2,"0"))})(t.timeRemaining)}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,a.jsx)("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-1000",style:{width:"".concat(100-t.timeRemaining/60*100,"%")}})})]}):(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2",children:t.isActive?"Busy":l("kitchen.clickToUse")})]})})}}},e=>{e.O(0,[298,283,441,964,358],()=>e(e.s=2617)),_N_E=e.O()}]);