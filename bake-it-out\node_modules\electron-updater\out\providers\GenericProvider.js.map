{"version": 3, "file": "GenericProvider.js", "sourceRoot": "", "sources": ["../../src/providers/GenericProvider.ts"], "names": [], "mappings": ";;;AAAA,+DAA4F;AAG5F,kCAAwE;AACxE,yCAA4F;AAE5F,MAAa,eAAgB,SAAQ,mBAAoB;IAGvD,YACmB,aAAmC,EACnC,OAAmB,EACpC,cAAsC;QAEtC,KAAK,CAAC,cAAc,CAAC,CAAA;QAJJ,kBAAa,GAAb,aAAa,CAAsB;QACnC,YAAO,GAAP,OAAO,CAAY;QAJrB,YAAO,GAAG,IAAA,iBAAU,EAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;IAQ7D,CAAC;IAED,IAAY,OAAO;QACjB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAA;QACjE,OAAO,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAA;IAC1F,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,MAAM,WAAW,GAAG,IAAA,yBAAkB,EAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QACpD,MAAM,UAAU,GAAG,IAAA,qBAAc,EAAC,WAAW,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;QAC5F,KAAK,IAAI,aAAa,GAAG,CAAC,GAAI,aAAa,EAAE,EAAE,CAAC;YAC9C,IAAI,CAAC;gBACH,OAAO,IAAA,0BAAe,EAAC,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,WAAW,EAAE,UAAU,CAAC,CAAA;YACrF,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,YAAY,gCAAS,IAAI,CAAC,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;oBACnD,MAAM,IAAA,+BAAQ,EAAC,wBAAwB,WAAW,kBAAkB,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,oCAAoC,CAAC,CAAA;gBACnI,CAAC;qBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;oBACrC,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;wBACtB,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;4BACpC,IAAI,CAAC;gCACH,UAAU,CAAC,OAAO,EAAE,IAAI,GAAG,aAAa,CAAC,CAAA;4BAC3C,CAAC;4BAAC,OAAO,CAAM,EAAE,CAAC;gCAChB,MAAM,CAAC,CAAC,CAAC,CAAA;4BACX,CAAC;wBACH,CAAC,CAAC,CAAA;wBACF,SAAQ;oBACV,CAAC;gBACH,CAAC;gBACD,MAAM,CAAC,CAAA;YACT,CAAC;QACH,CAAC;IACH,CAAC;IAED,YAAY,CAAC,UAAsB;QACjC,OAAO,IAAA,uBAAY,EAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;IAC/C,CAAC;CACF;AA7CD,0CA6CC", "sourcesContent": ["import { GenericServerOptions, HttpError, newError, UpdateInfo } from \"builder-util-runtime\"\nimport { AppUpdater } from \"../AppUpdater\"\nimport { ResolvedUpdateFileInfo } from \"../types\"\nimport { getChannelFilename, newBaseUrl, newUrlFromBase } from \"../util\"\nimport { parseUpdateInfo, Provider, ProviderRuntimeOptions, resolveFiles } from \"./Provider\"\n\nexport class GenericProvider extends Provider<UpdateInfo> {\n  private readonly baseUrl = newBaseUrl(this.configuration.url)\n\n  constructor(\n    private readonly configuration: GenericServerOptions,\n    private readonly updater: AppUpdater,\n    runtimeOptions: ProviderRuntimeOptions\n  ) {\n    super(runtimeOptions)\n  }\n\n  private get channel(): string {\n    const result = this.updater.channel || this.configuration.channel\n    return result == null ? this.getDefaultChannelName() : this.getCustomChannelName(result)\n  }\n\n  async getLatestVersion(): Promise<UpdateInfo> {\n    const channelFile = getChannelFilename(this.channel)\n    const channelUrl = newUrlFromBase(channelFile, this.baseUrl, this.updater.isAddNoCacheQuery)\n    for (let attemptNumber = 0; ; attemptNumber++) {\n      try {\n        return parseUpdateInfo(await this.httpRequest(channelUrl), channelFile, channelUrl)\n      } catch (e: any) {\n        if (e instanceof HttpError && e.statusCode === 404) {\n          throw newError(`Cannot find channel \"${channelFile}\" update info: ${e.stack || e.message}`, \"ERR_UPDATER_CHANNEL_FILE_NOT_FOUND\")\n        } else if (e.code === \"ECONNREFUSED\") {\n          if (attemptNumber < 3) {\n            await new Promise((resolve, reject) => {\n              try {\n                setTimeout(resolve, 1000 * attemptNumber)\n              } catch (e: any) {\n                reject(e)\n              }\n            })\n            continue\n          }\n        }\n        throw e\n      }\n    }\n  }\n\n  resolveFiles(updateInfo: UpdateInfo): Array<ResolvedUpdateFileInfo> {\n    return resolveFiles(updateInfo, this.baseUrl)\n  }\n}\n"]}