{"version": 3, "file": "AppImageUpdater.js", "sourceRoot": "", "sources": ["../src/AppImageUpdater.ts"], "names": [], "mappings": ";;;AAAA,+DAAkE;AAClE,iDAA4C;AAC5C,uCAAgC;AAChC,2BAA+B;AAC/B,6BAA4B;AAE5B,+CAA2D;AAE3D,4IAAwI;AACxI,mDAAyD;AACzD,mCAAmE;AAEnE,MAAa,eAAgB,SAAQ,yBAAW;IAC9C,YAAY,OAAkC,EAAE,GAAS;QACvD,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;IACrB,CAAC;IAEM,eAAe;QACpB,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE,CAAC;YACpC,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAChC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAA;YAC1F,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAA;YAC/D,CAAC;YACD,OAAO,KAAK,CAAA;QACd,CAAC;QACD,OAAO,KAAK,CAAC,eAAe,EAAE,CAAA;IAChC,CAAC;IAED,gBAAgB;IACN,gBAAgB,CAAC,qBAA4C;QACrE,MAAM,QAAQ,GAAG,qBAAqB,CAAC,qBAAqB,CAAC,QAAQ,CAAA;QACrE,MAAM,QAAQ,GAAG,IAAA,mBAAQ,EAAC,QAAQ,CAAC,YAAY,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAE,CAAA;QACzI,OAAO,IAAI,CAAC,eAAe,CAAC;YAC1B,aAAa,EAAE,UAAU;YACzB,QAAQ;YACR,qBAAqB;YACrB,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,eAAe,EAAE,EAAE;gBAC1C,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAE,CAAA;gBACxC,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;oBACpB,MAAM,IAAA,+BAAQ,EAAC,6BAA6B,EAAE,gCAAgC,CAAC,CAAA;gBACjF,CAAC;gBAED,IAAI,qBAAqB,CAAC,2BAA2B,IAAI,CAAC,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAAC,EAAE,CAAC;oBAC3J,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,UAAU,EAAE,eAAe,CAAC,CAAA;gBAC7E,CAAC;gBAED,MAAM,IAAA,gBAAK,EAAC,UAAU,EAAE,KAAK,CAAC,CAAA;YAChC,CAAC;SACF,CAAC,CAAA;IACJ,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,QAAgC,EAAE,OAAe,EAAE,UAAkB,EAAE,QAAuB,EAAE,qBAA4C;QAC7K,IAAI,CAAC;YACH,MAAM,eAAe,GAAkC;gBACrD,MAAM,EAAE,QAAQ,CAAC,GAAG;gBACpB,OAAO;gBACP,MAAM,EAAE,IAAI,CAAC,OAAO;gBACpB,OAAO,EAAE,UAAU;gBACnB,yBAAyB,EAAE,QAAQ,CAAC,yBAAyB;gBAC7D,cAAc,EAAE,qBAAqB,CAAC,cAAc;gBACpD,iBAAiB,EAAE,qBAAqB,CAAC,iBAAiB;aAC3D,CAAA;YAED,IAAI,IAAI,CAAC,aAAa,CAAC,yBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9C,eAAe,CAAC,UAAU,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAiB,EAAE,EAAE,CAAC,CAAA;YACrE,CAAC;YAED,MAAM,IAAI,+FAA8C,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC,QAAQ,EAAE,CAAA;YACtH,OAAO,KAAK,CAAA;QACd,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAA;YAChG,0DAA0D;YAC1D,OAAO,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAA;QACrC,CAAC;IACH,CAAC;IAES,SAAS,CAAC,OAAuB;QACzC,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAE,CAAA;QAC7C,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;YACzB,MAAM,IAAA,+BAAQ,EAAC,6BAA6B,EAAE,gCAAgC,CAAC,CAAA;QACjF,CAAC;QAED,8CAA8C;QAC9C,IAAA,eAAU,EAAC,YAAY,CAAC,CAAA;QAExB,IAAI,WAAmB,CAAA;QACvB,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAA;QACpD,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAA;QACxC,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;YAC1B,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC,CAAA;YAClF,OAAO,KAAK,CAAA;QACd,CAAC;QACD,oEAAoE;QACpE,gGAAgG;QAChG,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,gBAAgB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACjG,kDAAkD;YAClD,WAAW,GAAG,YAAY,CAAA;QAC5B,CAAC;aAAM,CAAC;YACN,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAA;QACnF,CAAC;QAED,IAAA,4BAAY,EAAC,IAAI,EAAE,CAAC,IAAI,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC,CAAA;QACtD,IAAI,WAAW,KAAK,YAAY,EAAE,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,WAAW,CAAC,CAAA;QACrD,CAAC;QAED,MAAM,GAAG,GAAsB;YAC7B,GAAG,OAAO,CAAC,GAAG;YACd,uBAAuB,EAAE,MAAM;SAChC,CAAA;QAED,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC5B,mEAAmE;YACnE,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAE,EAAE,GAAG,CAAC,CAAA;QACrC,CAAC;aAAM,CAAC;YACN,GAAG,CAAC,2BAA2B,GAAG,MAAM,CAAA;YACxC,IAAA,4BAAY,EAAC,WAAW,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAA;QACxC,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;CACF;AA7GD,0CA6GC", "sourcesContent": ["import { AllPublishOptions, newError } from \"builder-util-runtime\"\nimport { execFileSync } from \"child_process\"\nimport { chmod } from \"fs-extra\"\nimport { unlinkSync } from \"fs\"\nimport * as path from \"path\"\nimport { DownloadUpdateOptions } from \"./AppUpdater\"\nimport { BaseUpdater, InstallOptions } from \"./BaseUpdater\"\nimport { DifferentialDownloaderOptions } from \"./differentialDownloader/DifferentialDownloader\"\nimport { FileWithEmbeddedBlockMapDifferentialDownloader } from \"./differentialDownloader/FileWithEmbeddedBlockMapDifferentialDownloader\"\nimport { findFile, Provider } from \"./providers/Provider\"\nimport { DOWNLOAD_PROGRESS, ResolvedUpdateFileInfo } from \"./types\"\n\nexport class AppImageUpdater extends BaseUpdater {\n  constructor(options?: AllPublishOptions | null, app?: any) {\n    super(options, app)\n  }\n\n  public isUpdaterActive(): boolean {\n    if (process.env[\"APPIMAGE\"] == null) {\n      if (process.env[\"SNAP\"] == null) {\n        this._logger.warn(\"APPIMAGE env is not defined, current application is not an AppImage\")\n      } else {\n        this._logger.info(\"SNAP env is defined, updater is disabled\")\n      }\n      return false\n    }\n    return super.isUpdaterActive()\n  }\n\n  /*** @private */\n  protected doDownloadUpdate(downloadUpdateOptions: DownloadUpdateOptions): Promise<Array<string>> {\n    const provider = downloadUpdateOptions.updateInfoAndProvider.provider\n    const fileInfo = findFile(provider.resolveFiles(downloadUpdateOptions.updateInfoAndProvider.info), \"AppImage\", [\"rpm\", \"deb\", \"pacman\"])!\n    return this.executeDownload({\n      fileExtension: \"AppImage\",\n      fileInfo,\n      downloadUpdateOptions,\n      task: async (updateFile, downloadOptions) => {\n        const oldFile = process.env[\"APPIMAGE\"]!\n        if (oldFile == null) {\n          throw newError(\"APPIMAGE env is not defined\", \"ERR_UPDATER_OLD_FILE_NOT_FOUND\")\n        }\n\n        if (downloadUpdateOptions.disableDifferentialDownload || (await this.downloadDifferential(fileInfo, oldFile, updateFile, provider, downloadUpdateOptions))) {\n          await this.httpExecutor.download(fileInfo.url, updateFile, downloadOptions)\n        }\n\n        await chmod(updateFile, 0o755)\n      },\n    })\n  }\n\n  private async downloadDifferential(fileInfo: ResolvedUpdateFileInfo, oldFile: string, updateFile: string, provider: Provider<any>, downloadUpdateOptions: DownloadUpdateOptions) {\n    try {\n      const downloadOptions: DifferentialDownloaderOptions = {\n        newUrl: fileInfo.url,\n        oldFile,\n        logger: this._logger,\n        newFile: updateFile,\n        isUseMultipleRangeRequest: provider.isUseMultipleRangeRequest,\n        requestHeaders: downloadUpdateOptions.requestHeaders,\n        cancellationToken: downloadUpdateOptions.cancellationToken,\n      }\n\n      if (this.listenerCount(DOWNLOAD_PROGRESS) > 0) {\n        downloadOptions.onProgress = it => this.emit(DOWNLOAD_PROGRESS, it)\n      }\n\n      await new FileWithEmbeddedBlockMapDifferentialDownloader(fileInfo.info, this.httpExecutor, downloadOptions).download()\n      return false\n    } catch (e: any) {\n      this._logger.error(`Cannot download differentially, fallback to full download: ${e.stack || e}`)\n      // during test (developer machine mac) we must throw error\n      return process.platform === \"linux\"\n    }\n  }\n\n  protected doInstall(options: InstallOptions): boolean {\n    const appImageFile = process.env[\"APPIMAGE\"]!\n    if (appImageFile == null) {\n      throw newError(\"APPIMAGE env is not defined\", \"ERR_UPDATER_OLD_FILE_NOT_FOUND\")\n    }\n\n    // https://stackoverflow.com/a/1712051/1910191\n    unlinkSync(appImageFile)\n\n    let destination: string\n    const existingBaseName = path.basename(appImageFile)\n    const installerPath = this.installerPath\n    if (installerPath == null) {\n      this.dispatchError(new Error(\"No valid update available, can't quit and install\"))\n      return false\n    }\n    // https://github.com/electron-userland/electron-builder/issues/2964\n    // if no version in existing file name, it means that user wants to preserve current custom name\n    if (path.basename(installerPath) === existingBaseName || !/\\d+\\.\\d+\\.\\d+/.test(existingBaseName)) {\n      // no version in the file name, overwrite existing\n      destination = appImageFile\n    } else {\n      destination = path.join(path.dirname(appImageFile), path.basename(installerPath))\n    }\n\n    execFileSync(\"mv\", [\"-f\", installerPath, destination])\n    if (destination !== appImageFile) {\n      this.emit(\"appimage-filename-updated\", destination)\n    }\n\n    const env: NodeJS.ProcessEnv = {\n      ...process.env,\n      APPIMAGE_SILENT_INSTALL: \"true\",\n    }\n\n    if (options.isForceRunAfter) {\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      this.spawnLog(destination, [], env)\n    } else {\n      env.APPIMAGE_EXIT_AFTER_INSTALL = \"true\"\n      execFileSync(destination, [], { env })\n    }\n    return true\n  }\n}\n"]}