// Advanced progression system for Bake It Out

export interface LevelReward {
  type: 'recipe' | 'equipment' | 'money' | 'skill_point' | 'achievement'
  id: string
  name: string
  description: string
  value?: number
}

export interface PlayerLevel {
  level: number
  experience: number
  experienceRequired: number
  totalExperience: number
  rewards: LevelReward[]
}

export interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  category: 'baking' | 'business' | 'efficiency' | 'collection' | 'special'
  requirements: {
    type: 'orders_completed' | 'money_earned' | 'recipes_unlocked' | 'level_reached' | 'items_baked' | 'equipment_owned'
    target: number
    current?: number
  }[]
  reward: LevelReward
  unlocked: boolean
  completed: boolean
}

export interface SkillTree {
  id: string
  name: string
  description: string
  icon: string
  category: 'efficiency' | 'automation' | 'quality' | 'business'
  level: number
  maxLevel: number
  cost: number
  requirements: {
    playerLevel?: number
    skills?: string[]
    achievements?: string[]
  }
  effects: {
    type: 'baking_speed' | 'money_multiplier' | 'xp_multiplier' | 'ingredient_efficiency' | 'automation_unlock'
    value: number
  }[]
}

// Experience calculation with exponential growth
export function calculateExperienceRequired(level: number): number {
  if (level <= 1) return 0
  return Math.floor(100 * Math.pow(1.15, level - 1))
}

export function calculateTotalExperienceForLevel(level: number): number {
  let total = 0
  for (let i = 1; i <= level; i++) {
    total += calculateExperienceRequired(i)
  }
  return total
}

export function getLevelFromExperience(experience: number): PlayerLevel {
  let level = 1
  let totalExp = 0
  
  while (true) {
    const expRequired = calculateExperienceRequired(level + 1)
    if (totalExp + expRequired > experience) {
      break
    }
    totalExp += expRequired
    level++
  }
  
  const expRequired = calculateExperienceRequired(level + 1)
  const currentLevelExp = experience - totalExp
  
  return {
    level,
    experience: currentLevelExp,
    experienceRequired: expRequired,
    totalExperience: experience,
    rewards: getLevelRewards(level)
  }
}

export function getLevelRewards(level: number): LevelReward[] {
  const rewards: LevelReward[] = []
  
  // Money rewards every level
  rewards.push({
    type: 'money',
    id: `money_${level}`,
    name: 'Level Bonus',
    description: `Bonus money for reaching level ${level}`,
    value: level * 25
  })
  
  // Recipe unlocks at specific levels
  const recipeUnlocks: Record<number, string[]> = {
    2: ['cinnamon_rolls'],
    3: ['chocolate_brownies', 'sourdough_bread'],
    4: ['croissants'],
    5: ['cheesecake'],
    6: ['macarons'],
    7: ['honey_glazed_donuts'],
    8: ['sourdough_bread'],
    9: ['chocolate_souffle'],
    10: ['croquembouche'],
    12: ['opera_cake'],
    15: ['artisan_pizza_dough']
  }
  
  if (recipeUnlocks[level]) {
    recipeUnlocks[level].forEach(recipeId => {
      rewards.push({
        type: 'recipe',
        id: recipeId,
        name: 'New Recipe Unlocked',
        description: `You can now bake ${recipeId.replace(/_/g, ' ')}`
      })
    })
  }
  
  // Equipment unlocks
  const equipmentUnlocks: Record<number, string[]> = {
    3: ['professional_oven'],
    4: ['auto_mixer'],
    5: ['stand_mixer'],
    6: ['auto_oven'],
    7: ['conveyor_belt'],
    8: ['advanced_auto_mixer'],
    10: ['industrial_oven'],
    12: ['smart_conveyor_system']
  }
  
  if (equipmentUnlocks[level]) {
    equipmentUnlocks[level].forEach(equipmentId => {
      rewards.push({
        type: 'equipment',
        id: equipmentId,
        name: 'New Equipment Available',
        description: `${equipmentId.replace(/_/g, ' ')} is now available for purchase`
      })
    })
  }
  
  // Skill points every 2 levels
  if (level % 2 === 0) {
    rewards.push({
      type: 'skill_point',
      id: `skill_point_${level}`,
      name: 'Skill Point',
      description: 'Use this to upgrade your skills in the technology tree',
      value: 1
    })
  }
  
  return rewards
}

export const ACHIEVEMENTS: Achievement[] = [
  {
    id: 'first_order',
    name: 'First Customer',
    description: 'Complete your first order',
    icon: '🎯',
    category: 'baking',
    requirements: [{ type: 'orders_completed', target: 1 }],
    reward: { type: 'money', id: 'first_order_bonus', name: 'First Order Bonus', description: 'Bonus for first order', value: 50 },
    unlocked: true,
    completed: false
  },
  {
    id: 'baker_apprentice',
    name: 'Baker Apprentice',
    description: 'Complete 10 orders',
    icon: '👨‍🍳',
    category: 'baking',
    requirements: [{ type: 'orders_completed', target: 10 }],
    reward: { type: 'recipe', id: 'special_cookies', name: 'Special Recipe', description: 'Unlock special cookie recipe' },
    unlocked: true,
    completed: false
  },
  {
    id: 'money_maker',
    name: 'Money Maker',
    description: 'Earn $1000 total',
    icon: '💰',
    category: 'business',
    requirements: [{ type: 'money_earned', target: 1000 }],
    reward: { type: 'skill_point', id: 'money_maker_skill', name: 'Business Skill Point', description: 'Extra skill point for business success', value: 1 },
    unlocked: true,
    completed: false
  },
  {
    id: 'recipe_collector',
    name: 'Recipe Collector',
    description: 'Unlock 5 different recipes',
    icon: '📚',
    category: 'collection',
    requirements: [{ type: 'recipes_unlocked', target: 5 }],
    reward: { type: 'money', id: 'recipe_bonus', name: 'Recipe Collection Bonus', description: 'Bonus for collecting recipes', value: 200 },
    unlocked: true,
    completed: false
  },
  {
    id: 'level_master',
    name: 'Level Master',
    description: 'Reach level 10',
    icon: '⭐',
    category: 'special',
    requirements: [{ type: 'level_reached', target: 10 }],
    reward: { type: 'equipment', id: 'master_oven', name: 'Master Oven', description: 'Unlock the legendary Master Oven' },
    unlocked: true,
    completed: false
  }
]

export const SKILL_TREE: SkillTree[] = [
  {
    id: 'baking_speed_1',
    name: 'Quick Hands',
    description: 'Increase baking speed by 10%',
    icon: '⚡',
    category: 'efficiency',
    level: 0,
    maxLevel: 3,
    cost: 1,
    requirements: { playerLevel: 2 },
    effects: [{ type: 'baking_speed', value: 0.1 }]
  },
  {
    id: 'money_bonus_1',
    name: 'Business Sense',
    description: 'Increase money earned by 15%',
    icon: '💼',
    category: 'business',
    level: 0,
    maxLevel: 3,
    cost: 1,
    requirements: { playerLevel: 3 },
    effects: [{ type: 'money_multiplier', value: 0.15 }]
  },
  {
    id: 'xp_bonus_1',
    name: 'Fast Learner',
    description: 'Increase experience gained by 20%',
    icon: '📈',
    category: 'efficiency',
    level: 0,
    maxLevel: 2,
    cost: 2,
    requirements: { playerLevel: 4 },
    effects: [{ type: 'xp_multiplier', value: 0.2 }]
  },
  {
    id: 'ingredient_efficiency_1',
    name: 'Efficient Baker',
    description: 'Use 10% fewer ingredients',
    icon: '🌾',
    category: 'efficiency',
    level: 0,
    maxLevel: 2,
    cost: 2,
    requirements: { playerLevel: 5, skills: ['baking_speed_1'] },
    effects: [{ type: 'ingredient_efficiency', value: 0.1 }]
  },
  {
    id: 'automation_unlock_1',
    name: 'Automation Expert',
    description: 'Unlock advanced automation features',
    icon: '🤖',
    category: 'automation',
    level: 0,
    maxLevel: 1,
    cost: 3,
    requirements: { playerLevel: 8, achievements: ['baker_apprentice'] },
    effects: [{ type: 'automation_unlock', value: 1 }]
  }
]
