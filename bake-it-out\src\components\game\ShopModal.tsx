'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { useGame } from '@/contexts/GameContext'
import { useLanguage } from '@/contexts/LanguageContext'

interface ShopModalProps {
  isOpen: boolean
  onClose: () => void
}

export function ShopModal({ isOpen, onClose }: ShopModalProps) {
  const { player, inventory, spendMoney, addIngredient } = useGame()
  const { t } = useLanguage()
  const [quantities, setQuantities] = useState<Record<string, number>>({})

  if (!isOpen) return null

  const handleQuantityChange = (ingredientName: string, quantity: number) => {
    setQuantities(prev => ({
      ...prev,
      [ingredientName]: Math.max(0, quantity)
    }))
  }

  const buyIngredient = (ingredientName: string, cost: number) => {
    const quantity = quantities[ingredientName] || 1
    const totalCost = cost * quantity
    
    if (spendMoney(totalCost)) {
      addIngredient(ingredientName, quantity)
      setQuantities(prev => ({ ...prev, [ingredientName]: 0 }))
    }
  }

  const getTotalCost = (ingredientName: string, cost: number) => {
    const quantity = quantities[ingredientName] || 1
    return cost * quantity
  }

  const canAfford = (ingredientName: string, cost: number) => {
    return player.money >= getTotalCost(ingredientName, cost)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-orange-800">{t('modal.shop.title')}</h2>
            <div className="flex items-center space-x-4">
              <div className="bg-green-100 px-3 py-1 rounded-full">
                <span className="text-green-800 font-medium">{t('ui.money', { amount: player.money.toString() })}</span>
              </div>
              <Button variant="secondary" onClick={onClose}>
                {t('game.close')}
              </Button>
            </div>
          </div>
        </div>

        <div className="p-6">
          <div className="space-y-4 max-h-[60vh] overflow-y-auto">
            {inventory.map(ingredient => {
              const quantity = quantities[ingredient.name] || 1
              const totalCost = getTotalCost(ingredient.name, ingredient.cost)
              const affordable = canAfford(ingredient.name, ingredient.cost)

              return (
                <div
                  key={ingredient.name}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">{ingredient.icon}</div>
                    <div>
                      <h3 className="font-medium text-gray-800">{ingredient.name}</h3>
                      <p className="text-sm text-gray-600">
                        {t('shop.currentStock', { quantity: ingredient.quantity.toString() })}
                      </p>
                      <p className="text-sm text-green-600">
                        {t('inventory.cost', { cost: ingredient.cost.toString() })}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="secondary"
                        onClick={() => handleQuantityChange(ingredient.name, quantity - 1)}
                        disabled={quantity <= 1}
                      >
                        -
                      </Button>
                      <span className="w-12 text-center font-mono">{quantity}</span>
                      <Button
                        size="sm"
                        variant="secondary"
                        onClick={() => handleQuantityChange(ingredient.name, quantity + 1)}
                        disabled={!canAfford(ingredient.name, ingredient.cost) && quantity >= 1}
                      >
                        +
                      </Button>
                    </div>

                    <div className="text-right min-w-[80px]">
                      <div className={`font-medium ${affordable ? 'text-green-600' : 'text-red-600'}`}>
                        ${totalCost}
                      </div>
                      <Button
                        size="sm"
                        variant={affordable ? 'success' : 'secondary'}
                        onClick={() => buyIngredient(ingredient.name, ingredient.cost)}
                        disabled={!affordable}
                        className="mt-1"
                      >
                        {affordable ? t('shop.buy') : t('shop.tooExpensive')}
                      </Button>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-medium text-blue-800 mb-2">{t('shop.tips.title')}</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>{t('shop.tips.bulk')}</li>
              <li>{t('shop.tips.stock')}</li>
              <li>{t('shop.tips.rare')}</li>
              <li>{t('shop.tips.prices')}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
