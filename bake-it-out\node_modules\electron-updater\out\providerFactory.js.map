{"version": 3, "file": "providerFactory.js", "sourceRoot": "", "sources": ["../src/providerFactory.ts"], "names": [], "mappings": ";;AAoBA,wFAEC;AAED,oCA4DC;AApFD,+DAW6B;AAE7B,qEAAiE;AACjE,iEAA6D;AAC7D,+DAA2D;AAC3D,+DAA2D;AAC3D,6EAAyE;AAGzE,SAAgB,sCAAsC,CAAC,GAAW;IAChE,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAA;AAC1C,CAAC;AAED,SAAgB,YAAY,CAAC,IAA8C,EAAE,OAAmB,EAAE,cAAsC;IACtI,qCAAqC;IACrC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC7B,MAAM,IAAA,+BAAQ,EAAC,yCAAyC,EAAE,4CAA4C,CAAC,CAAA;IACzG,CAAC;IAED,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;IAC9B,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,QAAQ,CAAC,CAAC,CAAC;YACd,MAAM,aAAa,GAAG,IAAqB,CAAA;YAC3C,MAAM,KAAK,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,KAAK,CAAA;YAC5H,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;gBAClB,OAAO,IAAI,+BAAc,CAAC,aAAa,EAAE,OAAO,EAAE,cAAc,CAAC,CAAA;YACnE,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,6CAAqB,CAAC,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,CAAC,CAAA;YACjF,CAAC;QACH,CAAC;QAED,KAAK,WAAW;YACd,OAAO,IAAI,qCAAiB,CAAC,IAAwB,EAAE,OAAO,EAAE,cAAc,CAAC,CAAA;QAEjF,KAAK,QAAQ;YACX,OAAO,IAAI,+BAAc,CAAC,IAAqB,EAAE,OAAO,EAAE,cAAc,CAAC,CAAA;QAE3E,KAAK,IAAI,CAAC;QACV,KAAK,QAAQ;YACX,OAAO,IAAI,iCAAe,CACxB;gBACE,QAAQ,EAAE,SAAS;gBACnB,GAAG,EAAE,IAAA,+CAAwB,EAAC,IAAI,CAAC;gBACnC,OAAO,EAAG,IAAsB,CAAC,OAAO,IAAI,IAAI;aACjD,EACD,OAAO,EACP;gBACE,GAAG,cAAc;gBACjB,oEAAoE;gBACpE,yBAAyB,EAAE,KAAK;aACjC,CACF,CAAA;QAEH,KAAK,SAAS,CAAC,CAAC,CAAC;YACf,MAAM,OAAO,GAAG,IAA4B,CAAA;YAC5C,OAAO,IAAI,iCAAe,CAAC,OAAO,EAAE,OAAO,EAAE;gBAC3C,GAAG,cAAc;gBACjB,yBAAyB,EAAE,OAAO,CAAC,uBAAuB,KAAK,KAAK,IAAI,sCAAsC,CAAC,OAAO,CAAC,GAAG,CAAC;aAC5H,CAAC,CAAA;QACJ,CAAC;QAED,KAAK,QAAQ,CAAC,CAAC,CAAC;YACd,MAAM,OAAO,GAAG,IAA4B,CAAA;YAC5C,MAAM,WAAW,GAAG,OAAO,CAAC,cAAc,CAAA;YAC1C,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,MAAM,IAAA,+BAAQ,EAAC,+BAA+B,EAAE,4CAA4C,CAAC,CAAA;YAC/F,CAAC;YACD,OAAO,IAAI,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,cAAc,CAAC,CAAA;QAC1D,CAAC;QAED;YACE,MAAM,IAAA,+BAAQ,EAAC,yBAAyB,QAAQ,EAAE,EAAE,kCAAkC,CAAC,CAAA;IAC3F,CAAC;AACH,CAAC", "sourcesContent": ["import {\n  AllPublishOptions,\n  BaseS3Options,\n  BitbucketOptions,\n  CustomPublishOptions,\n  GenericServerOptions,\n  getS3LikeProviderBaseUrl,\n  GithubOptions,\n  KeygenOptions,\n  newError,\n  PublishConfiguration,\n} from \"builder-util-runtime\"\nimport { AppUpdater } from \"./AppUpdater\"\nimport { BitbucketProvider } from \"./providers/BitbucketProvider\"\nimport { GenericProvider } from \"./providers/GenericProvider\"\nimport { GitHubProvider } from \"./providers/GitHubProvider\"\nimport { KeygenProvider } from \"./providers/KeygenProvider\"\nimport { PrivateGitHubProvider } from \"./providers/PrivateGitHubProvider\"\nimport { Provider, ProviderRuntimeOptions } from \"./providers/Provider\"\n\nexport function isUrlProbablySupportMultiRangeRequests(url: string): boolean {\n  return !url.includes(\"s3.amazonaws.com\")\n}\n\nexport function createClient(data: PublishConfiguration | AllPublishOptions, updater: AppUpdater, runtimeOptions: ProviderRuntimeOptions): Provider<any> {\n  // noinspection SuspiciousTypeOfGuard\n  if (typeof data === \"string\") {\n    throw newError(\"Please pass PublishConfiguration object\", \"ERR_UPDATER_INVALID_PROVIDER_CONFIGURATION\")\n  }\n\n  const provider = data.provider\n  switch (provider) {\n    case \"github\": {\n      const githubOptions = data as GithubOptions\n      const token = (githubOptions.private ? process.env[\"GH_TOKEN\"] || process.env[\"GITHUB_TOKEN\"] : null) || githubOptions.token\n      if (token == null) {\n        return new GitHubProvider(githubOptions, updater, runtimeOptions)\n      } else {\n        return new PrivateGitHubProvider(githubOptions, updater, token, runtimeOptions)\n      }\n    }\n\n    case \"bitbucket\":\n      return new BitbucketProvider(data as BitbucketOptions, updater, runtimeOptions)\n\n    case \"keygen\":\n      return new KeygenProvider(data as KeygenOptions, updater, runtimeOptions)\n\n    case \"s3\":\n    case \"spaces\":\n      return new GenericProvider(\n        {\n          provider: \"generic\",\n          url: getS3LikeProviderBaseUrl(data),\n          channel: (data as BaseS3Options).channel || null,\n        },\n        updater,\n        {\n          ...runtimeOptions,\n          // https://github.com/minio/minio/issues/5285#issuecomment-350428955\n          isUseMultipleRangeRequest: false,\n        }\n      )\n\n    case \"generic\": {\n      const options = data as GenericServerOptions\n      return new GenericProvider(options, updater, {\n        ...runtimeOptions,\n        isUseMultipleRangeRequest: options.useMultipleRangeRequest !== false && isUrlProbablySupportMultiRangeRequests(options.url),\n      })\n    }\n\n    case \"custom\": {\n      const options = data as CustomPublishOptions\n      const constructor = options.updateProvider\n      if (!constructor) {\n        throw newError(\"Custom provider not specified\", \"ERR_UPDATER_INVALID_PROVIDER_CONFIGURATION\")\n      }\n      return new constructor(options, updater, runtimeOptions)\n    }\n\n    default:\n      throw newError(`Unsupported provider: ${provider}`, \"ERR_UPDATER_UNSUPPORTED_PROVIDER\")\n  }\n}\n"]}