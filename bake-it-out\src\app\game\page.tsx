'use client'

import { useState } from 'react'
import { useLanguage } from '@/contexts/LanguageContext'
import { GameProvider, useGame } from '@/contexts/GameContext'
import { Button } from '@/components/ui/Button'
import { Equipment } from '@/components/game/Equipment'
import { Order } from '@/components/game/Order'
import { RecipeModal } from '@/components/game/RecipeModal'
import { ShopModal } from '@/components/game/ShopModal'
import { BakingModal } from '@/components/game/BakingModal'
import { NotificationSystem, useNotifications } from '@/components/game/NotificationSystem'
import { LevelUpModal } from '@/components/game/LevelUpModal'
import { AchievementsModal } from '@/components/game/AchievementsModal'
import { SkillTreeModal } from '@/components/game/SkillTreeModal'
import { AutomationModal } from '@/components/game/AutomationModal'
import { EquipmentShopModal } from '@/components/game/EquipmentShopModal'

function GameContent() {
  const { t } = useLanguage()
  const {
    player,
    equipment,
    inventory,
    orders,
    achievements,
    skills,
    levelUpRewards,
    showLevelUp,
    updateEquipment,
    acceptOrder,
    completeOrder,
    declineOrder,
    generateNewOrder,
    upgradeSkill,
    checkAchievements,
    dismissLevelUp
  } = useGame()

  const [showRecipeModal, setShowRecipeModal] = useState(false)
  const [showShopModal, setShowShopModal] = useState(false)
  const [showBakingModal, setShowBakingModal] = useState(false)
  const [showAchievementsModal, setShowAchievementsModal] = useState(false)
  const [showSkillTreeModal, setShowSkillTreeModal] = useState(false)
  const [showAutomationModal, setShowAutomationModal] = useState(false)
  const [showEquipmentShopModal, setShowEquipmentShopModal] = useState(false)
  const [selectedEquipment, setSelectedEquipment] = useState<{id: string, name: string} | null>(null)

  const { notifications, removeNotification, showSuccess, showError, showInfo } = useNotifications()

  const handleEquipmentClick = (equipmentId: string, equipmentName: string) => {
    setSelectedEquipment({ id: equipmentId, name: equipmentName })
    setShowBakingModal(true)
  }

  const handleOrderAccept = (orderId: string) => {
    acceptOrder(orderId)
    showInfo('Order Accepted', 'You have accepted a new order!')
  }

  const handleOrderComplete = (orderId: string) => {
    const order = orders.find(o => o.id === orderId)
    if (order) {
      completeOrder(orderId)
      checkAchievements()
      showSuccess('Order Completed!', `You earned $${order.reward} and gained experience!`)
    }
  }

  const handleOrderDecline = (orderId: string) => {
    declineOrder(orderId)
    showInfo('Order Declined', 'Order has been removed from your queue.')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-yellow-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-orange-200 p-4">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div className="flex items-center space-x-6">
            <h1 className="text-2xl font-bold text-orange-800">🥖 Bake It Out</h1>
            <div className="flex items-center space-x-4 text-sm">
              <div className="bg-blue-100 px-3 py-1 rounded-full">
                <span className="text-blue-800">Level {player.level}</span>
              </div>
              <div className="bg-green-100 px-3 py-1 rounded-full">
                <span className="text-green-800">${player.money}</span>
              </div>
              <div className="bg-purple-100 px-3 py-1 rounded-full">
                <span className="text-purple-800">XP: {player.experience}/{player.maxExperience}</span>
              </div>
              <div className="bg-yellow-100 px-3 py-1 rounded-full">
                <span className="text-yellow-800">SP: {player.skillPoints}</span>
              </div>
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setShowAchievementsModal(true)}
              >
                🏆 Achievements
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setShowSkillTreeModal(true)}
              >
                🌟 Skills
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setShowAutomationModal(true)}
              >
                🤖 Automation
              </Button>
            </div>
          </div>
          <Button variant="secondary" onClick={() => window.location.href = '/'}>
            🏠 Home
          </Button>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6 grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Kitchen Area */}
        <div className="lg:col-span-3 space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-orange-800 mb-4">🏪 Kitchen</h2>

            {/* Equipment Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {equipment.map((eq) => (
                <Equipment
                  key={eq.id}
                  equipment={eq}
                  onClick={handleEquipmentClick}
                />
              ))}
            </div>
          </div>

          {/* Inventory */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-orange-800 mb-4">📦 Inventory</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {inventory.map((ingredient) => (
                <div key={ingredient.name} className="bg-gray-50 p-3 rounded-lg text-center">
                  <div className="text-2xl mb-1">{ingredient.icon}</div>
                  <div className="font-medium text-gray-800">{ingredient.name}</div>
                  <div className="text-sm text-gray-600">Qty: {ingredient.quantity}</div>
                  <div className="text-xs text-green-600">${ingredient.cost} each</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Orders Panel */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-orange-800">📋 Orders</h2>
              <Button
                size="sm"
                variant="primary"
                onClick={generateNewOrder}
              >
                + New Order
              </Button>
            </div>
            <div className="space-y-4">
              {orders.map((order) => (
                <Order
                  key={order.id}
                  order={order}
                  onAccept={handleOrderAccept}
                  onDecline={handleOrderDecline}
                  onComplete={handleOrderComplete}
                />
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-orange-800 mb-4">⚡ Quick Actions</h2>
            <div className="space-y-2">
              <Button
                variant="secondary"
                size="sm"
                className="w-full"
                onClick={() => setShowShopModal(true)}
              >
                🛒 Buy Ingredients
              </Button>
              <Button
                variant="secondary"
                size="sm"
                className="w-full"
                onClick={() => setShowRecipeModal(true)}
              >
                📖 View Recipes
              </Button>
              <Button
                variant="secondary"
                size="sm"
                className="w-full"
                onClick={() => setShowEquipmentShopModal(true)}
              >
                🔧 Equipment Shop
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      <RecipeModal
        isOpen={showRecipeModal}
        onClose={() => setShowRecipeModal(false)}
      />
      <ShopModal
        isOpen={showShopModal}
        onClose={() => setShowShopModal(false)}
      />
      <BakingModal
        isOpen={showBakingModal}
        onClose={() => setShowBakingModal(false)}
        equipmentId={selectedEquipment?.id || ''}
        equipmentName={selectedEquipment?.name || ''}
      />
      <AchievementsModal
        isOpen={showAchievementsModal}
        onClose={() => setShowAchievementsModal(false)}
        achievements={achievements}
      />
      <SkillTreeModal
        isOpen={showSkillTreeModal}
        onClose={() => setShowSkillTreeModal(false)}
        skills={skills}
        skillPoints={player.skillPoints}
        playerLevel={player.level}
        onUpgradeSkill={upgradeSkill}
      />
      <LevelUpModal
        isOpen={showLevelUp}
        onClose={dismissLevelUp}
        newLevel={player.level}
        rewards={levelUpRewards}
      />
      <AutomationModal
        isOpen={showAutomationModal}
        onClose={() => setShowAutomationModal(false)}
      />
      <EquipmentShopModal
        isOpen={showEquipmentShopModal}
        onClose={() => setShowEquipmentShopModal(false)}
      />

      {/* Notification System */}
      <NotificationSystem
        notifications={notifications}
        onRemove={removeNotification}
      />
    </div>
  )
}

export default function GamePage() {
  return (
    <GameProvider>
      <GameContent />
    </GameProvider>
  )
}
