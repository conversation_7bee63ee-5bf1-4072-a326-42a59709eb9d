'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Achievement } from '@/lib/progressionSystem'

interface AchievementsModalProps {
  isOpen: boolean
  onClose: () => void
  achievements: Achievement[]
}

export function AchievementsModal({ isOpen, onClose, achievements }: AchievementsModalProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  if (!isOpen) return null

  const categories = [
    { id: 'all', name: 'All', icon: '🏆' },
    { id: 'baking', name: 'Baking', icon: '👨‍🍳' },
    { id: 'business', name: 'Business', icon: '💼' },
    { id: 'efficiency', name: 'Efficiency', icon: '⚡' },
    { id: 'collection', name: 'Collection', icon: '📚' },
    { id: 'special', name: 'Special', icon: '⭐' }
  ]

  const filteredAchievements = selectedCategory === 'all' 
    ? achievements 
    : achievements.filter(achievement => achievement.category === selectedCategory)

  const completedCount = achievements.filter(a => a.completed).length
  const totalCount = achievements.length

  const getProgressPercentage = (achievement: Achievement) => {
    if (achievement.completed) return 100
    if (!achievement.requirements[0].current) return 0
    return Math.min(100, (achievement.requirements[0].current / achievement.requirements[0].target) * 100)
  }

  const getRewardIcon = (type: string) => {
    switch (type) {
      case 'recipe': return '📖'
      case 'equipment': return '⚙️'
      case 'money': return '💰'
      case 'skill_point': return '⭐'
      default: return '🎁'
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold text-orange-800">🏆 Achievements</h2>
              <p className="text-gray-600">
                {completedCount} of {totalCount} achievements completed
              </p>
            </div>
            <Button variant="secondary" onClick={onClose}>
              ✕ Close
            </Button>
          </div>
        </div>

        <div className="p-6">
          {/* Progress Bar */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">Overall Progress</span>
              <span className="text-sm text-gray-500">
                {Math.round((completedCount / totalCount) * 100)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className="bg-gradient-to-r from-yellow-400 to-orange-500 h-3 rounded-full transition-all duration-500"
                style={{ width: `${(completedCount / totalCount) * 100}%` }}
              ></div>
            </div>
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap gap-2 mb-6">
            {categories.map(category => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? 'primary' : 'secondary'}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
              >
                {category.icon} {category.name}
              </Button>
            ))}
          </div>

          {/* Achievements Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[50vh] overflow-y-auto">
            {filteredAchievements.map(achievement => {
              const progress = getProgressPercentage(achievement)
              const isCompleted = achievement.completed
              const isUnlocked = achievement.unlocked

              return (
                <div
                  key={achievement.id}
                  className={`p-4 rounded-lg border-2 ${
                    isCompleted
                      ? 'border-green-400 bg-green-50'
                      : isUnlocked
                      ? 'border-gray-300 bg-white'
                      : 'border-gray-200 bg-gray-50 opacity-60'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`text-3xl ${isCompleted ? 'grayscale-0' : 'grayscale'}`}>
                      {achievement.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className={`font-semibold ${isCompleted ? 'text-green-800' : 'text-gray-800'}`}>
                        {achievement.name}
                        {isCompleted && <span className="ml-2">✅</span>}
                      </h3>
                      <p className="text-sm text-gray-600 mb-2">
                        {achievement.description}
                      </p>

                      {/* Progress */}
                      {isUnlocked && !isCompleted && (
                        <div className="mb-2">
                          <div className="flex justify-between items-center mb-1">
                            <span className="text-xs text-gray-500">Progress</span>
                            <span className="text-xs text-gray-500">
                              {achievement.requirements[0].current || 0} / {achievement.requirements[0].target}
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${progress}%` }}
                            ></div>
                          </div>
                        </div>
                      )}

                      {/* Reward */}
                      <div className="flex items-center space-x-2 text-sm">
                        <span className="text-gray-500">Reward:</span>
                        <span className="text-lg">{getRewardIcon(achievement.reward.type)}</span>
                        <span className="text-gray-700">{achievement.reward.name}</span>
                        {achievement.reward.value && (
                          <span className="text-green-600 font-medium">
                            {achievement.reward.type === 'money' ? `$${achievement.reward.value}` : `+${achievement.reward.value}`}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>

          {filteredAchievements.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">🏆</div>
              <p>No achievements in this category.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
