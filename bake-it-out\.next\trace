[{"name": "generate-buildid", "duration": 507, "timestamp": 257194321189, "id": 4, "parentId": 1, "tags": {}, "startTime": 1752872523668, "traceId": "9ae2b8eb8cc771ab"}, {"name": "load-custom-routes", "duration": 834, "timestamp": 257194321928, "id": 5, "parentId": 1, "tags": {}, "startTime": 1752872523669, "traceId": "9ae2b8eb8cc771ab"}, {"name": "create-dist-dir", "duration": 890, "timestamp": 257194504663, "id": 6, "parentId": 1, "tags": {}, "startTime": 1752872523852, "traceId": "9ae2b8eb8cc771ab"}, {"name": "create-pages-mapping", "duration": 697, "timestamp": 257194535650, "id": 7, "parentId": 1, "tags": {}, "startTime": 1752872523883, "traceId": "9ae2b8eb8cc771ab"}, {"name": "collect-app-paths", "duration": 4576, "timestamp": 257194536455, "id": 8, "parentId": 1, "tags": {}, "startTime": 1752872523884, "traceId": "9ae2b8eb8cc771ab"}, {"name": "create-app-mapping", "duration": 8181, "timestamp": 257194541103, "id": 9, "parentId": 1, "tags": {}, "startTime": 1752872523888, "traceId": "9ae2b8eb8cc771ab"}, {"name": "public-dir-conflict-check", "duration": 3230, "timestamp": 257194551621, "id": 10, "parentId": 1, "tags": {}, "startTime": 1752872523899, "traceId": "9ae2b8eb8cc771ab"}, {"name": "generate-routes-manifest", "duration": 4264, "timestamp": 257194555282, "id": 11, "parentId": 1, "tags": {}, "startTime": 1752872523902, "traceId": "9ae2b8eb8cc771ab"}, {"name": "create-entrypoints", "duration": 60933, "timestamp": 257194596394, "id": 14, "parentId": 1, "tags": {}, "startTime": 1752872523943, "traceId": "9ae2b8eb8cc771ab"}, {"name": "generate-webpack-config", "duration": 410236, "timestamp": 257194657459, "id": 15, "parentId": 13, "tags": {}, "startTime": 1752872524005, "traceId": "9ae2b8eb8cc771ab"}, {"name": "next-trace-entrypoint-plugin", "duration": 4335, "timestamp": 257195459782, "id": 17, "parentId": 16, "tags": {}, "startTime": 1752872524807, "traceId": "9ae2b8eb8cc771ab"}, {"name": "add-entry", "duration": 410073, "timestamp": 257195472194, "id": 21, "parentId": 18, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1752872524819, "traceId": "9ae2b8eb8cc771ab"}, {"name": "add-entry", "duration": 590619, "timestamp": 257195472321, "id": 25, "parentId": 18, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1752872524819, "traceId": "9ae2b8eb8cc771ab"}, {"name": "add-entry", "duration": 752324, "timestamp": 257195472232, "id": 22, "parentId": 18, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1752872524819, "traceId": "9ae2b8eb8cc771ab"}, {"name": "add-entry", "duration": 859228, "timestamp": 257195472098, "id": 20, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Ffavicon.ico%2Froute&name=app%2Ffavicon.ico%2Froute&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5Cmarkv%5CDesktop%5Ccoding%20stuff%5Cbake%20it%20out%5Cbake-it-out%5Csrc%5Capp&appPaths=%2Ffavicon.ico&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!"}, "startTime": 1752872524819, "traceId": "9ae2b8eb8cc771ab"}, {"name": "build-module-tsx", "duration": 31870, "timestamp": 257196477366, "id": 26, "parentId": 16, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\contexts\\MultiplayerContext.tsx", "layer": "rsc"}, "startTime": 1752872525824, "traceId": "9ae2b8eb8cc771ab"}, {"name": "add-entry", "duration": 1040001, "timestamp": 257195471159, "id": 19, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=C%3A%5CUsers%5Cmarkv%5CDesktop%5Ccoding%20stuff%5Cbake%20it%20out%5Cbake-it-out%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cbuiltin%5Cglobal-not-found.js&appDir=C%3A%5CUsers%5Cmarkv%5CDesktop%5Ccoding%20stuff%5Cbake%20it%20out%5Cbake-it-out%5Csrc%5Capp&appPaths=C%3A%2FUsers%2Fmarkv%2FDesktop%2Fcoding%20stuff%2Fbake%20it%20out%2Fbake-it-out%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-not-found&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!"}, "startTime": 1752872524818, "traceId": "9ae2b8eb8cc771ab"}, {"name": "add-entry", "duration": 1038921, "timestamp": 257195472265, "id": 23, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fgame%2Fpage&name=app%2Fgame%2Fpage&pagePath=private-next-app-dir%2Fgame%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmarkv%5CDesktop%5Ccoding%20stuff%5Cbake%20it%20out%5Cbake-it-out%5Csrc%5Capp&appPaths=%2Fgame%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!"}, "startTime": 1752872524819, "traceId": "9ae2b8eb8cc771ab"}, {"name": "add-entry", "duration": 1038898, "timestamp": 257195472293, "id": 24, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmarkv%5CDesktop%5Ccoding%20stuff%5Cbake%20it%20out%5Cbake-it-out%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!"}, "startTime": 1752872524819, "traceId": "9ae2b8eb8cc771ab"}, {"name": "build-module-tsx", "duration": 113126, "timestamp": 257196640142, "id": 43, "parentId": 16, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\contexts\\MultiplayerContext.tsx", "layer": "ssr"}, "startTime": 1752872525987, "traceId": "9ae2b8eb8cc771ab"}, {"name": "make", "duration": 1738709, "timestamp": 257195470296, "id": 18, "parentId": 16, "tags": {}, "startTime": 1752872524817, "traceId": "9ae2b8eb8cc771ab"}, {"name": "get-entries", "duration": 4679, "timestamp": 257197212796, "id": 45, "parentId": 44, "tags": {}, "startTime": 1752872526560, "traceId": "9ae2b8eb8cc771ab"}, {"name": "node-file-trace-plugin", "duration": 519308, "timestamp": 257197227251, "id": 46, "parentId": 44, "tags": {"traceEntryCount": "10"}, "startTime": 1752872526574, "traceId": "9ae2b8eb8cc771ab"}, {"name": "collect-traced-files", "duration": 5692, "timestamp": 257197746598, "id": 47, "parentId": 44, "tags": {}, "startTime": 1752872527094, "traceId": "9ae2b8eb8cc771ab"}, {"name": "finish-modules", "duration": 540007, "timestamp": 257197212315, "id": 44, "parentId": 17, "tags": {}, "startTime": 1752872526559, "traceId": "9ae2b8eb8cc771ab"}, {"name": "chunk-graph", "duration": 42646, "timestamp": 257197848897, "id": 49, "parentId": 48, "tags": {}, "startTime": 1752872527196, "traceId": "9ae2b8eb8cc771ab"}, {"name": "optimize-modules", "duration": 117, "timestamp": 257197892053, "id": 51, "parentId": 48, "tags": {}, "startTime": 1752872527239, "traceId": "9ae2b8eb8cc771ab"}, {"name": "optimize-chunks", "duration": 43029, "timestamp": 257197892710, "id": 52, "parentId": 48, "tags": {}, "startTime": 1752872527240, "traceId": "9ae2b8eb8cc771ab"}, {"name": "optimize-tree", "duration": 75, "timestamp": 257197935922, "id": 53, "parentId": 48, "tags": {}, "startTime": 1752872527283, "traceId": "9ae2b8eb8cc771ab"}, {"name": "optimize-chunk-modules", "duration": 30860, "timestamp": 257197936233, "id": 54, "parentId": 48, "tags": {}, "startTime": 1752872527283, "traceId": "9ae2b8eb8cc771ab"}, {"name": "optimize", "duration": 75545, "timestamp": 257197891829, "id": 50, "parentId": 48, "tags": {}, "startTime": 1752872527239, "traceId": "9ae2b8eb8cc771ab"}, {"name": "module-hash", "duration": 32736, "timestamp": 257198008672, "id": 55, "parentId": 48, "tags": {}, "startTime": 1752872527356, "traceId": "9ae2b8eb8cc771ab"}, {"name": "code-generation", "duration": 36784, "timestamp": 257198041529, "id": 56, "parentId": 48, "tags": {}, "startTime": 1752872527389, "traceId": "9ae2b8eb8cc771ab"}, {"name": "hash", "duration": 16320, "timestamp": 257198086153, "id": 57, "parentId": 48, "tags": {}, "startTime": 1752872527433, "traceId": "9ae2b8eb8cc771ab"}, {"name": "code-generation-jobs", "duration": 536, "timestamp": 257198102463, "id": 58, "parentId": 48, "tags": {}, "startTime": 1752872527450, "traceId": "9ae2b8eb8cc771ab"}, {"name": "module-assets", "duration": 518, "timestamp": 257198102907, "id": 59, "parentId": 48, "tags": {}, "startTime": 1752872527450, "traceId": "9ae2b8eb8cc771ab"}, {"name": "create-chunk-assets", "duration": 10159, "timestamp": 257198103454, "id": 60, "parentId": 48, "tags": {}, "startTime": 1752872527451, "traceId": "9ae2b8eb8cc771ab"}, {"name": "minify-js", "duration": 22207, "timestamp": 257198140191, "id": 62, "parentId": 61, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1752872527487, "traceId": "9ae2b8eb8cc771ab"}, {"name": "minify-js", "duration": 14034, "timestamp": 257198148386, "id": 64, "parentId": 61, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1752872527495, "traceId": "9ae2b8eb8cc771ab"}, {"name": "minify-js", "duration": 13953, "timestamp": 257198148470, "id": 65, "parentId": 61, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1752872527496, "traceId": "9ae2b8eb8cc771ab"}, {"name": "minify-js", "duration": 13934, "timestamp": 257198148494, "id": 66, "parentId": 61, "tags": {"name": "../app/game/page.js", "cache": "HIT"}, "startTime": 1752872527496, "traceId": "9ae2b8eb8cc771ab"}, {"name": "minify-js", "duration": 13918, "timestamp": 257198148514, "id": 67, "parentId": 61, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1752872527496, "traceId": "9ae2b8eb8cc771ab"}, {"name": "minify-js", "duration": 13901, "timestamp": 257198148535, "id": 68, "parentId": 61, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1752872527496, "traceId": "9ae2b8eb8cc771ab"}, {"name": "minify-js", "duration": 13888, "timestamp": 257198148551, "id": 69, "parentId": 61, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1752872527496, "traceId": "9ae2b8eb8cc771ab"}, {"name": "minify-js", "duration": 13877, "timestamp": 257198148567, "id": 70, "parentId": 61, "tags": {"name": "985.js", "cache": "HIT"}, "startTime": 1752872527496, "traceId": "9ae2b8eb8cc771ab"}, {"name": "minify-js", "duration": 2662, "timestamp": 257198159786, "id": 72, "parentId": 61, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1752872527507, "traceId": "9ae2b8eb8cc771ab"}, {"name": "minify-js", "duration": 24918, "timestamp": 257198159881, "id": 73, "parentId": 61, "tags": {"name": "871.js", "cache": "MISS"}, "startTime": 1752872527507, "traceId": "9ae2b8eb8cc771ab"}, {"name": "minify-js", "duration": 122831, "timestamp": 257198140642, "id": 63, "parentId": 61, "tags": {"name": "../app/favicon.ico/route.js", "cache": "MISS"}, "startTime": 1752872527488, "traceId": "9ae2b8eb8cc771ab"}, {"name": "minify-js", "duration": 481472, "timestamp": 257198148597, "id": 71, "parentId": 61, "tags": {"name": "976.js", "cache": "MISS"}, "startTime": 1752872527496, "traceId": "9ae2b8eb8cc771ab"}, {"name": "minify-webpack-plugin-optimize", "duration": 508450, "timestamp": 257198121645, "id": 61, "parentId": 16, "tags": {"compilationName": "server", "mangle": "[object Object]"}, "startTime": 1752872527469, "traceId": "9ae2b8eb8cc771ab"}, {"name": "css-minimizer-plugin", "duration": 218, "timestamp": 257198630508, "id": 74, "parentId": 16, "tags": {}, "startTime": 1752872527978, "traceId": "9ae2b8eb8cc771ab"}, {"name": "create-trace-assets", "duration": 3079, "timestamp": 257198631084, "id": 75, "parentId": 17, "tags": {}, "startTime": 1752872527978, "traceId": "9ae2b8eb8cc771ab"}, {"name": "seal", "duration": 846895, "timestamp": 257197798667, "id": 48, "parentId": 16, "tags": {}, "startTime": 1752872527146, "traceId": "9ae2b8eb8cc771ab"}, {"name": "webpack-compilation", "duration": 3206957, "timestamp": 257195456093, "id": 16, "parentId": 13, "tags": {"name": "server"}, "startTime": 1752872524803, "traceId": "9ae2b8eb8cc771ab"}, {"name": "emit", "duration": 27989, "timestamp": 257198664040, "id": 76, "parentId": 13, "tags": {}, "startTime": 1752872528011, "traceId": "9ae2b8eb8cc771ab"}, {"name": "webpack-close", "duration": 650677, "timestamp": 257198695260, "id": 77, "parentId": 13, "tags": {"name": "server"}, "startTime": 1752872528042, "traceId": "9ae2b8eb8cc771ab"}, {"name": "webpack-generate-error-stats", "duration": 13090, "timestamp": 257199346140, "id": 78, "parentId": 77, "tags": {}, "startTime": 1752872528693, "traceId": "9ae2b8eb8cc771ab"}, {"name": "make", "duration": 1016, "timestamp": 257199392655, "id": 80, "parentId": 79, "tags": {}, "startTime": 1752872528740, "traceId": "9ae2b8eb8cc771ab"}, {"name": "chunk-graph", "duration": 21690, "timestamp": 257199396333, "id": 82, "parentId": 81, "tags": {}, "startTime": 1752872528743, "traceId": "9ae2b8eb8cc771ab"}, {"name": "optimize-modules", "duration": 39, "timestamp": 257199418443, "id": 84, "parentId": 81, "tags": {}, "startTime": 1752872528766, "traceId": "9ae2b8eb8cc771ab"}, {"name": "optimize-chunks", "duration": 378, "timestamp": 257199418808, "id": 85, "parentId": 81, "tags": {}, "startTime": 1752872528766, "traceId": "9ae2b8eb8cc771ab"}, {"name": "optimize-tree", "duration": 41, "timestamp": 257199419395, "id": 86, "parentId": 81, "tags": {}, "startTime": 1752872528766, "traceId": "9ae2b8eb8cc771ab"}, {"name": "optimize-chunk-modules", "duration": 308, "timestamp": 257199419704, "id": 87, "parentId": 81, "tags": {}, "startTime": 1752872528767, "traceId": "9ae2b8eb8cc771ab"}, {"name": "optimize", "duration": 2099, "timestamp": 257199418132, "id": 83, "parentId": 81, "tags": {}, "startTime": 1752872528765, "traceId": "9ae2b8eb8cc771ab"}, {"name": "module-hash", "duration": 44, "timestamp": 257199420912, "id": 88, "parentId": 81, "tags": {}, "startTime": 1752872528768, "traceId": "9ae2b8eb8cc771ab"}, {"name": "code-generation", "duration": 42, "timestamp": 257199421004, "id": 89, "parentId": 81, "tags": {}, "startTime": 1752872528768, "traceId": "9ae2b8eb8cc771ab"}, {"name": "hash", "duration": 264, "timestamp": 257199421194, "id": 90, "parentId": 81, "tags": {}, "startTime": 1752872528768, "traceId": "9ae2b8eb8cc771ab"}, {"name": "code-generation-jobs", "duration": 316, "timestamp": 257199421456, "id": 91, "parentId": 81, "tags": {}, "startTime": 1752872528769, "traceId": "9ae2b8eb8cc771ab"}, {"name": "module-assets", "duration": 71, "timestamp": 257199421736, "id": 92, "parentId": 81, "tags": {}, "startTime": 1752872528769, "traceId": "9ae2b8eb8cc771ab"}, {"name": "create-chunk-assets", "duration": 56, "timestamp": 257199421826, "id": 93, "parentId": 81, "tags": {}, "startTime": 1752872528769, "traceId": "9ae2b8eb8cc771ab"}, {"name": "minify-js", "duration": 131, "timestamp": 257199437176, "id": 95, "parentId": 94, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1752872528784, "traceId": "9ae2b8eb8cc771ab"}, {"name": "minify-webpack-plugin-optimize", "duration": 2768, "timestamp": 257199434568, "id": 94, "parentId": 79, "tags": {"compilationName": "edge-server", "mangle": "[object Object]"}, "startTime": 1752872528782, "traceId": "9ae2b8eb8cc771ab"}, {"name": "css-minimizer-plugin", "duration": 23, "timestamp": 257199437545, "id": 96, "parentId": 79, "tags": {}, "startTime": 1752872528785, "traceId": "9ae2b8eb8cc771ab"}, {"name": "seal", "duration": 47311, "timestamp": 257199395811, "id": 81, "parentId": 79, "tags": {}, "startTime": 1752872528743, "traceId": "9ae2b8eb8cc771ab"}, {"name": "webpack-compilation", "duration": 56595, "timestamp": 257199386880, "id": 79, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1752872528734, "traceId": "9ae2b8eb8cc771ab"}, {"name": "emit", "duration": 4696, "timestamp": 257199443651, "id": 97, "parentId": 13, "tags": {}, "startTime": 1752872528791, "traceId": "9ae2b8eb8cc771ab"}, {"name": "webpack-close", "duration": 703, "timestamp": 257199449752, "id": 98, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1752872528797, "traceId": "9ae2b8eb8cc771ab"}, {"name": "webpack-generate-error-stats", "duration": 1620, "timestamp": 257199450482, "id": 99, "parentId": 98, "tags": {}, "startTime": 1752872528798, "traceId": "9ae2b8eb8cc771ab"}, {"name": "build-module-tsx", "duration": 166670, "timestamp": 257199768940, "id": 113, "parentId": 100, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\contexts\\MultiplayerContext.tsx", "layer": "app-pages-browser"}, "startTime": 1752872529116, "traceId": "9ae2b8eb8cc771ab"}, {"name": "add-entry", "duration": 579942, "timestamp": 257199478966, "id": 105, "parentId": 101, "tags": {"request": "next-client-pages-loader?absolutePagePath=C%3A%5CUsers%5Cmarkv%5CDesktop%5Ccoding%20stuff%5Cbake%20it%20out%5Cbake-it-out%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cbuiltin%5Cglobal-not-found.js&page=%2F_not-found%2Fpage!"}, "startTime": 1752872528826, "traceId": "9ae2b8eb8cc771ab"}, {"name": "add-entry", "duration": 650337, "timestamp": 257199479065, "id": 111, "parentId": 101, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmarkv%5C%5CDesktop%5C%5Ccoding%20stuff%5C%5Cbake%20it%20out%5C%5Cbake-it-out%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1752872528826, "traceId": "9ae2b8eb8cc771ab"}, {"name": "add-entry", "duration": 691975, "timestamp": 257199479045, "id": 110, "parentId": 101, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmarkv%5C%5CDesktop%5C%5Ccoding%20stuff%5C%5Cbake%20it%20out%5C%5Cbake-it-out%5C%5Csrc%5C%5Capp%5C%5Cgame%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1752872528826, "traceId": "9ae2b8eb8cc771ab"}, {"name": "add-entry", "duration": 699825, "timestamp": 257199479000, "id": 106, "parentId": 101, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1752872528826, "traceId": "9ae2b8eb8cc771ab"}, {"name": "add-entry", "duration": 699841, "timestamp": 257199479025, "id": 108, "parentId": 101, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1752872528826, "traceId": "9ae2b8eb8cc771ab"}, {"name": "add-entry", "duration": 699863, "timestamp": 257199479015, "id": 107, "parentId": 101, "tags": {"request": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\node_modules\\next\\dist\\client\\router.js"}, "startTime": 1752872528826, "traceId": "9ae2b8eb8cc771ab"}, {"name": "add-entry", "duration": 713891, "timestamp": 257199478788, "id": 104, "parentId": 101, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmarkv%5C%5CDesktop%5C%5Ccoding%20stuff%5C%5Cbake%20it%20out%5C%5Cbake-it-out%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmarkv%5C%5CDesktop%5C%5Ccoding%20stuff%5C%5Cbake%20it%20out%5C%5Cbake-it-out%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmarkv%5C%5CDesktop%5C%5Ccoding%20stuff%5C%5Cbake%20it%20out%5C%5Cbake-it-out%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmarkv%5C%5CDesktop%5C%5Ccoding%20stuff%5C%5Cbake%20it%20out%5C%5Cbake-it-out%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmarkv%5C%5CDesktop%5C%5Ccoding%20stuff%5C%5Cbake%20it%20out%5C%5Cbake-it-out%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmarkv%5C%5CDesktop%5C%5Ccoding%20stuff%5C%5Cbake%20it%20out%5C%5Cbake-it-out%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmarkv%5C%5CDesktop%5C%5Ccoding%20stuff%5C%5Cbake%20it%20out%5C%5Cbake-it-out%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmarkv%5C%5CDesktop%5C%5Ccoding%20stuff%5C%5Cbake%20it%20out%5C%5Cbake-it-out%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmarkv%5C%5CDesktop%5C%5Ccoding%20stuff%5C%5Cbake%20it%20out%5C%5Cbake-it-out%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1752872528826, "traceId": "9ae2b8eb8cc771ab"}, {"name": "add-entry", "duration": 717463, "timestamp": 257199478594, "id": 103, "parentId": 101, "tags": {"request": "./node_modules/next/dist/client/app-next.js"}, "startTime": 1752872528826, "traceId": "9ae2b8eb8cc771ab"}, {"name": "add-entry", "duration": 747674, "timestamp": 257199478443, "id": 102, "parentId": 101, "tags": {"request": "./node_modules/next/dist/client/next.js"}, "startTime": 1752872528826, "traceId": "9ae2b8eb8cc771ab"}, {"name": "postcss-process", "duration": 360240, "timestamp": 257200994132, "id": 116, "parentId": 115, "tags": {}, "startTime": 1752872530341, "traceId": "9ae2b8eb8cc771ab"}, {"name": "postcss-loader", "duration": 1256532, "timestamp": 257200098150, "id": 115, "parentId": 114, "tags": {}, "startTime": 1752872529445, "traceId": "9ae2b8eb8cc771ab"}, {"name": "css-loader", "duration": 81868, "timestamp": 257201355026, "id": 117, "parentId": 114, "tags": {"astUsed": "true"}, "startTime": 1752872530702, "traceId": "9ae2b8eb8cc771ab"}, {"name": "build-module-css", "duration": 1465879, "timestamp": 257199997150, "id": 114, "parentId": 112, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\app\\globals.css", "layer": null}, "startTime": 1752872529344, "traceId": "9ae2b8eb8cc771ab"}, {"name": "build-module-css", "duration": 1723692, "timestamp": 257199747522, "id": 112, "parentId": 100, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\app\\globals.css", "layer": "app-pages-browser"}, "startTime": 1752872529095, "traceId": "9ae2b8eb8cc771ab"}, {"name": "build-module", "duration": 196, "timestamp": 257201471602, "id": 118, "parentId": 112, "tags": {}, "startTime": 1752872530819, "traceId": "9ae2b8eb8cc771ab"}, {"name": "add-entry", "duration": 1992945, "timestamp": 257199479033, "id": 109, "parentId": 101, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmarkv%5C%5CDesktop%5C%5Ccoding%20stuff%5C%5Cbake%20it%20out%5C%5Cbake-it-out%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmarkv%5C%5CDesktop%5C%5Ccoding%20stuff%5C%5Cbake%20it%20out%5C%5Cbake-it-out%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmarkv%5C%5CDesktop%5C%5Ccoding%20stuff%5C%5Cbake%20it%20out%5C%5Cbake-it-out%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmarkv%5C%5CDesktop%5C%5Ccoding%20stuff%5C%5Cbake%20it%20out%5C%5Cbake-it-out%5C%5Csrc%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmarkv%5C%5CDesktop%5C%5Ccoding%20stuff%5C%5Cbake%20it%20out%5C%5Cbake-it-out%5C%5Csrc%5C%5Ccontexts%5C%5CMultiplayerContext.tsx%22%2C%22ids%22%3A%5B%22MultiplayerProvider%22%5D%7D&server=false!"}, "startTime": 1752872528826, "traceId": "9ae2b8eb8cc771ab"}, {"name": "make", "duration": 1994412, "timestamp": 257199477715, "id": 101, "parentId": 100, "tags": {}, "startTime": 1752872528825, "traceId": "9ae2b8eb8cc771ab"}, {"name": "chunk-graph", "duration": 11919, "timestamp": 257201500297, "id": 120, "parentId": 119, "tags": {}, "startTime": 1752872530847, "traceId": "9ae2b8eb8cc771ab"}, {"name": "optimize-modules", "duration": 15, "timestamp": 257201512322, "id": 122, "parentId": 119, "tags": {}, "startTime": 1752872530859, "traceId": "9ae2b8eb8cc771ab"}, {"name": "optimize-chunks", "duration": 8036, "timestamp": 257201514043, "id": 124, "parentId": 119, "tags": {}, "startTime": 1752872530861, "traceId": "9ae2b8eb8cc771ab"}, {"name": "optimize-tree", "duration": 15, "timestamp": 257201522132, "id": 125, "parentId": 119, "tags": {}, "startTime": 1752872530869, "traceId": "9ae2b8eb8cc771ab"}, {"name": "optimize-chunk-modules", "duration": 10635, "timestamp": 257201522191, "id": 126, "parentId": 119, "tags": {}, "startTime": 1752872530869, "traceId": "9ae2b8eb8cc771ab"}]