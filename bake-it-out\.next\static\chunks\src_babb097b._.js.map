{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'danger' | 'success'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport const Button: React.FC<ButtonProps> = ({\n  variant = 'primary',\n  size = 'md',\n  className = '',\n  children,\n  ...props\n}) => {\n  const baseClasses = 'font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2'\n\n  const variantClasses = {\n    primary: 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500',\n    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500',\n    danger: 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500',\n    success: 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500',\n  }\n\n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-base',\n    lg: 'px-6 py-3 text-lg',\n  }\n\n  const combinedClasses = [\n    baseClasses,\n    variantClasses[variant],\n    sizeClasses[size],\n    className\n  ].join(' ')\n\n  return (\n    <button\n      className={combinedClasses}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAQO,MAAM,SAAgC;QAAC,EAC5C,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB;QACA,cAAc,CAAC,QAAQ;QACvB,WAAW,CAAC,KAAK;QACjB;KACD,CAAC,IAAI,CAAC;IAEP,qBACE,6LAAC;QACC,WAAW;QACV,GAAG,KAAK;kBAER;;;;;;AAGP;KArCa", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { Button } from '@/components/ui/Button'\nimport { useLanguage } from '@/contexts/LanguageContext'\n\nexport default function Home() {\n  const { language, setLanguage, t } = useLanguage()\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-orange-100 to-yellow-100 flex items-center justify-center\">\n      <div className=\"text-center space-y-8 p-8\">\n        <div className=\"space-y-4\">\n          <h1 className=\"text-6xl font-bold text-orange-800 mb-4\">\n            🥖 {t('game.title')}\n          </h1>\n          <p className=\"text-xl text-orange-700 max-w-2xl mx-auto\">\n            {t('game.subtitle')}\n          </p>\n        </div>\n\n        <div className=\"space-y-4\">\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button\n              size=\"lg\"\n              className=\"text-lg px-8 py-4\"\n              onClick={() => window.location.href = '/game'}\n            >\n              {t('game.play')}\n            </Button>\n            <Button variant=\"secondary\" size=\"lg\" className=\"text-lg px-8 py-4\">\n              {t('game.multiplayer')}\n            </Button>\n          </div>\n\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button\n              variant={language === 'en' ? 'primary' : 'secondary'}\n              size=\"md\"\n              onClick={() => setLanguage('en')}\n            >\n              {t('game.english')}\n            </Button>\n            <Button\n              variant={language === 'cs' ? 'primary' : 'secondary'}\n              size=\"md\"\n              onClick={() => setLanguage('cs')}\n            >\n              {t('game.czech')}\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 max-w-4xl mx-auto\">\n          <div className=\"bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center\">\n            <div className=\"text-3xl mb-3\">🏪</div>\n            <h3 className=\"font-semibold text-orange-800 mb-2\">{t('features.manage.title')}</h3>\n            <p className=\"text-orange-700 text-sm\">\n              {t('features.manage.description')}\n            </p>\n          </div>\n\n          <div className=\"bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center\">\n            <div className=\"text-3xl mb-3\">📈</div>\n            <h3 className=\"font-semibold text-orange-800 mb-2\">{t('features.levelup.title')}</h3>\n            <p className=\"text-orange-700 text-sm\">\n              {t('features.levelup.description')}\n            </p>\n          </div>\n\n          <div className=\"bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center\">\n            <div className=\"text-3xl mb-3\">👥</div>\n            <h3 className=\"font-semibold text-orange-800 mb-2\">{t('features.multiplayer.title')}</h3>\n            <p className=\"text-orange-700 text-sm\">\n              {t('features.multiplayer.description')}\n            </p>\n          </div>\n        </div>\n\n        <div className=\"mt-8 text-sm text-orange-600\">\n          <p>{t('status.development')}</p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAE/C,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCAA0C;gCAClD,EAAE;;;;;;;sCAER,6LAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;;;;;;;8BAIP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;8CAErC,EAAE;;;;;;8CAEL,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAY,MAAK;oCAAK,WAAU;8CAC7C,EAAE;;;;;;;;;;;;sCAIP,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,aAAa,OAAO,YAAY;oCACzC,MAAK;oCACL,SAAS,IAAM,YAAY;8CAE1B,EAAE;;;;;;8CAEL,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,aAAa,OAAO,YAAY;oCACzC,MAAK;oCACL,SAAS,IAAM,YAAY;8CAE1B,EAAE;;;;;;;;;;;;;;;;;;8BAKT,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;oCAAG,WAAU;8CAAsC,EAAE;;;;;;8CACtD,6LAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;sCAIP,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;oCAAG,WAAU;8CAAsC,EAAE;;;;;;8CACtD,6LAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;sCAIP,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;oCAAG,WAAU;8CAAsC,EAAE;;;;;;8CACtD,6LAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;;;;;;;8BAKT,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;kCAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;AAKhB;GA/EwB;;QACe,sIAAA,CAAA,cAAW;;;KAD1B", "debugId": null}}]}