'use client'

import { But<PERSON> } from '@/components/ui/Button'

export interface OrderData {
  id: string
  customerName: string
  items: string[]
  timeLimit: number
  reward: number
  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'failed'
  difficulty: number
}

interface OrderProps {
  order: OrderData
  onAccept: (orderId: string) => void
  onDecline: (orderId: string) => void
  onComplete?: (orderId: string) => void
}

export function Order({ order, onAccept, onDecline, onComplete }: OrderProps) {
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getStatusStyle = () => {
    switch (order.status) {
      case 'pending':
        return 'border-yellow-300 bg-yellow-50'
      case 'accepted':
      case 'in_progress':
        return 'border-blue-300 bg-blue-50'
      case 'completed':
        return 'border-green-300 bg-green-50'
      case 'failed':
        return 'border-red-300 bg-red-50'
      default:
        return 'border-gray-300 bg-gray-50'
    }
  }

  const getDifficultyStars = () => {
    return '⭐'.repeat(order.difficulty) + '☆'.repeat(5 - order.difficulty)
  }

  const getCustomerAvatar = () => {
    const avatars = ['👩', '👨', '👵', '👴', '👧', '👦']
    const index = order.customerName.length % avatars.length
    return avatars[index]
  }

  return (
    <div className={`p-4 rounded-lg border ${getStatusStyle()}`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <span className="text-lg">{getCustomerAvatar()}</span>
          <h3 className="font-medium text-gray-800">{order.customerName}</h3>
        </div>
        <span className="text-sm font-semibold text-green-600">${order.reward}</span>
      </div>
      
      <div className="text-sm text-gray-600 mb-2">
        {order.items.map((item, index) => (
          <div key={index} className="flex items-center space-x-1">
            <span>🧁</span>
            <span>{item}</span>
          </div>
        ))}
      </div>
      
      <div className="flex justify-between items-center mb-3">
        <div className="text-xs text-gray-500">
          ⏰ {formatTime(order.timeLimit)}
        </div>
        <div className="text-xs" title={`Difficulty: ${order.difficulty}/5`}>
          {getDifficultyStars()}
        </div>
      </div>

      {order.status === 'pending' && (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="success"
            onClick={() => onAccept(order.id)}
            className="flex-1"
          >
            ✅ Accept
          </Button>
          <Button 
            size="sm" 
            variant="danger" 
            onClick={() => onDecline(order.id)}
            className="flex-1"
          >
            ❌ Decline
          </Button>
        </div>
      )}
      
      {order.status === 'accepted' && (
        <div className="text-center">
          <div className="text-blue-600 text-sm font-medium mb-2">
            📋 Order Accepted
          </div>
          <Button
            size="sm"
            variant="primary"
            onClick={() => onComplete && onComplete(order.id)}
            className="w-full"
          >
            🎯 Complete Order
          </Button>
        </div>
      )}
      
      {order.status === 'in_progress' && (
        <div className="text-center text-orange-600 text-sm font-medium">
          🔄 In Progress...
        </div>
      )}
      
      {order.status === 'completed' && (
        <div className="text-center text-green-600 text-sm font-medium">
          ✅ Completed!
        </div>
      )}
      
      {order.status === 'failed' && (
        <div className="text-center text-red-600 text-sm font-medium">
          ❌ Failed
        </div>
      )}
    </div>
  )
}
