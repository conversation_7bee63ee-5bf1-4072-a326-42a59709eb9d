{"name": "bake-it-out", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "socket-server": "node server/socket-server.js", "dev:full": "concurrently \"npm run dev\" \"npm run socket-server\"", "electron": "electron electron/main.js", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron electron/main.js\"", "build-electron": "npm run build && npm run build-electron-main", "build-electron-main": "electron-builder", "dist": "npm run build && electron-builder --publish=never", "dist-win": "npm run build && electron-builder --win", "dist-mac": "npm run build && electron-builder --mac", "dist-linux": "npm run build && electron-builder --linux", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"clsx": "^2.1.1", "next": "15.4.1", "react": "19.1.0", "react-dom": "19.1.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.2.0", "electron": "^37.2.3", "electron-builder": "^26.0.12", "electron-is-dev": "^3.0.1", "electron-log": "^5.4.1", "electron-updater": "^6.6.2", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "typescript": "^5", "wait-on": "^8.0.3"}, "main": "electron/main.js", "homepage": "./", "build": {"appId": "com.bakeitout.app", "productName": "Bake It Out", "directories": {"output": "dist"}, "files": ["out/**/*", "electron/**/*", "server/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "server", "to": "server"}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "electron/assets/icon.ico", "publisherName": "Bake It Out Team", "verifyUpdateCodeSignature": false}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "electron/assets/icon.icns", "category": "public.app-category.games"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}], "icon": "electron/assets/icon.png", "category": "Game"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Bake It Out"}, "dmg": {"title": "Bake It Out", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}}}