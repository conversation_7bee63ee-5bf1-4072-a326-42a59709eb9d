{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/contexts/LanguageContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect } from 'react'\n\ntype Language = 'en' | 'cs'\n\ninterface LanguageContextType {\n  language: Language\n  setLanguage: (lang: Language) => void\n  t: (key: string, params?: Record<string, string>) => string\n}\n\nconst LanguageContext = createContext<LanguageContextType | undefined>(undefined)\n\n// Comprehensive translations object\nconst translations = {\n  en: {\n    // Main game\n    'game.title': 'Bake It Out',\n    'game.subtitle': 'Master the art of bakery management in this engaging multiplayer game. Complete orders, unlock recipes, automate your processes, and compete with friends!',\n    'game.play': '🎮 Start Playing',\n    'game.multiplayer': '👥 Multiplayer',\n    'game.english': '🇺🇸 English',\n    'game.czech': '🇨🇿 Čeština',\n    'game.home': '🏠 Home',\n    'game.close': '✕ Close',\n    'game.continue': '🚀 Continue Playing',\n\n    // Features\n    'features.manage.title': 'Manage Your Bakery',\n    'features.manage.description': 'Take orders, bake delicious goods, and serve happy customers',\n    'features.levelup.title': 'Level Up & Automate',\n    'features.levelup.description': 'Unlock new recipes, buy equipment, and automate your processes',\n    'features.multiplayer.title': 'Play Together',\n    'features.multiplayer.description': 'Cooperative and competitive multiplayer modes with friends',\n    'status.development': '🚧 Game in Development - Phase 5: Multilayer Support! 🚧',\n\n    // Game interface\n    'ui.level': 'Level {{level}}',\n    'ui.money': '${{amount}}',\n    'ui.experience': 'XP: {{current}}/{{max}}',\n    'ui.skillPoints': 'SP: {{points}}',\n    'ui.achievements': '🏆 Achievements',\n    'ui.skills': '🌟 Skills',\n    'ui.automation': '🤖 Automation',\n\n    // Kitchen\n    'kitchen.title': '🏪 Kitchen',\n    'kitchen.clickToUse': 'Click to use',\n    'kitchen.making': 'Making: {{recipe}}',\n    'kitchen.timeRemaining': 'Time: {{time}}',\n\n    // Inventory\n    'inventory.title': '📦 Inventory',\n    'inventory.quantity': 'Qty: {{qty}}',\n    'inventory.cost': '${{cost}} each',\n\n    // Orders\n    'orders.title': '📋 Orders',\n    'orders.newOrder': '+ New Order',\n    'orders.accept': 'Accept',\n    'orders.decline': 'Decline',\n    'orders.complete': 'Complete',\n    'orders.inProgress': 'In Progress',\n    'orders.timeLimit': 'Time: {{time}}',\n    'orders.reward': '${{amount}}',\n    'orders.customer': 'Customer: {{name}}',\n\n    // Quick Actions\n    'actions.title': '⚡ Quick Actions',\n    'actions.buyIngredients': '🛒 Buy Ingredients',\n    'actions.viewRecipes': '📖 View Recipes',\n    'actions.equipmentShop': '🔧 Equipment Shop',\n\n    // Modals\n    'modal.recipes.title': '📖 Recipe Book',\n    'modal.shop.title': '🛒 Ingredient Shop',\n    'modal.baking.title': '🔥 {{equipment}} - Select Recipe',\n    'modal.achievements.title': '🏆 Achievements',\n    'modal.skills.title': '🌟 Skill Tree',\n    'modal.automation.title': '🤖 Automation Control',\n    'modal.equipmentShop.title': '🏪 Equipment Shop',\n    'modal.settings.title': '⚙️ Settings',\n    'modal.bakeries.title': '🏪 Bakery Manager',\n    'modal.levelUp.title': 'Level Up!',\n    'modal.levelUp.subtitle': 'You reached Level {{level}}!',\n\n    // Recipe Modal\n    'recipes.all': 'All',\n    'recipes.cookies': 'Cookies',\n    'recipes.cakes': 'Cakes',\n    'recipes.bread': 'Bread',\n    'recipes.pastries': 'Pastries',\n    'recipes.ingredients': 'Ingredients:',\n    'recipes.difficulty': 'Difficulty:',\n    'recipes.time': 'Time:',\n    'recipes.canCraft': '✅ Can Craft',\n    'recipes.unlockLevel': 'Unlocked at Level {{level}}',\n    'recipes.noRecipes': 'No recipes available in this category.',\n    'recipes.levelUpToUnlock': 'Level up to unlock more recipes!',\n\n    // Shop Modal\n    'shop.currentStock': 'Current stock: {{quantity}}',\n    'shop.buy': 'Buy',\n    'shop.tooExpensive': 'Too Expensive',\n    'shop.tips.title': '💡 Shopping Tips',\n    'shop.tips.bulk': '• Buy ingredients in bulk to save time',\n    'shop.tips.stock': '• Keep an eye on your stock levels',\n    'shop.tips.rare': '• Some recipes require rare ingredients',\n    'shop.tips.prices': '• Prices may vary based on availability',\n\n    // Baking Modal\n    'baking.selectRecipe': 'Select Recipe',\n    'baking.noRecipes': 'No recipes available',\n    'baking.noIngredients': 'You don\\'t have enough ingredients to craft any recipes.',\n    'baking.buyIngredients': 'Buy Ingredients',\n    'baking.startBaking': '🔥 Start Baking',\n    'baking.instructions': '📋 Baking Instructions for {{recipe}}',\n    'baking.expectedReward': 'Expected reward: ${{amount}}',\n    'baking.makesSure': 'Make sure you have all ingredients before starting!',\n\n    // Achievements Modal\n    'achievements.completed': '{{completed}} of {{total}} achievements completed',\n    'achievements.overallProgress': 'Overall Progress',\n    'achievements.progress': 'Progress',\n    'achievements.reward': 'Reward:',\n    'achievements.noAchievements': 'No achievements in this category.',\n\n    // Skills Modal\n    'skills.availablePoints': 'Available Skill Points: {{points}}',\n    'skills.efficiency': 'Efficiency',\n    'skills.automation': 'Automation',\n    'skills.quality': 'Quality',\n    'skills.business': 'Business',\n    'skills.effects': 'Effects:',\n    'skills.requires': 'Requires: {{requirements}}',\n    'skills.requiresLevel': 'Requires Level {{level}}',\n    'skills.maxed': '✅ Maxed',\n    'skills.upgrade': '⬆️ Upgrade ({{cost}} SP)',\n    'skills.locked': '🔒 Locked',\n    'skills.noSkills': 'No skills in this category.',\n    'skills.tips.title': '💡 Skill Tips',\n    'skills.tips.earnPoints': '• Earn skill points by leveling up (1 point every 2 levels)',\n    'skills.tips.prerequisites': '• Some skills require other skills to be unlocked first',\n    'skills.tips.playstyle': '• Focus on skills that match your playstyle',\n    'skills.tips.efficiency': '• Efficiency skills help with resource management',\n\n    // Automation Modal\n    'automation.masterControl': '🎛️ Master Control',\n    'automation.enableAutomation': 'Enable Automation',\n    'automation.autoStart': 'Auto-start Equipment',\n    'automation.priorityMode': '🎯 Priority Mode',\n    'automation.efficiency': 'Efficiency (Orders First)',\n    'automation.profit': 'Profit (Highest Value)',\n    'automation.speed': 'Speed (Fastest Recipes)',\n    'automation.priorityDescription': 'How automation chooses what to bake',\n    'automation.performance': '⚡ Performance',\n    'automation.maxJobs': 'Max Concurrent Jobs: {{jobs}}',\n    'automation.safety': '🛡️ Safety',\n    'automation.stopWhenLow': 'Stop when ingredients below: {{threshold}}',\n    'automation.upgrades': '💡 Automation Upgrades',\n    'automation.upgradesDescription': 'Improve your automation efficiency, speed, and intelligence with these upgrades.',\n    'automation.purchase': 'Purchase',\n    'automation.noUpgrades': 'No upgrades available at your current level.',\n    'automation.levelUpForUpgrades': 'Level up to unlock more automation upgrades!',\n    'automation.automatedEquipment': 'Automated Equipment',\n    'automation.activeUpgrades': 'Active Upgrades',\n    'automation.automationStatus': 'Automation Status',\n    'automation.equipmentStatus': '🏭 Equipment Status',\n    'automation.running': 'Running',\n    'automation.idle': 'Idle',\n    'automation.noAutomatedEquipment': 'No automated equipment available.',\n    'automation.purchaseAutoEquipment': 'Purchase auto-equipment from the shop to get started!',\n\n    // Equipment Shop Modal\n    'equipmentShop.upgradeYourBakery': 'Upgrade your bakery with professional equipment',\n    'equipmentShop.basic': 'Basic',\n    'equipmentShop.automated': 'Automated',\n    'equipmentShop.advanced': 'Advanced',\n    'equipmentShop.efficiency': 'Efficiency: {{efficiency}}x',\n    'equipmentShop.automation': 'Automation:',\n    'equipmentShop.unlockLevel': 'Unlock Level: {{level}}',\n    'equipmentShop.purchase': '💰 Purchase',\n    'equipmentShop.noEquipment': 'No equipment available in this category.',\n    'equipmentShop.levelUpForEquipment': 'Level up to unlock more equipment!',\n    'equipmentShop.tips.title': '💡 Equipment Tips',\n    'equipmentShop.tips.automated': '• Automated equipment can run without your supervision',\n    'equipmentShop.tips.efficiency': '• Higher efficiency means faster production and better quality',\n    'equipmentShop.tips.conveyor': '• Conveyor belts connect equipment for seamless workflow',\n    'equipmentShop.tips.advanced': '• Advanced equipment unlocks at higher levels',\n\n    // Level Up Modal\n    'levelUp.levelRewards': '🎁 Level Rewards',\n    'levelUp.whatsNext': '💡 What\\'s Next?',\n    'levelUp.checkRecipes': '• Check out new recipes in your recipe book',\n    'levelUp.visitShop': '• Visit the shop for new equipment',\n    'levelUp.challengingOrders': '• Take on more challenging orders',\n    'levelUp.investSkills': '• Invest in skill upgrades',\n\n    // Settings Modal\n    'settings.title': '⚙️ Settings',\n    'settings.general': 'General',\n    'settings.audio': 'Audio',\n    'settings.graphics': 'Graphics',\n    'settings.save': 'Save & Data',\n    'settings.language': '🌍 Language',\n    'settings.gameplay': '🎮 Gameplay',\n    'settings.notifications': 'Enable Notifications',\n    'settings.tutorials': 'Show Tutorials',\n    'settings.animationSpeed': 'Animation Speed',\n    'settings.sound': 'Sound Effects',\n    'settings.music': 'Background Music',\n    'settings.quality': '🎨 Graphics Quality',\n    'settings.autoSave': '💾 Auto-Save',\n    'settings.enableAutoSave': 'Enable Auto-Save',\n    'settings.dataManagement': '📁 Data Management',\n    'settings.exportSave': '📤 Export Save',\n    'settings.importSave': '📥 Import Save',\n    'settings.cloudSync': '☁️ Cloud Sync',\n    'settings.cloudSyncDescription': 'Cloud sync allows you to save your progress online and play across multiple devices.',\n    'settings.comingSoon': 'Coming Soon',\n\n    // Bakery Manager Modal\n    'bakeries.title': '🏪 Bakery Manager',\n    'bakeries.subtitle': 'Manage your bakery empire',\n    'bakeries.owned': 'My Bakeries',\n    'bakeries.available': 'Available',\n    'bakeries.current': 'Current',\n    'bakeries.level': 'Level',\n    'bakeries.specialization': 'Specialization',\n    'bakeries.equipment': 'Equipment',\n    'bakeries.orders': 'Active Orders',\n    'bakeries.switchTo': 'Switch To',\n    'bakeries.noOwned': 'You don\\'t own any bakeries yet.',\n    'bakeries.purchase': '💰 Purchase',\n    'bakeries.tooExpensive': '💸 Too Expensive',\n    'bakeries.allOwned': 'You own all available bakeries!',\n    'bakeries.tips': '💡 Bakery Tips',\n    'bakeries.tip1': 'Each bakery specializes in different products for bonus efficiency',\n    'bakeries.tip2': 'Switch between bakeries to manage multiple locations',\n    'bakeries.tip3': 'Specialized bakeries attract customers looking for specific items',\n    'bakeries.tip4': 'Upgrade each bakery independently for maximum profit',\n\n    // Notifications\n    'notifications.orderAccepted': 'Order Accepted',\n    'notifications.orderAcceptedMessage': 'You have accepted a new order!',\n    'notifications.orderCompleted': 'Order Completed!',\n    'notifications.orderCompletedMessage': 'You earned ${{reward}} and gained experience!',\n    'notifications.orderDeclined': 'Order Declined',\n    'notifications.orderDeclinedMessage': 'Order has been removed from your queue.',\n    'notifications.bakeryPurchased': 'Bakery Purchased!',\n    'notifications.bakeryPurchasedMessage': 'You now own {{name}}!',\n    'notifications.bakerySwitched': 'Bakery Switched',\n    'notifications.bakerySwitchedMessage': 'Switched to {{name}}',\n\n    // Common buttons and actions\n    'common.accept': 'Accept',\n    'common.decline': 'Decline',\n    'common.complete': 'Complete',\n    'common.purchase': 'Purchase',\n    'common.upgrade': 'Upgrade',\n    'common.cancel': 'Cancel',\n    'common.confirm': 'Confirm',\n    'common.save': 'Save',\n    'common.load': 'Load',\n    'common.delete': 'Delete',\n    'common.edit': 'Edit',\n    'common.back': 'Back',\n    'common.next': 'Next',\n    'common.previous': 'Previous',\n    'common.yes': 'Yes',\n    'common.no': 'No'\n  },\n  cs: {\n    // Main game\n    'game.title': 'Bake It Out',\n    'game.subtitle': 'Ovládněte umění řízení pekárny v této poutavé multiplayerové hře. Plňte objednávky, odemykejte recepty, automatizujte procesy a soutěžte s přáteli!',\n    'game.play': '🎮 Začít hrát',\n    'game.multiplayer': '👥 Multiplayer',\n    'game.english': '🇺🇸 English',\n    'game.czech': '🇨🇿 Čeština',\n    'game.home': '🏠 Domů',\n    'game.close': '✕ Zavřít',\n    'game.continue': '🚀 Pokračovat ve hře',\n\n    // Features\n    'features.manage.title': 'Spravujte svou pekárnu',\n    'features.manage.description': 'Přijímejte objednávky, pečte lahodné výrobky a obsluhujte spokojené zákazníky',\n    'features.levelup.title': 'Postupujte a automatizujte',\n    'features.levelup.description': 'Odemykejte nové recepty, kupujte vybavení a automatizujte své procesy',\n    'features.multiplayer.title': 'Hrajte společně',\n    'features.multiplayer.description': 'Kooperativní a soutěžní multiplayerové režimy s přáteli',\n    'status.development': '🚧 Hra ve vývoji - Fáze 5: Vícevrstvá podpora! 🚧',\n\n    // Game interface\n    'ui.level': 'Úroveň {{level}}',\n    'ui.money': '{{amount}} Kč',\n    'ui.experience': 'XP: {{current}}/{{max}}',\n    'ui.skillPoints': 'SP: {{points}}',\n    'ui.achievements': '🏆 Úspěchy',\n    'ui.skills': '🌟 Dovednosti',\n    'ui.automation': '🤖 Automatizace',\n\n    // Kitchen\n    'kitchen.title': '🏪 Kuchyně',\n    'kitchen.clickToUse': 'Klikněte pro použití',\n    'kitchen.making': 'Připravuje: {{recipe}}',\n    'kitchen.timeRemaining': 'Čas: {{time}}',\n\n    // Inventory\n    'inventory.title': '📦 Sklad',\n    'inventory.quantity': 'Množství: {{qty}}',\n    'inventory.cost': '{{cost}} Kč za kus',\n\n    // Orders\n    'orders.title': '📋 Objednávky',\n    'orders.newOrder': '+ Nová objednávka',\n    'orders.accept': 'Přijmout',\n    'orders.decline': 'Odmítnout',\n    'orders.complete': 'Dokončit',\n    'orders.inProgress': 'Probíhá',\n    'orders.timeLimit': 'Čas: {{time}}',\n    'orders.reward': '{{amount}} Kč',\n    'orders.customer': 'Zákazník: {{name}}',\n\n    // Quick Actions\n    'actions.title': '⚡ Rychlé akce',\n    'actions.buyIngredients': '🛒 Koupit suroviny',\n    'actions.viewRecipes': '📖 Zobrazit recepty',\n    'actions.equipmentShop': '🔧 Obchod s vybavením',\n\n    // Modals\n    'modal.recipes.title': '📖 Kniha receptů',\n    'modal.shop.title': '🛒 Obchod se surovinami',\n    'modal.baking.title': '🔥 {{equipment}} - Vyberte recept',\n    'modal.achievements.title': '🏆 Úspěchy',\n    'modal.skills.title': '🌟 Strom dovedností',\n    'modal.automation.title': '🤖 Ovládání automatizace',\n    'modal.equipmentShop.title': '🏪 Obchod s vybavením',\n    'modal.settings.title': '⚙️ Nastavení',\n    'modal.bakeries.title': '🏪 Správce pekáren',\n    'modal.levelUp.title': 'Postup na vyšší úroveň!',\n    'modal.levelUp.subtitle': 'Dosáhli jste úrovně {{level}}!',\n\n    // Recipe Modal\n    'recipes.all': 'Vše',\n    'recipes.cookies': 'Sušenky',\n    'recipes.cakes': 'Dorty',\n    'recipes.bread': 'Chléb',\n    'recipes.pastries': 'Pečivo',\n    'recipes.ingredients': 'Suroviny:',\n    'recipes.difficulty': 'Obtížnost:',\n    'recipes.time': 'Čas:',\n    'recipes.canCraft': '✅ Lze vyrobit',\n    'recipes.unlockLevel': 'Odemčeno na úrovni {{level}}',\n    'recipes.noRecipes': 'V této kategorii nejsou k dispozici žádné recepty.',\n    'recipes.levelUpToUnlock': 'Postupte na vyšší úroveň pro odemčení dalších receptů!',\n\n    // Shop Modal\n    'shop.currentStock': 'Aktuální zásoba: {{quantity}}',\n    'shop.buy': 'Koupit',\n    'shop.tooExpensive': 'Příliš drahé',\n    'shop.tips.title': '💡 Tipy pro nakupování',\n    'shop.tips.bulk': '• Kupujte suroviny ve velkém množství pro úsporu času',\n    'shop.tips.stock': '• Sledujte úroveň svých zásob',\n    'shop.tips.rare': '• Některé recepty vyžadují vzácné suroviny',\n    'shop.tips.prices': '• Ceny se mohou lišit podle dostupnosti',\n\n    // Baking Modal\n    'baking.selectRecipe': 'Vyberte recept',\n    'baking.noRecipes': 'Žádné recepty k dispozici',\n    'baking.noIngredients': 'Nemáte dostatek surovin pro výrobu jakéhokoli receptu.',\n    'baking.buyIngredients': 'Koupit suroviny',\n    'baking.startBaking': '🔥 Začít péct',\n    'baking.instructions': '📋 Pokyny pro pečení {{recipe}}',\n    'baking.expectedReward': 'Očekávaná odměna: {{amount}} Kč',\n    'baking.makesSure': 'Ujistěte se, že máte všechny suroviny před začátkem!',\n\n    // Achievements Modal\n    'achievements.completed': '{{completed}} z {{total}} úspěchů dokončeno',\n    'achievements.overallProgress': 'Celkový pokrok',\n    'achievements.progress': 'Pokrok',\n    'achievements.reward': 'Odměna:',\n    'achievements.noAchievements': 'V této kategorii nejsou žádné úspěchy.',\n\n    // Skills Modal\n    'skills.availablePoints': 'Dostupné body dovedností: {{points}}',\n    'skills.efficiency': 'Efektivita',\n    'skills.automation': 'Automatizace',\n    'skills.quality': 'Kvalita',\n    'skills.business': 'Podnikání',\n    'skills.effects': 'Efekty:',\n    'skills.requires': 'Vyžaduje: {{requirements}}',\n    'skills.requiresLevel': 'Vyžaduje úroveň {{level}}',\n    'skills.maxed': '✅ Maximální',\n    'skills.upgrade': '⬆️ Vylepšit ({{cost}} SP)',\n    'skills.locked': '🔒 Uzamčeno',\n    'skills.noSkills': 'V této kategorii nejsou žádné dovednosti.',\n    'skills.tips.title': '💡 Tipy pro dovednosti',\n    'skills.tips.earnPoints': '• Získávejte body dovedností postupem na vyšší úroveň (1 bod každé 2 úrovně)',\n    'skills.tips.prerequisites': '• Některé dovednosti vyžadují nejprve odemčení jiných dovedností',\n    'skills.tips.playstyle': '• Zaměřte se na dovednosti, které odpovídají vašemu stylu hry',\n    'skills.tips.efficiency': '• Dovednosti efektivity pomáhají se správou zdrojů',\n\n    // Automation Modal\n    'automation.masterControl': '🎛️ Hlavní ovládání',\n    'automation.enableAutomation': 'Povolit automatizaci',\n    'automation.autoStart': 'Automatické spuštění vybavení',\n    'automation.priorityMode': '🎯 Režim priority',\n    'automation.efficiency': 'Efektivita (objednávky první)',\n    'automation.profit': 'Zisk (nejvyšší hodnota)',\n    'automation.speed': 'Rychlost (nejrychlejší recepty)',\n    'automation.priorityDescription': 'Jak automatizace vybírá, co péct',\n    'automation.performance': '⚡ Výkon',\n    'automation.maxJobs': 'Max současných úloh: {{jobs}}',\n    'automation.safety': '🛡️ Bezpečnost',\n    'automation.stopWhenLow': 'Zastavit, když suroviny klesnou pod: {{threshold}}',\n    'automation.upgrades': '💡 Vylepšení automatizace',\n    'automation.upgradesDescription': 'Vylepšete efektivitu, rychlost a inteligenci vaší automatizace.',\n    'automation.purchase': 'Koupit',\n    'automation.noUpgrades': 'Na vaší současné úrovni nejsou k dispozici žádná vylepšení.',\n    'automation.levelUpForUpgrades': 'Postupte na vyšší úroveň pro odemčení dalších vylepšení automatizace!',\n    'automation.automatedEquipment': 'Automatizované vybavení',\n    'automation.activeUpgrades': 'Aktivní vylepšení',\n    'automation.automationStatus': 'Stav automatizace',\n    'automation.equipmentStatus': '🏭 Stav vybavení',\n    'automation.running': 'Běží',\n    'automation.idle': 'Nečinné',\n    'automation.noAutomatedEquipment': 'Žádné automatizované vybavení k dispozici.',\n    'automation.purchaseAutoEquipment': 'Kupte si auto-vybavení z obchodu pro začátek!',\n\n    // Equipment Shop Modal\n    'equipmentShop.upgradeYourBakery': 'Vylepšete svou pekárnu profesionálním vybavením',\n    'equipmentShop.basic': 'Základní',\n    'equipmentShop.automated': 'Automatizované',\n    'equipmentShop.advanced': 'Pokročilé',\n    'equipmentShop.efficiency': 'Efektivita: {{efficiency}}x',\n    'equipmentShop.automation': 'Automatizace:',\n    'equipmentShop.unlockLevel': 'Úroveň odemčení: {{level}}',\n    'equipmentShop.purchase': '💰 Koupit',\n    'equipmentShop.noEquipment': 'V této kategorii není k dispozici žádné vybavení.',\n    'equipmentShop.levelUpForEquipment': 'Postupte na vyšší úroveň pro odemčení dalšího vybavení!',\n    'equipmentShop.tips.title': '💡 Tipy pro vybavení',\n    'equipmentShop.tips.automated': '• Automatizované vybavení může běžet bez vašeho dohledu',\n    'equipmentShop.tips.efficiency': '• Vyšší efektivita znamená rychlejší výrobu a lepší kvalitu',\n    'equipmentShop.tips.conveyor': '• Dopravní pásy spojují vybavení pro bezproblémový pracovní tok',\n    'equipmentShop.tips.advanced': '• Pokročilé vybavení se odemyká na vyšších úrovních',\n\n    // Level Up Modal\n    'levelUp.levelRewards': '🎁 Odměny za úroveň',\n    'levelUp.whatsNext': '💡 Co dál?',\n    'levelUp.checkRecipes': '• Podívejte se na nové recepty ve své knize receptů',\n    'levelUp.visitShop': '• Navštivte obchod pro nové vybavení',\n    'levelUp.challengingOrders': '• Přijměte náročnější objednávky',\n    'levelUp.investSkills': '• Investujte do vylepšení dovedností',\n\n    // Settings Modal\n    'settings.title': '⚙️ Nastavení',\n    'settings.general': 'Obecné',\n    'settings.audio': 'Zvuk',\n    'settings.graphics': 'Grafika',\n    'settings.save': 'Uložení a data',\n    'settings.language': '🌍 Jazyk',\n    'settings.gameplay': '🎮 Hratelnost',\n    'settings.notifications': 'Povolit oznámení',\n    'settings.tutorials': 'Zobrazit návody',\n    'settings.animationSpeed': 'Rychlost animace',\n    'settings.sound': 'Zvukové efekty',\n    'settings.music': 'Hudba na pozadí',\n    'settings.quality': '🎨 Kvalita grafiky',\n    'settings.autoSave': '💾 Automatické ukládání',\n    'settings.enableAutoSave': 'Povolit automatické ukládání',\n    'settings.dataManagement': '📁 Správa dat',\n    'settings.exportSave': '📤 Exportovat uložení',\n    'settings.importSave': '📥 Importovat uložení',\n    'settings.cloudSync': '☁️ Cloudová synchronizace',\n    'settings.cloudSyncDescription': 'Cloudová synchronizace vám umožňuje uložit pokrok online a hrát na více zařízeních.',\n    'settings.comingSoon': 'Již brzy',\n\n    // Bakery Manager Modal\n    'bakeries.title': '🏪 Správce pekáren',\n    'bakeries.subtitle': 'Spravujte své pekárenské impérium',\n    'bakeries.owned': 'Moje pekárny',\n    'bakeries.available': 'Dostupné',\n    'bakeries.current': 'Aktuální',\n    'bakeries.level': 'Úroveň',\n    'bakeries.specialization': 'Specializace',\n    'bakeries.equipment': 'Vybavení',\n    'bakeries.orders': 'Aktivní objednávky',\n    'bakeries.switchTo': 'Přepnout na',\n    'bakeries.noOwned': 'Ještě nevlastníte žádné pekárny.',\n    'bakeries.purchase': '💰 Koupit',\n    'bakeries.tooExpensive': '💸 Příliš drahé',\n    'bakeries.allOwned': 'Vlastníte všechny dostupné pekárny!',\n    'bakeries.tips': '💡 Tipy pro pekárny',\n    'bakeries.tip1': 'Každá pekárna se specializuje na různé produkty pro bonusovou efektivitu',\n    'bakeries.tip2': 'Přepínejte mezi pekárnami pro správu více lokalit',\n    'bakeries.tip3': 'Specializované pekárny přitahují zákazníky hledající konkrétní položky',\n    'bakeries.tip4': 'Vylepšujte každou pekárnu nezávisle pro maximální zisk',\n\n    // Notifications\n    'notifications.orderAccepted': 'Objednávka přijata',\n    'notifications.orderAcceptedMessage': 'Přijali jste novou objednávku!',\n    'notifications.orderCompleted': 'Objednávka dokončena!',\n    'notifications.orderCompletedMessage': 'Získali jste {{reward}} Kč a zkušenosti!',\n    'notifications.orderDeclined': 'Objednávka odmítnuta',\n    'notifications.orderDeclinedMessage': 'Objednávka byla odstraněna z vaší fronty.',\n    'notifications.bakeryPurchased': 'Pekárna zakoupena!',\n    'notifications.bakeryPurchasedMessage': 'Nyní vlastníte {{name}}!',\n    'notifications.bakerySwitched': 'Pekárna přepnuta',\n    'notifications.bakerySwitchedMessage': 'Přepnuto na {{name}}',\n\n    // Common buttons and actions\n    'common.accept': 'Přijmout',\n    'common.decline': 'Odmítnout',\n    'common.complete': 'Dokončit',\n    'common.purchase': 'Koupit',\n    'common.upgrade': 'Vylepšit',\n    'common.cancel': 'Zrušit',\n    'common.confirm': 'Potvrdit',\n    'common.save': 'Uložit',\n    'common.load': 'Načíst',\n    'common.delete': 'Smazat',\n    'common.edit': 'Upravit',\n    'common.back': 'Zpět',\n    'common.next': 'Další',\n    'common.previous': 'Předchozí',\n    'common.yes': 'Ano',\n    'common.no': 'Ne'\n  }\n}\n\nexport function LanguageProvider({ children }: { children: React.ReactNode }) {\n  const [language, setLanguage] = useState<Language>('en')\n\n  useEffect(() => {\n    // Load language from localStorage on client side\n    const savedLanguage = localStorage.getItem('language') as Language\n    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'cs')) {\n      setLanguage(savedLanguage)\n    }\n  }, [])\n\n  const handleSetLanguage = (lang: Language) => {\n    setLanguage(lang)\n    localStorage.setItem('language', lang)\n  }\n\n  const t = (key: string, params?: Record<string, string>) => {\n    let translation = translations[language][key as keyof typeof translations[typeof language]] || key\n    \n    if (params) {\n      Object.entries(params).forEach(([param, value]) => {\n        translation = translation.replace(`{{${param}}}`, value)\n      })\n    }\n    \n    return translation\n  }\n\n  return (\n    <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage, t }}>\n      {children}\n    </LanguageContext.Provider>\n  )\n}\n\nexport function useLanguage() {\n  const context = useContext(LanguageContext)\n  if (context === undefined) {\n    throw new Error('useLanguage must be used within a LanguageProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAYA,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAmC;AAEvE,oCAAoC;AACpC,MAAM,eAAe;IACnB,IAAI;QACF,YAAY;QACZ,cAAc;QACd,iBAAiB;QACjB,aAAa;QACb,oBAAoB;QACpB,gBAAgB;QAChB,cAAc;QACd,aAAa;QACb,cAAc;QACd,iBAAiB;QAEjB,WAAW;QACX,yBAAyB;QACzB,+BAA+B;QAC/B,0BAA0B;QAC1B,gCAAgC;QAChC,8BAA8B;QAC9B,oCAAoC;QACpC,sBAAsB;QAEtB,iBAAiB;QACjB,YAAY;QACZ,YAAY;QACZ,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,aAAa;QACb,iBAAiB;QAEjB,UAAU;QACV,iBAAiB;QACjB,sBAAsB;QACtB,kBAAkB;QAClB,yBAAyB;QAEzB,YAAY;QACZ,mBAAmB;QACnB,sBAAsB;QACtB,kBAAkB;QAElB,SAAS;QACT,gBAAgB;QAChB,mBAAmB;QACnB,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,qBAAqB;QACrB,oBAAoB;QACpB,iBAAiB;QACjB,mBAAmB;QAEnB,gBAAgB;QAChB,iBAAiB;QACjB,0BAA0B;QAC1B,uBAAuB;QACvB,yBAAyB;QAEzB,SAAS;QACT,uBAAuB;QACvB,oBAAoB;QACpB,sBAAsB;QACtB,4BAA4B;QAC5B,sBAAsB;QACtB,0BAA0B;QAC1B,6BAA6B;QAC7B,wBAAwB;QACxB,wBAAwB;QACxB,uBAAuB;QACvB,0BAA0B;QAE1B,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,iBAAiB;QACjB,iBAAiB;QACjB,oBAAoB;QACpB,uBAAuB;QACvB,sBAAsB;QACtB,gBAAgB;QAChB,oBAAoB;QACpB,uBAAuB;QACvB,qBAAqB;QACrB,2BAA2B;QAE3B,aAAa;QACb,qBAAqB;QACrB,YAAY;QACZ,qBAAqB;QACrB,mBAAmB;QACnB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,oBAAoB;QAEpB,eAAe;QACf,uBAAuB;QACvB,oBAAoB;QACpB,wBAAwB;QACxB,yBAAyB;QACzB,sBAAsB;QACtB,uBAAuB;QACvB,yBAAyB;QACzB,oBAAoB;QAEpB,qBAAqB;QACrB,0BAA0B;QAC1B,gCAAgC;QAChC,yBAAyB;QACzB,uBAAuB;QACvB,+BAA+B;QAE/B,eAAe;QACf,0BAA0B;QAC1B,qBAAqB;QACrB,qBAAqB;QACrB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,mBAAmB;QACnB,wBAAwB;QACxB,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QACjB,mBAAmB;QACnB,qBAAqB;QACrB,0BAA0B;QAC1B,6BAA6B;QAC7B,yBAAyB;QACzB,0BAA0B;QAE1B,mBAAmB;QACnB,4BAA4B;QAC5B,+BAA+B;QAC/B,wBAAwB;QACxB,2BAA2B;QAC3B,yBAAyB;QACzB,qBAAqB;QACrB,oBAAoB;QACpB,kCAAkC;QAClC,0BAA0B;QAC1B,sBAAsB;QACtB,qBAAqB;QACrB,0BAA0B;QAC1B,uBAAuB;QACvB,kCAAkC;QAClC,uBAAuB;QACvB,yBAAyB;QACzB,iCAAiC;QACjC,iCAAiC;QACjC,6BAA6B;QAC7B,+BAA+B;QAC/B,8BAA8B;QAC9B,sBAAsB;QACtB,mBAAmB;QACnB,mCAAmC;QACnC,oCAAoC;QAEpC,uBAAuB;QACvB,mCAAmC;QACnC,uBAAuB;QACvB,2BAA2B;QAC3B,0BAA0B;QAC1B,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,0BAA0B;QAC1B,6BAA6B;QAC7B,qCAAqC;QACrC,4BAA4B;QAC5B,gCAAgC;QAChC,iCAAiC;QACjC,+BAA+B;QAC/B,+BAA+B;QAE/B,iBAAiB;QACjB,wBAAwB;QACxB,qBAAqB;QACrB,wBAAwB;QACxB,qBAAqB;QACrB,6BAA6B;QAC7B,wBAAwB;QAExB,iBAAiB;QACjB,kBAAkB;QAClB,oBAAoB;QACpB,kBAAkB;QAClB,qBAAqB;QACrB,iBAAiB;QACjB,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,sBAAsB;QACtB,2BAA2B;QAC3B,kBAAkB;QAClB,kBAAkB;QAClB,oBAAoB;QACpB,qBAAqB;QACrB,2BAA2B;QAC3B,2BAA2B;QAC3B,uBAAuB;QACvB,uBAAuB;QACvB,sBAAsB;QACtB,iCAAiC;QACjC,uBAAuB;QAEvB,uBAAuB;QACvB,kBAAkB;QAClB,qBAAqB;QACrB,kBAAkB;QAClB,sBAAsB;QACtB,oBAAoB;QACpB,kBAAkB;QAClB,2BAA2B;QAC3B,sBAAsB;QACtB,mBAAmB;QACnB,qBAAqB;QACrB,oBAAoB;QACpB,qBAAqB;QACrB,yBAAyB;QACzB,qBAAqB;QACrB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QAEjB,gBAAgB;QAChB,+BAA+B;QAC/B,sCAAsC;QACtC,gCAAgC;QAChC,uCAAuC;QACvC,+BAA+B;QAC/B,sCAAsC;QACtC,iCAAiC;QACjC,wCAAwC;QACxC,gCAAgC;QAChC,uCAAuC;QAEvC,6BAA6B;QAC7B,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,kBAAkB;QAClB,eAAe;QACf,eAAe;QACf,iBAAiB;QACjB,eAAe;QACf,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,cAAc;QACd,aAAa;IACf;IACA,IAAI;QACF,YAAY;QACZ,cAAc;QACd,iBAAiB;QACjB,aAAa;QACb,oBAAoB;QACpB,gBAAgB;QAChB,cAAc;QACd,aAAa;QACb,cAAc;QACd,iBAAiB;QAEjB,WAAW;QACX,yBAAyB;QACzB,+BAA+B;QAC/B,0BAA0B;QAC1B,gCAAgC;QAChC,8BAA8B;QAC9B,oCAAoC;QACpC,sBAAsB;QAEtB,iBAAiB;QACjB,YAAY;QACZ,YAAY;QACZ,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,aAAa;QACb,iBAAiB;QAEjB,UAAU;QACV,iBAAiB;QACjB,sBAAsB;QACtB,kBAAkB;QAClB,yBAAyB;QAEzB,YAAY;QACZ,mBAAmB;QACnB,sBAAsB;QACtB,kBAAkB;QAElB,SAAS;QACT,gBAAgB;QAChB,mBAAmB;QACnB,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,qBAAqB;QACrB,oBAAoB;QACpB,iBAAiB;QACjB,mBAAmB;QAEnB,gBAAgB;QAChB,iBAAiB;QACjB,0BAA0B;QAC1B,uBAAuB;QACvB,yBAAyB;QAEzB,SAAS;QACT,uBAAuB;QACvB,oBAAoB;QACpB,sBAAsB;QACtB,4BAA4B;QAC5B,sBAAsB;QACtB,0BAA0B;QAC1B,6BAA6B;QAC7B,wBAAwB;QACxB,wBAAwB;QACxB,uBAAuB;QACvB,0BAA0B;QAE1B,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,iBAAiB;QACjB,iBAAiB;QACjB,oBAAoB;QACpB,uBAAuB;QACvB,sBAAsB;QACtB,gBAAgB;QAChB,oBAAoB;QACpB,uBAAuB;QACvB,qBAAqB;QACrB,2BAA2B;QAE3B,aAAa;QACb,qBAAqB;QACrB,YAAY;QACZ,qBAAqB;QACrB,mBAAmB;QACnB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,oBAAoB;QAEpB,eAAe;QACf,uBAAuB;QACvB,oBAAoB;QACpB,wBAAwB;QACxB,yBAAyB;QACzB,sBAAsB;QACtB,uBAAuB;QACvB,yBAAyB;QACzB,oBAAoB;QAEpB,qBAAqB;QACrB,0BAA0B;QAC1B,gCAAgC;QAChC,yBAAyB;QACzB,uBAAuB;QACvB,+BAA+B;QAE/B,eAAe;QACf,0BAA0B;QAC1B,qBAAqB;QACrB,qBAAqB;QACrB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,mBAAmB;QACnB,wBAAwB;QACxB,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QACjB,mBAAmB;QACnB,qBAAqB;QACrB,0BAA0B;QAC1B,6BAA6B;QAC7B,yBAAyB;QACzB,0BAA0B;QAE1B,mBAAmB;QACnB,4BAA4B;QAC5B,+BAA+B;QAC/B,wBAAwB;QACxB,2BAA2B;QAC3B,yBAAyB;QACzB,qBAAqB;QACrB,oBAAoB;QACpB,kCAAkC;QAClC,0BAA0B;QAC1B,sBAAsB;QACtB,qBAAqB;QACrB,0BAA0B;QAC1B,uBAAuB;QACvB,kCAAkC;QAClC,uBAAuB;QACvB,yBAAyB;QACzB,iCAAiC;QACjC,iCAAiC;QACjC,6BAA6B;QAC7B,+BAA+B;QAC/B,8BAA8B;QAC9B,sBAAsB;QACtB,mBAAmB;QACnB,mCAAmC;QACnC,oCAAoC;QAEpC,uBAAuB;QACvB,mCAAmC;QACnC,uBAAuB;QACvB,2BAA2B;QAC3B,0BAA0B;QAC1B,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,0BAA0B;QAC1B,6BAA6B;QAC7B,qCAAqC;QACrC,4BAA4B;QAC5B,gCAAgC;QAChC,iCAAiC;QACjC,+BAA+B;QAC/B,+BAA+B;QAE/B,iBAAiB;QACjB,wBAAwB;QACxB,qBAAqB;QACrB,wBAAwB;QACxB,qBAAqB;QACrB,6BAA6B;QAC7B,wBAAwB;QAExB,iBAAiB;QACjB,kBAAkB;QAClB,oBAAoB;QACpB,kBAAkB;QAClB,qBAAqB;QACrB,iBAAiB;QACjB,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,sBAAsB;QACtB,2BAA2B;QAC3B,kBAAkB;QAClB,kBAAkB;QAClB,oBAAoB;QACpB,qBAAqB;QACrB,2BAA2B;QAC3B,2BAA2B;QAC3B,uBAAuB;QACvB,uBAAuB;QACvB,sBAAsB;QACtB,iCAAiC;QACjC,uBAAuB;QAEvB,uBAAuB;QACvB,kBAAkB;QAClB,qBAAqB;QACrB,kBAAkB;QAClB,sBAAsB;QACtB,oBAAoB;QACpB,kBAAkB;QAClB,2BAA2B;QAC3B,sBAAsB;QACtB,mBAAmB;QACnB,qBAAqB;QACrB,oBAAoB;QACpB,qBAAqB;QACrB,yBAAyB;QACzB,qBAAqB;QACrB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QAEjB,gBAAgB;QAChB,+BAA+B;QAC/B,sCAAsC;QACtC,gCAAgC;QAChC,uCAAuC;QACvC,+BAA+B;QAC/B,sCAAsC;QACtC,iCAAiC;QACjC,wCAAwC;QACxC,gCAAgC;QAChC,uCAAuC;QAEvC,6BAA6B;QAC7B,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,kBAAkB;QAClB,eAAe;QACf,eAAe;QACf,iBAAiB;QACjB,eAAe;QACf,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,cAAc;QACd,aAAa;IACf;AACF;AAEO,SAAS,iBAAiB,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC/B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,iDAAiD;YACjD,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,IAAI,iBAAiB,CAAC,kBAAkB,QAAQ,kBAAkB,IAAI,GAAG;gBACvE,YAAY;YACd;QACF;qCAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,YAAY;QACZ,aAAa,OAAO,CAAC,YAAY;IACnC;IAEA,MAAM,IAAI,CAAC,KAAa;QACtB,IAAI,cAAc,YAAY,CAAC,SAAS,CAAC,IAAkD,IAAI;QAE/F,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC;oBAAC,CAAC,QAAO,MAAM;gBAC5C,cAAc,YAAY,OAAO,CAAC,AAAC,KAAU,OAAN,QAAM,OAAK;YACpD;QACF;QAEA,OAAO;IACT;IAEA,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;YAAU,aAAa;YAAmB;QAAE;kBAC5E;;;;;;AAGP;GAjCgB;KAAA;AAmCT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/lib/socket.ts"], "sourcesContent": ["// Socket.IO client setup for multiplayer functionality\n\nimport { io, Socket } from 'socket.io-client'\n\nexport interface Player {\n  id: string\n  name: string\n  avatar: string\n  level: number\n  isHost: boolean\n  isReady: boolean\n  bakeryId?: string\n}\n\nexport interface GameRoom {\n  id: string\n  name: string\n  mode: 'cooperative' | 'competitive' | 'sandbox'\n  maxPlayers: number\n  currentPlayers: number\n  players: Player[]\n  gameState: 'waiting' | 'starting' | 'playing' | 'paused' | 'finished'\n  settings: {\n    gameMode: 'cooperative' | 'competitive'\n    timeLimit?: number\n    targetScore?: number\n    difficulty: 'easy' | 'medium' | 'hard'\n    allowSpectators: boolean\n  }\n  createdAt: number\n  startedAt?: number\n}\n\nexport interface MultiplayerGameState {\n  roomId: string\n  players: Record<string, {\n    playerId: string\n    name: string\n    level: number\n    money: number\n    experience: number\n    skillPoints: number\n    bakeryId: string\n    isOnline: boolean\n    lastActivity: number\n  }>\n  sharedResources: {\n    inventory: Record<string, number>\n    orders: any[]\n    equipment: any[]\n    automationJobs: any[]\n  }\n  gameStats: {\n    totalOrders: number\n    totalRevenue: number\n    totalExperience: number\n    gameStartTime: number\n    gameEndTime?: number\n  }\n  events: {\n    id: string\n    type: 'order_completed' | 'equipment_purchased' | 'player_joined' | 'player_left' | 'game_started' | 'game_ended'\n    playerId?: string\n    data: any\n    timestamp: number\n  }[]\n}\n\nexport interface SocketEvents {\n  // Room management\n  'create_room': (roomData: Partial<GameRoom>) => void\n  'join_room': (roomId: string, playerData: Partial<Player>) => void\n  'leave_room': (roomId: string) => void\n  'room_created': (room: GameRoom) => void\n  'room_joined': (room: GameRoom, player: Player) => void\n  'room_left': (roomId: string) => void\n  'room_updated': (room: GameRoom) => void\n  'player_joined': (player: Player) => void\n  'player_left': (playerId: string) => void\n  'player_updated': (player: Player) => void\n  \n  // Game state synchronization\n  'game_state_update': (gameState: Partial<MultiplayerGameState>) => void\n  'game_state_sync': (gameState: MultiplayerGameState) => void\n  'player_action': (action: {\n    type: string\n    playerId: string\n    data: any\n    timestamp: number\n  }) => void\n  \n  // Game events\n  'game_start': (roomId: string) => void\n  'game_pause': (roomId: string) => void\n  'game_resume': (roomId: string) => void\n  'game_end': (roomId: string, results: any) => void\n  'game_started': (gameState: MultiplayerGameState) => void\n  'game_paused': () => void\n  'game_resumed': () => void\n  'game_ended': (results: any) => void\n  \n  // Chat and communication\n  'send_message': (message: {\n    playerId: string\n    playerName: string\n    content: string\n    timestamp: number\n  }) => void\n  'message_received': (message: {\n    playerId: string\n    playerName: string\n    content: string\n    timestamp: number\n  }) => void\n  \n  // Error handling\n  'error': (error: {\n    code: string\n    message: string\n    details?: any\n  }) => void\n  'disconnect': () => void\n  'reconnect': () => void\n}\n\nclass SocketManager {\n  private socket: Socket | null = null\n  private isConnected = false\n  private reconnectAttempts = 0\n  private maxReconnectAttempts = 5\n  private currentRoom: GameRoom | null = null\n  private currentPlayer: Player | null = null\n\n  constructor() {\n    if (typeof window !== 'undefined') {\n      this.connect()\n    }\n  }\n\n  connect() {\n    if (this.socket?.connected) return\n\n    this.socket = io(process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3001', {\n      transports: ['websocket', 'polling'],\n      timeout: 20000,\n      forceNew: true\n    })\n\n    this.setupEventListeners()\n  }\n\n  private setupEventListeners() {\n    if (!this.socket) return\n\n    this.socket.on('connect', () => {\n      console.log('Connected to multiplayer server')\n      this.isConnected = true\n      this.reconnectAttempts = 0\n    })\n\n    this.socket.on('disconnect', (reason) => {\n      console.log('Disconnected from multiplayer server:', reason)\n      this.isConnected = false\n      \n      if (reason === 'io server disconnect') {\n        // Server disconnected, try to reconnect\n        this.handleReconnect()\n      }\n    })\n\n    this.socket.on('connect_error', (error) => {\n      console.error('Connection error:', error)\n      this.handleReconnect()\n    })\n\n    this.socket.on('error', (error) => {\n      console.error('Socket error:', error)\n    })\n  }\n\n  private handleReconnect() {\n    if (this.reconnectAttempts < this.maxReconnectAttempts) {\n      this.reconnectAttempts++\n      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`)\n      \n      setTimeout(() => {\n        this.connect()\n      }, Math.pow(2, this.reconnectAttempts) * 1000) // Exponential backoff\n    } else {\n      console.error('Max reconnection attempts reached')\n    }\n  }\n\n  // Room management methods\n  createRoom(roomData: Partial<GameRoom>): Promise<GameRoom> {\n    return new Promise((resolve, reject) => {\n      if (!this.socket?.connected) {\n        reject(new Error('Not connected to server'))\n        return\n      }\n\n      this.socket.emit('create_room', roomData)\n      \n      this.socket.once('room_created', (room: GameRoom) => {\n        this.currentRoom = room\n        resolve(room)\n      })\n\n      this.socket.once('error', (error) => {\n        reject(new Error(error.message))\n      })\n    })\n  }\n\n  joinRoom(roomId: string, playerData: Partial<Player>): Promise<{ room: GameRoom, player: Player }> {\n    return new Promise((resolve, reject) => {\n      if (!this.socket?.connected) {\n        reject(new Error('Not connected to server'))\n        return\n      }\n\n      this.socket.emit('join_room', roomId, playerData)\n      \n      this.socket.once('room_joined', (room: GameRoom, player: Player) => {\n        this.currentRoom = room\n        this.currentPlayer = player\n        resolve({ room, player })\n      })\n\n      this.socket.once('error', (error) => {\n        reject(new Error(error.message))\n      })\n    })\n  }\n\n  leaveRoom() {\n    if (this.socket?.connected && this.currentRoom) {\n      this.socket.emit('leave_room', this.currentRoom.id)\n      this.currentRoom = null\n      this.currentPlayer = null\n    }\n  }\n\n  // Game state methods\n  sendPlayerAction(action: {\n    type: string\n    data: any\n  }) {\n    if (this.socket?.connected && this.currentPlayer) {\n      this.socket.emit('player_action', {\n        ...action,\n        playerId: this.currentPlayer.id,\n        timestamp: Date.now()\n      })\n    }\n  }\n\n  sendMessage(content: string) {\n    if (this.socket?.connected && this.currentPlayer) {\n      this.socket.emit('send_message', {\n        playerId: this.currentPlayer.id,\n        playerName: this.currentPlayer.name,\n        content,\n        timestamp: Date.now()\n      })\n    }\n  }\n\n  // Event subscription methods\n  on<K extends keyof SocketEvents>(event: K, callback: SocketEvents[K]) {\n    this.socket?.on(event, callback as any)\n  }\n\n  off<K extends keyof SocketEvents>(event: K, callback?: SocketEvents[K]) {\n    this.socket?.off(event, callback as any)\n  }\n\n  once<K extends keyof SocketEvents>(event: K, callback: SocketEvents[K]) {\n    this.socket?.once(event, callback as any)\n  }\n\n  // Utility methods\n  isSocketConnected(): boolean {\n    return this.isConnected && this.socket?.connected === true\n  }\n\n  getCurrentRoom(): GameRoom | null {\n    return this.currentRoom\n  }\n\n  getCurrentPlayer(): Player | null {\n    return this.currentPlayer\n  }\n\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect()\n      this.socket = null\n      this.isConnected = false\n      this.currentRoom = null\n      this.currentPlayer = null\n    }\n  }\n}\n\n// Singleton instance\nexport const socketManager = new SocketManager()\n\n// React hook for using socket in components\nexport function useSocket() {\n  return socketManager\n}\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;;AA8IlC;;AA5IrB;AAAA;;;AA2HA,MAAM;IAcJ,UAAU;YACJ;QAAJ,KAAI,eAAA,IAAI,CAAC,MAAM,cAAX,mCAAA,aAAa,SAAS,EAAE;QAE5B,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,kLAAA,CAAA,KAAE,AAAD,EAAE,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,yBAAyB;YAC9E,YAAY;gBAAC;gBAAa;aAAU;YACpC,SAAS;YACT,UAAU;QACZ;QAEA,IAAI,CAAC,mBAAmB;IAC1B;IAEQ,sBAAsB;QAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAElB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW;YACxB,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,iBAAiB,GAAG;QAC3B;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC;YAC5B,QAAQ,GAAG,CAAC,yCAAyC;YACrD,IAAI,CAAC,WAAW,GAAG;YAEnB,IAAI,WAAW,wBAAwB;gBACrC,wCAAwC;gBACxC,IAAI,CAAC,eAAe;YACtB;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAC;YAC/B,QAAQ,KAAK,CAAC,qBAAqB;YACnC,IAAI,CAAC,eAAe;QACtB;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC;YACvB,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEQ,kBAAkB;QACxB,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,EAAE;YACtD,IAAI,CAAC,iBAAiB;YACtB,QAAQ,GAAG,CAAC,AAAC,4BAAqD,OAA1B,IAAI,CAAC,iBAAiB,EAAC,KAA6B,OAA1B,IAAI,CAAC,oBAAoB,EAAC;YAE5F,WAAW;gBACT,IAAI,CAAC,OAAO;YACd,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,IAAI,OAAM,sBAAsB;QACvE,OAAO;YACL,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,0BAA0B;IAC1B,WAAW,QAA2B,EAAqB;QACzD,OAAO,IAAI,QAAQ,CAAC,SAAS;gBACtB;YAAL,IAAI,GAAC,eAAA,IAAI,CAAC,MAAM,cAAX,mCAAA,aAAa,SAAS,GAAE;gBAC3B,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe;YAEhC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC;gBAChC,IAAI,CAAC,WAAW,GAAG;gBACnB,QAAQ;YACV;YAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;gBACzB,OAAO,IAAI,MAAM,MAAM,OAAO;YAChC;QACF;IACF;IAEA,SAAS,MAAc,EAAE,UAA2B,EAA+C;QACjG,OAAO,IAAI,QAAQ,CAAC,SAAS;gBACtB;YAAL,IAAI,GAAC,eAAA,IAAI,CAAC,MAAM,cAAX,mCAAA,aAAa,SAAS,GAAE;gBAC3B,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,QAAQ;YAEtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,MAAgB;gBAC/C,IAAI,CAAC,WAAW,GAAG;gBACnB,IAAI,CAAC,aAAa,GAAG;gBACrB,QAAQ;oBAAE;oBAAM;gBAAO;YACzB;YAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;gBACzB,OAAO,IAAI,MAAM,MAAM,OAAO;YAChC;QACF;IACF;IAEA,YAAY;YACN;QAAJ,IAAI,EAAA,eAAA,IAAI,CAAC,MAAM,cAAX,mCAAA,aAAa,SAAS,KAAI,IAAI,CAAC,WAAW,EAAE;YAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,WAAW,CAAC,EAAE;YAClD,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,aAAa,GAAG;QACvB;IACF;IAEA,qBAAqB;IACrB,iBAAiB,MAGhB,EAAE;YACG;QAAJ,IAAI,EAAA,eAAA,IAAI,CAAC,MAAM,cAAX,mCAAA,aAAa,SAAS,KAAI,IAAI,CAAC,aAAa,EAAE;YAChD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB;gBAChC,GAAG,MAAM;gBACT,UAAU,IAAI,CAAC,aAAa,CAAC,EAAE;gBAC/B,WAAW,KAAK,GAAG;YACrB;QACF;IACF;IAEA,YAAY,OAAe,EAAE;YACvB;QAAJ,IAAI,EAAA,eAAA,IAAI,CAAC,MAAM,cAAX,mCAAA,aAAa,SAAS,KAAI,IAAI,CAAC,aAAa,EAAE;YAChD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB;gBAC/B,UAAU,IAAI,CAAC,aAAa,CAAC,EAAE;gBAC/B,YAAY,IAAI,CAAC,aAAa,CAAC,IAAI;gBACnC;gBACA,WAAW,KAAK,GAAG;YACrB;QACF;IACF;IAEA,6BAA6B;IAC7B,GAAiC,KAAQ,EAAE,QAAyB,EAAE;YACpE;SAAA,eAAA,IAAI,CAAC,MAAM,cAAX,mCAAA,aAAa,EAAE,CAAC,OAAO;IACzB;IAEA,IAAkC,KAAQ,EAAE,QAA0B,EAAE;YACtE;SAAA,eAAA,IAAI,CAAC,MAAM,cAAX,mCAAA,aAAa,GAAG,CAAC,OAAO;IAC1B;IAEA,KAAmC,KAAQ,EAAE,QAAyB,EAAE;YACtE;SAAA,eAAA,IAAI,CAAC,MAAM,cAAX,mCAAA,aAAa,IAAI,CAAC,OAAO;IAC3B;IAEA,kBAAkB;IAClB,oBAA6B;YACA;QAA3B,OAAO,IAAI,CAAC,WAAW,IAAI,EAAA,eAAA,IAAI,CAAC,MAAM,cAAX,mCAAA,aAAa,SAAS,MAAK;IACxD;IAEA,iBAAkC;QAChC,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA,mBAAkC;QAChC,OAAO,IAAI,CAAC,aAAa;IAC3B;IAEA,aAAa;QACX,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,aAAa,GAAG;QACvB;IACF;IAzKA,aAAc;QAPd,+KAAQ,UAAwB;QAChC,+KAAQ,eAAc;QACtB,+KAAQ,qBAAoB;QAC5B,+KAAQ,wBAAuB;QAC/B,+KAAQ,eAA+B;QACvC,+KAAQ,iBAA+B;QAGrC,wCAAmC;YACjC,IAAI,CAAC,OAAO;QACd;IACF;AAsKF;AAGO,MAAM,gBAAgB,IAAI;AAG1B,SAAS;IACd,OAAO;AACT", "debugId": null}}, {"offset": {"line": 745, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/contexts/MultiplayerContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect, useCallback } from 'react'\nimport { socket<PERSON><PERSON><PERSON>, GameRoom, Player, MultiplayerGameState } from '@/lib/socket'\n\ninterface ChatMessage {\n  id: string\n  playerId: string\n  playerName: string\n  content: string\n  timestamp: number\n}\n\ninterface MultiplayerContextType {\n  // Connection state\n  isConnected: boolean\n  isInRoom: boolean\n  connectionError: string | null\n  \n  // Room and player data\n  currentRoom: GameRoom | null\n  currentPlayer: Player | null\n  players: Player[]\n  \n  // Game state\n  gameState: 'waiting' | 'starting' | 'playing' | 'paused' | 'finished'\n  sharedGameState: MultiplayerGameState | null\n  \n  // Chat\n  messages: ChatMessage[]\n  \n  // Actions\n  createRoom: (roomData: Partial<GameRoom>, playerData: Partial<Player>) => Promise<void>\n  joinRoom: (roomId: string, playerData: Partial<Player>) => Promise<void>\n  leaveRoom: () => void\n  startGame: () => void\n  sendMessage: (content: string) => void\n  sendPlayerAction: (action: { type: string; data: any }) => void\n  \n  // Room management\n  setPlayerReady: (ready: boolean) => void\n  kickPlayer: (playerId: string) => void\n  updateRoomSettings: (settings: any) => void\n}\n\nconst MultiplayerContext = createContext<MultiplayerContextType | undefined>(undefined)\n\nexport function MultiplayerProvider({ children }: { children: React.ReactNode }) {\n  // Connection state\n  const [isConnected, setIsConnected] = useState(false)\n  const [isInRoom, setIsInRoom] = useState(false)\n  const [connectionError, setConnectionError] = useState<string | null>(null)\n  \n  // Room and player data\n  const [currentRoom, setCurrentRoom] = useState<GameRoom | null>(null)\n  const [currentPlayer, setCurrentPlayer] = useState<Player | null>(null)\n  const [players, setPlayers] = useState<Player[]>([])\n  \n  // Game state\n  const [gameState, setGameState] = useState<'waiting' | 'starting' | 'playing' | 'paused' | 'finished'>('waiting')\n  const [sharedGameState, setSharedGameState] = useState<MultiplayerGameState | null>(null)\n  \n  // Chat\n  const [messages, setMessages] = useState<ChatMessage[]>([])\n\n  // Initialize socket connection and event listeners\n  useEffect(() => {\n    const socket = socketManager\n\n    // Connection events\n    const handleConnect = () => {\n      setIsConnected(true)\n      setConnectionError(null)\n    }\n\n    const handleDisconnect = () => {\n      setIsConnected(false)\n      setIsInRoom(false)\n      setCurrentRoom(null)\n      setCurrentPlayer(null)\n      setPlayers([])\n    }\n\n    const handleError = (error: any) => {\n      setConnectionError(error.message || 'Connection error')\n      console.error('Multiplayer error:', error)\n    }\n\n    // Room events\n    const handleRoomCreated = (room: GameRoom) => {\n      setCurrentRoom(room)\n      setPlayers(room.players)\n      setIsInRoom(true)\n      setGameState(room.gameState)\n      \n      // Set current player as host\n      const hostPlayer = room.players.find(p => p.isHost)\n      if (hostPlayer) {\n        setCurrentPlayer(hostPlayer)\n      }\n    }\n\n    const handleRoomJoined = (room: GameRoom, player: Player) => {\n      setCurrentRoom(room)\n      setCurrentPlayer(player)\n      setPlayers(room.players)\n      setIsInRoom(true)\n      setGameState(room.gameState)\n    }\n\n    const handleRoomLeft = () => {\n      setCurrentRoom(null)\n      setCurrentPlayer(null)\n      setPlayers([])\n      setIsInRoom(false)\n      setGameState('waiting')\n      setSharedGameState(null)\n      setMessages([])\n    }\n\n    const handleRoomUpdated = (room: GameRoom) => {\n      setCurrentRoom(room)\n      setPlayers(room.players)\n      setGameState(room.gameState)\n    }\n\n    const handlePlayerJoined = (player: Player) => {\n      setPlayers(prev => [...prev, player])\n      addSystemMessage(`${player.name} joined the room`)\n    }\n\n    const handlePlayerLeft = (playerId: string) => {\n      setPlayers(prev => {\n        const leftPlayer = prev.find(p => p.id === playerId)\n        if (leftPlayer) {\n          addSystemMessage(`${leftPlayer.name} left the room`)\n        }\n        return prev.filter(p => p.id !== playerId)\n      })\n    }\n\n    // Game events\n    const handleGameStarted = (gameState: MultiplayerGameState) => {\n      setGameState('playing')\n      setSharedGameState(gameState)\n      addSystemMessage('Game started!')\n    }\n\n    const handleGameStateUpdate = (update: Partial<MultiplayerGameState>) => {\n      setSharedGameState(prev => prev ? { ...prev, ...update } : null)\n    }\n\n    const handlePlayerAction = (action: any) => {\n      // Handle player actions from other players\n      console.log('Player action received:', action)\n    }\n\n    // Chat events\n    const handleMessageReceived = (message: any) => {\n      const chatMessage: ChatMessage = {\n        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n        playerId: message.playerId,\n        playerName: message.playerName,\n        content: message.content,\n        timestamp: message.timestamp\n      }\n      setMessages(prev => [...prev, chatMessage])\n    }\n\n    // Register event listeners\n    socket.on('connect', handleConnect)\n    socket.on('disconnect', handleDisconnect)\n    socket.on('error', handleError)\n    socket.on('room_created', handleRoomCreated)\n    socket.on('room_joined', handleRoomJoined)\n    socket.on('room_left', handleRoomLeft)\n    socket.on('room_updated', handleRoomUpdated)\n    socket.on('player_joined', handlePlayerJoined)\n    socket.on('player_left', handlePlayerLeft)\n    socket.on('game_started', handleGameStarted)\n    socket.on('game_state_update', handleGameStateUpdate)\n    socket.on('player_action', handlePlayerAction)\n    socket.on('message_received', handleMessageReceived)\n\n    // Check initial connection state\n    setIsConnected(socket.isSocketConnected())\n\n    // Cleanup\n    return () => {\n      socket.off('connect', handleConnect)\n      socket.off('disconnect', handleDisconnect)\n      socket.off('error', handleError)\n      socket.off('room_created', handleRoomCreated)\n      socket.off('room_joined', handleRoomJoined)\n      socket.off('room_left', handleRoomLeft)\n      socket.off('room_updated', handleRoomUpdated)\n      socket.off('player_joined', handlePlayerJoined)\n      socket.off('player_left', handlePlayerLeft)\n      socket.off('game_started', handleGameStarted)\n      socket.off('game_state_update', handleGameStateUpdate)\n      socket.off('player_action', handlePlayerAction)\n      socket.off('message_received', handleMessageReceived)\n    }\n  }, [])\n\n  // Helper function to add system messages\n  const addSystemMessage = (content: string) => {\n    const systemMessage: ChatMessage = {\n      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n      playerId: 'system',\n      playerName: 'System',\n      content,\n      timestamp: Date.now()\n    }\n    setMessages(prev => [...prev, systemMessage])\n  }\n\n  // Action functions\n  const createRoom = useCallback(async (roomData: Partial<GameRoom>, playerData: Partial<Player>) => {\n    try {\n      setConnectionError(null)\n      const room = await socketManager.createRoom({\n        ...roomData,\n        hostName: playerData.name,\n        hostAvatar: playerData.avatar,\n        hostLevel: playerData.level\n      })\n      // Room created event will be handled by the event listener\n    } catch (error: any) {\n      setConnectionError(error.message)\n      throw error\n    }\n  }, [])\n\n  const joinRoom = useCallback(async (roomId: string, playerData: Partial<Player>) => {\n    try {\n      setConnectionError(null)\n      const { room, player } = await socketManager.joinRoom(roomId, playerData)\n      // Room joined event will be handled by the event listener\n    } catch (error: any) {\n      setConnectionError(error.message)\n      throw error\n    }\n  }, [])\n\n  const leaveRoom = useCallback(() => {\n    socketManager.leaveRoom()\n  }, [])\n\n  const startGame = useCallback(() => {\n    if (currentRoom && currentPlayer?.isHost) {\n      socketManager.sendPlayerAction({\n        type: 'start_game',\n        data: { roomId: currentRoom.id }\n      })\n    }\n  }, [currentRoom, currentPlayer])\n\n  const sendMessage = useCallback((content: string) => {\n    socketManager.sendMessage(content)\n  }, [])\n\n  const sendPlayerAction = useCallback((action: { type: string; data: any }) => {\n    socketManager.sendPlayerAction(action)\n  }, [])\n\n  const setPlayerReady = useCallback((ready: boolean) => {\n    if (currentPlayer) {\n      sendPlayerAction({\n        type: 'player_ready',\n        data: { ready }\n      })\n    }\n  }, [currentPlayer, sendPlayerAction])\n\n  const kickPlayer = useCallback((playerId: string) => {\n    if (currentPlayer?.isHost) {\n      sendPlayerAction({\n        type: 'kick_player',\n        data: { playerId }\n      })\n    }\n  }, [currentPlayer, sendPlayerAction])\n\n  const updateRoomSettings = useCallback((settings: any) => {\n    if (currentPlayer?.isHost) {\n      sendPlayerAction({\n        type: 'update_room_settings',\n        data: { settings }\n      })\n    }\n  }, [currentPlayer, sendPlayerAction])\n\n  const value: MultiplayerContextType = {\n    // Connection state\n    isConnected,\n    isInRoom,\n    connectionError,\n    \n    // Room and player data\n    currentRoom,\n    currentPlayer,\n    players,\n    \n    // Game state\n    gameState,\n    sharedGameState,\n    \n    // Chat\n    messages,\n    \n    // Actions\n    createRoom,\n    joinRoom,\n    leaveRoom,\n    startGame,\n    sendMessage,\n    sendPlayerAction,\n    \n    // Room management\n    setPlayerReady,\n    kickPlayer,\n    updateRoomSettings\n  }\n\n  return (\n    <MultiplayerContext.Provider value={value}>\n      {children}\n    </MultiplayerContext.Provider>\n  )\n}\n\nexport function useMultiplayer() {\n  const context = useContext(MultiplayerContext)\n  if (context === undefined) {\n    throw new Error('useMultiplayer must be used within a MultiplayerProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AA6CA,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAsC;AAEtE,SAAS,oBAAoB,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAClC,mBAAmB;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtE,uBAAuB;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAChE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEnD,aAAa;IACb,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8D;IACvG,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+B;IAEpF,OAAO;IACP,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAE1D,mDAAmD;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM,SAAS,uHAAA,CAAA,gBAAa;YAE5B,oBAAoB;YACpB,MAAM;+DAAgB;oBACpB,eAAe;oBACf,mBAAmB;gBACrB;;YAEA,MAAM;kEAAmB;oBACvB,eAAe;oBACf,YAAY;oBACZ,eAAe;oBACf,iBAAiB;oBACjB,WAAW,EAAE;gBACf;;YAEA,MAAM;6DAAc,CAAC;oBACnB,mBAAmB,MAAM,OAAO,IAAI;oBACpC,QAAQ,KAAK,CAAC,sBAAsB;gBACtC;;YAEA,cAAc;YACd,MAAM;mEAAoB,CAAC;oBACzB,eAAe;oBACf,WAAW,KAAK,OAAO;oBACvB,YAAY;oBACZ,aAAa,KAAK,SAAS;oBAE3B,6BAA6B;oBAC7B,MAAM,aAAa,KAAK,OAAO,CAAC,IAAI;sFAAC,CAAA,IAAK,EAAE,MAAM;;oBAClD,IAAI,YAAY;wBACd,iBAAiB;oBACnB;gBACF;;YAEA,MAAM;kEAAmB,CAAC,MAAgB;oBACxC,eAAe;oBACf,iBAAiB;oBACjB,WAAW,KAAK,OAAO;oBACvB,YAAY;oBACZ,aAAa,KAAK,SAAS;gBAC7B;;YAEA,MAAM;gEAAiB;oBACrB,eAAe;oBACf,iBAAiB;oBACjB,WAAW,EAAE;oBACb,YAAY;oBACZ,aAAa;oBACb,mBAAmB;oBACnB,YAAY,EAAE;gBAChB;;YAEA,MAAM;mEAAoB,CAAC;oBACzB,eAAe;oBACf,WAAW,KAAK,OAAO;oBACvB,aAAa,KAAK,SAAS;gBAC7B;;YAEA,MAAM;oEAAqB,CAAC;oBAC1B;4EAAW,CAAA,OAAQ;mCAAI;gCAAM;6BAAO;;oBACpC,iBAAiB,AAAC,GAAc,OAAZ,OAAO,IAAI,EAAC;gBAClC;;YAEA,MAAM;kEAAmB,CAAC;oBACxB;0EAAW,CAAA;4BACT,MAAM,aAAa,KAAK,IAAI;6FAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;4BAC3C,IAAI,YAAY;gCACd,iBAAiB,AAAC,GAAkB,OAAhB,WAAW,IAAI,EAAC;4BACtC;4BACA,OAAO,KAAK,MAAM;kFAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;wBACnC;;gBACF;;YAEA,cAAc;YACd,MAAM;mEAAoB,CAAC;oBACzB,aAAa;oBACb,mBAAmB;oBACnB,iBAAiB;gBACnB;;YAEA,MAAM;uEAAwB,CAAC;oBAC7B;+EAAmB,CAAA,OAAQ,OAAO;gCAAE,GAAG,IAAI;gCAAE,GAAG,MAAM;4BAAC,IAAI;;gBAC7D;;YAEA,MAAM;oEAAqB,CAAC;oBAC1B,2CAA2C;oBAC3C,QAAQ,GAAG,CAAC,2BAA2B;gBACzC;;YAEA,cAAc;YACd,MAAM;uEAAwB,CAAC;oBAC7B,MAAM,cAA2B;wBAC/B,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;wBACjE,UAAU,QAAQ,QAAQ;wBAC1B,YAAY,QAAQ,UAAU;wBAC9B,SAAS,QAAQ,OAAO;wBACxB,WAAW,QAAQ,SAAS;oBAC9B;oBACA;+EAAY,CAAA,OAAQ;mCAAI;gCAAM;6BAAY;;gBAC5C;;YAEA,2BAA2B;YAC3B,OAAO,EAAE,CAAC,WAAW;YACrB,OAAO,EAAE,CAAC,cAAc;YACxB,OAAO,EAAE,CAAC,SAAS;YACnB,OAAO,EAAE,CAAC,gBAAgB;YAC1B,OAAO,EAAE,CAAC,eAAe;YACzB,OAAO,EAAE,CAAC,aAAa;YACvB,OAAO,EAAE,CAAC,gBAAgB;YAC1B,OAAO,EAAE,CAAC,iBAAiB;YAC3B,OAAO,EAAE,CAAC,eAAe;YACzB,OAAO,EAAE,CAAC,gBAAgB;YAC1B,OAAO,EAAE,CAAC,qBAAqB;YAC/B,OAAO,EAAE,CAAC,iBAAiB;YAC3B,OAAO,EAAE,CAAC,oBAAoB;YAE9B,iCAAiC;YACjC,eAAe,OAAO,iBAAiB;YAEvC,UAAU;YACV;iDAAO;oBACL,OAAO,GAAG,CAAC,WAAW;oBACtB,OAAO,GAAG,CAAC,cAAc;oBACzB,OAAO,GAAG,CAAC,SAAS;oBACpB,OAAO,GAAG,CAAC,gBAAgB;oBAC3B,OAAO,GAAG,CAAC,eAAe;oBAC1B,OAAO,GAAG,CAAC,aAAa;oBACxB,OAAO,GAAG,CAAC,gBAAgB;oBAC3B,OAAO,GAAG,CAAC,iBAAiB;oBAC5B,OAAO,GAAG,CAAC,eAAe;oBAC1B,OAAO,GAAG,CAAC,gBAAgB;oBAC3B,OAAO,GAAG,CAAC,qBAAqB;oBAChC,OAAO,GAAG,CAAC,iBAAiB;oBAC5B,OAAO,GAAG,CAAC,oBAAoB;gBACjC;;QACF;wCAAG,EAAE;IAEL,yCAAyC;IACzC,MAAM,mBAAmB,CAAC;QACxB,MAAM,gBAA6B;YACjC,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YACjE,UAAU;YACV,YAAY;YACZ;YACA,WAAW,KAAK,GAAG;QACrB;QACA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAc;IAC9C;IAEA,mBAAmB;IACnB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,OAAO,UAA6B;YACjE,IAAI;gBACF,mBAAmB;gBACnB,MAAM,OAAO,MAAM,uHAAA,CAAA,gBAAa,CAAC,UAAU,CAAC;oBAC1C,GAAG,QAAQ;oBACX,UAAU,WAAW,IAAI;oBACzB,YAAY,WAAW,MAAM;oBAC7B,WAAW,WAAW,KAAK;gBAC7B;YACA,2DAA2D;YAC7D,EAAE,OAAO,OAAY;gBACnB,mBAAmB,MAAM,OAAO;gBAChC,MAAM;YACR;QACF;sDAAG,EAAE;IAEL,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,OAAO,QAAgB;YAClD,IAAI;gBACF,mBAAmB;gBACnB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,uHAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,QAAQ;YAC9D,0DAA0D;YAC5D,EAAE,OAAO,OAAY;gBACnB,mBAAmB,MAAM,OAAO;gBAChC,MAAM;YACR;QACF;oDAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAC5B,uHAAA,CAAA,gBAAa,CAAC,SAAS;QACzB;qDAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAC5B,IAAI,gBAAe,0BAAA,oCAAA,cAAe,MAAM,GAAE;gBACxC,uHAAA,CAAA,gBAAa,CAAC,gBAAgB,CAAC;oBAC7B,MAAM;oBACN,MAAM;wBAAE,QAAQ,YAAY,EAAE;oBAAC;gBACjC;YACF;QACF;qDAAG;QAAC;QAAa;KAAc;IAE/B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YAC/B,uHAAA,CAAA,gBAAa,CAAC,WAAW,CAAC;QAC5B;uDAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAAC;YACpC,uHAAA,CAAA,gBAAa,CAAC,gBAAgB,CAAC;QACjC;4DAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YAClC,IAAI,eAAe;gBACjB,iBAAiB;oBACf,MAAM;oBACN,MAAM;wBAAE;oBAAM;gBAChB;YACF;QACF;0DAAG;QAAC;QAAe;KAAiB;IAEpC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YAC9B,IAAI,0BAAA,oCAAA,cAAe,MAAM,EAAE;gBACzB,iBAAiB;oBACf,MAAM;oBACN,MAAM;wBAAE;oBAAS;gBACnB;YACF;QACF;sDAAG;QAAC;QAAe;KAAiB;IAEpC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE,CAAC;YACtC,IAAI,0BAAA,oCAAA,cAAe,MAAM,EAAE;gBACzB,iBAAiB;oBACf,MAAM;oBACN,MAAM;wBAAE;oBAAS;gBACnB;YACF;QACF;8DAAG;QAAC;QAAe;KAAiB;IAEpC,MAAM,QAAgC;QACpC,mBAAmB;QACnB;QACA;QACA;QAEA,uBAAuB;QACvB;QACA;QACA;QAEA,aAAa;QACb;QACA;QAEA,OAAO;QACP;QAEA,UAAU;QACV;QACA;QACA;QACA;QACA;QACA;QAEA,kBAAkB;QAClB;QACA;QACA;IACF;IAEA,qBACE,6LAAC,mBAAmB,QAAQ;QAAC,OAAO;kBACjC;;;;;;AAGP;GA3RgB;KAAA;AA6RT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}