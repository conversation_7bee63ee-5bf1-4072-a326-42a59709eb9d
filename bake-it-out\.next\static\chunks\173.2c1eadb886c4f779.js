(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[173],{12:(t,e,r)=>{"use strict";let n=r(9087),{setTimeout:i,clearTimeout:o}=r(6793),s=r(5901),a=r(4322),{RPCCommands:u,RPCEvents:f,RelationshipTypes:h}=r(7304),{pid:c,uuid:l}=r(2575);class p extends n{constructor(t={}){super(),this.options=t,this.accessToken=null,this.clientId=null,this.application=null,this.user=null;let e=a[t.transport];if(!e)throw TypeError("RPC_INVALID_TRANSPORT",t.transport);this.fetch=(t,e,{data:r,query:n}={})=>s(`${this.fetch.endpoint}${e}${n?new URLSearchParams(n):""}`,{method:t,body:r,headers:{Authorization:`Bearer ${this.accessToken}`}}).then(async t=>{let e=await t.json();if(!t.ok){let r=Error(t.status);throw r.body=e,r}return e}),this.fetch.endpoint="https://discord.com/api",this.transport=new e(this),this.transport.on("message",this._onRpcMessage.bind(this)),this._expecting=new Map,this._connectPromise=void 0}connect(t){return this._connectPromise||(this._connectPromise=new Promise((e,r)=>{this.clientId=t;let n=i(()=>r(Error("RPC_CONNECTION_TIMEOUT")),1e4);n.unref(),this.once("connected",()=>{o(n),e(this)}),this.transport.once("close",()=>{this._expecting.forEach(t=>{t.reject(Error("connection closed"))}),this.emit("disconnected"),r(Error("connection closed"))}),this.transport.connect().catch(r)})),this._connectPromise}async login(t={}){let{clientId:e,accessToken:r}=t;return(await this.connect(e),t.scopes)?(r||(r=await this.authorize(t)),this.authenticate(r)):(this.emit("ready"),this)}request(t,e,r){return new Promise((n,i)=>{let o=l();this.transport.send({cmd:t,args:e,evt:r,nonce:o}),this._expecting.set(o,{resolve:n,reject:i})})}_onRpcMessage(t){if(t.cmd===u.DISPATCH&&t.evt===f.READY)t.data.user&&(this.user=t.data.user),this.emit("connected");else if(this._expecting.has(t.nonce)){let{resolve:e,reject:r}=this._expecting.get(t.nonce);if("ERROR"===t.evt){let e=Error(t.data.message);e.code=t.data.code,e.data=t.data,r(e)}else e(t.data);this._expecting.delete(t.nonce)}else this.emit(t.evt,t.data)}async authorize({scopes:t,clientSecret:e,rpcToken:r,redirectUri:n,prompt:i}={}){e&&!0===r&&(r=(await this.fetch("POST","/oauth2/token/rpc",{data:new URLSearchParams({client_id:this.clientId,client_secret:e})})).rpc_token);let{code:o}=await this.request("AUTHORIZE",{scopes:t,client_id:this.clientId,prompt:i,rpc_token:r});return(await this.fetch("POST","/oauth2/token",{data:new URLSearchParams({client_id:this.clientId,client_secret:e,code:o,grant_type:"authorization_code",redirect_uri:n})})).access_token}authenticate(t){return this.request("AUTHENTICATE",{access_token:t}).then(({application:e,user:r})=>(this.accessToken=t,this.application=e,this.user=r,this.emit("ready"),this))}getGuild(t,e){return this.request(u.GET_GUILD,{guild_id:t,timeout:e})}getGuilds(t){return this.request(u.GET_GUILDS,{timeout:t})}getChannel(t,e){return this.request(u.GET_CHANNEL,{channel_id:t,timeout:e})}async getChannels(t,e){let{channels:r}=await this.request(u.GET_CHANNELS,{timeout:e,guild_id:t});return r}setCertifiedDevices(t){return this.request(u.SET_CERTIFIED_DEVICES,{devices:t.map(t=>({type:t.type,id:t.uuid,vendor:t.vendor,model:t.model,related:t.related,echo_cancellation:t.echoCancellation,noise_suppression:t.noiseSuppression,automatic_gain_control:t.automaticGainControl,hardware_mute:t.hardwareMute}))})}setUserVoiceSettings(t,e){return this.request(u.SET_USER_VOICE_SETTINGS,{user_id:t,pan:e.pan,mute:e.mute,volume:e.volume})}selectVoiceChannel(t,{timeout:e,force:r=!1}={}){return this.request(u.SELECT_VOICE_CHANNEL,{channel_id:t,timeout:e,force:r})}selectTextChannel(t,{timeout:e}={}){return this.request(u.SELECT_TEXT_CHANNEL,{channel_id:t,timeout:e})}getVoiceSettings(){return this.request(u.GET_VOICE_SETTINGS).then(t=>({automaticGainControl:t.automatic_gain_control,echoCancellation:t.echo_cancellation,noiseSuppression:t.noise_suppression,qos:t.qos,silenceWarning:t.silence_warning,deaf:t.deaf,mute:t.mute,input:{availableDevices:t.input.available_devices,device:t.input.device_id,volume:t.input.volume},output:{availableDevices:t.output.available_devices,device:t.output.device_id,volume:t.output.volume},mode:{type:t.mode.type,autoThreshold:t.mode.auto_threshold,threshold:t.mode.threshold,shortcut:t.mode.shortcut,delay:t.mode.delay}}))}setVoiceSettings(t){return this.request(u.SET_VOICE_SETTINGS,{automatic_gain_control:t.automaticGainControl,echo_cancellation:t.echoCancellation,noise_suppression:t.noiseSuppression,qos:t.qos,silence_warning:t.silenceWarning,deaf:t.deaf,mute:t.mute,input:t.input?{device_id:t.input.device,volume:t.input.volume}:void 0,output:t.output?{device_id:t.output.device,volume:t.output.volume}:void 0,mode:t.mode?{type:t.mode.type,auto_threshold:t.mode.autoThreshold,threshold:t.mode.threshold,shortcut:t.mode.shortcut,delay:t.mode.delay}:void 0})}captureShortcut(t){var e;let r=(e=f.CAPTURE_SHORTCUT_CHANGE,`${e}${JSON.stringify(void 0)}`),n=()=>(this._subscriptions.delete(r),this.request(u.CAPTURE_SHORTCUT,{action:"STOP"}));return this._subscriptions.set(r,({shortcut:e})=>{t(e,n)}),this.request(u.CAPTURE_SHORTCUT,{action:"START"}).then(()=>n)}setActivity(t={},e=c()){let r,n,i,o;if(t.startTimestamp||t.endTimestamp){if((r={start:t.startTimestamp,end:t.endTimestamp}).start instanceof Date&&(r.start=Math.round(r.start.getTime())),r.end instanceof Date&&(r.end=Math.round(r.end.getTime())),r.start>2147483647e3)throw RangeError("timestamps.start must fit into a unix timestamp");if(r.end>2147483647e3)throw RangeError("timestamps.end must fit into a unix timestamp")}return(t.largeImageKey||t.largeImageText||t.smallImageKey||t.smallImageText)&&(n={large_image:t.largeImageKey,large_text:t.largeImageText,small_image:t.smallImageKey,small_text:t.smallImageText}),(t.partySize||t.partyId||t.partyMax)&&(i={id:t.partyId},(t.partySize||t.partyMax)&&(i.size=[t.partySize,t.partyMax])),(t.matchSecret||t.joinSecret||t.spectateSecret)&&(o={match:t.matchSecret,join:t.joinSecret,spectate:t.spectateSecret}),this.request(u.SET_ACTIVITY,{pid:e,activity:{state:t.state,details:t.details,timestamps:r,assets:n,party:i,secrets:o,buttons:t.buttons,instance:!!t.instance}})}clearActivity(t=c()){return this.request(u.SET_ACTIVITY,{pid:t})}sendJoinInvite(t){return this.request(u.SEND_ACTIVITY_JOIN_INVITE,{user_id:t.id||t})}sendJoinRequest(t){return this.request(u.SEND_ACTIVITY_JOIN_REQUEST,{user_id:t.id||t})}closeJoinRequest(t){return this.request(u.CLOSE_ACTIVITY_JOIN_REQUEST,{user_id:t.id||t})}createLobby(t,e,r){return this.request(u.CREATE_LOBBY,{type:t,capacity:e,metadata:r})}updateLobby(t,{type:e,owner:r,capacity:n,metadata:i}={}){return this.request(u.UPDATE_LOBBY,{id:t.id||t,type:e,owner_id:r&&r.id||r,capacity:n,metadata:i})}deleteLobby(t){return this.request(u.DELETE_LOBBY,{id:t.id||t})}connectToLobby(t,e){return this.request(u.CONNECT_TO_LOBBY,{id:t,secret:e})}sendToLobby(t,e){return this.request(u.SEND_TO_LOBBY,{id:t.id||t,data:e})}disconnectFromLobby(t){return this.request(u.DISCONNECT_FROM_LOBBY,{id:t.id||t})}updateLobbyMember(t,e,r){return this.request(u.UPDATE_LOBBY_MEMBER,{lobby_id:t.id||t,user_id:e.id||e,metadata:r})}getRelationships(){let t=Object.keys(h);return this.request(u.GET_RELATIONSHIPS).then(e=>e.relationships.map(e=>({...e,type:t[e.type]})))}async subscribe(t,e){return await this.request(u.SUBSCRIBE,e,t),{unsubscribe:()=>this.request(u.UNSUBSCRIBE,e,t)}}async destroy(){await this.transport.close()}}t.exports=p},271:(t,e,r)=>{var n=r(9509);"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//"),({189:function(){!function(t,e){"use strict";if(!t.setImmediate){var r,i,o,s,a,u=1,f={},h=!1,c=t.document,l=Object.getPrototypeOf&&Object.getPrototypeOf(t);l=l&&l.setTimeout?l:t,"[object process]"===({}).toString.call(t.process)?a=function(t){n.nextTick(function(){E(t)})}:function(){if(t.postMessage&&!t.importScripts){var e=!0,r=t.onmessage;return t.onmessage=function(){e=!1},t.postMessage("","*"),t.onmessage=r,e}}()?(r="setImmediate$"+Math.random()+"$",i=function(e){e.source===t&&"string"==typeof e.data&&0===e.data.indexOf(r)&&E(+e.data.slice(r.length))},t.addEventListener?t.addEventListener("message",i,!1):t.attachEvent("onmessage",i),a=function(e){t.postMessage(r+e,"*")}):t.MessageChannel?((o=new MessageChannel).port1.onmessage=function(t){E(t.data)},a=function(t){o.port2.postMessage(t)}):c&&"onreadystatechange"in c.createElement("script")?(s=c.documentElement,a=function(t){var e=c.createElement("script");e.onreadystatechange=function(){E(t),e.onreadystatechange=null,s.removeChild(e),e=null},s.appendChild(e)}):a=function(t){setTimeout(E,0,t)},l.setImmediate=function(t){"function"!=typeof t&&(t=Function(""+t));for(var e=Array(arguments.length-1),r=0;r<e.length;r++)e[r]=arguments[r+1];var n={callback:t,args:e};return f[u]=n,a(u),u++},l.clearImmediate=p}function p(t){delete f[t]}function E(t){if(h)setTimeout(E,0,t);else{var r=f[t];if(r){h=!0;try{var n=r.callback,i=r.args;switch(i.length){case 0:n();break;case 1:n(i[0]);break;case 2:n(i[0],i[1]);break;case 3:n(i[0],i[1],i[2]);break;default:n.apply(e,i)}}finally{p(t),h=!1}}}}}("undefined"==typeof self?void 0===r.g?this:r.g:self)}})[189](),t.exports={}},485:(t,e,r)=>{"use strict";let n=r(9087),{browser:i}=r(7304),o=i?window.WebSocket:r(3291);class s extends n{constructor(t){super(),this.client=t,this.ws=null,this.tries=0}async connect(){let t=6463+this.tries%10;this.tries+=1,this.ws=new o(`ws://127.0.0.1:${t}/?v=1&client_id=${this.client.clientId}`,i?void 0:{origin:this.client.options.origin}),this.ws.onopen=this.onOpen.bind(this),this.ws.onclose=this.onClose.bind(this),this.ws.onerror=this.onError.bind(this),this.ws.onmessage=this.onMessage.bind(this)}onOpen(){this.emit("open")}onClose(t){t.wasClean&&this.emit("close",t)}onError(t){try{this.ws.close()}catch{}this.tries>20?this.emit("error",t.error):setTimeout(()=>{this.connect()},250)}onMessage(t){this.emit("message",JSON.parse(t.data))}send(t){this.ws.send(JSON.stringify(t))}ping(){}close(){return new Promise(t=>{this.once("close",t),this.ws.close()})}}t.exports=s},2575:(t,e,r)=>{"use strict";let n;var i=r(9509);try{let{app:t}=r(3204);n=t.setAsDefaultProtocolClient.bind(t)}catch(t){try{n=r(2551)}catch(t){}}"function"!=typeof n&&(n=()=>!1),t.exports={pid:function(){return void 0!==i?i.pid:null},register:n,uuid:()=>{let t="";for(let e=0;e<32;e+=1){let r;if((8===e||12===e||16===e||20===e)&&(t+="-"),12===e)r=4;else{let t=16*Math.random()|0;r=16===e?3&t:t}t+=r.toString(16)}return t}}},4134:(t,e,r)=>{"use strict";var n=r(7719),i=r(7610),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function s(t){if(t>0x7fffffff)throw RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,a.prototype),e}function a(t,e,r){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return h(t)}return u(t,e,r)}function u(t,e,r){if("string"==typeof t){var n=t,i=e;if(("string"!=typeof i||""===i)&&(i="utf8"),!a.isEncoding(i))throw TypeError("Unknown encoding: "+i);var o=0|E(n,i),u=s(o),f=u.write(n,i);return f!==o&&(u=u.slice(0,f)),u}if(ArrayBuffer.isView(t)){var h=t;if(b(h,Uint8Array)){var d=new Uint8Array(h);return l(d.buffer,d.byteOffset,d.byteLength)}return c(h)}if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(b(t,ArrayBuffer)||t&&b(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(b(t,SharedArrayBuffer)||t&&b(t.buffer,SharedArrayBuffer)))return l(t,e,r);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');var y=t.valueOf&&t.valueOf();if(null!=y&&y!==t)return a.from(y,e,r);var _=function(t){if(a.isBuffer(t)){var e=0|p(t.length),r=s(e);return 0===r.length||t.copy(r,0,0,e),r}return void 0!==t.length?"number"!=typeof t.length||function(t){return t!=t}(t.length)?s(0):c(t):"Buffer"===t.type&&Array.isArray(t.data)?c(t.data):void 0}(t);if(_)return _;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return a.from(t[Symbol.toPrimitive]("string"),e,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function f(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function h(t){return f(t),s(t<0?0:0|p(t))}function c(t){for(var e=t.length<0?0:0|p(t.length),r=s(e),n=0;n<e;n+=1)r[n]=255&t[n];return r}function l(t,e,r){var n;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),a.prototype),n}function p(t){if(t>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function E(t,e){if(a.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||b(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return O(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return N(t).length;default:if(i)return n?-1:O(t).length;e=(""+e).toLowerCase(),i=!0}}function d(t,e,r){var i,o,s,a=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=e;o<r;++o)i+=w[t[o]];return i}(this,e,r);case"utf8":case"utf-8":return g(this,e,r);case"ascii":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}(this,e,r);case"latin1":case"binary":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}(this,e,r);case"base64":return i=this,o=e,s=r,0===o&&s===i.length?n.fromByteArray(i):n.fromByteArray(i.slice(o,s));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,r){for(var n=t.slice(e,r),i="",o=0;o<n.length-1;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}(this,e,r);default:if(a)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),a=!0}}function y(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function _(t,e,r,n,i){var o;if(0===t.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(o=r*=1)!=o&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length)if(i)return -1;else r=t.length-1;else if(r<0)if(!i)return -1;else r=0;if("string"==typeof e&&(e=a.from(e,n)),a.isBuffer(e))return 0===e.length?-1:T(t,e,r,n,i);if("number"==typeof e){if(e&=255,"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(t,e,r);else return Uint8Array.prototype.lastIndexOf.call(t,e,r);return T(t,[e],r,n,i)}throw TypeError("val must be string, number or Buffer")}function T(t,e,r,n,i){var o,s=1,a=t.length,u=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return -1;s=2,a/=2,u/=2,r/=2}function f(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(i){var h=-1;for(o=r;o<a;o++)if(f(t,o)===f(e,-1===h?0:o-h)){if(-1===h&&(h=o),o-h+1===u)return h*s}else -1!==h&&(o-=o-h),h=-1}else for(r+u>a&&(r=a-u),o=r;o>=0;o--){for(var c=!0,l=0;l<u;l++)if(f(t,o+l)!==f(e,l)){c=!1;break}if(c)return o}return -1}function g(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var o,s,a,u,f=t[i],h=null,c=f>239?4:f>223?3:f>191?2:1;if(i+c<=r)switch(c){case 1:f<128&&(h=f);break;case 2:(192&(o=t[i+1]))==128&&(u=(31&f)<<6|63&o)>127&&(h=u);break;case 3:o=t[i+1],s=t[i+2],(192&o)==128&&(192&s)==128&&(u=(15&f)<<12|(63&o)<<6|63&s)>2047&&(u<55296||u>57343)&&(h=u);break;case 4:o=t[i+1],s=t[i+2],a=t[i+3],(192&o)==128&&(192&s)==128&&(192&a)==128&&(u=(15&f)<<18|(63&o)<<12|(63&s)<<6|63&a)>65535&&u<1114112&&(h=u)}null===h?(h=65533,c=1):h>65535&&(h-=65536,n.push(h>>>10&1023|55296),h=56320|1023&h),n.push(h),i+=c}var l=n,p=l.length;if(p<=4096)return String.fromCharCode.apply(String,l);for(var E="",d=0;d<p;)E+=String.fromCharCode.apply(String,l.slice(d,d+=4096));return E}function I(t,e,r){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>r)throw RangeError("Trying to access beyond buffer length")}function m(t,e,r,n,i,o){if(!a.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw RangeError('"value" argument is out of bounds');if(r+n>t.length)throw RangeError("Index out of range")}function v(t,e,r,n,i,o){if(r+n>t.length||r<0)throw RangeError("Index out of range")}function A(t,e,r,n,o){return e*=1,r>>>=0,o||v(t,e,r,4,34028234663852886e22,-34028234663852886e22),i.write(t,e,r,n,23,4),r+4}function C(t,e,r,n,o){return e*=1,r>>>=0,o||v(t,e,r,8,17976931348623157e292,-17976931348623157e292),i.write(t,e,r,n,52,8),r+8}e.hp=a,e.IS=50,a.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(t,e,r){return u(t,e,r)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(t,e,r){return(f(t),t<=0)?s(t):void 0!==e?"string"==typeof r?s(t).fill(e,r):s(t).fill(e):s(t)},a.allocUnsafe=function(t){return h(t)},a.allocUnsafeSlow=function(t){return h(t)},a.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==a.prototype},a.compare=function(t,e){if(b(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),b(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(t)||!a.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,n=e.length,i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:+(n<r)},a.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(t,e){if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return a.alloc(0);if(void 0===e)for(r=0,e=0;r<t.length;++r)e+=t[r].length;var r,n=a.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var o=t[r];if(b(o,Uint8Array))i+o.length>n.length?a.from(o).copy(n,i):Uint8Array.prototype.set.call(n,o,i);else if(a.isBuffer(o))o.copy(n,i);else throw TypeError('"list" argument must be an Array of Buffers');i+=o.length}return n},a.byteLength=E,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)y(this,e,e+1);return this},a.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)y(this,e,e+3),y(this,e+1,e+2);return this},a.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)y(this,e,e+7),y(this,e+1,e+6),y(this,e+2,e+5),y(this,e+3,e+4);return this},a.prototype.toString=function(){var t=this.length;return 0===t?"":0==arguments.length?g(this,0,t):d.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(t){if(!a.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===a.compare(this,t)},a.prototype.inspect=function(){var t="",r=e.IS;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},o&&(a.prototype[o]=a.prototype.inspect),a.prototype.compare=function(t,e,r,n,i){if(b(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return -1;if(e>=r)return 1;if(e>>>=0,r>>>=0,n>>>=0,i>>>=0,this===t)return 0;for(var o=i-n,s=r-e,u=Math.min(o,s),f=this.slice(n,i),h=t.slice(e,r),c=0;c<u;++c)if(f[c]!==h[c]){o=f[c],s=h[c];break}return o<s?-1:+(s<o)},a.prototype.includes=function(t,e,r){return -1!==this.indexOf(t,e,r)},a.prototype.indexOf=function(t,e,r){return _(this,t,e,r,!0)},a.prototype.lastIndexOf=function(t,e,r){return _(this,t,e,r,!1)},a.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,o,s,a,u,f,h,c,l=this.length-e;if((void 0===r||r>l)&&(r=l),t.length>0&&(r<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var p=!1;;)switch(n){case"hex":return function(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=e.length;n>o/2&&(n=o/2);for(var s=0;s<n;++s){var a,u=parseInt(e.substr(2*s,2),16);if((a=u)!=a)break;t[r+s]=u}return s}(this,t,e,r);case"utf8":case"utf-8":return i=e,o=r,S(O(t,this.length-i),this,i,o);case"ascii":case"latin1":case"binary":return s=e,a=r,S(function(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(t),this,s,a);case"base64":return u=e,f=r,S(N(t),this,u,f);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return h=e,c=r,S(function(t,e){for(var r,n,i=[],o=0;o<t.length&&!((e-=2)<0);++o)n=(r=t.charCodeAt(o))>>8,i.push(r%256),i.push(n);return i}(t,this.length-h),this,h,c);default:if(p)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),p=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.slice=function(t,e){var r=this.length;t=~~t,e=void 0===e?r:~~e,t<0?(t+=r)<0&&(t=0):t>r&&(t=r),e<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);var n=this.subarray(t,e);return Object.setPrototypeOf(n,a.prototype),n},a.prototype.readUintLE=a.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||I(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n},a.prototype.readUintBE=a.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||I(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},a.prototype.readUint8=a.prototype.readUInt8=function(t,e){return t>>>=0,e||I(t,1,this.length),this[t]},a.prototype.readUint16LE=a.prototype.readUInt16LE=function(t,e){return t>>>=0,e||I(t,2,this.length),this[t]|this[t+1]<<8},a.prototype.readUint16BE=a.prototype.readUInt16BE=function(t,e){return t>>>=0,e||I(t,2,this.length),this[t]<<8|this[t+1]},a.prototype.readUint32LE=a.prototype.readUInt32LE=function(t,e){return t>>>=0,e||I(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+0x1000000*this[t+3]},a.prototype.readUint32BE=a.prototype.readUInt32BE=function(t,e){return t>>>=0,e||I(t,4,this.length),0x1000000*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},a.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||I(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},a.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||I(t,e,this.length);for(var n=e,i=1,o=this[t+--n];n>0&&(i*=256);)o+=this[t+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*e)),o},a.prototype.readInt8=function(t,e){return(t>>>=0,e||I(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},a.prototype.readInt16LE=function(t,e){t>>>=0,e||I(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt16BE=function(t,e){t>>>=0,e||I(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt32LE=function(t,e){return t>>>=0,e||I(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},a.prototype.readInt32BE=function(t,e){return t>>>=0,e||I(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},a.prototype.readFloatLE=function(t,e){return t>>>=0,e||I(t,4,this.length),i.read(this,t,!0,23,4)},a.prototype.readFloatBE=function(t,e){return t>>>=0,e||I(t,4,this.length),i.read(this,t,!1,23,4)},a.prototype.readDoubleLE=function(t,e){return t>>>=0,e||I(t,8,this.length),i.read(this,t,!0,52,8)},a.prototype.readDoubleBE=function(t,e){return t>>>=0,e||I(t,8,this.length),i.read(this,t,!1,52,8)},a.prototype.writeUintLE=a.prototype.writeUIntLE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;m(this,t,e,r,i,0)}var o=1,s=0;for(this[e]=255&t;++s<r&&(o*=256);)this[e+s]=t/o&255;return e+r},a.prototype.writeUintBE=a.prototype.writeUIntBE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;m(this,t,e,r,i,0)}var o=r-1,s=1;for(this[e+o]=255&t;--o>=0&&(s*=256);)this[e+o]=t/s&255;return e+r},a.prototype.writeUint8=a.prototype.writeUInt8=function(t,e,r){return t*=1,e>>>=0,r||m(this,t,e,1,255,0),this[e]=255&t,e+1},a.prototype.writeUint16LE=a.prototype.writeUInt16LE=function(t,e,r){return t*=1,e>>>=0,r||m(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeUint16BE=a.prototype.writeUInt16BE=function(t,e,r){return t*=1,e>>>=0,r||m(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeUint32LE=a.prototype.writeUInt32LE=function(t,e,r){return t*=1,e>>>=0,r||m(this,t,e,4,0xffffffff,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},a.prototype.writeUint32BE=a.prototype.writeUInt32BE=function(t,e,r){return t*=1,e>>>=0,r||m(this,t,e,4,0xffffffff,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeIntLE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var i=Math.pow(2,8*r-1);m(this,t,e,r,i-1,-i)}var o=0,s=1,a=0;for(this[e]=255&t;++o<r&&(s*=256);)t<0&&0===a&&0!==this[e+o-1]&&(a=1),this[e+o]=(t/s|0)-a&255;return e+r},a.prototype.writeIntBE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var i=Math.pow(2,8*r-1);m(this,t,e,r,i-1,-i)}var o=r-1,s=1,a=0;for(this[e+o]=255&t;--o>=0&&(s*=256);)t<0&&0===a&&0!==this[e+o+1]&&(a=1),this[e+o]=(t/s|0)-a&255;return e+r},a.prototype.writeInt8=function(t,e,r){return t*=1,e>>>=0,r||m(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},a.prototype.writeInt16LE=function(t,e,r){return t*=1,e>>>=0,r||m(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeInt16BE=function(t,e,r){return t*=1,e>>>=0,r||m(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeInt32LE=function(t,e,r){return t*=1,e>>>=0,r||m(this,t,e,4,0x7fffffff,-0x80000000),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},a.prototype.writeInt32BE=function(t,e,r){return t*=1,e>>>=0,r||m(this,t,e,4,0x7fffffff,-0x80000000),t<0&&(t=0xffffffff+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeFloatLE=function(t,e,r){return A(this,t,e,!0,r)},a.prototype.writeFloatBE=function(t,e,r){return A(this,t,e,!1,r)},a.prototype.writeDoubleLE=function(t,e,r){return C(this,t,e,!0,r)},a.prototype.writeDoubleBE=function(t,e,r){return C(this,t,e,!1,r)},a.prototype.copy=function(t,e,r,n){if(!a.isBuffer(t))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i=n-r;return this===t&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(e,r,n):Uint8Array.prototype.set.call(t,this.subarray(r,n),e),i},a.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!a.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===t.length){var i,o=t.charCodeAt(0);("utf8"===n&&o<128||"latin1"===n)&&(t=o)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw RangeError("Out of range index");if(r<=e)return this;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(i=e;i<r;++i)this[i]=t;else{var s=a.isBuffer(t)?t:a.from(t,n),u=s.length;if(0===u)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<r-e;++i)this[i+e]=s[i%u]}return this};var L=/[^+/0-9A-Za-z-_]/g;function O(t,e){e=e||1/0;for(var r,n=t.length,i=null,o=[],s=0;s<n;++s){if((r=t.charCodeAt(s))>55295&&r<57344){if(!i){if(r>56319||s+1===n){(e-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return o}function N(t){return n.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(L,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function S(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length)&&!(i>=t.length);++i)e[i+r]=t[i];return i}function b(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}var w=function(){for(var t="0123456789abcdef",e=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)e[n+i]=t[r]+t[i];return e}()},4322:(t,e,r)=>{"use strict";t.exports={ipc:r(7024),websocket:r(485)}},5173:(t,e,r)=>{"use strict";let n=r(2575);t.exports={Client:r(12),register:t=>n.register(`discord-${t}`)}},5901:(t,e,r)=>{"use strict";var n=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw Error("unable to locate global object")}();t.exports=e=n.fetch,n.fetch&&(e.default=n.fetch.bind(n)),e.Headers=n.Headers,e.Request=n.Request,e.Response=n.Response},6793:(t,e,r)=>{!function(){var e={845:function(t,e,n){var i=void 0!==r.g&&r.g||"undefined"!=typeof self&&self||window,o=Function.prototype.apply;function s(t,e){this._id=t,this._clearFn=e}e.setTimeout=function(){return new s(o.call(setTimeout,i,arguments),clearTimeout)},e.setInterval=function(){return new s(o.call(setInterval,i,arguments),clearInterval)},e.clearTimeout=e.clearInterval=function(t){t&&t.close()},s.prototype.unref=s.prototype.ref=function(){},s.prototype.close=function(){this._clearFn.call(i,this._id)},e.enroll=function(t,e){clearTimeout(t._idleTimeoutId),t._idleTimeout=e},e.unenroll=function(t){clearTimeout(t._idleTimeoutId),t._idleTimeout=-1},e._unrefActive=e.active=function(t){clearTimeout(t._idleTimeoutId);var e=t._idleTimeout;e>=0&&(t._idleTimeoutId=setTimeout(function(){t._onTimeout&&t._onTimeout()},e))},n(505),e.setImmediate="undefined"!=typeof self&&self.setImmediate||void 0!==r.g&&r.g.setImmediate||this&&this.setImmediate,e.clearImmediate="undefined"!=typeof self&&self.clearImmediate||void 0!==r.g&&r.g.clearImmediate||this&&this.clearImmediate},505:function(t){"use strict";t.exports=r(271)}},n={};function i(t){var r=n[t];if(void 0!==r)return r.exports;var o=n[t]={exports:{}},s=!0;try{e[t].call(o.exports,o,o.exports,i),s=!1}finally{s&&delete n[t]}return o.exports}i.ab="//",t.exports=i(845)}()},7024:(t,e,r)=>{"use strict";var n=r(9509),i=r(4134).hp;let o=r(5710),s=r(9087),a=r(5901),{uuid:u}=r(2575),f={HANDSHAKE:0,FRAME:1,CLOSE:2,PING:3,PONG:4};async function h(t=0){if(t>30)throw Error("Could not find endpoint");let e=`http://127.0.0.1:${6463+t%10}`;try{let r=await a(e);if(404===r.status)return e;return h(t+1)}catch(e){return h(t+1)}}function c(t,e){e=JSON.stringify(e);let r=i.byteLength(e),n=i.alloc(8+r);return n.writeInt32LE(t,0),n.writeInt32LE(r,4),n.write(e,8,r),n}let l={full:"",op:void 0};function p(t,e){let r,n=t.read();if(!n)return;let{op:i}=l;if(""===l.full){i=l.op=n.readInt32LE(0);let t=n.readInt32LE(4);r=n.slice(8,t+8)}else r=n.toString();try{let t=JSON.parse(l.full+r);e({op:i,data:t}),l.full="",l.op=void 0}catch(t){l.full+=r}p(t,e)}class E extends s{constructor(t){super(),this.client=t,this.socket=null}async connect(){let t=this.socket=await function t(e=0){return new Promise((r,i)=>{let s=function(t){if("win32"===n.platform)return`\\\\?\\pipe\\discord-ipc-${t}`;let{env:{XDG_RUNTIME_DIR:e,TMPDIR:r,TMP:i,TEMP:o}}=n;return`${(e||r||i||o||"/tmp").replace(/\/$/,"")}/discord-ipc-${t}`}(e),a=()=>{e<10?r(t(e+1)):i(Error("Could not connect"))},u=o.createConnection(s,()=>{u.removeListener("error",a),r(u)});u.once("error",a)})}();t.on("close",this.onClose.bind(this)),t.on("error",this.onClose.bind(this)),this.emit("open"),t.write(c(f.HANDSHAKE,{v:1,client_id:this.client.clientId})),t.pause(),t.on("readable",()=>{p(t,({op:t,data:e})=>{switch(t){case f.PING:this.send(e,f.PONG);break;case f.FRAME:if(!e)return;"AUTHORIZE"===e.cmd&&"ERROR"!==e.evt&&h().then(t=>{this.client.request.endpoint=t}).catch(t=>{this.client.emit("error",t)}),this.emit("message",e);break;case f.CLOSE:this.emit("close",e)}})})}onClose(t){this.emit("close",t)}send(t,e=f.FRAME){this.socket.write(c(e,t))}async close(){return new Promise(t=>{this.once("close",t),this.send({},f.CLOSE),this.socket.end()})}ping(){this.send(u(),f.PING)}}t.exports=E,t.exports.encode=c,t.exports.decode=p},7304:(t,e)=>{"use strict";function r(t){let e={};for(let r of t)e[r]=r;return e}e.browser="undefined"!=typeof window,e.RPCCommands=r(["DISPATCH","AUTHORIZE","AUTHENTICATE","GET_GUILD","GET_GUILDS","GET_CHANNEL","GET_CHANNELS","CREATE_CHANNEL_INVITE","GET_RELATIONSHIPS","GET_USER","SUBSCRIBE","UNSUBSCRIBE","SET_USER_VOICE_SETTINGS","SET_USER_VOICE_SETTINGS_2","SELECT_VOICE_CHANNEL","GET_SELECTED_VOICE_CHANNEL","SELECT_TEXT_CHANNEL","GET_VOICE_SETTINGS","SET_VOICE_SETTINGS_2","SET_VOICE_SETTINGS","CAPTURE_SHORTCUT","SET_ACTIVITY","SEND_ACTIVITY_JOIN_INVITE","CLOSE_ACTIVITY_JOIN_REQUEST","ACTIVITY_INVITE_USER","ACCEPT_ACTIVITY_INVITE","INVITE_BROWSER","DEEP_LINK","CONNECTIONS_CALLBACK","BRAINTREE_POPUP_BRIDGE_CALLBACK","GIFT_CODE_BROWSER","GUILD_TEMPLATE_BROWSER","OVERLAY","BROWSER_HANDOFF","SET_CERTIFIED_DEVICES","GET_IMAGE","CREATE_LOBBY","UPDATE_LOBBY","DELETE_LOBBY","UPDATE_LOBBY_MEMBER","CONNECT_TO_LOBBY","DISCONNECT_FROM_LOBBY","SEND_TO_LOBBY","SEARCH_LOBBIES","CONNECT_TO_LOBBY_VOICE","DISCONNECT_FROM_LOBBY_VOICE","SET_OVERLAY_LOCKED","OPEN_OVERLAY_ACTIVITY_INVITE","OPEN_OVERLAY_GUILD_INVITE","OPEN_OVERLAY_VOICE_SETTINGS","VALIDATE_APPLICATION","GET_ENTITLEMENT_TICKET","GET_APPLICATION_TICKET","START_PURCHASE","GET_SKUS","GET_ENTITLEMENTS","GET_NETWORKING_CONFIG","NETWORKING_SYSTEM_METRICS","NETWORKING_PEER_METRICS","NETWORKING_CREATE_TOKEN","SET_USER_ACHIEVEMENT","GET_USER_ACHIEVEMENTS"]),e.RPCEvents=r(["CURRENT_USER_UPDATE","GUILD_STATUS","GUILD_CREATE","CHANNEL_CREATE","RELATIONSHIP_UPDATE","VOICE_CHANNEL_SELECT","VOICE_STATE_CREATE","VOICE_STATE_DELETE","VOICE_STATE_UPDATE","VOICE_SETTINGS_UPDATE","VOICE_SETTINGS_UPDATE_2","VOICE_CONNECTION_STATUS","SPEAKING_START","SPEAKING_STOP","GAME_JOIN","GAME_SPECTATE","ACTIVITY_JOIN","ACTIVITY_JOIN_REQUEST","ACTIVITY_SPECTATE","ACTIVITY_INVITE","NOTIFICATION_CREATE","MESSAGE_CREATE","MESSAGE_UPDATE","MESSAGE_DELETE","LOBBY_DELETE","LOBBY_UPDATE","LOBBY_MEMBER_CONNECT","LOBBY_MEMBER_DISCONNECT","LOBBY_MEMBER_UPDATE","LOBBY_MESSAGE","CAPTURE_SHORTCUT_CHANGE","OVERLAY","OVERLAY_UPDATE","ENTITLEMENT_CREATE","ENTITLEMENT_DELETE","USER_ACHIEVEMENT_UPDATE","READY","ERROR"]),e.RPCErrors={CAPTURE_SHORTCUT_ALREADY_LISTENING:5004,GET_GUILD_TIMED_OUT:5002,INVALID_ACTIVITY_JOIN_REQUEST:4012,INVALID_ACTIVITY_SECRET:5005,INVALID_CHANNEL:4005,INVALID_CLIENTID:4007,INVALID_COMMAND:4002,INVALID_ENTITLEMENT:4015,INVALID_EVENT:4004,INVALID_GIFT_CODE:4016,INVALID_GUILD:4003,INVALID_INVITE:4011,INVALID_LOBBY:4013,INVALID_LOBBY_SECRET:4014,INVALID_ORIGIN:4008,INVALID_PAYLOAD:4e3,INVALID_PERMISSIONS:4006,INVALID_TOKEN:4009,INVALID_USER:4010,LOBBY_FULL:5007,NO_ELIGIBLE_ACTIVITY:5006,OAUTH2_ERROR:5e3,PURCHASE_CANCELED:5008,PURCHASE_ERROR:5009,RATE_LIMITED:5011,SELECT_CHANNEL_TIMED_OUT:5001,SELECT_VOICE_FORCE_REQUIRED:5003,SERVICE_UNAVAILABLE:1001,TRANSACTION_ABORTED:1002,UNAUTHORIZED_FOR_ACHIEVEMENT:5010,UNKNOWN_ERROR:1e3},e.RPCCloseCodes={CLOSE_NORMAL:1e3,CLOSE_UNSUPPORTED:1003,CLOSE_ABNORMAL:1006,INVALID_CLIENTID:4e3,INVALID_ORIGIN:4001,RATELIMITED:4002,TOKEN_REVOKED:4003,INVALID_VERSION:4004,INVALID_ENCODING:4005},e.LobbyTypes={PRIVATE:1,PUBLIC:2},e.RelationshipTypes={NONE:0,FRIEND:1,BLOCKED:2,PENDING_INCOMING:3,PENDING_OUTGOING:4,IMPLICIT:5}},7610:(t,e)=>{e.read=function(t,e,r,n,i){var o,s,a=8*i-n-1,u=(1<<a)-1,f=u>>1,h=-7,c=r?i-1:0,l=r?-1:1,p=t[e+c];for(c+=l,o=p&(1<<-h)-1,p>>=-h,h+=a;h>0;o=256*o+t[e+c],c+=l,h-=8);for(s=o&(1<<-h)-1,o>>=-h,h+=n;h>0;s=256*s+t[e+c],c+=l,h-=8);if(0===o)o=1-f;else{if(o===u)return s?NaN:1/0*(p?-1:1);s+=Math.pow(2,n),o-=f}return(p?-1:1)*s*Math.pow(2,o-n)},e.write=function(t,e,r,n,i,o){var s,a,u,f=8*o-i-1,h=(1<<f)-1,c=h>>1,l=5960464477539062e-23*(23===i),p=n?0:o-1,E=n?1:-1,d=+(e<0||0===e&&1/e<0);for(isNaN(e=Math.abs(e))||e===1/0?(a=+!!isNaN(e),s=h):(s=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-s))<1&&(s--,u*=2),s+c>=1?e+=l/u:e+=l*Math.pow(2,1-c),e*u>=2&&(s++,u/=2),s+c>=h?(a=0,s=h):s+c>=1?(a=(e*u-1)*Math.pow(2,i),s+=c):(a=e*Math.pow(2,c-1)*Math.pow(2,i),s=0));i>=8;t[r+p]=255&a,p+=E,a/=256,i-=8);for(s=s<<i|a,f+=i;f>0;t[r+p]=255&s,p+=E,s/=256,f-=8);t[r+p-E]|=128*d}},7719:(t,e)=>{"use strict";e.byteLength=function(t){var e=u(t),r=e[0],n=e[1];return(r+n)*3/4-n},e.toByteArray=function(t){var e,r,o=u(t),s=o[0],a=o[1],f=new i((s+a)*3/4-a),h=0,c=a>0?s-4:s;for(r=0;r<c;r+=4)e=n[t.charCodeAt(r)]<<18|n[t.charCodeAt(r+1)]<<12|n[t.charCodeAt(r+2)]<<6|n[t.charCodeAt(r+3)],f[h++]=e>>16&255,f[h++]=e>>8&255,f[h++]=255&e;return 2===a&&(e=n[t.charCodeAt(r)]<<2|n[t.charCodeAt(r+1)]>>4,f[h++]=255&e),1===a&&(e=n[t.charCodeAt(r)]<<10|n[t.charCodeAt(r+1)]<<4|n[t.charCodeAt(r+2)]>>2,f[h++]=e>>8&255,f[h++]=255&e),f},e.fromByteArray=function(t){for(var e,n=t.length,i=n%3,o=[],s=0,a=n-i;s<a;s+=16383)o.push(function(t,e,n){for(var i,o=[],s=e;s<n;s+=3)i=(t[s]<<16&0xff0000)+(t[s+1]<<8&65280)+(255&t[s+2]),o.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return o.join("")}(t,s,s+16383>a?a:s+16383));return 1===i?o.push(r[(e=t[n-1])>>2]+r[e<<4&63]+"=="):2===i&&o.push(r[(e=(t[n-2]<<8)+t[n-1])>>10]+r[e>>4&63]+r[e<<2&63]+"="),o.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,a=o.length;s<a;++s)r[s]=o[s],n[o.charCodeAt(s)]=s;function u(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");-1===r&&(r=e);var n=r===e?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},9087:t=>{!function(){"use strict";var e={864:function(t){var e,r="object"==typeof Reflect?Reflect:null,n=r&&"function"==typeof r.apply?r.apply:function(t,e,r){return Function.prototype.apply.call(t,e,r)};e=r&&"function"==typeof r.ownKeys?r.ownKeys:Object.getOwnPropertySymbols?function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:function(t){return Object.getOwnPropertyNames(t)};var i=Number.isNaN||function(t){return t!=t};function o(){o.init.call(this)}t.exports=o,t.exports.once=function(t,e){return new Promise(function(r,n){var i,o,s;function a(r){t.removeListener(e,u),n(r)}function u(){"function"==typeof t.removeListener&&t.removeListener("error",a),r([].slice.call(arguments))}d(t,e,u,{once:!0}),"error"!==e&&(i=t,o=a,s={once:!0},"function"==typeof i.on&&d(i,"error",o,s))})},o.EventEmitter=o,o.prototype._events=void 0,o.prototype._eventsCount=0,o.prototype._maxListeners=void 0;var s=10;function a(t){if("function"!=typeof t)throw TypeError('The "listener" argument must be of type Function. Received type '+typeof t)}function u(t){return void 0===t._maxListeners?o.defaultMaxListeners:t._maxListeners}function f(t,e,r,n){if(a(r),void 0===(o=t._events)?(o=t._events=Object.create(null),t._eventsCount=0):(void 0!==o.newListener&&(t.emit("newListener",e,r.listener?r.listener:r),o=t._events),s=o[e]),void 0===s)s=o[e]=r,++t._eventsCount;else if("function"==typeof s?s=o[e]=n?[r,s]:[s,r]:n?s.unshift(r):s.push(r),(i=u(t))>0&&s.length>i&&!s.warned){s.warned=!0;var i,o,s,f=Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");f.name="MaxListenersExceededWarning",f.emitter=t,f.type=e,f.count=s.length,console&&console.warn&&console.warn(f)}return t}function h(){if(!this.fired)return(this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0==arguments.length)?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function c(t,e,r){var n={fired:!1,wrapFn:void 0,target:t,type:e,listener:r},i=h.bind(n);return i.listener=r,n.wrapFn=i,i}function l(t,e,r){var n=t._events;if(void 0===n)return[];var i=n[e];return void 0===i?[]:"function"==typeof i?r?[i.listener||i]:[i]:r?function(t){for(var e=Array(t.length),r=0;r<e.length;++r)e[r]=t[r].listener||t[r];return e}(i):E(i,i.length)}function p(t){var e=this._events;if(void 0!==e){var r=e[t];if("function"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function E(t,e){for(var r=Array(e),n=0;n<e;++n)r[n]=t[n];return r}function d(t,e,r,n){if("function"==typeof t.on)n.once?t.once(e,r):t.on(e,r);else if("function"==typeof t.addEventListener)t.addEventListener(e,function i(o){n.once&&t.removeEventListener(e,i),r(o)});else throw TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof t)}Object.defineProperty(o,"defaultMaxListeners",{enumerable:!0,get:function(){return s},set:function(t){if("number"!=typeof t||t<0||i(t))throw RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");s=t}}),o.init=function(){(void 0===this._events||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},o.prototype.setMaxListeners=function(t){if("number"!=typeof t||t<0||i(t))throw RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this},o.prototype.getMaxListeners=function(){return u(this)},o.prototype.emit=function(t){for(var e=[],r=1;r<arguments.length;r++)e.push(arguments[r]);var i="error"===t,o=this._events;if(void 0!==o)i=i&&void 0===o.error;else if(!i)return!1;if(i){if(e.length>0&&(s=e[0]),s instanceof Error)throw s;var s,a=Error("Unhandled error."+(s?" ("+s.message+")":""));throw a.context=s,a}var u=o[t];if(void 0===u)return!1;if("function"==typeof u)n(u,this,e);else for(var f=u.length,h=E(u,f),r=0;r<f;++r)n(h[r],this,e);return!0},o.prototype.addListener=function(t,e){return f(this,t,e,!1)},o.prototype.on=o.prototype.addListener,o.prototype.prependListener=function(t,e){return f(this,t,e,!0)},o.prototype.once=function(t,e){return a(e),this.on(t,c(this,t,e)),this},o.prototype.prependOnceListener=function(t,e){return a(e),this.prependListener(t,c(this,t,e)),this},o.prototype.removeListener=function(t,e){var r,n,i,o,s;if(a(e),void 0===(n=this._events)||void 0===(r=n[t]))return this;if(r===e||r.listener===e)0==--this._eventsCount?this._events=Object.create(null):(delete n[t],n.removeListener&&this.emit("removeListener",t,r.listener||e));else if("function"!=typeof r){for(i=-1,o=r.length-1;o>=0;o--)if(r[o]===e||r[o].listener===e){s=r[o].listener,i=o;break}if(i<0)return this;0===i?r.shift():function(t,e){for(;e+1<t.length;e++)t[e]=t[e+1];t.pop()}(r,i),1===r.length&&(n[t]=r[0]),void 0!==n.removeListener&&this.emit("removeListener",t,s||e)}return this},o.prototype.off=o.prototype.removeListener,o.prototype.removeAllListeners=function(t){var e,r,n;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0==arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[t]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[t]),this;if(0==arguments.length){var i,o=Object.keys(r);for(n=0;n<o.length;++n)"removeListener"!==(i=o[n])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(e=r[t]))this.removeListener(t,e);else if(void 0!==e)for(n=e.length-1;n>=0;n--)this.removeListener(t,e[n]);return this},o.prototype.listeners=function(t){return l(this,t,!0)},o.prototype.rawListeners=function(t){return l(this,t,!1)},o.listenerCount=function(t,e){return"function"==typeof t.listenerCount?t.listenerCount(e):p.call(t,e)},o.prototype.listenerCount=p,o.prototype.eventNames=function(){return this._eventsCount>0?e(this._events):[]}}},r={};function n(t){var i=r[t];if(void 0!==i)return i.exports;var o=r[t]={exports:{}},s=!0;try{e[t](o,o.exports,n),s=!1}finally{s&&delete r[t]}return o.exports}n.ab="//",t.exports=n(864)}()}}]);