'use client'

import { But<PERSON> } from '@/components/ui/Button'
import { LevelReward } from '@/lib/progressionSystem'

interface LevelUpModalProps {
  isOpen: boolean
  onClose: () => void
  newLevel: number
  rewards: LevelReward[]
}

export function LevelUpModal({ isOpen, onClose, newLevel, rewards }: LevelUpModalProps) {
  if (!isOpen) return null

  const getRewardIcon = (type: string) => {
    switch (type) {
      case 'recipe': return '📖'
      case 'equipment': return '⚙️'
      case 'money': return '💰'
      case 'skill_point': return '⭐'
      case 'achievement': return '🏆'
      default: return '🎁'
    }
  }

  const getRewardColor = (type: string) => {
    switch (type) {
      case 'recipe': return 'bg-blue-50 border-blue-300 text-blue-800'
      case 'equipment': return 'bg-purple-50 border-purple-300 text-purple-800'
      case 'money': return 'bg-green-50 border-green-300 text-green-800'
      case 'skill_point': return 'bg-yellow-50 border-yellow-300 text-yellow-800'
      case 'achievement': return 'bg-orange-50 border-orange-300 text-orange-800'
      default: return 'bg-gray-50 border-gray-300 text-gray-800'
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full overflow-hidden">
        {/* Header with celebration */}
        <div className="bg-gradient-to-r from-yellow-400 to-orange-500 p-6 text-center">
          <div className="text-6xl mb-2">🎉</div>
          <h2 className="text-3xl font-bold text-white mb-2">Level Up!</h2>
          <p className="text-xl text-yellow-100">
            You reached Level {newLevel}!
          </p>
        </div>

        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            🎁 Level Rewards
          </h3>

          <div className="space-y-3 mb-6">
            {rewards.map((reward, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg border ${getRewardColor(reward.type)}`}
              >
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{getRewardIcon(reward.type)}</span>
                  <div className="flex-1">
                    <h4 className="font-medium">{reward.name}</h4>
                    <p className="text-sm opacity-80">{reward.description}</p>
                    {reward.value && (
                      <p className="text-sm font-semibold">
                        {reward.type === 'money' ? `$${reward.value}` : `+${reward.value}`}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="bg-blue-50 p-4 rounded-lg mb-6">
            <h4 className="font-medium text-blue-800 mb-2">💡 What's Next?</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Check out new recipes in your recipe book</li>
              <li>• Visit the shop for new equipment</li>
              <li>• Take on more challenging orders</li>
              <li>• Invest in skill upgrades</li>
            </ul>
          </div>

          <Button
            variant="primary"
            size="lg"
            className="w-full"
            onClick={onClose}
          >
            🚀 Continue Playing
          </Button>
        </div>
      </div>
    </div>
  )
}
