import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface Player {
  id: string
  user_id: string
  level: number
  experience: number
  money: number
  created_at: string
  updated_at: string
}

export interface Recipe {
  id: string
  name: string
  description: string
  ingredients: string[] // JSON array of ingredient names
  baking_time: number // in seconds
  difficulty: number // 1-5
  unlock_level: number
  base_price: number
  created_at: string
}

export interface Equipment {
  id: string
  name: string
  type: 'oven' | 'mixer' | 'conveyor' | 'auto_mixer' | 'auto_oven'
  level: number
  cost: number
  unlock_level: number
  automation_level: number // 0 = manual, 1-3 = automation levels
  efficiency_multiplier: number
  created_at: string
}

export interface PlayerRecipe {
  id: string
  player_id: string
  recipe_id: string
  unlocked_at: string
}

export interface PlayerEquipment {
  id: string
  player_id: string
  equipment_id: string
  quantity: number
  purchased_at: string
}

export interface GameSession {
  id: string
  host_player_id: string
  session_code: string
  max_players: number
  current_players: number
  is_active: boolean
  game_mode: 'cooperative' | 'competitive'
  created_at: string
}

export interface Order {
  id: string
  player_id: string
  session_id?: string
  customer_name: string
  items: OrderItem[]
  total_price: number
  time_limit: number // in seconds
  status: 'pending' | 'in_progress' | 'completed' | 'failed'
  created_at: string
  completed_at?: string
}

export interface OrderItem {
  recipe_id: string
  quantity: number
  price: number
}

export interface Ingredient {
  id: string
  name: string
  cost: number
  availability: number // 0-1 (percentage)
  created_at: string
}
