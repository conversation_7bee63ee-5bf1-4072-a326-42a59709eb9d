{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { Button } from '@/components/ui/Button'\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-orange-100 to-yellow-100 flex items-center justify-center\">\n      <div className=\"text-center space-y-8 p-8\">\n        <div className=\"space-y-4\">\n          <h1 className=\"text-6xl font-bold text-orange-800 mb-4\">\n            🥖 Bake It Out\n          </h1>\n          <p className=\"text-xl text-orange-700 max-w-2xl mx-auto\">\n            Master the art of bakery management in this engaging multiplayer game.\n            Complete orders, unlock recipes, automate your processes, and compete with friends!\n          </p>\n        </div>\n\n        <div className=\"space-y-4\">\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button size=\"lg\" className=\"text-lg px-8 py-4\">\n              🎮 Start Playing\n            </Button>\n            <Button variant=\"secondary\" size=\"lg\" className=\"text-lg px-8 py-4\">\n              👥 Multiplayer\n            </Button>\n          </div>\n\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button variant=\"secondary\" size=\"md\">\n              🇺🇸 English\n            </Button>\n            <Button variant=\"secondary\" size=\"md\">\n              🇨🇿 Čeština\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 max-w-4xl mx-auto\">\n          <div className=\"bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center\">\n            <div className=\"text-3xl mb-3\">🏪</div>\n            <h3 className=\"font-semibold text-orange-800 mb-2\">Manage Your Bakery</h3>\n            <p className=\"text-orange-700 text-sm\">\n              Take orders, bake delicious goods, and serve happy customers\n            </p>\n          </div>\n\n          <div className=\"bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center\">\n            <div className=\"text-3xl mb-3\">📈</div>\n            <h3 className=\"font-semibold text-orange-800 mb-2\">Level Up & Automate</h3>\n            <p className=\"text-orange-700 text-sm\">\n              Unlock new recipes, buy equipment, and automate your processes\n            </p>\n          </div>\n\n          <div className=\"bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center\">\n            <div className=\"text-3xl mb-3\">👥</div>\n            <h3 className=\"font-semibold text-orange-800 mb-2\">Play Together</h3>\n            <p className=\"text-orange-700 text-sm\">\n              Cooperative and competitive multiplayer modes with friends\n            </p>\n          </div>\n        </div>\n\n        <div className=\"mt-8 text-sm text-orange-600\">\n          <p>🚧 Game in Development - Phase 1: Project Setup Complete! 🚧</p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0C;;;;;;sCAGxD,8OAAC;4BAAE,WAAU;sCAA4C;;;;;;;;;;;;8BAM3D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,MAAK;oCAAK,WAAU;8CAAoB;;;;;;8CAGhD,8OAAC;oCAAO,SAAQ;oCAAY,MAAK;oCAAK,WAAU;8CAAoB;;;;;;;;;;;;sCAKtE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,SAAQ;oCAAY,MAAK;8CAAK;;;;;;8CAGtC,8OAAC;oCAAO,SAAQ;oCAAY,MAAK;8CAAK;;;;;;;;;;;;;;;;;;8BAM1C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,8OAAC;oCAAE,WAAU;8CAA0B;;;;;;;;;;;;sCAKzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,8OAAC;oCAAE,WAAU;8CAA0B;;;;;;;;;;;;sCAKzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,8OAAC;oCAAE,WAAU;8CAA0B;;;;;;;;;;;;;;;;;;8BAM3C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}