// Automation system for Bake It Out

export interface AutomationSettings {
  enabled: boolean
  autoStart: boolean
  preferredRecipes: string[]
  maxConcurrentJobs: number
  priorityMode: 'speed' | 'efficiency' | 'profit'
  ingredientThreshold: number // Minimum ingredients before stopping
}

export interface AutomationJob {
  id: string
  equipmentId: string
  recipeId: string
  startTime: number
  duration: number
  status: 'queued' | 'running' | 'completed' | 'failed'
  ingredients: { name: string; quantity: number }[]
  efficiency: number
}

export interface ConveyorBelt {
  id: string
  name: string
  level: number
  speed: number // items per minute
  capacity: number // max items on belt
  connections: string[] // connected equipment IDs
  items: ConveyorItem[]
  isActive: boolean
}

export interface ConveyorItem {
  id: string
  recipeId: string
  position: number // 0-1 along the belt
  targetEquipmentId: string
}

export interface AutomationUpgrade {
  id: string
  name: string
  description: string
  type: 'speed' | 'efficiency' | 'capacity' | 'intelligence'
  cost: number
  unlockLevel: number
  effects: {
    speedMultiplier?: number
    efficiencyBonus?: number
    capacityIncrease?: number
    autoQueueing?: boolean
    smartPrioritization?: boolean
  }
}

export const AUTOMATION_UPGRADES: AutomationUpgrade[] = [
  {
    id: 'auto_queue_basic',
    name: 'Basic Auto-Queue',
    description: 'Equipment automatically starts the next recipe when finished',
    type: 'intelligence',
    cost: 500,
    unlockLevel: 4,
    effects: { autoQueueing: true }
  },
  {
    id: 'efficiency_boost_1',
    name: 'Efficiency Boost I',
    description: 'Automated equipment uses 10% fewer ingredients',
    type: 'efficiency',
    cost: 750,
    unlockLevel: 5,
    effects: { efficiencyBonus: 0.1 }
  },
  {
    id: 'speed_boost_1',
    name: 'Speed Boost I',
    description: 'Automated equipment works 15% faster',
    type: 'speed',
    cost: 1000,
    unlockLevel: 6,
    effects: { speedMultiplier: 1.15 }
  },
  {
    id: 'smart_prioritization',
    name: 'Smart Prioritization',
    description: 'Automation prioritizes orders based on profit and urgency',
    type: 'intelligence',
    cost: 1500,
    unlockLevel: 8,
    effects: { smartPrioritization: true }
  },
  {
    id: 'efficiency_boost_2',
    name: 'Efficiency Boost II',
    description: 'Automated equipment uses 20% fewer ingredients',
    type: 'efficiency',
    cost: 2000,
    unlockLevel: 10,
    effects: { efficiencyBonus: 0.2 }
  },
  {
    id: 'speed_boost_2',
    name: 'Speed Boost II',
    description: 'Automated equipment works 30% faster',
    type: 'speed',
    cost: 2500,
    unlockLevel: 12,
    effects: { speedMultiplier: 1.3 }
  }
]

export function calculateAutomationEfficiency(
  baseEfficiency: number,
  automationLevel: number,
  upgrades: string[],
  skillBonuses: number = 0
): number {
  let efficiency = baseEfficiency

  // Automation level bonus
  efficiency *= (1 + automationLevel * 0.1)

  // Upgrade bonuses
  upgrades.forEach(upgradeId => {
    const upgrade = AUTOMATION_UPGRADES.find(u => u.id === upgradeId)
    if (upgrade?.effects.efficiencyBonus) {
      efficiency *= (1 + upgrade.effects.efficiencyBonus)
    }
  })

  // Skill bonuses
  efficiency *= (1 + skillBonuses)

  return Math.min(efficiency, 2.0) // Cap at 200% efficiency
}

export function calculateAutomationSpeed(
  baseSpeed: number,
  automationLevel: number,
  upgrades: string[],
  skillBonuses: number = 0
): number {
  let speed = baseSpeed

  // Automation level bonus
  speed *= (1 + automationLevel * 0.05)

  // Upgrade bonuses
  upgrades.forEach(upgradeId => {
    const upgrade = AUTOMATION_UPGRADES.find(u => u.id === upgradeId)
    if (upgrade?.effects.speedMultiplier) {
      speed *= upgrade.effects.speedMultiplier
    }
  })

  // Skill bonuses
  speed *= (1 + skillBonuses)

  return speed
}

export function canAutomate(equipmentType: string, automationLevel: number): boolean {
  const automationRequirements: Record<string, number> = {
    'oven': 1,
    'mixer': 1,
    'counter': 2,
    'auto_oven': 0,
    'auto_mixer': 0,
    'conveyor': 0
  }

  return automationLevel >= (automationRequirements[equipmentType] || 999)
}

export function generateAutomationJob(
  equipmentId: string,
  recipeId: string,
  recipe: any,
  efficiency: number
): AutomationJob {
  const adjustedDuration = Math.floor(recipe.bakingTime / efficiency)
  const adjustedIngredients = recipe.ingredients.map((ing: any) => ({
    ...ing,
    quantity: Math.ceil(ing.quantity * (1 - (efficiency - 1) * 0.1))
  }))

  return {
    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
    equipmentId,
    recipeId,
    startTime: Date.now(),
    duration: adjustedDuration,
    status: 'queued',
    ingredients: adjustedIngredients,
    efficiency
  }
}

export function selectOptimalRecipe(
  availableRecipes: any[],
  inventory: any[],
  priorityMode: 'speed' | 'efficiency' | 'profit',
  currentOrders: any[]
): string | null {
  const craftableRecipes = availableRecipes.filter(recipe =>
    recipe.ingredients.every((ingredient: any) => {
      const inventoryItem = inventory.find(item => item.name === ingredient.name)
      return inventoryItem && inventoryItem.quantity >= ingredient.quantity
    })
  )

  if (craftableRecipes.length === 0) return null

  switch (priorityMode) {
    case 'speed':
      return craftableRecipes.reduce((fastest, recipe) =>
        recipe.bakingTime < fastest.bakingTime ? recipe : fastest
      ).id

    case 'profit':
      return craftableRecipes.reduce((mostProfitable, recipe) =>
        recipe.basePrice > mostProfitable.basePrice ? recipe : mostProfitable
      ).id

    case 'efficiency':
      // Prioritize recipes needed for current orders
      const neededRecipes = currentOrders.flatMap(order => order.items)
      const neededCraftable = craftableRecipes.filter(recipe =>
        neededRecipes.includes(recipe.name)
      )
      
      if (neededCraftable.length > 0) {
        return neededCraftable[0].id
      }
      
      return craftableRecipes[0].id

    default:
      return craftableRecipes[0].id
  }
}

export function updateConveyorBelt(
  belt: ConveyorBelt,
  deltaTime: number
): ConveyorBelt {
  const speed = belt.speed / 60 // convert to items per second
  const moveDistance = speed * deltaTime

  const updatedItems = belt.items.map(item => ({
    ...item,
    position: Math.min(1, item.position + moveDistance)
  }))

  // Remove items that reached the end
  const activeItems = updatedItems.filter(item => item.position < 1)

  return {
    ...belt,
    items: activeItems
  }
}

export function addItemToConveyor(
  belt: ConveyorBelt,
  item: Omit<ConveyorItem, 'position'>
): ConveyorBelt {
  if (belt.items.length >= belt.capacity) {
    return belt // Belt is full
  }

  const newItem: ConveyorItem = {
    ...item,
    position: 0
  }

  return {
    ...belt,
    items: [...belt.items, newItem]
  }
}
