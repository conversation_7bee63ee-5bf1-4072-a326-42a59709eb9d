import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

export function formatMoney(amount: number): string {
  return `$${amount.toLocaleString()}`
}

export function generateGameCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

export function calculateExperienceForLevel(level: number): number {
  return level * 100 + (level - 1) * 50
}

export function calculateLevelFromExperience(experience: number): number {
  let level = 1
  let totalExp = 0
  
  while (totalExp + calculateExperienceForLevel(level) <= experience) {
    totalExp += calculateExperienceForLevel(level)
    level++
  }
  
  return level
}
