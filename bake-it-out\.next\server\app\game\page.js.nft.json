{"version": 1, "files": ["../../webpack-runtime.js", "../../chunks/985.js", "../../chunks/976.js", "../../chunks/871.js", "page_client-reference-manifest.js", "../../../../src/contexts/GameContext.tsx", "../../../../package.json", "../../../../src/components/ui/Button.tsx", "../../../../src/components/game/Equipment.tsx", "../../../../src/components/game/Order.tsx", "../../../../src/components/game/RecipeModal.tsx", "../../../../src/components/game/ShopModal.tsx", "../../../../src/components/game/BakingModal.tsx", "../../../../src/components/game/NotificationSystem.tsx", "../../../../src/components/game/LevelUpModal.tsx", "../../../../src/components/game/AchievementsModal.tsx", "../../../../src/components/game/SkillTreeModal.tsx", "../../../../src/components/game/AutomationModal.tsx", "../../../../src/components/game/EquipmentShopModal.tsx", "../../../../src/components/game/SettingsModal.tsx", "../../../../src/components/game/BakeryManagerModal.tsx", "../../../../src/lib/automationSystem.ts", "../../../../src/lib/gameLogic.ts", "../../../../src/lib/progressionSystem.ts", "../../../../src/lib/saveSystem.ts"]}