(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{255:(e,t,s)=>{"use strict";function r(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return r}}),s(5155),s(7650),s(5744),s(589)},637:(e,t,s)=>{"use strict";s.d(t,{MultiplayerProvider:()=>d,K:()=>m});var r=s(5155),a=s(2115),l=s(4298),n=s(9509);class i{connect(){var e;null!=(e=this.socket)&&e.connected||(this.socket=(0,l.io)(n.env.NEXT_PUBLIC_SOCKET_URL||"http://localhost:3001",{transports:["websocket","polling"],timeout:2e4,forceNew:!0}),this.setupEventListeners())}setupEventListeners(){this.socket&&(this.socket.on("connect",()=>{console.log("Connected to multiplayer server"),this.isConnected=!0,this.reconnectAttempts=0}),this.socket.on("disconnect",e=>{console.log("Disconnected from multiplayer server:",e),this.isConnected=!1,"io server disconnect"===e&&this.handleReconnect()}),this.socket.on("connect_error",e=>{console.error("Connection error:",e),this.handleReconnect()}),this.socket.on("error",e=>{console.error("Socket error:",e)}))}handleReconnect(){this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,console.log("Attempting to reconnect (".concat(this.reconnectAttempts,"/").concat(this.maxReconnectAttempts,")...")),setTimeout(()=>{this.connect()},1e3*Math.pow(2,this.reconnectAttempts))):console.error("Max reconnection attempts reached")}createRoom(e){return new Promise((t,s)=>{var r;if(!(null==(r=this.socket)?void 0:r.connected))return void s(Error("Not connected to server"));this.socket.emit("create_room",e),this.socket.once("room_created",e=>{this.currentRoom=e,t(e)}),this.socket.once("error",e=>{s(Error(e.message))})})}joinRoom(e,t){return new Promise((s,r)=>{var a;if(!(null==(a=this.socket)?void 0:a.connected))return void r(Error("Not connected to server"));this.socket.emit("join_room",e,t),this.socket.once("room_joined",(e,t)=>{this.currentRoom=e,this.currentPlayer=t,s({room:e,player:t})}),this.socket.once("error",e=>{r(Error(e.message))})})}leaveRoom(){var e;(null==(e=this.socket)?void 0:e.connected)&&this.currentRoom&&(this.socket.emit("leave_room",this.currentRoom.id),this.currentRoom=null,this.currentPlayer=null)}sendPlayerAction(e){var t;(null==(t=this.socket)?void 0:t.connected)&&this.currentPlayer&&this.socket.emit("player_action",{...e,playerId:this.currentPlayer.id,timestamp:Date.now()})}sendMessage(e){var t;(null==(t=this.socket)?void 0:t.connected)&&this.currentPlayer&&this.socket.emit("send_message",{playerId:this.currentPlayer.id,playerName:this.currentPlayer.name,content:e,timestamp:Date.now()})}on(e,t){var s;null==(s=this.socket)||s.on(e,t)}off(e,t){var s;null==(s=this.socket)||s.off(e,t)}once(e,t){var s;null==(s=this.socket)||s.once(e,t)}isSocketConnected(){var e;return this.isConnected&&(null==(e=this.socket)?void 0:e.connected)===!0}getCurrentRoom(){return this.currentRoom}getCurrentPlayer(){return this.currentPlayer}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null,this.isConnected=!1,this.currentRoom=null,this.currentPlayer=null)}constructor(){this.socket=null,this.isConnected=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.currentRoom=null,this.currentPlayer=null,this.connect()}}let o=new i,c=(0,a.createContext)(void 0);function d(e){let{children:t}=e,[s,l]=(0,a.useState)(!1),[n,i]=(0,a.useState)(!1),[d,m]=(0,a.useState)(null),[u,x]=(0,a.useState)(null),[h,p]=(0,a.useState)(null),[g,y]=(0,a.useState)([]),[v,f]=(0,a.useState)("waiting"),[j,b]=(0,a.useState)(null),[N,w]=(0,a.useState)([]);(0,a.useEffect)(()=>{let e=()=>{l(!0),m(null)},t=()=>{l(!1),i(!1),x(null),p(null),y([])},s=e=>{m(e.message||"Connection error"),console.error("Multiplayer error:",e)},r=e=>{x(e),y(e.players),i(!0),f(e.gameState);let t=e.players.find(e=>e.isHost);t&&p(t)},a=(e,t)=>{x(e),p(t),y(e.players),i(!0),f(e.gameState)},n=()=>{x(null),p(null),y([]),i(!1),f("waiting"),b(null),w([])},c=e=>{x(e),y(e.players),f(e.gameState)},d=e=>{y(t=>[...t,e]),k("".concat(e.name," joined the room"))},u=e=>{y(t=>{let s=t.find(t=>t.id===e);return s&&k("".concat(s.name," left the room")),t.filter(t=>t.id!==e)})},h=e=>{f("playing"),b(e),k("Game started!")},g=e=>{b(t=>t?{...t,...e}:null)},v=e=>{console.log("Player action received:",e)},j=e=>{let t={id:Date.now().toString()+Math.random().toString(36).substring(2,11),playerId:e.playerId,playerName:e.playerName,content:e.content,timestamp:e.timestamp};w(e=>[...e,t])};return o.on("connect",e),o.on("disconnect",t),o.on("error",s),o.on("room_created",r),o.on("room_joined",a),o.on("room_left",n),o.on("room_updated",c),o.on("player_joined",d),o.on("player_left",u),o.on("game_started",h),o.on("game_state_update",g),o.on("player_action",v),o.on("message_received",j),l(o.isSocketConnected()),()=>{o.off("connect",e),o.off("disconnect",t),o.off("error",s),o.off("room_created",r),o.off("room_joined",a),o.off("room_left",n),o.off("room_updated",c),o.off("player_joined",d),o.off("player_left",u),o.off("game_started",h),o.off("game_state_update",g),o.off("player_action",v),o.off("message_received",j)}},[]);let k=e=>{let t={id:Date.now().toString()+Math.random().toString(36).substring(2,11),playerId:"system",playerName:"System",content:e,timestamp:Date.now()};w(e=>[...e,t])},C=(0,a.useCallback)(async(e,t)=>{try{m(null),await o.createRoom({...e,hostName:t.name,hostAvatar:t.avatar,hostLevel:t.level})}catch(e){throw m(e.message),e}},[]),_=(0,a.useCallback)(async(e,t)=>{try{m(null);let{room:s,player:r}=await o.joinRoom(e,t)}catch(e){throw m(e.message),e}},[]),S=(0,a.useCallback)(()=>{o.leaveRoom()},[]),P=(0,a.useCallback)(()=>{u&&(null==h?void 0:h.isHost)&&o.sendPlayerAction({type:"start_game",data:{roomId:u.id}})},[u,h]),R=(0,a.useCallback)(e=>{o.sendMessage(e)},[]),D=(0,a.useCallback)(e=>{o.sendPlayerAction(e)},[]),A=(0,a.useCallback)(e=>{h&&D({type:"player_ready",data:{ready:e}})},[h,D]),O=(0,a.useCallback)(e=>{(null==h?void 0:h.isHost)&&D({type:"kick_player",data:{playerId:e}})},[h,D]),M=(0,a.useCallback)(e=>{(null==h?void 0:h.isHost)&&D({type:"update_room_settings",data:{settings:e}})},[h,D]);return(0,r.jsx)(c.Provider,{value:{isConnected:s,isInRoom:n,connectionError:d,currentRoom:u,currentPlayer:h,players:g,gameState:v,sharedGameState:j,messages:N,createRoom:C,joinRoom:_,leaveRoom:S,startGame:P,sendMessage:R,sendPlayerAction:D,setPlayerReady:A,kickPlayer:O,updateRoomSettings:M},children:t})}function m(){let e=(0,a.useContext)(c);return void 0===e?(console.warn("useMultiplayer called outside of MultiplayerProvider, using fallback"),{isConnected:!1,connectionStatus:"disconnected",currentRoom:null,gameState:null,createRoom:async()=>{},joinRoom:async()=>{},leaveRoom:()=>{},sendChatMessage:()=>{},updateGameState:()=>{},setPlayerReady:()=>{}}):e}},2146:(e,t,s)=>{"use strict";function r(e){let{reason:t,children:s}=e;return s}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return r}}),s(5262)},2163:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(5155),a=s(3741),l=s(9283);function n(e){let{order:t,onAccept:s,onDecline:n,onComplete:i}=e,{t:o}=(0,l.o)();return(0,r.jsxs)("div",{className:"p-4 rounded-lg border ".concat((()=>{switch(t.status){case"pending":return"border-yellow-300 bg-yellow-50";case"accepted":case"in_progress":return"border-blue-300 bg-blue-50";case"completed":return"border-green-300 bg-green-50";case"failed":return"border-red-300 bg-red-50";default:return"border-gray-300 bg-gray-50"}})()),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-lg",children:(()=>{let e=["\uD83D\uDC69","\uD83D\uDC68","\uD83D\uDC75","\uD83D\uDC74","\uD83D\uDC67","\uD83D\uDC66"],s=t.customerName.length%e.length;return e[s]})()}),(0,r.jsx)("h3",{className:"font-medium text-gray-800",children:t.customerName})]}),(0,r.jsx)("span",{className:"text-sm font-semibold text-green-600",children:o("orders.reward",{amount:t.reward.toString()})})]}),(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:t.items.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("span",{children:"\uD83E\uDDC1"}),(0,r.jsx)("span",{children:e})]},t))}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["⏰ ",o("orders.timeLimit",{time:(e=>{let t=Math.floor(e/60);return"".concat(t,":").concat((e%60).toString().padStart(2,"0"))})(t.timeLimit)})]}),(0,r.jsx)("div",{className:"text-xs",title:"Difficulty: ".concat(t.difficulty,"/5"),children:"⭐".repeat(t.difficulty)+"☆".repeat(5-t.difficulty)})]}),"pending"===t.status&&(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(a.$,{size:"sm",variant:"success",onClick:()=>s(t.id),className:"flex-1",children:["✅ ",o("orders.accept")]}),(0,r.jsxs)(a.$,{size:"sm",variant:"danger",onClick:()=>n(t.id),className:"flex-1",children:["❌ ",o("orders.decline")]})]}),"accepted"===t.status&&(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-blue-600 text-sm font-medium mb-2",children:"\uD83D\uDCCB Order Accepted"}),(0,r.jsxs)(a.$,{size:"sm",variant:"primary",onClick:()=>i&&i(t.id),className:"w-full",children:["\uD83C\uDFAF ",o("orders.complete")]})]}),"in_progress"===t.status&&(0,r.jsxs)("div",{className:"text-center text-orange-600 text-sm font-medium",children:["\uD83D\uDD04 ",o("orders.inProgress")]}),"completed"===t.status&&(0,r.jsx)("div",{className:"text-center text-green-600 text-sm font-medium",children:"✅ Completed!"}),"failed"===t.status&&(0,r.jsx)("div",{className:"text-center text-red-600 text-sm font-medium",children:"❌ Failed"})]})}},2617:(e,t,s)=>{Promise.resolve().then(s.bind(s,4870))},3741:(e,t,s)=>{"use strict";s.d(t,{$:()=>a});var r=s(5155);s(2115);let a=e=>{let{variant:t="primary",size:s="md",className:a="",children:l,...n}=e,i=["font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2",{primary:"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500",secondary:"bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500",danger:"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500",success:"bg-green-600 hover:bg-green-700 text-white focus:ring-green-500"}[t],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"}[s],a].join(" ");return(0,r.jsx)("button",{className:i,...n,children:l})}},4054:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{bindSnapshot:function(){return n},createAsyncLocalStorage:function(){return l},createSnapshot:function(){return i}});let s=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class r{disable(){throw s}getStore(){}run(){throw s}exit(){throw s}enterWith(){throw s}static bind(e){return e}}let a="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function l(){return a?new a:new r}function n(e){return a?a.bind(e):r.bind(e)}function i(){return a?a.snapshot():function(e,...t){return e(...t)}}},4870:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(5155),a=s(2115),l=s(3741),n=s(9283),i=s(637);function o(e){let{isOpen:t,onClose:s}=e,{t:o}=(0,n.o)(),{isConnected:c,isInRoom:d,connectionError:m,currentRoom:u,currentPlayer:x,players:h,gameState:p,messages:g,createRoom:y,joinRoom:v,leaveRoom:f,startGame:j,sendMessage:b,setPlayerReady:N}=(0,i.K)(),[w,k]=(0,a.useState)("create"),[C,_]=(0,a.useState)(""),[S,P]=(0,a.useState)(""),[R,D]=(0,a.useState)(""),[A,O]=(0,a.useState)("cooperative"),[M,E]=(0,a.useState)(4),[L,$]=(0,a.useState)(""),[I,z]=(0,a.useState)(!1);if(!t)return null;let T=async()=>{if(S.trim()&&C.trim())try{await y({name:C,mode:A,maxPlayers:M,settings:{gameMode:A,difficulty:"medium",allowSpectators:!0}},{name:S,avatar:"\uD83D\uDC68‍\uD83C\uDF73",level:1}),k("room")}catch(e){console.error("Failed to create room:",e)}},F=async()=>{if(S.trim()&&R.trim())try{await v(R.toUpperCase(),{name:S,avatar:"\uD83D\uDC68‍\uD83C\uDF73",level:1}),k("room")}catch(e){console.error("Failed to join room:",e)}},H=()=>{L.trim()&&(b(L),$(""))},K=[{id:"create",name:o("multiplayer.createRoom"),icon:"\uD83C\uDFD7️"},{id:"join",name:o("multiplayer.joinRoom"),icon:"\uD83D\uDEAA"},...d?[{id:"room",name:o("multiplayer.room"),icon:"\uD83C\uDFE0"}]:[]];return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,r.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:o("multiplayer.lobby")}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"px-3 py-1 rounded-full text-sm ".concat(c?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:c?o("multiplayer.connected"):o("multiplayer.disconnected")}),(0,r.jsx)(l.$,{variant:"secondary",onClick:s,children:o("game.close")})]})]}),m&&(0,r.jsx)("div",{className:"mt-2 p-2 bg-red-100 text-red-800 rounded text-sm",children:o("multiplayer.connection.error",{error:m})})]}),(0,r.jsx)("div",{className:"border-b border-gray-200",children:(0,r.jsx)("div",{className:"flex space-x-0",children:K.map(e=>(0,r.jsxs)("button",{onClick:()=>k(e.id),className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(w===e.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.icon," ",e.name]},e.id))})}),(0,r.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["create"===w&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:o("multiplayer.yourName")}),(0,r.jsx)("input",{type:"text",value:S,onChange:e=>P(e.target.value),placeholder:o("multiplayer.enterName"),className:"w-full p-3 border rounded-lg",maxLength:20})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:o("multiplayer.roomName")}),(0,r.jsx)("input",{type:"text",value:C,onChange:e=>_(e.target.value),placeholder:o("multiplayer.enterRoomName"),className:"w-full p-3 border rounded-lg",maxLength:30})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:o("multiplayer.gameMode")}),(0,r.jsxs)("select",{value:A,onChange:e=>O(e.target.value),className:"w-full p-3 border rounded-lg",children:[(0,r.jsx)("option",{value:"cooperative",children:o("multiplayer.cooperative")}),(0,r.jsx)("option",{value:"competitive",children:o("multiplayer.competitive")})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:o("multiplayer.maxPlayers",{count:M.toString()})}),(0,r.jsx)("input",{type:"range",min:"2",max:"8",value:M,onChange:e=>E(parseInt(e.target.value)),className:"w-full"})]})]}),(0,r.jsx)(l.$,{variant:"primary",size:"lg",className:"w-full",onClick:T,disabled:!c||!S.trim()||!C.trim(),children:o("multiplayer.create.title")})]}),"join"===w&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:o("multiplayer.yourName")}),(0,r.jsx)("input",{type:"text",value:S,onChange:e=>P(e.target.value),placeholder:o("multiplayer.enterName"),className:"w-full p-3 border rounded-lg",maxLength:20})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:o("multiplayer.roomId")}),(0,r.jsx)("input",{type:"text",value:R,onChange:e=>D(e.target.value.toUpperCase()),placeholder:o("multiplayer.enterRoomId"),className:"w-full p-3 border rounded-lg font-mono",maxLength:6})]})]}),(0,r.jsx)(l.$,{variant:"primary",size:"lg",className:"w-full",onClick:F,disabled:!c||!S.trim()||!R.trim(),children:o("multiplayer.join.title")})]}),"room"===w&&u&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("h3",{className:"font-semibold text-blue-800",children:u.name}),(0,r.jsxs)("div",{className:"text-sm text-blue-600",children:[o("multiplayer.roomId"),": ",(0,r.jsx)("span",{className:"font-mono font-bold",children:u.id})]})]}),(0,r.jsx)("div",{className:"text-sm text-blue-700",children:o("multiplayer.room.info",{mode:u.mode,current:u.currentPlayers.toString(),max:u.maxPlayers.toString()})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:o("multiplayer.players",{count:h.length.toString()})}),(0,r.jsx)("div",{className:"space-y-2",children:h.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"text-2xl",children:e.avatar}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"font-medium",children:[e.name,e.isHost&&(0,r.jsx)("span",{className:"ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded",children:o("multiplayer.host")})]}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:o("multiplayer.level",{level:e.level.toString()})})]})]}),(0,r.jsx)("div",{className:"px-2 py-1 rounded text-xs ".concat(e.isReady?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"),children:e.isReady?o("common.ready"):o("common.notReady")})]},e.id))})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(l.$,{variant:I?"success":"secondary",onClick:()=>{let e=!I;z(e),N(e)},className:"flex-1",children:I?o("multiplayer.room.readyUp"):o("multiplayer.room.notReady")}),(null==x?void 0:x.isHost)&&(0,r.jsx)(l.$,{variant:"primary",onClick:()=>{(null==x?void 0:x.isHost)&&j()},disabled:!h.every(e=>e.isReady)||h.length<2,children:o("multiplayer.room.startGame")}),(0,r.jsx)(l.$,{variant:"secondary",onClick:()=>{f(),k("create"),z(!1)},children:o("multiplayer.room.leaveRoom")})]}),(0,r.jsxs)("div",{className:"border-t pt-4",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:o("multiplayer.chat")}),(0,r.jsx)("div",{className:"bg-gray-50 p-3 rounded-lg h-32 overflow-y-auto mb-3",children:g.map(e=>(0,r.jsxs)("div",{className:"text-sm mb-1",children:[(0,r.jsxs)("span",{className:"font-medium ".concat("system"===e.playerId?"text-blue-600":"text-gray-800"),children:[e.playerName,":"]}),(0,r.jsx)("span",{className:"ml-2",children:e.content})]},e.id))}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("input",{type:"text",value:L,onChange:e=>$(e.target.value),onKeyDown:e=>"Enter"===e.key&&H(),placeholder:o("multiplayer.typeMessage"),className:"flex-1 p-2 border rounded",maxLength:100}),(0,r.jsx)(l.$,{size:"sm",onClick:H,children:o("common.send")})]})]})]})]})]})})}var c=s(9419),d=s(2163);function m(e){let{isOpen:t,onClose:s}=e,{t:o}=(0,n.o)(),{currentRoom:m,currentPlayer:u,players:x,gameState:h,sharedGameState:p,sendPlayerAction:g,leaveRoom:y}=(0,i.K)(),[v,f]=(0,a.useState)("game");if(!t||!m||"playing"!==h)return null;let j=(e,t)=>{g({type:"use_equipment",data:{equipmentId:e,equipmentName:t,playerId:null==u?void 0:u.id}})},b=(e,t)=>{g({type:"order_action",data:{orderId:e,action:t,playerId:null==u?void 0:u.id}})},N=[{id:"game",name:o("multiplayer.game.tabs.game"),icon:"\uD83C\uDFAE"},{id:"players",name:o("multiplayer.game.tabs.players"),icon:"\uD83D\uDC65"},{id:"chat",name:o("multiplayer.game.tabs.chat"),icon:"\uD83D\uDCAC"}];return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:o("multiplayer.game.title",{roomName:m.name})}),(0,r.jsxs)("p",{className:"text-gray-600",children:[o("multiplayer.game.mode",{mode:m.mode})," • ",o("multiplayer.game.playersCount",{count:x.length.toString()})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,r.jsx)("span",{className:"text-green-800 text-sm",children:o("multiplayer.game.playing")})}),(0,r.jsx)(l.$,{variant:"secondary",onClick:()=>{y(),s()},children:o("multiplayer.game.leaveGame")})]})]})}),(0,r.jsx)("div",{className:"border-b border-gray-200",children:(0,r.jsx)("div",{className:"flex space-x-0",children:N.map(e=>(0,r.jsxs)("button",{onClick:()=>f(e.id),className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(v===e.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.icon," ",e.name]},e.id))})}),(0,r.jsxs)("div",{className:"p-6 max-h-[70vh] overflow-y-auto",children:["game"===v&&(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"lg:col-span-2",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:o("multiplayer.sharedKitchen")}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[{id:"oven1",name:"Shared Oven",type:"oven",isActive:!1,level:1,efficiency:1,automationLevel:0},{id:"mixer1",name:"Shared Mixer",type:"mixer",isActive:!1,level:1,efficiency:1,automationLevel:0},{id:"counter1",name:"Shared Counter",type:"counter",isActive:!1,level:1,efficiency:1,automationLevel:0}].map(e=>(0,r.jsx)(c.$,{equipment:e,onClick:j},e.id))})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mt-6",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:o("multiplayer.sharedOrders")}),(0,r.jsx)("div",{className:"space-y-4",children:[{id:"1",customerName:"Shared Customer",items:["Chocolate Chip Cookies"],timeLimit:300,reward:50,status:"pending",difficulty:1}].map(e=>(0,r.jsx)(d.p,{order:e,onAccept:e=>b(e,"accept"),onDecline:e=>b(e,"decline"),onComplete:e=>b(e,"complete")},e.id))})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:o("multiplayer.sharedInventory")}),(0,r.jsx)("div",{className:"space-y-3",children:[{name:"Flour",quantity:20,cost:2},{name:"Sugar",quantity:15,cost:3},{name:"Eggs",quantity:12,cost:4}].map((e,t)=>(0,r.jsx)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-gray-800",children:e.name}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["Qty: ",e.quantity]})]})},t))})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:o("multiplayer.teamStats")}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:o("multiplayer.ordersCompleted")}),(0,r.jsx)("span",{className:"font-medium",children:"0"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:o("multiplayer.totalRevenue")}),(0,r.jsx)("span",{className:"font-medium",children:"$0"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:o("multiplayer.teamExperience")}),(0,r.jsx)("span",{className:"font-medium",children:"0 XP"})]})]})]})]})]}),"players"===v&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-orange-800",children:o("multiplayer.players",{count:x.length.toString()})}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:x.map(e=>(0,r.jsxs)("div",{className:"p-4 rounded-lg border-2 ".concat(e.id===(null==u?void 0:u.id)?"border-orange-400 bg-orange-50":"border-gray-300 bg-white"),children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,r.jsx)("span",{className:"text-3xl",children:e.avatar}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"font-semibold text-gray-800",children:[e.name,e.id===(null==u?void 0:u.id)&&(0,r.jsx)("span",{className:"ml-2 text-sm text-orange-600",children:o("multiplayer.you")})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[o("multiplayer.level",{level:e.level.toString()}),e.isHost&&(0,r.jsx)("span",{className:"ml-2 bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs",children:o("multiplayer.host")})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:o("multiplayer.status")}),(0,r.jsx)("span",{className:"text-green-600",children:o("multiplayer.online")})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:o("multiplayer.contribution")}),(0,r.jsx)("span",{className:"font-medium",children:"0 orders"})]})]})]},e.id))})]}),"chat"===v&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-orange-800",children:o("multiplayer.teamChat")}),(0,r.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg h-64 overflow-y-auto",children:(0,r.jsx)("div",{className:"text-sm text-gray-500 text-center",children:o("multiplayer.chatPlaceholder")})}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("input",{type:"text",placeholder:o("multiplayer.typeMessage"),className:"flex-1 p-3 border rounded-lg"}),(0,r.jsx)(l.$,{children:o("common.send")})]})]})]}),(0,r.jsx)("div",{className:"p-4 bg-blue-50 border-t border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"text-sm text-blue-700",children:"cooperative"===m.mode?o("multiplayer.mode.cooperative.description"):o("multiplayer.mode.competitive.description")}),(0,r.jsx)("div",{className:"text-sm text-blue-600",children:o("multiplayer.gameTime",{time:"00:00"})})]})})]})})}var u=s(6645);let x=s.n(u)()(()=>s.e(112).then(s.bind(s,8731)),{loadableGenerated:{webpack:()=>[8731]},ssr:!1});function h(){let{language:e,setLanguage:t,t:s}=(0,n.o)(),{gameState:c}=(0,i.K)(),[d,u]=(0,a.useState)(!1),[h,p]=(0,a.useState)(!1),[g,y]=(0,a.useState)(!1);return g?(0,r.jsx)(x,{}):(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-orange-100 to-yellow-100 flex items-center justify-center",children:[(0,r.jsxs)("div",{className:"text-center space-y-8 p-8",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("h1",{className:"text-6xl font-bold text-orange-800 mb-4",children:["\uD83E\uDD56 ",s("game.title")]}),(0,r.jsx)("p",{className:"text-xl text-orange-700 max-w-2xl mx-auto",children:s("game.subtitle")})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)(l.$,{size:"lg",className:"text-lg px-8 py-4",onClick:()=>y(!0),children:s("game.play")}),(0,r.jsx)(l.$,{variant:"secondary",size:"lg",className:"text-lg px-8 py-4",onClick:()=>u(!0),children:s("game.multiplayer")})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)(l.$,{variant:"en"===e?"primary":"secondary",size:"md",onClick:()=>t("en"),children:s("game.english")}),(0,r.jsx)(l.$,{variant:"cs"===e?"primary":"secondary",size:"md",onClick:()=>t("cs"),children:s("game.czech")})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center",children:[(0,r.jsx)("div",{className:"text-3xl mb-3",children:"\uD83C\uDFEA"}),(0,r.jsx)("h3",{className:"font-semibold text-orange-800 mb-2",children:s("features.manage.title")}),(0,r.jsx)("p",{className:"text-orange-700 text-sm",children:s("features.manage.description")})]}),(0,r.jsxs)("div",{className:"bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center",children:[(0,r.jsx)("div",{className:"text-3xl mb-3",children:"\uD83D\uDCC8"}),(0,r.jsx)("h3",{className:"font-semibold text-orange-800 mb-2",children:s("features.levelup.title")}),(0,r.jsx)("p",{className:"text-orange-700 text-sm",children:s("features.levelup.description")})]}),(0,r.jsxs)("div",{className:"bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center",children:[(0,r.jsx)("div",{className:"text-3xl mb-3",children:"\uD83D\uDC65"}),(0,r.jsx)("h3",{className:"font-semibold text-orange-800 mb-2",children:s("features.multiplayer.title")}),(0,r.jsx)("p",{className:"text-orange-700 text-sm",children:s("features.multiplayer.description")})]})]}),(0,r.jsx)("div",{className:"mt-8 text-sm text-orange-600",children:(0,r.jsx)("p",{children:s("status.development")})})]}),(0,r.jsx)(o,{isOpen:d,onClose:()=>u(!1)}),(0,r.jsx)(m,{isOpen:h||"playing"===c,onClose:()=>p(!1)})]})}},5744:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return r.workAsyncStorageInstance}});let r=s(7828)},6645:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let r=s(8229)._(s(7357));function a(e,t){var s;let a={};"function"==typeof e&&(a.loader=e);let l={...a,...t};return(0,r.default)({...l,modules:null==(s=l.loadableGenerated)?void 0:s.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7357:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let r=s(5155),a=s(2115),l=s(2146);function n(e){return{default:e&&"default"in e?e.default:e}}s(255);let i={loader:()=>Promise.resolve(n(()=>null)),loading:null,ssr:!0},o=function(e){let t={...i,...e},s=(0,a.lazy)(()=>t.loader().then(n)),o=t.loading;function c(e){let n=o?(0,r.jsx)(o,{isLoading:!0,pastDelay:!0,error:null}):null,i=!t.ssr||!!t.loading,c=i?a.Suspense:a.Fragment,d=t.ssr?(0,r.jsxs)(r.Fragment,{children:[null,(0,r.jsx)(s,{...e})]}):(0,r.jsx)(l.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(s,{...e})});return(0,r.jsx)(c,{...i?{fallback:n}:{},children:d})}return c.displayName="LoadableComponent",c}},7828:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return r}});let r=(0,s(4054).createAsyncLocalStorage)()},9419:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var r=s(5155),a=s(9283);function l(e){let{equipment:t,onClick:s}=e,{t:l}=(0,a.o)();return(0,r.jsx)("div",{className:"p-4 rounded-lg border-2 cursor-pointer transition-all ".concat(t.isActive?"border-green-400 bg-green-50":"border-gray-200 bg-gray-50 hover:border-orange-300 hover:bg-orange-50"),onClick:()=>!t.isActive&&s(t.id,t.name),children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl mb-2",children:(e=>{switch(e){case"oven":return"\uD83D\uDD25";case"mixer":return"\uD83E\uDD44";case"counter":return"\uD83C\uDF7D️";default:return"⚙️"}})(t.type)}),(0,r.jsx)("h3",{className:"font-medium text-gray-800",children:t.name}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["Level ",t.level]}),t.isActive&&t.timeRemaining?(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("div",{className:"text-sm text-green-600",children:l("kitchen.making",{recipe:t.currentRecipe||""})}),(0,r.jsx)("div",{className:"text-lg font-mono text-green-700",children:(e=>{let t=Math.floor(e/60);return"".concat(t,":").concat((e%60).toString().padStart(2,"0"))})(t.timeRemaining)}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,r.jsx)("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-1000",style:{width:"".concat(100-t.timeRemaining/60*100,"%")}})})]}):(0,r.jsx)("div",{className:"text-sm text-gray-500 mt-2",children:t.isActive?"Busy":l("kitchen.clickToUse")})]})})}}},e=>{e.O(0,[298,283,441,964,358],()=>e(e.s=2617)),_N_E=e.O()}]);