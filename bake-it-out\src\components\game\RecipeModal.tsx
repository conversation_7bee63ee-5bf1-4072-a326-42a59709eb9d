'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Recipe, getAvailableRecipes } from '@/lib/gameLogic'
import { useGame } from '@/contexts/GameContext'
import { useLanguage } from '@/contexts/LanguageContext'

interface RecipeModalProps {
  isOpen: boolean
  onClose: () => void
}

export function RecipeModal({ isOpen, onClose }: RecipeModalProps) {
  const { player, inventory } = useGame()
  const { t } = useLanguage()
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  
  if (!isOpen) return null

  const availableRecipes = getAvailableRecipes(player.level)
  const filteredRecipes = selectedCategory === 'all' 
    ? availableRecipes 
    : availableRecipes.filter(recipe => recipe.category === selectedCategory)

  const canCraft = (recipe: Recipe) => {
    return recipe.ingredients.every(ingredient => {
      const inventoryItem = inventory.find(item => item.name === ingredient.name)
      return inventoryItem && inventoryItem.quantity >= ingredient.quantity
    })
  }

  const getDifficultyStars = (difficulty: number) => {
    return '⭐'.repeat(difficulty) + '☆'.repeat(5 - difficulty)
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const categories = [
    { id: 'all', name: t('recipes.all'), icon: '🍽️' },
    { id: 'cookies', name: t('recipes.cookies'), icon: '🍪' },
    { id: 'cakes', name: t('recipes.cakes'), icon: '🧁' },
    { id: 'bread', name: t('recipes.bread'), icon: '🍞' },
    { id: 'pastries', name: t('recipes.pastries'), icon: '🥐' }
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-orange-800">{t('modal.recipes.title')}</h2>
            <Button variant="secondary" onClick={onClose}>
              {t('game.close')}
            </Button>
          </div>
        </div>

        <div className="p-6">
          {/* Category Filter */}
          <div className="flex flex-wrap gap-2 mb-6">
            {categories.map(category => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? 'primary' : 'secondary'}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
              >
                {category.icon} {category.name}
              </Button>
            ))}
          </div>

          {/* Recipes Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto">
            {filteredRecipes.map(recipe => (
              <div
                key={recipe.id}
                className={`p-4 rounded-lg border-2 ${
                  canCraft(recipe)
                    ? 'border-green-300 bg-green-50'
                    : 'border-gray-300 bg-gray-50'
                }`}
              >
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-semibold text-gray-800">{recipe.name}</h3>
                  <span className="text-sm text-green-600">${recipe.basePrice}</span>
                </div>

                <div className="text-xs text-gray-500 mb-2">
                  {getDifficultyStars(recipe.difficulty)} • ⏱️ {formatTime(recipe.bakingTime)}
                </div>

                <div className="space-y-1 mb-3">
                  <div className="text-sm font-medium text-gray-700">{t('recipes.ingredients')}</div>
                  {recipe.ingredients.map((ingredient, index) => {
                    const inventoryItem = inventory.find(item => item.name === ingredient.name)
                    const hasEnough = inventoryItem && inventoryItem.quantity >= ingredient.quantity

                    return (
                      <div
                        key={index}
                        className={`text-xs flex justify-between ${
                          hasEnough ? 'text-green-600' : 'text-red-600'
                        }`}
                      >
                        <span>{ingredient.name}</span>
                        <span>
                          {ingredient.quantity}
                          {inventoryItem && (
                            <span className="ml-1">
                              ({inventoryItem.quantity} available)
                            </span>
                          )}
                        </span>
                      </div>
                    )
                  })}
                </div>

                <div className="text-xs text-gray-500">
                  {t('recipes.unlockLevel', { level: recipe.unlockLevel.toString() })}
                </div>

                {canCraft(recipe) && (
                  <div className="mt-2">
                    <Button size="sm" variant="success" className="w-full">
                      {t('recipes.canCraft')}
                    </Button>
                  </div>
                )}
              </div>
            ))}
          </div>

          {filteredRecipes.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">📝</div>
              <p>{t('recipes.noRecipes')}</p>
              <p className="text-sm">{t('recipes.levelUpToUnlock')}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
