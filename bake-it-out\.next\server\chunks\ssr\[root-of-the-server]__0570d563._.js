module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/contexts/LanguageContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "LanguageProvider": ()=>LanguageProvider,
    "useLanguage": ()=>useLanguage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const LanguageContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Comprehensive translations object
const translations = {
    en: {
        // Main game
        'game.title': 'Bake It Out',
        'game.subtitle': 'Master the art of bakery management in this engaging multiplayer game. Complete orders, unlock recipes, automate your processes, and compete with friends!',
        'game.play': '🎮 Start Playing',
        'game.multiplayer': '👥 Multiplayer',
        'game.english': '🇺🇸 English',
        'game.czech': '🇨🇿 Čeština',
        'game.home': '🏠 Home',
        'game.close': '✕ Close',
        'game.continue': '🚀 Continue Playing',
        // Features
        'features.manage.title': 'Manage Your Bakery',
        'features.manage.description': 'Take orders, bake delicious goods, and serve happy customers',
        'features.levelup.title': 'Level Up & Automate',
        'features.levelup.description': 'Unlock new recipes, buy equipment, and automate your processes',
        'features.multiplayer.title': 'Play Together',
        'features.multiplayer.description': 'Cooperative and competitive multiplayer modes with friends',
        'status.development': '🚧 Game in Development - Phase 5: Multilayer Support! 🚧',
        // Game interface
        'ui.level': 'Level {{level}}',
        'ui.money': '${{amount}}',
        'ui.experience': 'XP: {{current}}/{{max}}',
        'ui.skillPoints': 'SP: {{points}}',
        'ui.achievements': '🏆 Achievements',
        'ui.skills': '🌟 Skills',
        'ui.automation': '🤖 Automation',
        // Kitchen
        'kitchen.title': '🏪 Kitchen',
        'kitchen.clickToUse': 'Click to use',
        'kitchen.making': 'Making: {{recipe}}',
        'kitchen.timeRemaining': 'Time: {{time}}',
        // Inventory
        'inventory.title': '📦 Inventory',
        'inventory.quantity': 'Qty: {{qty}}',
        'inventory.cost': '${{cost}} each',
        // Orders
        'orders.title': '📋 Orders',
        'orders.newOrder': '+ New Order',
        'orders.accept': 'Accept',
        'orders.decline': 'Decline',
        'orders.complete': 'Complete',
        'orders.inProgress': 'In Progress',
        'orders.timeLimit': 'Time: {{time}}',
        'orders.reward': '${{amount}}',
        'orders.customer': 'Customer: {{name}}',
        // Quick Actions
        'actions.title': '⚡ Quick Actions',
        'actions.buyIngredients': '🛒 Buy Ingredients',
        'actions.viewRecipes': '📖 View Recipes',
        'actions.equipmentShop': '🔧 Equipment Shop',
        // Modals
        'modal.recipes.title': '📖 Recipe Book',
        'modal.shop.title': '🛒 Ingredient Shop',
        'modal.baking.title': '🔥 {{equipment}} - Select Recipe',
        'modal.achievements.title': '🏆 Achievements',
        'modal.skills.title': '🌟 Skill Tree',
        'modal.automation.title': '🤖 Automation Control',
        'modal.equipmentShop.title': '🏪 Equipment Shop',
        'modal.settings.title': '⚙️ Settings',
        'modal.bakeries.title': '🏪 Bakery Manager',
        'modal.levelUp.title': 'Level Up!',
        'modal.levelUp.subtitle': 'You reached Level {{level}}!',
        // Recipe Modal
        'recipes.all': 'All',
        'recipes.cookies': 'Cookies',
        'recipes.cakes': 'Cakes',
        'recipes.bread': 'Bread',
        'recipes.pastries': 'Pastries',
        'recipes.ingredients': 'Ingredients:',
        'recipes.difficulty': 'Difficulty:',
        'recipes.time': 'Time:',
        'recipes.canCraft': '✅ Can Craft',
        'recipes.unlockLevel': 'Unlocked at Level {{level}}',
        'recipes.noRecipes': 'No recipes available in this category.',
        'recipes.levelUpToUnlock': 'Level up to unlock more recipes!',
        // Shop Modal
        'shop.currentStock': 'Current stock: {{quantity}}',
        'shop.buy': 'Buy',
        'shop.tooExpensive': 'Too Expensive',
        'shop.tips.title': '💡 Shopping Tips',
        'shop.tips.bulk': '• Buy ingredients in bulk to save time',
        'shop.tips.stock': '• Keep an eye on your stock levels',
        'shop.tips.rare': '• Some recipes require rare ingredients',
        'shop.tips.prices': '• Prices may vary based on availability',
        // Baking Modal
        'baking.selectRecipe': 'Select Recipe',
        'baking.noRecipes': 'No recipes available',
        'baking.noIngredients': 'You don\'t have enough ingredients to craft any recipes.',
        'baking.buyIngredients': 'Buy Ingredients',
        'baking.startBaking': '🔥 Start Baking',
        'baking.instructions': '📋 Baking Instructions for {{recipe}}',
        'baking.expectedReward': 'Expected reward: ${{amount}}',
        'baking.makesSure': 'Make sure you have all ingredients before starting!',
        // Achievements Modal
        'achievements.completed': '{{completed}} of {{total}} achievements completed',
        'achievements.overallProgress': 'Overall Progress',
        'achievements.progress': 'Progress',
        'achievements.reward': 'Reward:',
        'achievements.noAchievements': 'No achievements in this category.',
        // Skills Modal
        'skills.availablePoints': 'Available Skill Points: {{points}}',
        'skills.efficiency': 'Efficiency',
        'skills.automation': 'Automation',
        'skills.quality': 'Quality',
        'skills.business': 'Business',
        'skills.effects': 'Effects:',
        'skills.requires': 'Requires: {{requirements}}',
        'skills.requiresLevel': 'Requires Level {{level}}',
        'skills.maxed': '✅ Maxed',
        'skills.upgrade': '⬆️ Upgrade ({{cost}} SP)',
        'skills.locked': '🔒 Locked',
        'skills.noSkills': 'No skills in this category.',
        'skills.tips.title': '💡 Skill Tips',
        'skills.tips.earnPoints': '• Earn skill points by leveling up (1 point every 2 levels)',
        'skills.tips.prerequisites': '• Some skills require other skills to be unlocked first',
        'skills.tips.playstyle': '• Focus on skills that match your playstyle',
        'skills.tips.efficiency': '• Efficiency skills help with resource management',
        // Automation Modal
        'automation.masterControl': '🎛️ Master Control',
        'automation.enableAutomation': 'Enable Automation',
        'automation.autoStart': 'Auto-start Equipment',
        'automation.priorityMode': '🎯 Priority Mode',
        'automation.efficiency': 'Efficiency (Orders First)',
        'automation.profit': 'Profit (Highest Value)',
        'automation.speed': 'Speed (Fastest Recipes)',
        'automation.priorityDescription': 'How automation chooses what to bake',
        'automation.performance': '⚡ Performance',
        'automation.maxJobs': 'Max Concurrent Jobs: {{jobs}}',
        'automation.safety': '🛡️ Safety',
        'automation.stopWhenLow': 'Stop when ingredients below: {{threshold}}',
        'automation.upgrades': '💡 Automation Upgrades',
        'automation.upgradesDescription': 'Improve your automation efficiency, speed, and intelligence with these upgrades.',
        'automation.purchase': 'Purchase',
        'automation.noUpgrades': 'No upgrades available at your current level.',
        'automation.levelUpForUpgrades': 'Level up to unlock more automation upgrades!',
        'automation.automatedEquipment': 'Automated Equipment',
        'automation.activeUpgrades': 'Active Upgrades',
        'automation.automationStatus': 'Automation Status',
        'automation.equipmentStatus': '🏭 Equipment Status',
        'automation.running': 'Running',
        'automation.idle': 'Idle',
        'automation.noAutomatedEquipment': 'No automated equipment available.',
        'automation.purchaseAutoEquipment': 'Purchase auto-equipment from the shop to get started!',
        // Equipment Shop Modal
        'equipmentShop.upgradeYourBakery': 'Upgrade your bakery with professional equipment',
        'equipmentShop.basic': 'Basic',
        'equipmentShop.automated': 'Automated',
        'equipmentShop.advanced': 'Advanced',
        'equipmentShop.efficiency': 'Efficiency: {{efficiency}}x',
        'equipmentShop.automation': 'Automation:',
        'equipmentShop.unlockLevel': 'Unlock Level: {{level}}',
        'equipmentShop.purchase': '💰 Purchase',
        'equipmentShop.noEquipment': 'No equipment available in this category.',
        'equipmentShop.levelUpForEquipment': 'Level up to unlock more equipment!',
        'equipmentShop.tips.title': '💡 Equipment Tips',
        'equipmentShop.tips.automated': '• Automated equipment can run without your supervision',
        'equipmentShop.tips.efficiency': '• Higher efficiency means faster production and better quality',
        'equipmentShop.tips.conveyor': '• Conveyor belts connect equipment for seamless workflow',
        'equipmentShop.tips.advanced': '• Advanced equipment unlocks at higher levels',
        // Level Up Modal
        'levelUp.levelRewards': '🎁 Level Rewards',
        'levelUp.whatsNext': '💡 What\'s Next?',
        'levelUp.checkRecipes': '• Check out new recipes in your recipe book',
        'levelUp.visitShop': '• Visit the shop for new equipment',
        'levelUp.challengingOrders': '• Take on more challenging orders',
        'levelUp.investSkills': '• Invest in skill upgrades',
        // Settings Modal
        'settings.title': '⚙️ Settings',
        'settings.general': 'General',
        'settings.audio': 'Audio',
        'settings.graphics': 'Graphics',
        'settings.save': 'Save & Data',
        'settings.language': '🌍 Language',
        'settings.gameplay': '🎮 Gameplay',
        'settings.notifications': 'Enable Notifications',
        'settings.tutorials': 'Show Tutorials',
        'settings.animationSpeed': 'Animation Speed',
        'settings.sound': 'Sound Effects',
        'settings.music': 'Background Music',
        'settings.quality': '🎨 Graphics Quality',
        'settings.autoSave': '💾 Auto-Save',
        'settings.enableAutoSave': 'Enable Auto-Save',
        'settings.dataManagement': '📁 Data Management',
        'settings.exportSave': '📤 Export Save',
        'settings.importSave': '📥 Import Save',
        'settings.cloudSync': '☁️ Cloud Sync',
        'settings.cloudSyncDescription': 'Cloud sync allows you to save your progress online and play across multiple devices.',
        'settings.comingSoon': 'Coming Soon',
        // Bakery Manager Modal
        'bakeries.title': '🏪 Bakery Manager',
        'bakeries.subtitle': 'Manage your bakery empire',
        'bakeries.owned': 'My Bakeries',
        'bakeries.available': 'Available',
        'bakeries.current': 'Current',
        'bakeries.level': 'Level',
        'bakeries.specialization': 'Specialization',
        'bakeries.equipment': 'Equipment',
        'bakeries.orders': 'Active Orders',
        'bakeries.switchTo': 'Switch To',
        'bakeries.noOwned': 'You don\'t own any bakeries yet.',
        'bakeries.purchase': '💰 Purchase',
        'bakeries.tooExpensive': '💸 Too Expensive',
        'bakeries.allOwned': 'You own all available bakeries!',
        'bakeries.tips': '💡 Bakery Tips',
        'bakeries.tip1': 'Each bakery specializes in different products for bonus efficiency',
        'bakeries.tip2': 'Switch between bakeries to manage multiple locations',
        'bakeries.tip3': 'Specialized bakeries attract customers looking for specific items',
        'bakeries.tip4': 'Upgrade each bakery independently for maximum profit',
        // Notifications
        'notifications.orderAccepted': 'Order Accepted',
        'notifications.orderAcceptedMessage': 'You have accepted a new order!',
        'notifications.orderCompleted': 'Order Completed!',
        'notifications.orderCompletedMessage': 'You earned ${{reward}} and gained experience!',
        'notifications.orderDeclined': 'Order Declined',
        'notifications.orderDeclinedMessage': 'Order has been removed from your queue.',
        'notifications.bakeryPurchased': 'Bakery Purchased!',
        'notifications.bakeryPurchasedMessage': 'You now own {{name}}!',
        'notifications.bakerySwitched': 'Bakery Switched',
        'notifications.bakerySwitchedMessage': 'Switched to {{name}}',
        // Common buttons and actions
        'common.accept': 'Accept',
        'common.decline': 'Decline',
        'common.complete': 'Complete',
        'common.purchase': 'Purchase',
        'common.upgrade': 'Upgrade',
        'common.cancel': 'Cancel',
        'common.confirm': 'Confirm',
        'common.save': 'Save',
        'common.load': 'Load',
        'common.delete': 'Delete',
        'common.edit': 'Edit',
        'common.back': 'Back',
        'common.next': 'Next',
        'common.previous': 'Previous',
        'common.yes': 'Yes',
        'common.no': 'No',
        'common.create': 'Create',
        'common.join': 'Join',
        'common.leave': 'Leave',
        'common.start': 'Start',
        'common.ready': 'Ready',
        'common.notReady': 'Not Ready',
        'common.send': 'Send',
        // Multiplayer
        'multiplayer.lobby': '👥 Multiplayer Lobby',
        'multiplayer.connected': '🟢 Connected',
        'multiplayer.disconnected': '🔴 Disconnected',
        'multiplayer.createRoom': 'Create Room',
        'multiplayer.joinRoom': 'Join Room',
        'multiplayer.room': 'Room',
        'multiplayer.yourName': 'Your Name',
        'multiplayer.enterName': 'Enter your name',
        'multiplayer.roomName': 'Room Name',
        'multiplayer.enterRoomName': 'Enter room name',
        'multiplayer.gameMode': 'Game Mode',
        'multiplayer.cooperative': '🤝 Cooperative',
        'multiplayer.competitive': '⚔️ Competitive',
        'multiplayer.maxPlayers': 'Max Players: {{count}}',
        'multiplayer.roomId': 'Room ID',
        'multiplayer.enterRoomId': 'Enter room ID',
        'multiplayer.players': 'Players ({{count}})',
        'multiplayer.host': 'HOST',
        'multiplayer.level': 'Level {{level}}',
        'multiplayer.chat': 'Chat',
        'multiplayer.typeMessage': 'Type a message...',
        'multiplayer.gameTime': 'Game Time: {{time}}',
        'multiplayer.teamStats': '📊 Team Stats',
        'multiplayer.ordersCompleted': 'Orders Completed:',
        'multiplayer.totalRevenue': 'Total Revenue:',
        'multiplayer.teamExperience': 'Team Experience:',
        'multiplayer.sharedKitchen': '🏪 Shared Kitchen',
        'multiplayer.sharedOrders': '📋 Shared Orders',
        'multiplayer.sharedInventory': '📦 Shared Inventory',
        'multiplayer.contribution': 'Contribution:',
        'multiplayer.online': '🟢 Online',
        'multiplayer.status': 'Status:',
        'multiplayer.you': '(You)',
        'multiplayer.teamChat': '💬 Team Chat',
        'multiplayer.chatPlaceholder': 'Chat messages will appear here...',
        // Multiplayer game modes
        'multiplayer.mode.cooperative.description': '🤝 Cooperative Mode: Work together to complete orders and grow your shared bakery!',
        'multiplayer.mode.competitive.description': '⚔️ Competitive Mode: Compete against other players to complete the most orders!',
        // Multiplayer game interface
        'multiplayer.game.title': '🎮 Multiplayer Game - {{roomName}}',
        'multiplayer.game.mode': 'Mode: {{mode}}',
        'multiplayer.game.playersCount': 'Players: {{count}}',
        'multiplayer.game.playing': '🟢 Playing',
        'multiplayer.game.leaveGame': '🚪 Leave Game',
        'multiplayer.game.tabs.game': 'Game',
        'multiplayer.game.tabs.players': 'Players',
        'multiplayer.game.tabs.chat': 'Chat',
        // Room creation and joining
        'multiplayer.create.title': '🏗️ Create Room',
        'multiplayer.join.title': '🚪 Join Room',
        'multiplayer.room.info': 'Mode: {{mode}} • Players: {{current}}/{{max}}',
        'multiplayer.room.readyUp': '✅ Ready',
        'multiplayer.room.notReady': '⏳ Not Ready',
        'multiplayer.room.startGame': '🚀 Start Game',
        'multiplayer.room.leaveRoom': '🚪 Leave',
        // Connection states
        'multiplayer.connection.connecting': 'Connecting...',
        'multiplayer.connection.reconnecting': 'Reconnecting...',
        'multiplayer.connection.failed': 'Connection failed',
        'multiplayer.connection.error': '⚠️ {{error}}',
        // System messages
        'multiplayer.system.playerJoined': '{{name}} joined the room',
        'multiplayer.system.playerLeft': '{{name}} left the room',
        'multiplayer.system.gameStarted': 'Game started!',
        'multiplayer.system.gameEnded': 'Game ended!',
        'multiplayer.system.roomCreated': 'Room created successfully',
        'multiplayer.system.roomJoined': 'Joined room successfully'
    },
    cs: {
        // Main game
        'game.title': 'Bake It Out',
        'game.subtitle': 'Ovládněte umění řízení pekárny v této poutavé multiplayerové hře. Plňte objednávky, odemykejte recepty, automatizujte procesy a soutěžte s přáteli!',
        'game.play': '🎮 Začít hrát',
        'game.multiplayer': '👥 Multiplayer',
        'game.english': '🇺🇸 English',
        'game.czech': '🇨🇿 Čeština',
        'game.home': '🏠 Domů',
        'game.close': '✕ Zavřít',
        'game.continue': '🚀 Pokračovat ve hře',
        // Features
        'features.manage.title': 'Spravujte svou pekárnu',
        'features.manage.description': 'Přijímejte objednávky, pečte lahodné výrobky a obsluhujte spokojené zákazníky',
        'features.levelup.title': 'Postupujte a automatizujte',
        'features.levelup.description': 'Odemykejte nové recepty, kupujte vybavení a automatizujte své procesy',
        'features.multiplayer.title': 'Hrajte společně',
        'features.multiplayer.description': 'Kooperativní a soutěžní multiplayerové režimy s přáteli',
        'status.development': '🚧 Hra ve vývoji - Fáze 5: Vícevrstvá podpora! 🚧',
        // Game interface
        'ui.level': 'Úroveň {{level}}',
        'ui.money': '{{amount}} Kč',
        'ui.experience': 'XP: {{current}}/{{max}}',
        'ui.skillPoints': 'SP: {{points}}',
        'ui.achievements': '🏆 Úspěchy',
        'ui.skills': '🌟 Dovednosti',
        'ui.automation': '🤖 Automatizace',
        // Kitchen
        'kitchen.title': '🏪 Kuchyně',
        'kitchen.clickToUse': 'Klikněte pro použití',
        'kitchen.making': 'Připravuje: {{recipe}}',
        'kitchen.timeRemaining': 'Čas: {{time}}',
        // Inventory
        'inventory.title': '📦 Sklad',
        'inventory.quantity': 'Množství: {{qty}}',
        'inventory.cost': '{{cost}} Kč za kus',
        // Orders
        'orders.title': '📋 Objednávky',
        'orders.newOrder': '+ Nová objednávka',
        'orders.accept': 'Přijmout',
        'orders.decline': 'Odmítnout',
        'orders.complete': 'Dokončit',
        'orders.inProgress': 'Probíhá',
        'orders.timeLimit': 'Čas: {{time}}',
        'orders.reward': '{{amount}} Kč',
        'orders.customer': 'Zákazník: {{name}}',
        // Quick Actions
        'actions.title': '⚡ Rychlé akce',
        'actions.buyIngredients': '🛒 Koupit suroviny',
        'actions.viewRecipes': '📖 Zobrazit recepty',
        'actions.equipmentShop': '🔧 Obchod s vybavením',
        // Modals
        'modal.recipes.title': '📖 Kniha receptů',
        'modal.shop.title': '🛒 Obchod se surovinami',
        'modal.baking.title': '🔥 {{equipment}} - Vyberte recept',
        'modal.achievements.title': '🏆 Úspěchy',
        'modal.skills.title': '🌟 Strom dovedností',
        'modal.automation.title': '🤖 Ovládání automatizace',
        'modal.equipmentShop.title': '🏪 Obchod s vybavením',
        'modal.settings.title': '⚙️ Nastavení',
        'modal.bakeries.title': '🏪 Správce pekáren',
        'modal.levelUp.title': 'Postup na vyšší úroveň!',
        'modal.levelUp.subtitle': 'Dosáhli jste úrovně {{level}}!',
        // Recipe Modal
        'recipes.all': 'Vše',
        'recipes.cookies': 'Sušenky',
        'recipes.cakes': 'Dorty',
        'recipes.bread': 'Chléb',
        'recipes.pastries': 'Pečivo',
        'recipes.ingredients': 'Suroviny:',
        'recipes.difficulty': 'Obtížnost:',
        'recipes.time': 'Čas:',
        'recipes.canCraft': '✅ Lze vyrobit',
        'recipes.unlockLevel': 'Odemčeno na úrovni {{level}}',
        'recipes.noRecipes': 'V této kategorii nejsou k dispozici žádné recepty.',
        'recipes.levelUpToUnlock': 'Postupte na vyšší úroveň pro odemčení dalších receptů!',
        // Shop Modal
        'shop.currentStock': 'Aktuální zásoba: {{quantity}}',
        'shop.buy': 'Koupit',
        'shop.tooExpensive': 'Příliš drahé',
        'shop.tips.title': '💡 Tipy pro nakupování',
        'shop.tips.bulk': '• Kupujte suroviny ve velkém množství pro úsporu času',
        'shop.tips.stock': '• Sledujte úroveň svých zásob',
        'shop.tips.rare': '• Některé recepty vyžadují vzácné suroviny',
        'shop.tips.prices': '• Ceny se mohou lišit podle dostupnosti',
        // Baking Modal
        'baking.selectRecipe': 'Vyberte recept',
        'baking.noRecipes': 'Žádné recepty k dispozici',
        'baking.noIngredients': 'Nemáte dostatek surovin pro výrobu jakéhokoli receptu.',
        'baking.buyIngredients': 'Koupit suroviny',
        'baking.startBaking': '🔥 Začít péct',
        'baking.instructions': '📋 Pokyny pro pečení {{recipe}}',
        'baking.expectedReward': 'Očekávaná odměna: {{amount}} Kč',
        'baking.makesSure': 'Ujistěte se, že máte všechny suroviny před začátkem!',
        // Achievements Modal
        'achievements.completed': '{{completed}} z {{total}} úspěchů dokončeno',
        'achievements.overallProgress': 'Celkový pokrok',
        'achievements.progress': 'Pokrok',
        'achievements.reward': 'Odměna:',
        'achievements.noAchievements': 'V této kategorii nejsou žádné úspěchy.',
        // Skills Modal
        'skills.availablePoints': 'Dostupné body dovedností: {{points}}',
        'skills.efficiency': 'Efektivita',
        'skills.automation': 'Automatizace',
        'skills.quality': 'Kvalita',
        'skills.business': 'Podnikání',
        'skills.effects': 'Efekty:',
        'skills.requires': 'Vyžaduje: {{requirements}}',
        'skills.requiresLevel': 'Vyžaduje úroveň {{level}}',
        'skills.maxed': '✅ Maximální',
        'skills.upgrade': '⬆️ Vylepšit ({{cost}} SP)',
        'skills.locked': '🔒 Uzamčeno',
        'skills.noSkills': 'V této kategorii nejsou žádné dovednosti.',
        'skills.tips.title': '💡 Tipy pro dovednosti',
        'skills.tips.earnPoints': '• Získávejte body dovedností postupem na vyšší úroveň (1 bod každé 2 úrovně)',
        'skills.tips.prerequisites': '• Některé dovednosti vyžadují nejprve odemčení jiných dovedností',
        'skills.tips.playstyle': '• Zaměřte se na dovednosti, které odpovídají vašemu stylu hry',
        'skills.tips.efficiency': '• Dovednosti efektivity pomáhají se správou zdrojů',
        // Automation Modal
        'automation.masterControl': '🎛️ Hlavní ovládání',
        'automation.enableAutomation': 'Povolit automatizaci',
        'automation.autoStart': 'Automatické spuštění vybavení',
        'automation.priorityMode': '🎯 Režim priority',
        'automation.efficiency': 'Efektivita (objednávky první)',
        'automation.profit': 'Zisk (nejvyšší hodnota)',
        'automation.speed': 'Rychlost (nejrychlejší recepty)',
        'automation.priorityDescription': 'Jak automatizace vybírá, co péct',
        'automation.performance': '⚡ Výkon',
        'automation.maxJobs': 'Max současných úloh: {{jobs}}',
        'automation.safety': '🛡️ Bezpečnost',
        'automation.stopWhenLow': 'Zastavit, když suroviny klesnou pod: {{threshold}}',
        'automation.upgrades': '💡 Vylepšení automatizace',
        'automation.upgradesDescription': 'Vylepšete efektivitu, rychlost a inteligenci vaší automatizace.',
        'automation.purchase': 'Koupit',
        'automation.noUpgrades': 'Na vaší současné úrovni nejsou k dispozici žádná vylepšení.',
        'automation.levelUpForUpgrades': 'Postupte na vyšší úroveň pro odemčení dalších vylepšení automatizace!',
        'automation.automatedEquipment': 'Automatizované vybavení',
        'automation.activeUpgrades': 'Aktivní vylepšení',
        'automation.automationStatus': 'Stav automatizace',
        'automation.equipmentStatus': '🏭 Stav vybavení',
        'automation.running': 'Běží',
        'automation.idle': 'Nečinné',
        'automation.noAutomatedEquipment': 'Žádné automatizované vybavení k dispozici.',
        'automation.purchaseAutoEquipment': 'Kupte si auto-vybavení z obchodu pro začátek!',
        // Equipment Shop Modal
        'equipmentShop.upgradeYourBakery': 'Vylepšete svou pekárnu profesionálním vybavením',
        'equipmentShop.basic': 'Základní',
        'equipmentShop.automated': 'Automatizované',
        'equipmentShop.advanced': 'Pokročilé',
        'equipmentShop.efficiency': 'Efektivita: {{efficiency}}x',
        'equipmentShop.automation': 'Automatizace:',
        'equipmentShop.unlockLevel': 'Úroveň odemčení: {{level}}',
        'equipmentShop.purchase': '💰 Koupit',
        'equipmentShop.noEquipment': 'V této kategorii není k dispozici žádné vybavení.',
        'equipmentShop.levelUpForEquipment': 'Postupte na vyšší úroveň pro odemčení dalšího vybavení!',
        'equipmentShop.tips.title': '💡 Tipy pro vybavení',
        'equipmentShop.tips.automated': '• Automatizované vybavení může běžet bez vašeho dohledu',
        'equipmentShop.tips.efficiency': '• Vyšší efektivita znamená rychlejší výrobu a lepší kvalitu',
        'equipmentShop.tips.conveyor': '• Dopravní pásy spojují vybavení pro bezproblémový pracovní tok',
        'equipmentShop.tips.advanced': '• Pokročilé vybavení se odemyká na vyšších úrovních',
        // Level Up Modal
        'levelUp.levelRewards': '🎁 Odměny za úroveň',
        'levelUp.whatsNext': '💡 Co dál?',
        'levelUp.checkRecipes': '• Podívejte se na nové recepty ve své knize receptů',
        'levelUp.visitShop': '• Navštivte obchod pro nové vybavení',
        'levelUp.challengingOrders': '• Přijměte náročnější objednávky',
        'levelUp.investSkills': '• Investujte do vylepšení dovedností',
        // Settings Modal
        'settings.title': '⚙️ Nastavení',
        'settings.general': 'Obecné',
        'settings.audio': 'Zvuk',
        'settings.graphics': 'Grafika',
        'settings.save': 'Uložení a data',
        'settings.language': '🌍 Jazyk',
        'settings.gameplay': '🎮 Hratelnost',
        'settings.notifications': 'Povolit oznámení',
        'settings.tutorials': 'Zobrazit návody',
        'settings.animationSpeed': 'Rychlost animace',
        'settings.sound': 'Zvukové efekty',
        'settings.music': 'Hudba na pozadí',
        'settings.quality': '🎨 Kvalita grafiky',
        'settings.autoSave': '💾 Automatické ukládání',
        'settings.enableAutoSave': 'Povolit automatické ukládání',
        'settings.dataManagement': '📁 Správa dat',
        'settings.exportSave': '📤 Exportovat uložení',
        'settings.importSave': '📥 Importovat uložení',
        'settings.cloudSync': '☁️ Cloudová synchronizace',
        'settings.cloudSyncDescription': 'Cloudová synchronizace vám umožňuje uložit pokrok online a hrát na více zařízeních.',
        'settings.comingSoon': 'Již brzy',
        // Bakery Manager Modal
        'bakeries.title': '🏪 Správce pekáren',
        'bakeries.subtitle': 'Spravujte své pekárenské impérium',
        'bakeries.owned': 'Moje pekárny',
        'bakeries.available': 'Dostupné',
        'bakeries.current': 'Aktuální',
        'bakeries.level': 'Úroveň',
        'bakeries.specialization': 'Specializace',
        'bakeries.equipment': 'Vybavení',
        'bakeries.orders': 'Aktivní objednávky',
        'bakeries.switchTo': 'Přepnout na',
        'bakeries.noOwned': 'Ještě nevlastníte žádné pekárny.',
        'bakeries.purchase': '💰 Koupit',
        'bakeries.tooExpensive': '💸 Příliš drahé',
        'bakeries.allOwned': 'Vlastníte všechny dostupné pekárny!',
        'bakeries.tips': '💡 Tipy pro pekárny',
        'bakeries.tip1': 'Každá pekárna se specializuje na různé produkty pro bonusovou efektivitu',
        'bakeries.tip2': 'Přepínejte mezi pekárnami pro správu více lokalit',
        'bakeries.tip3': 'Specializované pekárny přitahují zákazníky hledající konkrétní položky',
        'bakeries.tip4': 'Vylepšujte každou pekárnu nezávisle pro maximální zisk',
        // Notifications
        'notifications.orderAccepted': 'Objednávka přijata',
        'notifications.orderAcceptedMessage': 'Přijali jste novou objednávku!',
        'notifications.orderCompleted': 'Objednávka dokončena!',
        'notifications.orderCompletedMessage': 'Získali jste {{reward}} Kč a zkušenosti!',
        'notifications.orderDeclined': 'Objednávka odmítnuta',
        'notifications.orderDeclinedMessage': 'Objednávka byla odstraněna z vaší fronty.',
        'notifications.bakeryPurchased': 'Pekárna zakoupena!',
        'notifications.bakeryPurchasedMessage': 'Nyní vlastníte {{name}}!',
        'notifications.bakerySwitched': 'Pekárna přepnuta',
        'notifications.bakerySwitchedMessage': 'Přepnuto na {{name}}',
        // Common buttons and actions
        'common.accept': 'Přijmout',
        'common.decline': 'Odmítnout',
        'common.complete': 'Dokončit',
        'common.purchase': 'Koupit',
        'common.upgrade': 'Vylepšit',
        'common.cancel': 'Zrušit',
        'common.confirm': 'Potvrdit',
        'common.save': 'Uložit',
        'common.load': 'Načíst',
        'common.delete': 'Smazat',
        'common.edit': 'Upravit',
        'common.back': 'Zpět',
        'common.next': 'Další',
        'common.previous': 'Předchozí',
        'common.yes': 'Ano',
        'common.no': 'Ne',
        'common.create': 'Vytvořit',
        'common.join': 'Připojit se',
        'common.leave': 'Odejít',
        'common.start': 'Začít',
        'common.ready': 'Připraven',
        'common.notReady': 'Nepřipraven',
        'common.send': 'Odeslat',
        // Multiplayer
        'multiplayer.lobby': '👥 Multiplayerová lobby',
        'multiplayer.connected': '🟢 Připojeno',
        'multiplayer.disconnected': '🔴 Odpojeno',
        'multiplayer.createRoom': 'Vytvořit místnost',
        'multiplayer.joinRoom': 'Připojit se k místnosti',
        'multiplayer.room': 'Místnost',
        'multiplayer.yourName': 'Vaše jméno',
        'multiplayer.enterName': 'Zadejte své jméno',
        'multiplayer.roomName': 'Název místnosti',
        'multiplayer.enterRoomName': 'Zadejte název místnosti',
        'multiplayer.gameMode': 'Herní režim',
        'multiplayer.cooperative': '🤝 Kooperativní',
        'multiplayer.competitive': '⚔️ Soutěžní',
        'multiplayer.maxPlayers': 'Max hráčů: {{count}}',
        'multiplayer.roomId': 'ID místnosti',
        'multiplayer.enterRoomId': 'Zadejte ID místnosti',
        'multiplayer.players': 'Hráči ({{count}})',
        'multiplayer.host': 'HOSTITEL',
        'multiplayer.level': 'Úroveň {{level}}',
        'multiplayer.chat': 'Chat',
        'multiplayer.typeMessage': 'Napište zprávu...',
        'multiplayer.gameTime': 'Herní čas: {{time}}',
        'multiplayer.teamStats': '📊 Týmové statistiky',
        'multiplayer.ordersCompleted': 'Dokončené objednávky:',
        'multiplayer.totalRevenue': 'Celkový příjem:',
        'multiplayer.teamExperience': 'Týmové zkušenosti:',
        'multiplayer.sharedKitchen': '🏪 Sdílená kuchyně',
        'multiplayer.sharedOrders': '📋 Sdílené objednávky',
        'multiplayer.sharedInventory': '📦 Sdílený sklad',
        'multiplayer.contribution': 'Příspěvek:',
        'multiplayer.online': '🟢 Online',
        'multiplayer.status': 'Stav:',
        'multiplayer.you': '(Vy)',
        'multiplayer.teamChat': '💬 Týmový chat',
        'multiplayer.chatPlaceholder': 'Zde se zobrazí zprávy chatu...',
        // Multiplayer game modes
        'multiplayer.mode.cooperative.description': '🤝 Kooperativní režim: Spolupracujte na dokončování objednávek a rozvoji sdílené pekárny!',
        'multiplayer.mode.competitive.description': '⚔️ Soutěžní režim: Soutěžte s ostatními hráči o dokončení nejvíce objednávek!',
        // Multiplayer game interface
        'multiplayer.game.title': '🎮 Multiplayerová hra - {{roomName}}',
        'multiplayer.game.mode': 'Režim: {{mode}}',
        'multiplayer.game.playersCount': 'Hráči: {{count}}',
        'multiplayer.game.playing': '🟢 Hraje se',
        'multiplayer.game.leaveGame': '🚪 Opustit hru',
        'multiplayer.game.tabs.game': 'Hra',
        'multiplayer.game.tabs.players': 'Hráči',
        'multiplayer.game.tabs.chat': 'Chat',
        // Room creation and joining
        'multiplayer.create.title': '🏗️ Vytvořit místnost',
        'multiplayer.join.title': '🚪 Připojit se k místnosti',
        'multiplayer.room.info': 'Režim: {{mode}} • Hráči: {{current}}/{{max}}',
        'multiplayer.room.readyUp': '✅ Připraven',
        'multiplayer.room.notReady': '⏳ Nepřipraven',
        'multiplayer.room.startGame': '🚀 Začít hru',
        'multiplayer.room.leaveRoom': '🚪 Opustit',
        // Connection states
        'multiplayer.connection.connecting': 'Připojování...',
        'multiplayer.connection.reconnecting': 'Znovu se připojuje...',
        'multiplayer.connection.failed': 'Připojení selhalo',
        'multiplayer.connection.error': '⚠️ {{error}}',
        // System messages
        'multiplayer.system.playerJoined': '{{name}} se připojil do místnosti',
        'multiplayer.system.playerLeft': '{{name}} opustil místnost',
        'multiplayer.system.gameStarted': 'Hra začala!',
        'multiplayer.system.gameEnded': 'Hra skončila!',
        'multiplayer.system.roomCreated': 'Místnost byla úspěšně vytvořena',
        'multiplayer.system.roomJoined': 'Úspěšně jste se připojili do místnosti'
    }
};
function LanguageProvider({ children }) {
    const [language, setLanguage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('en');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Load language from localStorage on client side
        const savedLanguage = localStorage.getItem('language');
        if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'cs')) {
            setLanguage(savedLanguage);
        }
    }, []);
    const handleSetLanguage = (lang)=>{
        setLanguage(lang);
        localStorage.setItem('language', lang);
    };
    const t = (key, params)=>{
        let translation = translations[language][key] || key;
        if (params) {
            Object.entries(params).forEach(([param, value])=>{
                translation = translation.replace(`{{${param}}}`, value);
            });
        }
        return translation;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(LanguageContext.Provider, {
        value: {
            language,
            setLanguage: handleSetLanguage,
            t
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/LanguageContext.tsx",
        lineNumber: 724,
        columnNumber: 5
    }, this);
}
function useLanguage() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(LanguageContext);
    if (context === undefined) {
        throw new Error('useLanguage must be used within a LanguageProvider');
    }
    return context;
}
}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/child_process [external] (child_process, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("child_process", () => require("child_process"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[project]/src/lib/socket.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Socket.IO client setup for multiplayer functionality
__turbopack_context__.s({
    "socketManager": ()=>socketManager,
    "useSocket": ()=>useSocket
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm-debug/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm-debug/index.js [app-ssr] (ecmascript) <locals>");
;
class SocketManager {
    socket = null;
    isConnected = false;
    reconnectAttempts = 0;
    maxReconnectAttempts = 5;
    currentRoom = null;
    currentPlayer = null;
    constructor(){
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }
    connect() {
        if (this.socket?.connected) return;
        this.socket = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["io"])(process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3001', {
            transports: [
                'websocket',
                'polling'
            ],
            timeout: 20000,
            forceNew: true
        });
        this.setupEventListeners();
    }
    setupEventListeners() {
        if (!this.socket) return;
        this.socket.on('connect', ()=>{
            console.log('Connected to multiplayer server');
            this.isConnected = true;
            this.reconnectAttempts = 0;
        });
        this.socket.on('disconnect', (reason)=>{
            console.log('Disconnected from multiplayer server:', reason);
            this.isConnected = false;
            if (reason === 'io server disconnect') {
                // Server disconnected, try to reconnect
                this.handleReconnect();
            }
        });
        this.socket.on('connect_error', (error)=>{
            console.error('Connection error:', error);
            this.handleReconnect();
        });
        this.socket.on('error', (error)=>{
            console.error('Socket error:', error);
        });
    }
    handleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
            setTimeout(()=>{
                this.connect();
            }, Math.pow(2, this.reconnectAttempts) * 1000); // Exponential backoff
        } else {
            console.error('Max reconnection attempts reached');
        }
    }
    // Room management methods
    createRoom(roomData) {
        return new Promise((resolve, reject)=>{
            if (!this.socket?.connected) {
                reject(new Error('Not connected to server'));
                return;
            }
            this.socket.emit('create_room', roomData);
            this.socket.once('room_created', (room)=>{
                this.currentRoom = room;
                resolve(room);
            });
            this.socket.once('error', (error)=>{
                reject(new Error(error.message));
            });
        });
    }
    joinRoom(roomId, playerData) {
        return new Promise((resolve, reject)=>{
            if (!this.socket?.connected) {
                reject(new Error('Not connected to server'));
                return;
            }
            this.socket.emit('join_room', roomId, playerData);
            this.socket.once('room_joined', (room, player)=>{
                this.currentRoom = room;
                this.currentPlayer = player;
                resolve({
                    room,
                    player
                });
            });
            this.socket.once('error', (error)=>{
                reject(new Error(error.message));
            });
        });
    }
    leaveRoom() {
        if (this.socket?.connected && this.currentRoom) {
            this.socket.emit('leave_room', this.currentRoom.id);
            this.currentRoom = null;
            this.currentPlayer = null;
        }
    }
    // Game state methods
    sendPlayerAction(action) {
        if (this.socket?.connected && this.currentPlayer) {
            this.socket.emit('player_action', {
                ...action,
                playerId: this.currentPlayer.id,
                timestamp: Date.now()
            });
        }
    }
    sendMessage(content) {
        if (this.socket?.connected && this.currentPlayer) {
            this.socket.emit('send_message', {
                playerId: this.currentPlayer.id,
                playerName: this.currentPlayer.name,
                content,
                timestamp: Date.now()
            });
        }
    }
    // Event subscription methods
    on(event, callback) {
        this.socket?.on(event, callback);
    }
    off(event, callback) {
        this.socket?.off(event, callback);
    }
    once(event, callback) {
        this.socket?.once(event, callback);
    }
    // Utility methods
    isSocketConnected() {
        return this.isConnected && this.socket?.connected === true;
    }
    getCurrentRoom() {
        return this.currentRoom;
    }
    getCurrentPlayer() {
        return this.currentPlayer;
    }
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
            this.isConnected = false;
            this.currentRoom = null;
            this.currentPlayer = null;
        }
    }
}
const socketManager = new SocketManager();
function useSocket() {
    return socketManager;
}
}),
"[project]/src/contexts/MultiplayerContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "MultiplayerProvider": ()=>MultiplayerProvider,
    "useMultiplayer": ()=>useMultiplayer
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/socket.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
const MultiplayerContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function MultiplayerProvider({ children }) {
    // Connection state
    const [isConnected, setIsConnected] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isInRoom, setIsInRoom] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [connectionError, setConnectionError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // Room and player data
    const [currentRoom, setCurrentRoom] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [currentPlayer, setCurrentPlayer] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [players, setPlayers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    // Game state
    const [gameState, setGameState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('waiting');
    const [sharedGameState, setSharedGameState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // Chat
    const [messages, setMessages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    // Initialize socket connection and event listeners
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const socket = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socketManager"];
        // Connection events
        const handleConnect = ()=>{
            setIsConnected(true);
            setConnectionError(null);
        };
        const handleDisconnect = ()=>{
            setIsConnected(false);
            setIsInRoom(false);
            setCurrentRoom(null);
            setCurrentPlayer(null);
            setPlayers([]);
        };
        const handleError = (error)=>{
            setConnectionError(error.message || 'Connection error');
            console.error('Multiplayer error:', error);
        };
        // Room events
        const handleRoomCreated = (room)=>{
            setCurrentRoom(room);
            setPlayers(room.players);
            setIsInRoom(true);
            setGameState(room.gameState);
            // Set current player as host
            const hostPlayer = room.players.find((p)=>p.isHost);
            if (hostPlayer) {
                setCurrentPlayer(hostPlayer);
            }
        };
        const handleRoomJoined = (room, player)=>{
            setCurrentRoom(room);
            setCurrentPlayer(player);
            setPlayers(room.players);
            setIsInRoom(true);
            setGameState(room.gameState);
        };
        const handleRoomLeft = ()=>{
            setCurrentRoom(null);
            setCurrentPlayer(null);
            setPlayers([]);
            setIsInRoom(false);
            setGameState('waiting');
            setSharedGameState(null);
            setMessages([]);
        };
        const handleRoomUpdated = (room)=>{
            setCurrentRoom(room);
            setPlayers(room.players);
            setGameState(room.gameState);
        };
        const handlePlayerJoined = (player)=>{
            setPlayers((prev)=>[
                    ...prev,
                    player
                ]);
            addSystemMessage(`${player.name} joined the room`);
        };
        const handlePlayerLeft = (playerId)=>{
            setPlayers((prev)=>{
                const leftPlayer = prev.find((p)=>p.id === playerId);
                if (leftPlayer) {
                    addSystemMessage(`${leftPlayer.name} left the room`);
                }
                return prev.filter((p)=>p.id !== playerId);
            });
        };
        // Game events
        const handleGameStarted = (gameState)=>{
            setGameState('playing');
            setSharedGameState(gameState);
            addSystemMessage('Game started!');
        };
        const handleGameStateUpdate = (update)=>{
            setSharedGameState((prev)=>prev ? {
                    ...prev,
                    ...update
                } : null);
        };
        const handlePlayerAction = (action)=>{
            // Handle player actions from other players
            console.log('Player action received:', action);
        };
        // Chat events
        const handleMessageReceived = (message)=>{
            const chatMessage = {
                id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
                playerId: message.playerId,
                playerName: message.playerName,
                content: message.content,
                timestamp: message.timestamp
            };
            setMessages((prev)=>[
                    ...prev,
                    chatMessage
                ]);
        };
        // Register event listeners
        socket.on('connect', handleConnect);
        socket.on('disconnect', handleDisconnect);
        socket.on('error', handleError);
        socket.on('room_created', handleRoomCreated);
        socket.on('room_joined', handleRoomJoined);
        socket.on('room_left', handleRoomLeft);
        socket.on('room_updated', handleRoomUpdated);
        socket.on('player_joined', handlePlayerJoined);
        socket.on('player_left', handlePlayerLeft);
        socket.on('game_started', handleGameStarted);
        socket.on('game_state_update', handleGameStateUpdate);
        socket.on('player_action', handlePlayerAction);
        socket.on('message_received', handleMessageReceived);
        // Check initial connection state
        setIsConnected(socket.isSocketConnected());
        // Cleanup
        return ()=>{
            socket.off('connect', handleConnect);
            socket.off('disconnect', handleDisconnect);
            socket.off('error', handleError);
            socket.off('room_created', handleRoomCreated);
            socket.off('room_joined', handleRoomJoined);
            socket.off('room_left', handleRoomLeft);
            socket.off('room_updated', handleRoomUpdated);
            socket.off('player_joined', handlePlayerJoined);
            socket.off('player_left', handlePlayerLeft);
            socket.off('game_started', handleGameStarted);
            socket.off('game_state_update', handleGameStateUpdate);
            socket.off('player_action', handlePlayerAction);
            socket.off('message_received', handleMessageReceived);
        };
    }, []);
    // Helper function to add system messages
    const addSystemMessage = (content)=>{
        const systemMessage = {
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            playerId: 'system',
            playerName: 'System',
            content,
            timestamp: Date.now()
        };
        setMessages((prev)=>[
                ...prev,
                systemMessage
            ]);
    };
    // Action functions
    const createRoom = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (roomData, playerData)=>{
        try {
            setConnectionError(null);
            const room = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socketManager"].createRoom({
                ...roomData,
                hostName: playerData.name,
                hostAvatar: playerData.avatar,
                hostLevel: playerData.level
            });
        // Room created event will be handled by the event listener
        } catch (error) {
            setConnectionError(error.message);
            throw error;
        }
    }, []);
    const joinRoom = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (roomId, playerData)=>{
        try {
            setConnectionError(null);
            const { room, player } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socketManager"].joinRoom(roomId, playerData);
        // Room joined event will be handled by the event listener
        } catch (error) {
            setConnectionError(error.message);
            throw error;
        }
    }, []);
    const leaveRoom = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socketManager"].leaveRoom();
    }, []);
    const startGame = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (currentRoom && currentPlayer?.isHost) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socketManager"].sendPlayerAction({
                type: 'start_game',
                data: {
                    roomId: currentRoom.id
                }
            });
        }
    }, [
        currentRoom,
        currentPlayer
    ]);
    const sendMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((content)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socketManager"].sendMessage(content);
    }, []);
    const sendPlayerAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((action)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socketManager"].sendPlayerAction(action);
    }, []);
    const setPlayerReady = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((ready)=>{
        if (currentPlayer) {
            sendPlayerAction({
                type: 'player_ready',
                data: {
                    ready
                }
            });
        }
    }, [
        currentPlayer,
        sendPlayerAction
    ]);
    const kickPlayer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((playerId)=>{
        if (currentPlayer?.isHost) {
            sendPlayerAction({
                type: 'kick_player',
                data: {
                    playerId
                }
            });
        }
    }, [
        currentPlayer,
        sendPlayerAction
    ]);
    const updateRoomSettings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((settings)=>{
        if (currentPlayer?.isHost) {
            sendPlayerAction({
                type: 'update_room_settings',
                data: {
                    settings
                }
            });
        }
    }, [
        currentPlayer,
        sendPlayerAction
    ]);
    const value = {
        // Connection state
        isConnected,
        isInRoom,
        connectionError,
        // Room and player data
        currentRoom,
        currentPlayer,
        players,
        // Game state
        gameState,
        sharedGameState,
        // Chat
        messages,
        // Actions
        createRoom,
        joinRoom,
        leaveRoom,
        startGame,
        sendMessage,
        sendPlayerAction,
        // Room management
        setPlayerReady,
        kickPlayer,
        updateRoomSettings
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(MultiplayerContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/MultiplayerContext.tsx",
        lineNumber: 327,
        columnNumber: 5
    }, this);
}
function useMultiplayer() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(MultiplayerContext);
    if (context === undefined) {
        throw new Error('useMultiplayer must be used within a MultiplayerProvider');
    }
    return context;
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__0570d563._.js.map