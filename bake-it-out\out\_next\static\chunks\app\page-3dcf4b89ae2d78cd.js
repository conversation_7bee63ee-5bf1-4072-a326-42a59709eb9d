(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{255:(e,t,s)=>{"use strict";function a(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return a}}),s(5155),s(7650),s(5744),s(589)},637:(e,t,s)=>{"use strict";s.d(t,{MultiplayerProvider:()=>d,K:()=>m});var a=s(5155),l=s(2115),r=s(4298),n=s(9509);class i{connect(){var e;null!=(e=this.socket)&&e.connected||(this.socket=(0,r.io)(n.env.NEXT_PUBLIC_SOCKET_URL||"http://localhost:3001",{transports:["websocket","polling"],timeout:2e4,forceNew:!0}),this.setupEventListeners())}setupEventListeners(){this.socket&&(this.socket.on("connect",()=>{console.log("Connected to multiplayer server"),this.isConnected=!0,this.reconnectAttempts=0}),this.socket.on("disconnect",e=>{console.log("Disconnected from multiplayer server:",e),this.isConnected=!1,"io server disconnect"===e&&this.handleReconnect()}),this.socket.on("connect_error",e=>{console.error("Connection error:",e),this.handleReconnect()}),this.socket.on("error",e=>{console.error("Socket error:",e)}))}handleReconnect(){this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,console.log("Attempting to reconnect (".concat(this.reconnectAttempts,"/").concat(this.maxReconnectAttempts,")...")),setTimeout(()=>{this.connect()},1e3*Math.pow(2,this.reconnectAttempts))):console.error("Max reconnection attempts reached")}createRoom(e){return new Promise((t,s)=>{var a;if(!(null==(a=this.socket)?void 0:a.connected))return void s(Error("Not connected to server"));this.socket.emit("create_room",e),this.socket.once("room_created",e=>{this.currentRoom=e,t(e)}),this.socket.once("error",e=>{s(Error(e.message))})})}joinRoom(e,t){return new Promise((s,a)=>{var l;if(!(null==(l=this.socket)?void 0:l.connected))return void a(Error("Not connected to server"));this.socket.emit("join_room",e,t),this.socket.once("room_joined",(e,t)=>{this.currentRoom=e,this.currentPlayer=t,s({room:e,player:t})}),this.socket.once("error",e=>{a(Error(e.message))})})}leaveRoom(){var e;(null==(e=this.socket)?void 0:e.connected)&&this.currentRoom&&(this.socket.emit("leave_room",this.currentRoom.id),this.currentRoom=null,this.currentPlayer=null)}sendPlayerAction(e){var t;(null==(t=this.socket)?void 0:t.connected)&&this.currentPlayer&&this.socket.emit("player_action",{...e,playerId:this.currentPlayer.id,timestamp:Date.now()})}sendMessage(e){var t;(null==(t=this.socket)?void 0:t.connected)&&this.currentPlayer&&this.socket.emit("send_message",{playerId:this.currentPlayer.id,playerName:this.currentPlayer.name,content:e,timestamp:Date.now()})}on(e,t){var s;null==(s=this.socket)||s.on(e,t)}off(e,t){var s;null==(s=this.socket)||s.off(e,t)}once(e,t){var s;null==(s=this.socket)||s.once(e,t)}isSocketConnected(){var e;return this.isConnected&&(null==(e=this.socket)?void 0:e.connected)===!0}getCurrentRoom(){return this.currentRoom}getCurrentPlayer(){return this.currentPlayer}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null,this.isConnected=!1,this.currentRoom=null,this.currentPlayer=null)}constructor(){this.socket=null,this.isConnected=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.currentRoom=null,this.currentPlayer=null,this.connect()}}let o=new i,c=(0,l.createContext)(void 0);function d(e){let{children:t}=e,[s,r]=(0,l.useState)(!1),[n,i]=(0,l.useState)(!1),[d,m]=(0,l.useState)(null),[u,x]=(0,l.useState)(null),[h,g]=(0,l.useState)(null),[p,y]=(0,l.useState)([]),[v,j]=(0,l.useState)("waiting"),[b,f]=(0,l.useState)(null),[N,w]=(0,l.useState)([]);(0,l.useEffect)(()=>{let e=()=>{r(!0),m(null)},t=()=>{r(!1),i(!1),x(null),g(null),y([])},s=e=>{m(e.message||"Connection error"),console.error("Multiplayer error:",e)},a=e=>{x(e),y(e.players),i(!0),j(e.gameState);let t=e.players.find(e=>e.isHost);t&&g(t)},l=(e,t)=>{x(e),g(t),y(e.players),i(!0),j(e.gameState)},n=()=>{x(null),g(null),y([]),i(!1),j("waiting"),f(null),w([])},c=e=>{x(e),y(e.players),j(e.gameState)},d=e=>{y(t=>[...t,e]),k("".concat(e.name," joined the room"))},u=e=>{y(t=>{let s=t.find(t=>t.id===e);return s&&k("".concat(s.name," left the room")),t.filter(t=>t.id!==e)})},h=e=>{j("playing"),f(e),k("Game started!")},p=e=>{f(t=>t?{...t,...e}:null)},v=e=>{console.log("Player action received:",e)},b=e=>{let t={id:Date.now().toString()+Math.random().toString(36).substring(2,11),playerId:e.playerId,playerName:e.playerName,content:e.content,timestamp:e.timestamp};w(e=>[...e,t])};return o.on("connect",e),o.on("disconnect",t),o.on("error",s),o.on("room_created",a),o.on("room_joined",l),o.on("room_left",n),o.on("room_updated",c),o.on("player_joined",d),o.on("player_left",u),o.on("game_started",h),o.on("game_state_update",p),o.on("player_action",v),o.on("message_received",b),r(o.isSocketConnected()),()=>{o.off("connect",e),o.off("disconnect",t),o.off("error",s),o.off("room_created",a),o.off("room_joined",l),o.off("room_left",n),o.off("room_updated",c),o.off("player_joined",d),o.off("player_left",u),o.off("game_started",h),o.off("game_state_update",p),o.off("player_action",v),o.off("message_received",b)}},[]);let k=e=>{let t={id:Date.now().toString()+Math.random().toString(36).substring(2,11),playerId:"system",playerName:"System",content:e,timestamp:Date.now()};w(e=>[...e,t])},C=(0,l.useCallback)(async(e,t)=>{try{m(null),await o.createRoom({...e,hostName:t.name,hostAvatar:t.avatar,hostLevel:t.level})}catch(e){throw m(e.message),e}},[]),D=(0,l.useCallback)(async(e,t)=>{try{m(null);let{room:s,player:a}=await o.joinRoom(e,t)}catch(e){throw m(e.message),e}},[]),S=(0,l.useCallback)(()=>{o.leaveRoom()},[]),_=(0,l.useCallback)(()=>{u&&(null==h?void 0:h.isHost)&&o.sendPlayerAction({type:"start_game",data:{roomId:u.id}})},[u,h]),P=(0,l.useCallback)(e=>{o.sendMessage(e)},[]),R=(0,l.useCallback)(e=>{o.sendPlayerAction(e)},[]),A=(0,l.useCallback)(e=>{h&&R({type:"player_ready",data:{ready:e}})},[h,R]),E=(0,l.useCallback)(e=>{(null==h?void 0:h.isHost)&&R({type:"kick_player",data:{playerId:e}})},[h,R]),O=(0,l.useCallback)(e=>{(null==h?void 0:h.isHost)&&R({type:"update_room_settings",data:{settings:e}})},[h,R]);return(0,a.jsx)(c.Provider,{value:{isConnected:s,isInRoom:n,connectionError:d,currentRoom:u,currentPlayer:h,players:p,gameState:v,sharedGameState:b,messages:N,createRoom:C,joinRoom:D,leaveRoom:S,startGame:_,sendMessage:P,sendPlayerAction:R,setPlayerReady:A,kickPlayer:E,updateRoomSettings:O},children:t})}function m(){let e=(0,l.useContext)(c);return void 0===e?(console.warn("useMultiplayer called outside of MultiplayerProvider, using fallback"),{isConnected:!1,connectionStatus:"disconnected",currentRoom:null,gameState:null,createRoom:async()=>{},joinRoom:async()=>{},leaveRoom:()=>{},sendChatMessage:()=>{},updateGameState:()=>{},setPlayerReady:()=>{}}):e}},1006:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var a=s(5155),l=s(2115),r=s(9283),n=s(3741),i=s(637);function o(e){let{isOpen:t,onClose:s}=e,{t:o}=(0,r.o)(),{isConnected:c,isInRoom:d,connectionError:m,currentRoom:u,currentPlayer:x,players:h,gameState:g,messages:p,createRoom:y,joinRoom:v,leaveRoom:j,startGame:b,sendMessage:f,setPlayerReady:N}=(0,i.K)(),[w,k]=(0,l.useState)("create"),[C,D]=(0,l.useState)(""),[S,_]=(0,l.useState)(""),[P,R]=(0,l.useState)(""),[A,E]=(0,l.useState)("cooperative"),[O,M]=(0,l.useState)(4),[I,z]=(0,l.useState)(""),[F,L]=(0,l.useState)(!1);if(!t)return null;let $=async()=>{if(S.trim()&&C.trim())try{await y({name:C,mode:A,maxPlayers:O,settings:{gameMode:A,difficulty:"medium",allowSpectators:!0}},{name:S,avatar:"\uD83D\uDC68‍\uD83C\uDF73",level:1}),k("room")}catch(e){console.error("Failed to create room:",e)}},T=async()=>{if(S.trim()&&P.trim())try{await v(P.toUpperCase(),{name:S,avatar:"\uD83D\uDC68‍\uD83C\uDF73",level:1}),k("room")}catch(e){console.error("Failed to join room:",e)}},B=()=>{I.trim()&&(f(I),z(""))},H=[{id:"create",name:o("multiplayer.createRoom"),icon:"\uD83C\uDFD7️"},{id:"join",name:o("multiplayer.joinRoom"),icon:"\uD83D\uDEAA"},...d?[{id:"room",name:o("multiplayer.room"),icon:"\uD83C\uDFE0"}]:[]];return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:o("multiplayer.lobby")}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"px-3 py-1 rounded-full text-sm ".concat(c?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:c?o("multiplayer.connected"):o("multiplayer.disconnected")}),(0,a.jsx)(n.$,{variant:"secondary",onClick:s,children:o("game.close")})]})]}),m&&(0,a.jsx)("div",{className:"mt-2 p-2 bg-red-100 text-red-800 rounded text-sm",children:o("multiplayer.connection.error",{error:m})})]}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("div",{className:"flex space-x-0",children:H.map(e=>(0,a.jsxs)("button",{onClick:()=>k(e.id),className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(w===e.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.icon," ",e.name]},e.id))})}),(0,a.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["create"===w&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:o("multiplayer.yourName")}),(0,a.jsx)("input",{type:"text",value:S,onChange:e=>_(e.target.value),placeholder:o("multiplayer.enterName"),className:"w-full p-3 border rounded-lg",maxLength:20})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:o("multiplayer.roomName")}),(0,a.jsx)("input",{type:"text",value:C,onChange:e=>D(e.target.value),placeholder:o("multiplayer.enterRoomName"),className:"w-full p-3 border rounded-lg",maxLength:30})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:o("multiplayer.gameMode")}),(0,a.jsxs)("select",{value:A,onChange:e=>E(e.target.value),className:"w-full p-3 border rounded-lg",children:[(0,a.jsx)("option",{value:"cooperative",children:o("multiplayer.cooperative")}),(0,a.jsx)("option",{value:"competitive",children:o("multiplayer.competitive")})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:o("multiplayer.maxPlayers",{count:O.toString()})}),(0,a.jsx)("input",{type:"range",min:"2",max:"8",value:O,onChange:e=>M(parseInt(e.target.value)),className:"w-full"})]})]}),(0,a.jsx)(n.$,{variant:"primary",size:"lg",className:"w-full",onClick:$,disabled:!c||!S.trim()||!C.trim(),children:o("multiplayer.create.title")})]}),"join"===w&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:o("multiplayer.yourName")}),(0,a.jsx)("input",{type:"text",value:S,onChange:e=>_(e.target.value),placeholder:o("multiplayer.enterName"),className:"w-full p-3 border rounded-lg",maxLength:20})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:o("multiplayer.roomId")}),(0,a.jsx)("input",{type:"text",value:P,onChange:e=>R(e.target.value.toUpperCase()),placeholder:o("multiplayer.enterRoomId"),className:"w-full p-3 border rounded-lg font-mono",maxLength:6})]})]}),(0,a.jsx)(n.$,{variant:"primary",size:"lg",className:"w-full",onClick:T,disabled:!c||!S.trim()||!P.trim(),children:o("multiplayer.join.title")})]}),"room"===w&&u&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("h3",{className:"font-semibold text-blue-800",children:u.name}),(0,a.jsxs)("div",{className:"text-sm text-blue-600",children:[o("multiplayer.roomId"),": ",(0,a.jsx)("span",{className:"font-mono font-bold",children:u.id})]})]}),(0,a.jsx)("div",{className:"text-sm text-blue-700",children:o("multiplayer.room.info",{mode:u.mode,current:u.currentPlayers.toString(),max:u.maxPlayers.toString()})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:o("multiplayer.players",{count:h.length.toString()})}),(0,a.jsx)("div",{className:"space-y-2",children:h.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-2xl",children:e.avatar}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium",children:[e.name,e.isHost&&(0,a.jsx)("span",{className:"ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded",children:o("multiplayer.host")})]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:o("multiplayer.level",{level:e.level.toString()})})]})]}),(0,a.jsx)("div",{className:"px-2 py-1 rounded text-xs ".concat(e.isReady?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"),children:e.isReady?o("common.ready"):o("common.notReady")})]},e.id))})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(n.$,{variant:F?"success":"secondary",onClick:()=>{let e=!F;L(e),N(e)},className:"flex-1",children:F?o("multiplayer.room.readyUp"):o("multiplayer.room.notReady")}),(null==x?void 0:x.isHost)&&(0,a.jsx)(n.$,{variant:"primary",onClick:()=>{(null==x?void 0:x.isHost)&&b()},disabled:!h.every(e=>e.isReady)||h.length<2,children:o("multiplayer.room.startGame")}),(0,a.jsx)(n.$,{variant:"secondary",onClick:()=>{j(),k("create"),L(!1)},children:o("multiplayer.room.leaveRoom")})]}),(0,a.jsxs)("div",{className:"border-t pt-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:o("multiplayer.chat")}),(0,a.jsx)("div",{className:"bg-gray-50 p-3 rounded-lg h-32 overflow-y-auto mb-3",children:p.map(e=>(0,a.jsxs)("div",{className:"text-sm mb-1",children:[(0,a.jsxs)("span",{className:"font-medium ".concat("system"===e.playerId?"text-blue-600":"text-gray-800"),children:[e.playerName,":"]}),(0,a.jsx)("span",{className:"ml-2",children:e.content})]},e.id))}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("input",{type:"text",value:I,onChange:e=>z(e.target.value),onKeyDown:e=>"Enter"===e.key&&B(),placeholder:o("multiplayer.typeMessage"),className:"flex-1 p-2 border rounded",maxLength:100}),(0,a.jsx)(n.$,{size:"sm",onClick:B,children:o("common.send")})]})]})]})]})]})})}var c=s(9419),d=s(2163);function m(e){let{isOpen:t,onClose:s}=e,{t:o}=(0,r.o)(),{currentRoom:m,currentPlayer:u,players:x,gameState:h,sharedGameState:g,sendPlayerAction:p,leaveRoom:y}=(0,i.K)(),[v,j]=(0,l.useState)("game");if(!t||!m||"playing"!==h)return null;let b=(e,t)=>{p({type:"use_equipment",data:{equipmentId:e,equipmentName:t,playerId:null==u?void 0:u.id}})},f=(e,t)=>{p({type:"order_action",data:{orderId:e,action:t,playerId:null==u?void 0:u.id}})},N=[{id:"game",name:o("multiplayer.game.tabs.game"),icon:"\uD83C\uDFAE"},{id:"players",name:o("multiplayer.game.tabs.players"),icon:"\uD83D\uDC65"},{id:"chat",name:o("multiplayer.game.tabs.chat"),icon:"\uD83D\uDCAC"}];return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:o("multiplayer.game.title",{roomName:m.name})}),(0,a.jsxs)("p",{className:"text-gray-600",children:[o("multiplayer.game.mode",{mode:m.mode})," • ",o("multiplayer.game.playersCount",{count:x.length.toString()})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,a.jsx)("span",{className:"text-green-800 text-sm",children:o("multiplayer.game.playing")})}),(0,a.jsx)(n.$,{variant:"secondary",onClick:()=>{y(),s()},children:o("multiplayer.game.leaveGame")})]})]})}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("div",{className:"flex space-x-0",children:N.map(e=>(0,a.jsxs)("button",{onClick:()=>j(e.id),className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(v===e.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.icon," ",e.name]},e.id))})}),(0,a.jsxs)("div",{className:"p-6 max-h-[70vh] overflow-y-auto",children:["game"===v&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:o("multiplayer.sharedKitchen")}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[{id:"oven1",name:"Shared Oven",type:"oven",isActive:!1,level:1,efficiency:1,automationLevel:0},{id:"mixer1",name:"Shared Mixer",type:"mixer",isActive:!1,level:1,efficiency:1,automationLevel:0},{id:"counter1",name:"Shared Counter",type:"counter",isActive:!1,level:1,efficiency:1,automationLevel:0}].map(e=>(0,a.jsx)(c.$,{equipment:e,onClick:b},e.id))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mt-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:o("multiplayer.sharedOrders")}),(0,a.jsx)("div",{className:"space-y-4",children:[{id:"1",customerName:"Shared Customer",items:["Chocolate Chip Cookies"],timeLimit:300,reward:50,status:"pending",difficulty:1}].map(e=>(0,a.jsx)(d.p,{order:e,onAccept:e=>f(e,"accept"),onDecline:e=>f(e,"decline"),onComplete:e=>f(e,"complete")},e.id))})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:o("multiplayer.sharedInventory")}),(0,a.jsx)("div",{className:"space-y-3",children:[{name:"Flour",quantity:20,cost:2},{name:"Sugar",quantity:15,cost:3},{name:"Eggs",quantity:12,cost:4}].map((e,t)=>(0,a.jsx)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-800",children:e.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Qty: ",e.quantity]})]})},t))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:o("multiplayer.teamStats")}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:o("multiplayer.ordersCompleted")}),(0,a.jsx)("span",{className:"font-medium",children:"0"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:o("multiplayer.totalRevenue")}),(0,a.jsx)("span",{className:"font-medium",children:"$0"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:o("multiplayer.teamExperience")}),(0,a.jsx)("span",{className:"font-medium",children:"0 XP"})]})]})]})]})]}),"players"===v&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-orange-800",children:o("multiplayer.players",{count:x.length.toString()})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:x.map(e=>(0,a.jsxs)("div",{className:"p-4 rounded-lg border-2 ".concat(e.id===(null==u?void 0:u.id)?"border-orange-400 bg-orange-50":"border-gray-300 bg-white"),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,a.jsx)("span",{className:"text-3xl",children:e.avatar}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"font-semibold text-gray-800",children:[e.name,e.id===(null==u?void 0:u.id)&&(0,a.jsx)("span",{className:"ml-2 text-sm text-orange-600",children:o("multiplayer.you")})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[o("multiplayer.level",{level:e.level.toString()}),e.isHost&&(0,a.jsx)("span",{className:"ml-2 bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs",children:o("multiplayer.host")})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:o("multiplayer.status")}),(0,a.jsx)("span",{className:"text-green-600",children:o("multiplayer.online")})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:o("multiplayer.contribution")}),(0,a.jsx)("span",{className:"font-medium",children:"0 orders"})]})]})]},e.id))})]}),"chat"===v&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-orange-800",children:o("multiplayer.teamChat")}),(0,a.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg h-64 overflow-y-auto",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 text-center",children:o("multiplayer.chatPlaceholder")})}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("input",{type:"text",placeholder:o("multiplayer.typeMessage"),className:"flex-1 p-3 border rounded-lg"}),(0,a.jsx)(n.$,{children:o("common.send")})]})]})]}),(0,a.jsx)("div",{className:"p-4 bg-blue-50 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"text-sm text-blue-700",children:"cooperative"===m.mode?o("multiplayer.mode.cooperative.description"):o("multiplayer.mode.competitive.description")}),(0,a.jsx)("div",{className:"text-sm text-blue-600",children:o("multiplayer.gameTime",{time:"00:00"})})]})})]})})}function u(e){let{onStartSinglePlayer:t,onStartMultiplayer:s,onShowSettings:i,onShowCredits:o,onExit:c}=e,{language:d,setLanguage:m,t:u}=(0,r.o)(),[x,h]=(0,l.useState)(!1);return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-orange-100 via-yellow-50 to-orange-200 flex items-center justify-center relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[(0,a.jsx)("div",{className:"absolute top-10 left-10 text-6xl",children:"\uD83E\uDD56"}),(0,a.jsx)("div",{className:"absolute top-20 right-20 text-4xl",children:"\uD83E\uDDC1"}),(0,a.jsx)("div",{className:"absolute bottom-20 left-20 text-5xl",children:"\uD83C\uDF70"}),(0,a.jsx)("div",{className:"absolute bottom-10 right-10 text-3xl",children:"\uD83E\uDD50"}),(0,a.jsx)("div",{className:"absolute top-1/2 left-1/4 text-4xl",children:"\uD83C\uDF6A"}),(0,a.jsx)("div",{className:"absolute top-1/3 right-1/3 text-5xl",children:"\uD83C\uDF82"})]}),(0,a.jsxs)("div",{className:"relative z-10 text-center space-y-8 p-8 max-w-md w-full",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83E\uDD56"}),(0,a.jsx)("h1",{className:"text-5xl font-bold text-orange-800 mb-2",children:"Bake It Out"}),(0,a.jsx)("p",{className:"text-lg text-orange-600 font-medium",children:u("game.subtitle","Multiplayer Bakery Management")})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(n.$,{size:"lg",className:"w-full text-lg py-4 bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600 text-white font-semibold shadow-lg transform hover:scale-105 transition-all duration-200",onClick:t,children:["\uD83C\uDFAE ",u("menu.singlePlayer","Single Player")]}),(0,a.jsxs)(n.$,{size:"lg",className:"w-full text-lg py-4 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold shadow-lg transform hover:scale-105 transition-all duration-200",onClick:s,children:["\uD83D\uDC65 ",u("menu.multiplayer","Multiplayer")]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)(n.$,{variant:"secondary",size:"md",className:"py-3 bg-white/80 hover:bg-white shadow-md",onClick:i,children:["⚙️ ",u("menu.settings","Settings")]}),(0,a.jsxs)(n.$,{variant:"secondary",size:"md",className:"py-3 bg-white/80 hover:bg-white shadow-md",onClick:()=>h(!x),children:["\uD83C\uDF0D ","en"===d?"English":"Čeština"]})]}),x&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-4 space-y-2",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:u("menu.selectLanguage","Select Language")}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsx)(n.$,{variant:"en"===d?"primary":"secondary",size:"sm",onClick:()=>{m("en"),h(!1)},children:"\uD83C\uDDFA\uD83C\uDDF8 English"}),(0,a.jsx)(n.$,{variant:"cs"===d?"primary":"secondary",size:"sm",onClick:()=>{m("cs"),h(!1)},children:"\uD83C\uDDE8\uD83C\uDDFF Čeština"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)(n.$,{variant:"secondary",size:"md",className:"py-3 bg-white/80 hover:bg-white shadow-md",onClick:o,children:["ℹ️ ",u("menu.about","About")]}),c&&(0,a.jsxs)(n.$,{variant:"secondary",size:"md",className:"py-3 bg-red-100 hover:bg-red-200 text-red-700 shadow-md",onClick:c,children:["\uD83D\uDEAA ",u("menu.exit","Exit")]})]})]}),(0,a.jsxs)("div",{className:"text-sm text-orange-500 opacity-75",children:["v1.0.0 - ",u("menu.version","Beta Version")]})]})]})}function x(e){let{isOpen:t,onClose:s}=e,{t:l}=(0,r.o)();return t?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-orange-500 to-yellow-500 p-6 text-white",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold",children:["ℹ️ ",l("credits.title","About Bake It Out")]}),(0,a.jsx)("p",{className:"text-orange-100 text-sm",children:l("credits.subtitle","Game Information & Credits")})]}),(0,a.jsx)(n.$,{variant:"secondary",size:"sm",className:"bg-white/20 hover:bg-white/30 text-white border-white/30",onClick:s,children:"✕"})]})}),(0,a.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[70vh]",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83E\uDD56"}),(0,a.jsx)("h3",{className:"text-3xl font-bold text-orange-800 mb-2",children:"Bake It Out"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-4",children:l("credits.description","A multiplayer bakery management game with real-time collaboration and localization support")}),(0,a.jsx)("div",{className:"bg-orange-100 rounded-lg p-4 inline-block",children:(0,a.jsxs)("div",{className:"text-sm text-orange-800",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("strong",{children:[l("credits.version","Version"),":"]})," 1.0.0"]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("strong",{children:[l("credits.release","Release"),":"]})," ",l("credits.releaseDate","December 2024")]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("strong",{children:[l("credits.platform","Platform"),":"]})," ",l("credits.platforms","Windows, macOS, Linux")]})]})})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("h4",{className:"text-xl font-semibold text-gray-800 mb-4",children:["\uD83C\uDF1F ",l("credits.features","Key Features")]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDC65"}),(0,a.jsx)("h5",{className:"font-semibold text-blue-800",children:l("credits.multiplayer","Real-time Multiplayer")}),(0,a.jsx)("p",{className:"text-sm text-blue-600",children:l("credits.multiplayerDesc","Collaborate with friends in real-time bakery management")})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDF0D"}),(0,a.jsx)("h5",{className:"font-semibold text-green-800",children:l("credits.localization","Localization")}),(0,a.jsx)("p",{className:"text-sm text-green-600",children:l("credits.localizationDesc","Full support for English and Czech languages")})]}),(0,a.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDFC6"}),(0,a.jsx)("h5",{className:"font-semibold text-purple-800",children:l("credits.progression","Progression System")}),(0,a.jsx)("p",{className:"text-sm text-purple-600",children:l("credits.progressionDesc","Achievements, skills, and equipment upgrades")})]}),(0,a.jsxs)("div",{className:"bg-orange-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83E\uDD16"}),(0,a.jsx)("h5",{className:"font-semibold text-orange-800",children:l("credits.automation","Automation")}),(0,a.jsx)("p",{className:"text-sm text-orange-600",children:l("credits.automationDesc","Advanced automation and efficiency systems")})]})]})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("h4",{className:"text-xl font-semibold text-gray-800 mb-4",children:["\uD83D\uDD27 ",l("credits.technology","Technology Stack")]}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-gray-700",children:"Frontend:"}),(0,a.jsx)("div",{className:"text-gray-600",children:"Next.js, React, TypeScript"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-gray-700",children:"Styling:"}),(0,a.jsx)("div",{className:"text-gray-600",children:"Tailwind CSS"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-gray-700",children:"Desktop:"}),(0,a.jsx)("div",{className:"text-gray-600",children:"Electron"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-gray-700",children:"Multiplayer:"}),(0,a.jsx)("div",{className:"text-gray-600",children:"Socket.IO"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-gray-700",children:"Database:"}),(0,a.jsx)("div",{className:"text-gray-600",children:"Supabase"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-gray-700",children:"i18n:"}),(0,a.jsx)("div",{className:"text-gray-600",children:"Custom Context"})]})]})})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("h4",{className:"text-xl font-semibold text-gray-800 mb-4",children:["\uD83D\uDC68‍\uD83D\uDCBB ",l("credits.team","Development Team")]}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDFAE"}),(0,a.jsx)("div",{className:"font-semibold text-gray-800",children:l("credits.developedBy","Developed by the Bake It Out Team")}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mt-2",children:l("credits.teamDesc","Built with passion for gaming and baking!")})]})})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h4",{className:"text-xl font-semibold text-gray-800 mb-4",children:["\uD83D\uDE4F ",l("credits.thanks","Special Thanks")]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600 space-y-2",children:[(0,a.jsxs)("p",{children:["• ",l("credits.thanksPlayers","All beta testers and players for their feedback")]}),(0,a.jsxs)("p",{children:["• ",l("credits.thanksTranslators","Czech language translators and cultural consultants")]}),(0,a.jsxs)("p",{children:["• ",l("credits.thanksOpenSource","Open source community for amazing tools and libraries")]}),(0,a.jsxs)("p",{children:["• ",l("credits.thanksBakers","Real bakers who inspired the game mechanics")]})]})]}),(0,a.jsxs)("div",{className:"border-t pt-6",children:[(0,a.jsxs)("h4",{className:"text-lg font-semibold text-gray-800 mb-3",children:["\uD83D\uDCDE ",l("credits.contact","Contact & Support")]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("strong",{className:"text-gray-700",children:[l("credits.website","Website"),":"]}),(0,a.jsx)("div",{className:"text-blue-600",children:"www.bakeitout.game"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("strong",{className:"text-gray-700",children:[l("credits.support","Support"),":"]}),(0,a.jsx)("div",{className:"text-blue-600",children:"<EMAIL>"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("strong",{className:"text-gray-700",children:[l("credits.github","GitHub"),":"]}),(0,a.jsx)("div",{className:"text-blue-600",children:"github.com/bakeitout/game"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("strong",{className:"text-gray-700",children:[l("credits.discord","Discord"),":"]}),(0,a.jsx)("div",{className:"text-blue-600",children:"discord.gg/bakeitout"})]})]})]})]}),(0,a.jsx)("div",{className:"bg-gray-50 px-6 py-4 text-center",children:(0,a.jsx)(n.$,{variant:"primary",onClick:s,className:"bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600",children:l("credits.close","Close")})})]})}):null}var h=s(2785),g=s(6645);let p=s.n(g)()(()=>s.e(128).then(s.bind(s,4128)),{loadableGenerated:{webpack:()=>[4128]},ssr:!1});function y(){let{language:e,setLanguage:t,t:s}=(0,r.o)(),{gameState:n}=(0,i.K)(),[c,d]=(0,l.useState)(!1),[g,y]=(0,l.useState)(!1),[v,j]=(0,l.useState)(!1),[b,f]=(0,l.useState)(!1),[N,w]=(0,l.useState)(!1),[k,C]=(0,l.useState)({soundEnabled:!0,musicEnabled:!0,notifications:!0,autoSave:!0});return v?(0,a.jsx)(p,{}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u,{onStartSinglePlayer:()=>j(!0),onStartMultiplayer:()=>d(!0),onShowSettings:()=>w(!0),onShowCredits:()=>f(!0),onExit:window.electronAPI?()=>{window.electronAPI&&window.electronAPI.quit()}:void 0}),(0,a.jsx)(x,{isOpen:b,onClose:()=>f(!1)}),(0,a.jsx)(h.b,{isOpen:N,onClose:()=>w(!1),settings:k,onSettingsChange:e=>{C(e)}}),(0,a.jsx)(o,{isOpen:c,onClose:()=>d(!1)}),(0,a.jsx)(m,{isOpen:g||"playing"===n,onClose:()=>y(!1)})]})}},2146:(e,t,s)=>{"use strict";function a(e){let{reason:t,children:s}=e;return s}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return a}}),s(5262)},2617:(e,t,s)=>{Promise.resolve().then(s.bind(s,1006))},4054:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{bindSnapshot:function(){return n},createAsyncLocalStorage:function(){return r},createSnapshot:function(){return i}});let s=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class a{disable(){throw s}getStore(){}run(){throw s}exit(){throw s}enterWith(){throw s}static bind(e){return e}}let l="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function r(){return l?new l:new a}function n(e){return l?l.bind(e):a.bind(e)}function i(){return l?l.snapshot():function(e,...t){return e(...t)}}},5744:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return a.workAsyncStorageInstance}});let a=s(7828)},6645:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let a=s(8229)._(s(7357));function l(e,t){var s;let l={};"function"==typeof e&&(l.loader=e);let r={...l,...t};return(0,a.default)({...r,modules:null==(s=r.loadableGenerated)?void 0:s.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7357:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let a=s(5155),l=s(2115),r=s(2146);function n(e){return{default:e&&"default"in e?e.default:e}}s(255);let i={loader:()=>Promise.resolve(n(()=>null)),loading:null,ssr:!0},o=function(e){let t={...i,...e},s=(0,l.lazy)(()=>t.loader().then(n)),o=t.loading;function c(e){let n=o?(0,a.jsx)(o,{isLoading:!0,pastDelay:!0,error:null}):null,i=!t.ssr||!!t.loading,c=i?l.Suspense:l.Fragment,d=t.ssr?(0,a.jsxs)(a.Fragment,{children:[null,(0,a.jsx)(s,{...e})]}):(0,a.jsx)(r.BailoutToCSR,{reason:"next/dynamic",children:(0,a.jsx)(s,{...e})});return(0,a.jsx)(c,{...i?{fallback:n}:{},children:d})}return c.displayName="LoadableComponent",c}},7828:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return a}});let a=(0,s(4054).createAsyncLocalStorage)()}},e=>{e.O(0,[298,283,81,441,964,358],()=>e(e.s=2617)),_N_E=e.O()}]);