// Discord Rich Presence integration for Bake It Out
// Shows current game status, activity, and multiplayer information

// Dynamic import for Discord RPC to avoid SSR issues
let DiscordRPC: any = null
if (typeof window !== 'undefined') {
  import('discord-rpc').then((module) => {
    DiscordRPC = module.Client
  }).catch(() => {
    console.log('Discord RPC not available in this environment')
  })
}

export interface GameActivity {
  state: string
  details: string
  largeImageKey?: string
  largeImageText?: string
  smallImageKey?: string
  smallImageText?: string
  startTimestamp?: number
  endTimestamp?: number
  partyId?: string
  partySize?: number
  partyMax?: number
  joinSecret?: string
  spectateSecret?: string
  matchSecret?: string
  buttons?: Array<{
    label: string
    url: string
  }>
}

export interface PlayerStatus {
  level: number
  money: number
  currentActivity: 'menu' | 'baking' | 'managing' | 'multiplayer' | 'idle'
  bakeryName?: string
  currentOrder?: string
  multiplayerRoom?: {
    id: string
    playerCount: number
    maxPlayers: number
  }
  playTime?: number
}

class DiscordRPCService {
  private client: any = null
  private isConnected: boolean = false
  private isEnabled: boolean = true
  private startTime: number = Date.now()
  private currentActivity: GameActivity | null = null
  private reconnectAttempts: number = 0
  private maxReconnectAttempts: number = 5
  private reconnectDelay: number = 5000

  // Discord Application ID for Bake It Out
  private readonly CLIENT_ID = '1234567890123456789' // This would be your actual Discord app ID

  constructor() {
    // Only initialize in browser environment
    if (typeof window !== 'undefined') {
      setTimeout(() => this.initializeRPC(), 1000)
    }
  }

  private async initializeRPC() {
    if (!this.isEnabled || typeof window === 'undefined' || !DiscordRPC) return

    try {
      this.client = new DiscordRPC({ transport: 'ipc' })
      
      this.client.on('ready', () => {
        console.log('Discord RPC connected successfully')
        this.isConnected = true
        this.reconnectAttempts = 0
        this.updateActivity({
          state: 'In Main Menu',
          details: 'Starting the bakery adventure',
          largeImageKey: 'bake_it_out_logo',
          largeImageText: 'Bake It Out - Bakery Management Game',
          startTimestamp: this.startTime
        })
      })

      this.client.on('disconnected', () => {
        console.log('Discord RPC disconnected')
        this.isConnected = false
        this.attemptReconnect()
      })

      await this.client.login({ clientId: this.CLIENT_ID })
    } catch (error) {
      console.error('Failed to initialize Discord RPC:', error)
      this.isConnected = false
      this.attemptReconnect()
    }
  }

  private async attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Max reconnection attempts reached. Discord RPC disabled.')
      return
    }

    this.reconnectAttempts++
    console.log(`Attempting to reconnect to Discord RPC (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
    
    setTimeout(() => {
      this.initializeRPC()
    }, this.reconnectDelay)
  }

  public async updatePlayerStatus(playerStatus: PlayerStatus) {
    if (!this.isConnected || !this.client) return

    const activity = this.createActivityFromPlayerStatus(playerStatus)
    await this.updateActivity(activity)
  }

  private createActivityFromPlayerStatus(status: PlayerStatus): GameActivity {
    const baseActivity: GameActivity = {
      state: '',
      details: '',
      largeImageKey: 'bake_it_out_logo',
      largeImageText: 'Bake It Out - Bakery Management Game',
      startTimestamp: this.startTime
    }

    switch (status.currentActivity) {
      case 'menu':
        return {
          ...baseActivity,
          state: 'In Main Menu',
          details: 'Choosing game mode',
          smallImageKey: 'menu_icon',
          smallImageText: 'Main Menu'
        }

      case 'baking':
        return {
          ...baseActivity,
          state: `Level ${status.level} Baker`,
          details: status.currentOrder 
            ? `Baking: ${status.currentOrder}`
            : 'Managing the bakery',
          smallImageKey: 'baking_icon',
          smallImageText: 'Baking',
          buttons: [
            {
              label: 'Play Bake It Out',
              url: 'https://bakeitout.game'
            }
          ]
        }

      case 'managing':
        return {
          ...baseActivity,
          state: `Level ${status.level} - $${status.money}`,
          details: status.bakeryName 
            ? `Managing ${status.bakeryName}`
            : 'Managing bakery',
          smallImageKey: 'management_icon',
          smallImageText: 'Bakery Management'
        }

      case 'multiplayer':
        const multiplayerActivity = {
          ...baseActivity,
          state: `Level ${status.level} Baker`,
          details: 'Playing with friends',
          smallImageKey: 'multiplayer_icon',
          smallImageText: 'Multiplayer',
          buttons: [
            {
              label: 'Join Game',
              url: 'https://bakeitout.game/join'
            }
          ]
        }

        if (status.multiplayerRoom) {
          multiplayerActivity.partyId = status.multiplayerRoom.id
          multiplayerActivity.partySize = status.multiplayerRoom.playerCount
          multiplayerActivity.partyMax = status.multiplayerRoom.maxPlayers
          multiplayerActivity.details = `Multiplayer Bakery (${status.multiplayerRoom.playerCount}/${status.multiplayerRoom.maxPlayers})`
        }

        return multiplayerActivity

      case 'idle':
        return {
          ...baseActivity,
          state: `Level ${status.level} Baker`,
          details: 'Taking a break',
          smallImageKey: 'idle_icon',
          smallImageText: 'Idle'
        }

      default:
        return {
          ...baseActivity,
          state: 'Playing Bake It Out',
          details: 'Bakery Management Game'
        }
    }
  }

  public async updateActivity(activity: GameActivity) {
    if (!this.isConnected || !this.client) return

    try {
      await this.client.setActivity(activity)
      this.currentActivity = activity
      console.log('Discord RPC activity updated:', activity.details)
    } catch (error) {
      console.error('Failed to update Discord RPC activity:', error)
    }
  }

  public async setMenuActivity() {
    await this.updateActivity({
      state: 'In Main Menu',
      details: 'Choosing game mode',
      largeImageKey: 'bake_it_out_logo',
      largeImageText: 'Bake It Out - Bakery Management Game',
      smallImageKey: 'menu_icon',
      smallImageText: 'Main Menu',
      startTimestamp: this.startTime
    })
  }

  public async setGameActivity(level: number, money: number, activity?: string) {
    await this.updateActivity({
      state: `Level ${level} - $${money}`,
      details: activity || 'Managing bakery',
      largeImageKey: 'bake_it_out_logo',
      largeImageText: 'Bake It Out - Bakery Management Game',
      smallImageKey: 'baking_icon',
      smallImageText: 'In Game',
      startTimestamp: this.startTime,
      buttons: [
        {
          label: 'Play Bake It Out',
          url: 'https://bakeitout.game'
        }
      ]
    })
  }

  public async setMultiplayerActivity(roomId: string, playerCount: number, maxPlayers: number) {
    await this.updateActivity({
      state: 'Multiplayer Bakery',
      details: `Playing with friends (${playerCount}/${maxPlayers})`,
      largeImageKey: 'bake_it_out_logo',
      largeImageText: 'Bake It Out - Bakery Management Game',
      smallImageKey: 'multiplayer_icon',
      smallImageText: 'Multiplayer',
      startTimestamp: this.startTime,
      partyId: roomId,
      partySize: playerCount,
      partyMax: maxPlayers,
      buttons: [
        {
          label: 'Join Game',
          url: `https://bakeitout.game/join/${roomId}`
        }
      ]
    })
  }

  public async setBakingActivity(level: number, currentOrder: string) {
    await this.updateActivity({
      state: `Level ${level} Baker`,
      details: `Baking: ${currentOrder}`,
      largeImageKey: 'bake_it_out_logo',
      largeImageText: 'Bake It Out - Bakery Management Game',
      smallImageKey: 'baking_icon',
      smallImageText: 'Baking',
      startTimestamp: this.startTime
    })
  }

  public async clearActivity() {
    if (!this.isConnected || !this.client) return

    try {
      await this.client.clearActivity()
      this.currentActivity = null
      console.log('Discord RPC activity cleared')
    } catch (error) {
      console.error('Failed to clear Discord RPC activity:', error)
    }
  }

  public setEnabled(enabled: boolean) {
    this.isEnabled = enabled
    
    if (!enabled && this.isConnected) {
      this.clearActivity()
      this.disconnect()
    } else if (enabled && !this.isConnected) {
      this.initializeRPC()
    }
  }

  public isRPCEnabled(): boolean {
    return this.isEnabled
  }

  public isRPCConnected(): boolean {
    return this.isConnected
  }

  public getCurrentActivity(): GameActivity | null {
    return this.currentActivity
  }

  public async disconnect() {
    if (this.client && this.isConnected) {
      try {
        await this.client.destroy()
        console.log('Discord RPC disconnected')
      } catch (error) {
        console.error('Error disconnecting Discord RPC:', error)
      }
    }
    
    this.client = null
    this.isConnected = false
    this.currentActivity = null
  }

  // Cleanup method for when the app closes
  public async cleanup() {
    await this.clearActivity()
    await this.disconnect()
  }
}

// Export singleton instance
export const discordRPC = new DiscordRPCService()

// Export for use in components
export default discordRPC
