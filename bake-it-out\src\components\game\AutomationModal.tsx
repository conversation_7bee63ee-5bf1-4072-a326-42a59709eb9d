'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { AutomationSettings, AUTOMATION_UPGRADES } from '@/lib/automationSystem'
import { useGame } from '@/contexts/GameContext'

interface AutomationModalProps {
  isOpen: boolean
  onClose: () => void
}

export function AutomationModal({ isOpen, onClose }: AutomationModalProps) {
  const { player, equipment, automationSettings, updateAutomationSettings, purchaseAutomationUpgrade } = useGame()
  const [selectedTab, setSelectedTab] = useState<'settings' | 'upgrades' | 'status'>('settings')

  if (!isOpen) return null

  const automatedEquipment = equipment.filter(eq => eq.automationLevel > 0)
  const availableUpgrades = AUTOMATION_UPGRADES.filter(upgrade => 
    player.level >= upgrade.unlockLevel && !player.automationUpgrades?.includes(upgrade.id)
  )

  const handleSettingChange = (key: keyof AutomationSettings, value: any) => {
    updateAutomationSettings({ [key]: value })
  }

  const tabs = [
    { id: 'settings', name: 'Settings', icon: '⚙️' },
    { id: 'upgrades', name: 'Upgrades', icon: '🔧' },
    { id: 'status', name: 'Status', icon: '📊' }
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-orange-800">🤖 Automation Control</h2>
            <Button variant="secondary" onClick={onClose}>
              ✕ Close
            </Button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <div className="flex space-x-0">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
                  selectedTab === tab.id
                    ? 'border-orange-500 text-orange-600 bg-orange-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                {tab.icon} {tab.name}
              </button>
            ))}
          </div>
        </div>

        <div className="p-6 max-h-[60vh] overflow-y-auto">
          {/* Settings Tab */}
          {selectedTab === 'settings' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Master Control */}
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-blue-800 mb-3">🎛️ Master Control</h3>
                  <div className="space-y-3">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={automationSettings?.enabled || false}
                        onChange={(e) => handleSettingChange('enabled', e.target.checked)}
                        className="rounded"
                      />
                      <span className="text-sm">Enable Automation</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={automationSettings?.autoStart || false}
                        onChange={(e) => handleSettingChange('autoStart', e.target.checked)}
                        className="rounded"
                      />
                      <span className="text-sm">Auto-start Equipment</span>
                    </label>
                  </div>
                </div>

                {/* Priority Settings */}
                <div className="bg-green-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-green-800 mb-3">🎯 Priority Mode</h3>
                  <select
                    value={automationSettings?.priorityMode || 'efficiency'}
                    onChange={(e) => handleSettingChange('priorityMode', e.target.value)}
                    className="w-full p-2 border rounded-lg"
                  >
                    <option value="efficiency">Efficiency (Orders First)</option>
                    <option value="profit">Profit (Highest Value)</option>
                    <option value="speed">Speed (Fastest Recipes)</option>
                  </select>
                  <p className="text-xs text-green-600 mt-1">
                    How automation chooses what to bake
                  </p>
                </div>

                {/* Capacity Settings */}
                <div className="bg-purple-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-purple-800 mb-3">⚡ Performance</h3>
                  <div className="space-y-2">
                    <label className="block text-sm">
                      Max Concurrent Jobs: {automationSettings?.maxConcurrentJobs || 2}
                    </label>
                    <input
                      type="range"
                      min="1"
                      max="5"
                      value={automationSettings?.maxConcurrentJobs || 2}
                      onChange={(e) => handleSettingChange('maxConcurrentJobs', parseInt(e.target.value))}
                      className="w-full"
                    />
                  </div>
                </div>

                {/* Safety Settings */}
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-yellow-800 mb-3">🛡️ Safety</h3>
                  <div className="space-y-2">
                    <label className="block text-sm">
                      Stop when ingredients below: {automationSettings?.ingredientThreshold || 5}
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="20"
                      value={automationSettings?.ingredientThreshold || 5}
                      onChange={(e) => handleSettingChange('ingredientThreshold', parseInt(e.target.value))}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Upgrades Tab */}
          {selectedTab === 'upgrades' && (
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg mb-4">
                <h3 className="font-medium text-blue-800 mb-2">💡 Automation Upgrades</h3>
                <p className="text-sm text-blue-700">
                  Improve your automation efficiency, speed, and intelligence with these upgrades.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {availableUpgrades.map(upgrade => (
                  <div
                    key={upgrade.id}
                    className="p-4 border rounded-lg bg-white hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-semibold text-gray-800">{upgrade.name}</h4>
                      <span className="text-sm text-green-600 font-medium">${upgrade.cost}</span>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{upgrade.description}</p>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500 uppercase tracking-wide">
                        {upgrade.type}
                      </span>
                      <Button
                        size="sm"
                        variant={player.money >= upgrade.cost ? 'primary' : 'secondary'}
                        disabled={player.money < upgrade.cost}
                        onClick={() => purchaseAutomationUpgrade(upgrade.id)}
                      >
                        {player.money >= upgrade.cost ? 'Purchase' : 'Too Expensive'}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              {availableUpgrades.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <div className="text-4xl mb-2">🔧</div>
                  <p>No upgrades available at your current level.</p>
                  <p className="text-sm">Level up to unlock more automation upgrades!</p>
                </div>
              )}
            </div>
          )}

          {/* Status Tab */}
          {selectedTab === 'status' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-green-50 p-4 rounded-lg text-center">
                  <div className="text-2xl text-green-600 mb-1">{automatedEquipment.length}</div>
                  <div className="text-sm text-green-800">Automated Equipment</div>
                </div>
                <div className="bg-blue-50 p-4 rounded-lg text-center">
                  <div className="text-2xl text-blue-600 mb-1">
                    {automationSettings?.enabled ? '✅' : '❌'}
                  </div>
                  <div className="text-sm text-blue-800">Automation Status</div>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg text-center">
                  <div className="text-2xl text-purple-600 mb-1">
                    {player.automationUpgrades?.length || 0}
                  </div>
                  <div className="text-sm text-purple-800">Active Upgrades</div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-semibold text-gray-800">🏭 Equipment Status</h3>
                {automatedEquipment.length > 0 ? (
                  <div className="space-y-2">
                    {automatedEquipment.map(eq => (
                      <div key={eq.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div>
                          <span className="font-medium">{eq.name}</span>
                          <span className="text-sm text-gray-500 ml-2">
                            Level {eq.automationLevel} • {eq.efficiency}x efficiency
                          </span>
                        </div>
                        <div className={`px-2 py-1 rounded text-xs ${
                          eq.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
                        }`}>
                          {eq.isActive ? 'Running' : 'Idle'}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4 text-gray-500">
                    <p>No automated equipment available.</p>
                    <p className="text-sm">Purchase auto-equipment from the shop to get started!</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
