[{"C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\app\\game\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\app\\layout.tsx": "2", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\app\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\AchievementsModal.tsx": "4", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\AutomationModal.tsx": "5", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\BakeryManagerModal.tsx": "6", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\BakingModal.tsx": "7", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\Equipment.tsx": "8", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\EquipmentShopModal.tsx": "9", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\LevelUpModal.tsx": "10", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\NotificationSystem.tsx": "11", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\Order.tsx": "12", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\RecipeModal.tsx": "13", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\SettingsModal.tsx": "14", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\ShopModal.tsx": "15", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\SkillTreeModal.tsx": "16", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\multiplayer\\MultiplayerGame.tsx": "17", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\multiplayer\\MultiplayerLobby.tsx": "18", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\ui\\Button.tsx": "19", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\contexts\\GameContext.tsx": "20", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\contexts\\LanguageContext.tsx": "21", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\contexts\\MultiplayerContext.tsx": "22", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\hooks\\useAuth.ts": "23", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\hooks\\usePlayer.ts": "24", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\lib\\automationSystem.ts": "25", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\lib\\gameLogic.ts": "26", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\lib\\progressionSystem.ts": "27", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\lib\\saveSystem.ts": "28", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\lib\\socket.ts": "29", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\lib\\supabase.ts": "30", "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\lib\\utils.ts": "31"}, {"size": 12962, "mtime": 1752872415637, "results": "32", "hashOfConfig": "33"}, {"size": 1096, "mtime": 1752870370571, "results": "34", "hashOfConfig": "33"}, {"size": 3958, "mtime": 1752870481971, "results": "35", "hashOfConfig": "33"}, {"size": 7137, "mtime": 1752867303293, "results": "36", "hashOfConfig": "33"}, {"size": 11488, "mtime": 1752868050550, "results": "37", "hashOfConfig": "33"}, {"size": 12693, "mtime": 1752868852960, "results": "38", "hashOfConfig": "33"}, {"size": 6409, "mtime": 1752866809798, "results": "39", "hashOfConfig": "33"}, {"size": 2671, "mtime": 1752869742986, "results": "40", "hashOfConfig": "33"}, {"size": 9066, "mtime": 1752868426913, "results": "41", "hashOfConfig": "33"}, {"size": 3528, "mtime": 1752867271164, "results": "42", "hashOfConfig": "33"}, {"size": 3743, "mtime": 1752866906207, "results": "43", "hashOfConfig": "33"}, {"size": 4191, "mtime": 1752869647033, "results": "44", "hashOfConfig": "33"}, {"size": 5757, "mtime": 1752869403394, "results": "45", "hashOfConfig": "33"}, {"size": 11929, "mtime": 1752868796024, "results": "46", "hashOfConfig": "33"}, {"size": 5615, "mtime": 1752869494348, "results": "47", "hashOfConfig": "33"}, {"size": 8883, "mtime": 1752867343201, "results": "48", "hashOfConfig": "33"}, {"size": 11824, "mtime": 1752871275332, "results": "49", "hashOfConfig": "33"}, {"size": 14167, "mtime": 1752871090204, "results": "50", "hashOfConfig": "33"}, {"size": 1183, "mtime": 1752865751457, "results": "51", "hashOfConfig": "33"}, {"size": 13884, "mtime": 1752868378893, "results": "52", "hashOfConfig": "33"}, {"size": 33993, "mtime": 1752870883359, "results": "53", "hashOfConfig": "33"}, {"size": 9913, "mtime": 1752872510287, "results": "54", "hashOfConfig": "33"}, {"size": 1359, "mtime": 1752865336705, "results": "55", "hashOfConfig": "33"}, {"size": 3163, "mtime": 1752865355159, "results": "56", "hashOfConfig": "33"}, {"size": 7019, "mtime": 1752868002043, "results": "57", "hashOfConfig": "33"}, {"size": 5046, "mtime": 1752866563981, "results": "58", "hashOfConfig": "33"}, {"size": 7708, "mtime": 1752867247991, "results": "59", "hashOfConfig": "33"}, {"size": 9156, "mtime": 1752868750565, "results": "60", "hashOfConfig": "33"}, {"size": 7921, "mtime": 1752870095369, "results": "61", "hashOfConfig": "33"}, {"size": 1978, "mtime": 1752865143923, "results": "62", "hashOfConfig": "33"}, {"size": 1099, "mtime": 1752865304052, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1klkg1", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 16, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 11, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\app\\game\\page.tsx", ["157", "158", "159"], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\AchievementsModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\AutomationModal.tsx", ["160", "161"], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\BakeryManagerModal.tsx", ["162"], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\BakingModal.tsx", ["163", "164"], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\Equipment.tsx", ["165"], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\EquipmentShopModal.tsx", ["166", "167", "168"], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\LevelUpModal.tsx", ["169"], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\NotificationSystem.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\Order.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\RecipeModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\SettingsModal.tsx", ["170", "171", "172", "173"], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\ShopModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\game\\SkillTreeModal.tsx", ["174"], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\multiplayer\\MultiplayerGame.tsx", ["175", "176", "177"], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\multiplayer\\MultiplayerLobby.tsx", ["178", "179", "180"], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\contexts\\GameContext.tsx", ["181", "182", "183", "184", "185", "186"], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\contexts\\LanguageContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\contexts\\MultiplayerContext.tsx", ["187", "188", "189", "190", "191", "192", "193", "194", "195", "196", "197", "198", "199", "200", "201", "202", "203", "204"], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\hooks\\useAuth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\hooks\\usePlayer.ts", ["205"], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\lib\\automationSystem.ts", ["206", "207", "208", "209", "210", "211"], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\lib\\gameLogic.ts", [], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\lib\\progressionSystem.ts", [], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\lib\\saveSystem.ts", ["212", "213", "214", "215", "216", "217", "218", "219", "220", "221", "222"], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\lib\\socket.ts", ["223", "224", "225", "226", "227", "228", "229", "230", "231", "232", "233", "234"], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\lib\\utils.ts", [], [], {"ruleId": "235", "severity": 1, "message": "236", "line": 32, "column": 5, "nodeType": null, "messageId": "237", "endLine": 32, "endColumn": 20}, {"ruleId": "235", "severity": 1, "message": "238", "line": 85, "column": 59, "nodeType": null, "messageId": "237", "endLine": 85, "endColumn": 68}, {"ruleId": "239", "severity": 2, "message": "240", "line": 120, "column": 41, "nodeType": "241", "messageId": "242", "endLine": 120, "endColumn": 44, "suggestions": "243"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 24, "column": 70, "nodeType": "241", "messageId": "242", "endLine": 24, "endColumn": 73, "suggestions": "244"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 52, "column": 57, "nodeType": "241", "messageId": "242", "endLine": 52, "endColumn": 60, "suggestions": "245"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 166, "column": 57, "nodeType": "241", "messageId": "242", "endLine": 166, "endColumn": 60, "suggestions": "246"}, {"ruleId": "247", "severity": 2, "message": "248", "line": 34, "column": 7, "nodeType": "249", "endLine": 34, "endColumn": 20}, {"ruleId": "250", "severity": 2, "message": "251", "line": 89, "column": 24, "nodeType": "252", "messageId": "253", "suggestions": "254"}, {"ruleId": "235", "severity": 1, "message": "255", "line": 3, "column": 10, "nodeType": null, "messageId": "237", "endLine": 3, "endColumn": 18}, {"ruleId": "235", "severity": 1, "message": "256", "line": 113, "column": 19, "nodeType": null, "messageId": "237", "endLine": 113, "endColumn": 28}, {"ruleId": "235", "severity": 1, "message": "236", "line": 113, "column": 42, "nodeType": null, "messageId": "237", "endLine": 113, "endColumn": 57}, {"ruleId": "239", "severity": 2, "message": "240", "line": 137, "column": 28, "nodeType": "241", "messageId": "242", "endLine": 137, "endColumn": 31, "suggestions": "257"}, {"ruleId": "250", "severity": 2, "message": "251", "line": 78, "column": 67, "nodeType": "252", "messageId": "253", "suggestions": "258"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 32, "column": 64, "nodeType": "241", "messageId": "242", "endLine": 32, "endColumn": 67, "suggestions": "259"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 57, "column": 58, "nodeType": "241", "messageId": "242", "endLine": 57, "endColumn": 61, "suggestions": "260"}, {"ruleId": "235", "severity": 1, "message": "261", "line": 83, "column": 16, "nodeType": null, "messageId": "237", "endLine": 83, "endColumn": 21}, {"ruleId": "239", "severity": 2, "message": "240", "line": 117, "column": 55, "nodeType": "241", "messageId": "242", "endLine": 117, "endColumn": 58, "suggestions": "262"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 68, "column": 41, "nodeType": "241", "messageId": "242", "endLine": 68, "endColumn": 44, "suggestions": "263"}, {"ruleId": "235", "severity": 1, "message": "264", "line": 3, "column": 20, "nodeType": null, "messageId": "237", "endLine": 3, "endColumn": 29}, {"ruleId": "235", "severity": 1, "message": "265", "line": 22, "column": 5, "nodeType": null, "messageId": "237", "endLine": 22, "endColumn": 20}, {"ruleId": "239", "severity": 2, "message": "240", "line": 119, "column": 57, "nodeType": "241", "messageId": "242", "endLine": 119, "endColumn": 60, "suggestions": "266"}, {"ruleId": "235", "severity": 1, "message": "267", "line": 22, "column": 5, "nodeType": null, "messageId": "237", "endLine": 22, "endColumn": 14}, {"ruleId": "239", "severity": 2, "message": "240", "line": 148, "column": 55, "nodeType": "241", "messageId": "242", "endLine": 148, "endColumn": 58, "suggestions": "268"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 201, "column": 68, "nodeType": "241", "messageId": "242", "endLine": 201, "endColumn": 71, "suggestions": "269"}, {"ruleId": "235", "severity": 1, "message": "270", "line": 21, "column": 3, "nodeType": null, "messageId": "237", "endLine": 21, "endColumn": 27}, {"ruleId": "235", "severity": 1, "message": "271", "line": 79, "column": 7, "nodeType": null, "messageId": "237", "endLine": 79, "endColumn": 14}, {"ruleId": "235", "severity": 1, "message": "272", "line": 90, "column": 7, "nodeType": null, "messageId": "237", "endLine": 90, "endColumn": 21}, {"ruleId": "235", "severity": 1, "message": "273", "line": 153, "column": 25, "nodeType": null, "messageId": "237", "endLine": 153, "endColumn": 41}, {"ruleId": "247", "severity": 2, "message": "248", "line": 290, "column": 15, "nodeType": "249", "endLine": 290, "endColumn": 28}, {"ruleId": "247", "severity": 2, "message": "248", "line": 432, "column": 7, "nodeType": "249", "endLine": 432, "endColumn": 20}, {"ruleId": "239", "severity": 2, "message": "240", "line": 38, "column": 52, "nodeType": "241", "messageId": "242", "endLine": 38, "endColumn": 55, "suggestions": "274"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 43, "column": 34, "nodeType": "241", "messageId": "242", "endLine": 43, "endColumn": 37, "suggestions": "275"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 84, "column": 33, "nodeType": "241", "messageId": "242", "endLine": 84, "endColumn": 36, "suggestions": "276"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 153, "column": 41, "nodeType": "241", "messageId": "242", "endLine": 153, "endColumn": 44, "suggestions": "277"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 159, "column": 45, "nodeType": "241", "messageId": "242", "endLine": 159, "endColumn": 48, "suggestions": "278"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 171, "column": 28, "nodeType": "241", "messageId": "242", "endLine": 171, "endColumn": 31, "suggestions": "279"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 172, "column": 31, "nodeType": "241", "messageId": "242", "endLine": 172, "endColumn": 34, "suggestions": "280"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 173, "column": 26, "nodeType": "241", "messageId": "242", "endLine": 173, "endColumn": 29, "suggestions": "281"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 190, "column": 31, "nodeType": "241", "messageId": "242", "endLine": 190, "endColumn": 34, "suggestions": "282"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 191, "column": 34, "nodeType": "241", "messageId": "242", "endLine": 191, "endColumn": 37, "suggestions": "283"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 192, "column": 29, "nodeType": "241", "messageId": "242", "endLine": 192, "endColumn": 32, "suggestions": "284"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 227, "column": 12, "nodeType": "241", "messageId": "242", "endLine": 227, "endColumn": 15, "suggestions": "285"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 229, "column": 21, "nodeType": "241", "messageId": "242", "endLine": 229, "endColumn": 24, "suggestions": "286"}, {"ruleId": "235", "severity": 1, "message": "287", "line": 238, "column": 15, "nodeType": null, "messageId": "237", "endLine": 238, "endColumn": 19}, {"ruleId": "235", "severity": 1, "message": "288", "line": 238, "column": 21, "nodeType": null, "messageId": "237", "endLine": 238, "endColumn": 27}, {"ruleId": "239", "severity": 2, "message": "240", "line": 240, "column": 21, "nodeType": "241", "messageId": "242", "endLine": 240, "endColumn": 24, "suggestions": "289"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 263, "column": 71, "nodeType": "241", "messageId": "242", "endLine": 263, "endColumn": 74, "suggestions": "290"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 285, "column": 53, "nodeType": "241", "messageId": "242", "endLine": 285, "endColumn": 56, "suggestions": "291"}, {"ruleId": "292", "severity": 1, "message": "293", "line": 19, "column": 6, "nodeType": "294", "endLine": 19, "endColumn": 12, "suggestions": "295"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 180, "column": 11, "nodeType": "241", "messageId": "242", "endLine": 180, "endColumn": 14, "suggestions": "296"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 184, "column": 60, "nodeType": "241", "messageId": "242", "endLine": 184, "endColumn": 63, "suggestions": "297"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 202, "column": 21, "nodeType": "241", "messageId": "242", "endLine": 202, "endColumn": 24, "suggestions": "298"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 203, "column": 14, "nodeType": "241", "messageId": "242", "endLine": 203, "endColumn": 17, "suggestions": "299"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 205, "column": 18, "nodeType": "241", "messageId": "242", "endLine": 205, "endColumn": 21, "suggestions": "300"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 208, "column": 43, "nodeType": "241", "messageId": "242", "endLine": 208, "endColumn": 46, "suggestions": "301"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 17, "column": 14, "nodeType": "241", "messageId": "242", "endLine": 17, "endColumn": 17, "suggestions": "302"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 18, "column": 14, "nodeType": "241", "messageId": "242", "endLine": 18, "endColumn": 17, "suggestions": "303"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 19, "column": 17, "nodeType": "241", "messageId": "242", "endLine": 19, "endColumn": 20, "suggestions": "304"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 20, "column": 11, "nodeType": "241", "messageId": "242", "endLine": 20, "endColumn": 14, "suggestions": "305"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 21, "column": 23, "nodeType": "241", "messageId": "242", "endLine": 21, "endColumn": 26, "suggestions": "306"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 39, "column": 14, "nodeType": "241", "messageId": "242", "endLine": 39, "endColumn": 17, "suggestions": "307"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 40, "column": 14, "nodeType": "241", "messageId": "242", "endLine": 40, "endColumn": 17, "suggestions": "308"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 41, "column": 11, "nodeType": "241", "messageId": "242", "endLine": 41, "endColumn": 14, "suggestions": "309"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 42, "column": 19, "nodeType": "241", "messageId": "242", "endLine": 42, "endColumn": 22, "suggestions": "310"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 43, "column": 18, "nodeType": "241", "messageId": "242", "endLine": 43, "endColumn": 21, "suggestions": "311"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 204, "column": 32, "nodeType": "241", "messageId": "242", "endLine": 204, "endColumn": 35, "suggestions": "312"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 49, "column": 13, "nodeType": "241", "messageId": "242", "endLine": 49, "endColumn": 16, "suggestions": "313"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 50, "column": 16, "nodeType": "241", "messageId": "242", "endLine": 50, "endColumn": 19, "suggestions": "314"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 51, "column": 21, "nodeType": "241", "messageId": "242", "endLine": 51, "endColumn": 24, "suggestions": "315"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 64, "column": 11, "nodeType": "241", "messageId": "242", "endLine": 64, "endColumn": 14, "suggestions": "316"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 88, "column": 11, "nodeType": "241", "messageId": "242", "endLine": 88, "endColumn": 14, "suggestions": "317"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 96, "column": 41, "nodeType": "241", "messageId": "242", "endLine": 96, "endColumn": 44, "suggestions": "318"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 100, "column": 27, "nodeType": "241", "messageId": "242", "endLine": 100, "endColumn": 30, "suggestions": "319"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 120, "column": 15, "nodeType": "241", "messageId": "242", "endLine": 120, "endColumn": 18, "suggestions": "320"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 247, "column": 11, "nodeType": "241", "messageId": "242", "endLine": 247, "endColumn": 14, "suggestions": "321"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 271, "column": 40, "nodeType": "241", "messageId": "242", "endLine": 271, "endColumn": 43, "suggestions": "322"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 275, "column": 41, "nodeType": "241", "messageId": "242", "endLine": 275, "endColumn": 44, "suggestions": "323"}, {"ruleId": "239", "severity": 2, "message": "240", "line": 279, "column": 42, "nodeType": "241", "messageId": "242", "endLine": 279, "endColumn": 45, "suggestions": "324"}, "@typescript-eslint/no-unused-vars", "'updateEquipment' is assigned a value but never used.", "unusedVar", "'showError' is assigned a value but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["325", "326"], ["327", "328"], ["329", "330"], ["331", "332"], "react-hooks/rules-of-hooks", "React Hook \"useIngredient\" cannot be called inside a callback. React Hooks must be called in a React function component or a custom React Hook function.", "Identifier", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["333", "334", "335", "336"], "'useState' is defined but never used.", "'equipment' is assigned a value but never used.", ["337", "338"], ["339", "340", "341", "342"], ["343", "344"], ["345", "346"], "'error' is defined but never used.", ["347", "348"], ["349", "350"], "'useEffect' is defined but never used.", "'sharedGameState' is assigned a value but never used.", ["351", "352"], "'gameState' is assigned a value but never used.", ["353", "354"], ["355", "356"], "'calculateAutomationSpeed' is defined but never used.", "'RECIPES' is assigned a value but never used.", "'CUSTOMER_NAMES' is assigned a value but never used.", "'setConveyorBelts' is assigned a value but never used.", ["357", "358"], ["359", "360"], ["361", "362"], ["363", "364"], ["365", "366"], ["367", "368"], ["369", "370"], ["371", "372"], ["373", "374"], ["375", "376"], ["377", "378"], ["379", "380"], ["381", "382"], "'room' is assigned a value but never used.", "'player' is assigned a value but never used.", ["383", "384"], ["385", "386"], ["387", "388"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchPlayer'. Either include it or remove the dependency array.", "ArrayExpression", ["389"], ["390", "391"], ["392", "393"], ["394", "395"], ["396", "397"], ["398", "399"], ["400", "401"], ["402", "403"], ["404", "405"], ["406", "407"], ["408", "409"], ["410", "411"], ["412", "413"], ["414", "415"], ["416", "417"], ["418", "419"], ["420", "421"], ["422", "423"], ["424", "425"], ["426", "427"], ["428", "429"], ["430", "431"], ["432", "433"], ["434", "435"], ["436", "437"], ["438", "439"], ["440", "441"], ["442", "443"], ["444", "445"], ["446", "447"], {"messageId": "448", "fix": "449", "desc": "450"}, {"messageId": "451", "fix": "452", "desc": "453"}, {"messageId": "448", "fix": "454", "desc": "450"}, {"messageId": "451", "fix": "455", "desc": "453"}, {"messageId": "448", "fix": "456", "desc": "450"}, {"messageId": "451", "fix": "457", "desc": "453"}, {"messageId": "448", "fix": "458", "desc": "450"}, {"messageId": "451", "fix": "459", "desc": "453"}, {"messageId": "460", "data": "461", "fix": "462", "desc": "463"}, {"messageId": "460", "data": "464", "fix": "465", "desc": "466"}, {"messageId": "460", "data": "467", "fix": "468", "desc": "469"}, {"messageId": "460", "data": "470", "fix": "471", "desc": "472"}, {"messageId": "448", "fix": "473", "desc": "450"}, {"messageId": "451", "fix": "474", "desc": "453"}, {"messageId": "460", "data": "475", "fix": "476", "desc": "463"}, {"messageId": "460", "data": "477", "fix": "478", "desc": "466"}, {"messageId": "460", "data": "479", "fix": "480", "desc": "469"}, {"messageId": "460", "data": "481", "fix": "482", "desc": "472"}, {"messageId": "448", "fix": "483", "desc": "450"}, {"messageId": "451", "fix": "484", "desc": "453"}, {"messageId": "448", "fix": "485", "desc": "450"}, {"messageId": "451", "fix": "486", "desc": "453"}, {"messageId": "448", "fix": "487", "desc": "450"}, {"messageId": "451", "fix": "488", "desc": "453"}, {"messageId": "448", "fix": "489", "desc": "450"}, {"messageId": "451", "fix": "490", "desc": "453"}, {"messageId": "448", "fix": "491", "desc": "450"}, {"messageId": "451", "fix": "492", "desc": "453"}, {"messageId": "448", "fix": "493", "desc": "450"}, {"messageId": "451", "fix": "494", "desc": "453"}, {"messageId": "448", "fix": "495", "desc": "450"}, {"messageId": "451", "fix": "496", "desc": "453"}, {"messageId": "448", "fix": "497", "desc": "450"}, {"messageId": "451", "fix": "498", "desc": "453"}, {"messageId": "448", "fix": "499", "desc": "450"}, {"messageId": "451", "fix": "500", "desc": "453"}, {"messageId": "448", "fix": "501", "desc": "450"}, {"messageId": "451", "fix": "502", "desc": "453"}, {"messageId": "448", "fix": "503", "desc": "450"}, {"messageId": "451", "fix": "504", "desc": "453"}, {"messageId": "448", "fix": "505", "desc": "450"}, {"messageId": "451", "fix": "506", "desc": "453"}, {"messageId": "448", "fix": "507", "desc": "450"}, {"messageId": "451", "fix": "508", "desc": "453"}, {"messageId": "448", "fix": "509", "desc": "450"}, {"messageId": "451", "fix": "510", "desc": "453"}, {"messageId": "448", "fix": "511", "desc": "450"}, {"messageId": "451", "fix": "512", "desc": "453"}, {"messageId": "448", "fix": "513", "desc": "450"}, {"messageId": "451", "fix": "514", "desc": "453"}, {"messageId": "448", "fix": "515", "desc": "450"}, {"messageId": "451", "fix": "516", "desc": "453"}, {"messageId": "448", "fix": "517", "desc": "450"}, {"messageId": "451", "fix": "518", "desc": "453"}, {"messageId": "448", "fix": "519", "desc": "450"}, {"messageId": "451", "fix": "520", "desc": "453"}, {"messageId": "448", "fix": "521", "desc": "450"}, {"messageId": "451", "fix": "522", "desc": "453"}, {"messageId": "448", "fix": "523", "desc": "450"}, {"messageId": "451", "fix": "524", "desc": "453"}, {"messageId": "448", "fix": "525", "desc": "450"}, {"messageId": "451", "fix": "526", "desc": "453"}, {"messageId": "448", "fix": "527", "desc": "450"}, {"messageId": "451", "fix": "528", "desc": "453"}, {"desc": "529", "fix": "530"}, {"messageId": "448", "fix": "531", "desc": "450"}, {"messageId": "451", "fix": "532", "desc": "453"}, {"messageId": "448", "fix": "533", "desc": "450"}, {"messageId": "451", "fix": "534", "desc": "453"}, {"messageId": "448", "fix": "535", "desc": "450"}, {"messageId": "451", "fix": "536", "desc": "453"}, {"messageId": "448", "fix": "537", "desc": "450"}, {"messageId": "451", "fix": "538", "desc": "453"}, {"messageId": "448", "fix": "539", "desc": "450"}, {"messageId": "451", "fix": "540", "desc": "453"}, {"messageId": "448", "fix": "541", "desc": "450"}, {"messageId": "451", "fix": "542", "desc": "453"}, {"messageId": "448", "fix": "543", "desc": "450"}, {"messageId": "451", "fix": "544", "desc": "453"}, {"messageId": "448", "fix": "545", "desc": "450"}, {"messageId": "451", "fix": "546", "desc": "453"}, {"messageId": "448", "fix": "547", "desc": "450"}, {"messageId": "451", "fix": "548", "desc": "453"}, {"messageId": "448", "fix": "549", "desc": "450"}, {"messageId": "451", "fix": "550", "desc": "453"}, {"messageId": "448", "fix": "551", "desc": "450"}, {"messageId": "451", "fix": "552", "desc": "453"}, {"messageId": "448", "fix": "553", "desc": "450"}, {"messageId": "451", "fix": "554", "desc": "453"}, {"messageId": "448", "fix": "555", "desc": "450"}, {"messageId": "451", "fix": "556", "desc": "453"}, {"messageId": "448", "fix": "557", "desc": "450"}, {"messageId": "451", "fix": "558", "desc": "453"}, {"messageId": "448", "fix": "559", "desc": "450"}, {"messageId": "451", "fix": "560", "desc": "453"}, {"messageId": "448", "fix": "561", "desc": "450"}, {"messageId": "451", "fix": "562", "desc": "453"}, {"messageId": "448", "fix": "563", "desc": "450"}, {"messageId": "451", "fix": "564", "desc": "453"}, {"messageId": "448", "fix": "565", "desc": "450"}, {"messageId": "451", "fix": "566", "desc": "453"}, {"messageId": "448", "fix": "567", "desc": "450"}, {"messageId": "451", "fix": "568", "desc": "453"}, {"messageId": "448", "fix": "569", "desc": "450"}, {"messageId": "451", "fix": "570", "desc": "453"}, {"messageId": "448", "fix": "571", "desc": "450"}, {"messageId": "451", "fix": "572", "desc": "453"}, {"messageId": "448", "fix": "573", "desc": "450"}, {"messageId": "451", "fix": "574", "desc": "453"}, {"messageId": "448", "fix": "575", "desc": "450"}, {"messageId": "451", "fix": "576", "desc": "453"}, {"messageId": "448", "fix": "577", "desc": "450"}, {"messageId": "451", "fix": "578", "desc": "453"}, {"messageId": "448", "fix": "579", "desc": "450"}, {"messageId": "451", "fix": "580", "desc": "453"}, {"messageId": "448", "fix": "581", "desc": "450"}, {"messageId": "451", "fix": "582", "desc": "453"}, {"messageId": "448", "fix": "583", "desc": "450"}, {"messageId": "451", "fix": "584", "desc": "453"}, {"messageId": "448", "fix": "585", "desc": "450"}, {"messageId": "451", "fix": "586", "desc": "453"}, {"messageId": "448", "fix": "587", "desc": "450"}, {"messageId": "451", "fix": "588", "desc": "453"}, "suggestUnknown", {"range": "589", "text": "590"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "591", "text": "592"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "593", "text": "590"}, {"range": "594", "text": "592"}, {"range": "595", "text": "590"}, {"range": "596", "text": "592"}, {"range": "597", "text": "590"}, {"range": "598", "text": "592"}, "replaceWithAlt", {"alt": "599"}, {"range": "600", "text": "601"}, "Replace with `&apos;`.", {"alt": "602"}, {"range": "603", "text": "604"}, "Replace with `&lsquo;`.", {"alt": "605"}, {"range": "606", "text": "607"}, "Replace with `&#39;`.", {"alt": "608"}, {"range": "609", "text": "610"}, "Replace with `&rsquo;`.", {"range": "611", "text": "590"}, {"range": "612", "text": "592"}, {"alt": "599"}, {"range": "613", "text": "614"}, {"alt": "602"}, {"range": "615", "text": "616"}, {"alt": "605"}, {"range": "617", "text": "618"}, {"alt": "608"}, {"range": "619", "text": "620"}, {"range": "621", "text": "590"}, {"range": "622", "text": "592"}, {"range": "623", "text": "590"}, {"range": "624", "text": "592"}, {"range": "625", "text": "590"}, {"range": "626", "text": "592"}, {"range": "627", "text": "590"}, {"range": "628", "text": "592"}, {"range": "629", "text": "590"}, {"range": "630", "text": "592"}, {"range": "631", "text": "590"}, {"range": "632", "text": "592"}, {"range": "633", "text": "590"}, {"range": "634", "text": "592"}, {"range": "635", "text": "590"}, {"range": "636", "text": "592"}, {"range": "637", "text": "590"}, {"range": "638", "text": "592"}, {"range": "639", "text": "590"}, {"range": "640", "text": "592"}, {"range": "641", "text": "590"}, {"range": "642", "text": "592"}, {"range": "643", "text": "590"}, {"range": "644", "text": "592"}, {"range": "645", "text": "590"}, {"range": "646", "text": "592"}, {"range": "647", "text": "590"}, {"range": "648", "text": "592"}, {"range": "649", "text": "590"}, {"range": "650", "text": "592"}, {"range": "651", "text": "590"}, {"range": "652", "text": "592"}, {"range": "653", "text": "590"}, {"range": "654", "text": "592"}, {"range": "655", "text": "590"}, {"range": "656", "text": "592"}, {"range": "657", "text": "590"}, {"range": "658", "text": "592"}, {"range": "659", "text": "590"}, {"range": "660", "text": "592"}, {"range": "661", "text": "590"}, {"range": "662", "text": "592"}, {"range": "663", "text": "590"}, {"range": "664", "text": "592"}, {"range": "665", "text": "590"}, {"range": "666", "text": "592"}, "Update the dependencies array to be: [fetchPlayer, user]", {"range": "667", "text": "668"}, {"range": "669", "text": "590"}, {"range": "670", "text": "592"}, {"range": "671", "text": "590"}, {"range": "672", "text": "592"}, {"range": "673", "text": "590"}, {"range": "674", "text": "592"}, {"range": "675", "text": "590"}, {"range": "676", "text": "592"}, {"range": "677", "text": "590"}, {"range": "678", "text": "592"}, {"range": "679", "text": "590"}, {"range": "680", "text": "592"}, {"range": "681", "text": "590"}, {"range": "682", "text": "592"}, {"range": "683", "text": "590"}, {"range": "684", "text": "592"}, {"range": "685", "text": "590"}, {"range": "686", "text": "592"}, {"range": "687", "text": "590"}, {"range": "688", "text": "592"}, {"range": "689", "text": "590"}, {"range": "690", "text": "592"}, {"range": "691", "text": "590"}, {"range": "692", "text": "592"}, {"range": "693", "text": "590"}, {"range": "694", "text": "592"}, {"range": "695", "text": "590"}, {"range": "696", "text": "592"}, {"range": "697", "text": "590"}, {"range": "698", "text": "592"}, {"range": "699", "text": "590"}, {"range": "700", "text": "592"}, {"range": "701", "text": "590"}, {"range": "702", "text": "592"}, {"range": "703", "text": "590"}, {"range": "704", "text": "592"}, {"range": "705", "text": "590"}, {"range": "706", "text": "592"}, {"range": "707", "text": "590"}, {"range": "708", "text": "592"}, {"range": "709", "text": "590"}, {"range": "710", "text": "592"}, {"range": "711", "text": "590"}, {"range": "712", "text": "592"}, {"range": "713", "text": "590"}, {"range": "714", "text": "592"}, {"range": "715", "text": "590"}, {"range": "716", "text": "592"}, {"range": "717", "text": "590"}, {"range": "718", "text": "592"}, {"range": "719", "text": "590"}, {"range": "720", "text": "592"}, {"range": "721", "text": "590"}, {"range": "722", "text": "592"}, {"range": "723", "text": "590"}, {"range": "724", "text": "592"}, {"range": "725", "text": "590"}, {"range": "726", "text": "592"}, [4199, 4202], "unknown", [4199, 4202], "never", [929, 932], [929, 932], [1988, 1991], [1988, 1991], [4681, 4684], [4681, 4684], "&apos;", [2751, 2838], "\n                You don&apos;t have enough ingredients to craft any recipes.\n              ", "&lsquo;", [2751, 2838], "\n                You don&lsquo;t have enough ingredients to craft any recipes.\n              ", "&#39;", [2751, 2838], "\n                You don&#39;t have enough ingredients to craft any recipes.\n              ", "&rsquo;", [2751, 2838], "\n                You don&rsquo;t have enough ingredients to craft any recipes.\n              ", [3325, 3328], [3325, 3328], [2908, 2923], "💡 What&apos;s Next?", [2908, 2923], "💡 What&lsquo;s Next?", [2908, 2923], "💡 What&#39;s Next?", [2908, 2923], "💡 What&rsquo;s Next?", [959, 962], [959, 962], [1644, 1647], [1644, 1647], [3830, 3833], [3830, 3833], [2049, 2052], [2049, 2052], [3996, 3999], [3996, 3999], [4223, 4226], [4223, 4226], [6404, 6407], [6404, 6407], [1051, 1054], [1051, 1054], [1207, 1210], [1207, 1210], [2572, 2575], [2572, 2575], [4532, 4535], [4532, 4535], [4715, 4718], [4715, 4718], [5118, 5121], [5118, 5121], [5168, 5171], [5168, 5171], [5216, 5219], [5216, 5219], [5886, 5889], [5886, 5889], [5939, 5942], [5939, 5942], [5990, 5993], [5990, 5993], [7274, 7277], [7274, 7277], [7365, 7368], [7365, 7368], [7740, 7743], [7740, 7743], [8324, 8327], [8324, 8327], [8887, 8890], [8887, 8890], [482, 488], "[fetchPlayer, user]", [4351, 4354], [4351, 4354], [4525, 4528], [4525, 4528], [4937, 4940], [4937, 4940], [4957, 4960], [4957, 4960], [5032, 5035], [5032, 5035], [5160, 5163], [5160, 5163], [368, 371], [368, 371], [387, 390], [387, 390], [409, 412], [409, 412], [425, 428], [425, 428], [453, 456], [453, 456], [856, 859], [856, 859], [875, 878], [875, 878], [891, 894], [891, 894], [915, 918], [915, 918], [938, 941], [938, 941], [5342, 5345], [5342, 5345], [1071, 1074], [1071, 1074], [1092, 1095], [1092, 1095], [1118, 1121], [1118, 1121], [1452, 1455], [1452, 1455], [2272, 2275], [2272, 2275], [2495, 2498], [2495, 2498], [2651, 2654], [2651, 2654], [3052, 3055], [3052, 3055], [6371, 6374], [6371, 6374], [7018, 7021], [7018, 7021], [7144, 7147], [7144, 7147], [7271, 7274], [7271, 7274]]