"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[247],{2247:(e,s,t)=>{t.r(s),t.d(s,{default:()=>$});var i=t(5155),a=t(2115),n=t(9283);let l=[{id:"chocolate_chip_cookies",name:"Chocolate Chip Cookies",ingredients:[{name:"Flour",quantity:2},{name:"Sugar",quantity:1},{name:"Butter",quantity:1},{name:"Chocolate Chips",quantity:1}],bakingTime:45,difficulty:1,unlockLevel:1,basePrice:25,category:"cookies"},{id:"vanilla_muffins",name:"Vanilla Muffins",ingredients:[{name:"Flour",quantity:2},{name:"Sugar",quantity:1},{name:"Eggs",quantity:1},{name:"Vanilla",quantity:1}],bakingTime:60,difficulty:1,unlockLevel:1,basePrice:20,category:"cakes"},{id:"cinnamon_rolls",name:"Cinnamon Rolls",ingredients:[{name:"Flour",quantity:3},{name:"Sugar",quantity:2},{name:"Butter",quantity:2},{name:"Eggs",quantity:1}],bakingTime:90,difficulty:2,unlockLevel:2,basePrice:35,category:"pastries"},{id:"chocolate_brownies",name:"Chocolate Brownies",ingredients:[{name:"Flour",quantity:2},{name:"Sugar",quantity:2},{name:"Butter",quantity:1},{name:"Chocolate Chips",quantity:2}],bakingTime:75,difficulty:2,unlockLevel:2,basePrice:30,category:"cakes"},{id:"sourdough_bread",name:"Sourdough Bread",ingredients:[{name:"Flour",quantity:4},{name:"Salt",quantity:1}],bakingTime:180,difficulty:3,unlockLevel:3,basePrice:45,category:"bread"}],r=["Alice Johnson","Bob Smith","Carol Davis","David Wilson","Emma Brown","Frank Miller","Grace Taylor","Henry Anderson","Ivy Thomas","Jack Martinez","Kate Garcia","Liam Rodriguez","Mia Lopez","Noah Gonzalez","Olivia Hernandez","Paul Perez","Quinn Turner","Ruby Phillips","Sam Campbell","Tina Parker"];function c(e,s){return e.ingredients.every(e=>{let t=s.find(s=>s.name===e.name);return t&&t.quantity>=e.quantity})}function d(e){return l.find(s=>s.id===e)}function o(e){return l.filter(s=>s.unlockLevel<=e)}function m(e){return e<=1?0:Math.floor(100*Math.pow(1.15,e-1))}let u=[{id:"first_order",name:"First Customer",description:"Complete your first order",icon:"\uD83C\uDFAF",category:"baking",requirements:[{type:"orders_completed",target:1}],reward:{type:"money",id:"first_order_bonus",name:"First Order Bonus",description:"Bonus for first order",value:50},unlocked:!0,completed:!1},{id:"baker_apprentice",name:"Baker Apprentice",description:"Complete 10 orders",icon:"\uD83D\uDC68‍\uD83C\uDF73",category:"baking",requirements:[{type:"orders_completed",target:10}],reward:{type:"skill_point",id:"apprentice_skill",name:"Skill Point",description:"Gain 1 skill point",value:1},unlocked:!0,completed:!1},{id:"baker_journeyman",name:"Baker Journeyman",description:"Complete 50 orders",icon:"\uD83D\uDC68‍\uD83C\uDF73",category:"baking",requirements:[{type:"orders_completed",target:50}],reward:{type:"money",id:"journeyman_bonus",name:"Journeyman Bonus",description:"Large money bonus",value:500},unlocked:!0,completed:!1},{id:"master_baker",name:"Master Baker",description:"Complete 100 orders",icon:"\uD83C\uDFC6",category:"baking",requirements:[{type:"orders_completed",target:100}],reward:{type:"skill_point",id:"master_skill",name:"Master Skill Points",description:"Gain 3 skill points",value:3},unlocked:!0,completed:!1},{id:"speed_baker",name:"Speed Baker",description:"Bake 100 items",icon:"⚡",category:"efficiency",requirements:[{type:"items_baked",target:100}],reward:{type:"skill_point",id:"speed_skill",name:"Speed Skill Point",description:"Gain 1 skill point",value:1},unlocked:!0,completed:!1},{id:"money_maker",name:"Money Maker",description:"Earn $1000 total",icon:"\uD83D\uDCB0",category:"business",requirements:[{type:"money_earned",target:1e3}],reward:{type:"skill_point",id:"money_maker_skill",name:"Business Skill Point",description:"Extra skill point for business success",value:1},unlocked:!0,completed:!1},{id:"recipe_collector",name:"Recipe Collector",description:"Unlock 5 different recipes",icon:"\uD83D\uDCDA",category:"collection",requirements:[{type:"recipes_unlocked",target:5}],reward:{type:"money",id:"recipe_bonus",name:"Recipe Collection Bonus",description:"Bonus for collecting recipes",value:200},unlocked:!0,completed:!1},{id:"level_master",name:"Level Master",description:"Reach level 10",icon:"⭐",category:"special",requirements:[{type:"level_reached",target:10}],reward:{type:"skill_point",id:"level_master_skill",name:"Master Level Bonus",description:"Gain 2 skill points",value:2},unlocked:!0,completed:!1},{id:"first_hundred",name:"First Hundred",description:"Earn $100 total",icon:"\uD83D\uDCB5",category:"business",requirements:[{type:"money_earned",target:100}],reward:{type:"money",id:"first_hundred_bonus",name:"Business Bonus",description:"Small business milestone bonus",value:25},unlocked:!0,completed:!1},{id:"entrepreneur",name:"Entrepreneur",description:"Earn $5000 total",icon:"\uD83C\uDFE2",category:"business",requirements:[{type:"money_earned",target:5e3}],reward:{type:"skill_point",id:"entrepreneur_skill",name:"Business Skill Points",description:"Gain 2 skill points",value:2},unlocked:!0,completed:!1},{id:"equipment_enthusiast",name:"Equipment Enthusiast",description:"Own 3 pieces of equipment",icon:"⚙️",category:"collection",requirements:[{type:"equipment_owned",target:3}],reward:{type:"money",id:"equipment_bonus",name:"Equipment Bonus",description:"Equipment investment bonus",value:300},unlocked:!0,completed:!1},{id:"rising_star",name:"Rising Star",description:"Reach level 5",icon:"\uD83C\uDF1F",category:"special",requirements:[{type:"level_reached",target:5}],reward:{type:"skill_point",id:"rising_star_skill",name:"Rising Star Bonus",description:"Gain 1 skill point",value:1},unlocked:!0,completed:!1},{id:"legendary_baker",name:"Legendary Baker",description:"Reach level 20",icon:"\uD83D\uDC51",category:"special",requirements:[{type:"level_reached",target:20}],reward:{type:"skill_point",id:"legendary_skill",name:"Legendary Bonus",description:"Gain 5 skill points",value:5},unlocked:!0,completed:!1}],x=[{id:"baking_speed_1",name:"Quick Hands",description:"Increase baking speed by 10%",icon:"⚡",category:"efficiency",level:0,maxLevel:3,cost:1,requirements:{playerLevel:2},effects:[{type:"baking_speed",value:.1}]},{id:"money_bonus_1",name:"Business Sense",description:"Increase money earned by 15%",icon:"\uD83D\uDCBC",category:"business",level:0,maxLevel:3,cost:1,requirements:{playerLevel:3},effects:[{type:"money_multiplier",value:.15}]},{id:"xp_bonus_1",name:"Fast Learner",description:"Increase experience gained by 20%",icon:"\uD83D\uDCC8",category:"efficiency",level:0,maxLevel:2,cost:2,requirements:{playerLevel:4},effects:[{type:"xp_multiplier",value:.2}]},{id:"ingredient_efficiency_1",name:"Efficient Baker",description:"Use 10% fewer ingredients",icon:"\uD83C\uDF3E",category:"efficiency",level:0,maxLevel:2,cost:2,requirements:{playerLevel:5,skills:["baking_speed_1"]},effects:[{type:"ingredient_efficiency",value:.1}]},{id:"automation_unlock_1",name:"Automation Expert",description:"Unlock advanced automation features",icon:"\uD83E\uDD16",category:"automation",level:0,maxLevel:1,cost:3,requirements:{playerLevel:8,achievements:["baker_apprentice"]},effects:[{type:"automation_unlock",value:1}]}],p=[{id:"auto_queue_basic",name:"Basic Auto-Queue",description:"Equipment automatically starts the next recipe when finished",type:"intelligence",cost:500,unlockLevel:4,effects:{autoQueueing:!0}},{id:"efficiency_boost_1",name:"Efficiency Boost I",description:"Automated equipment uses 10% fewer ingredients",type:"efficiency",cost:750,unlockLevel:5,effects:{efficiencyBonus:.1}},{id:"speed_boost_1",name:"Speed Boost I",description:"Automated equipment works 15% faster",type:"speed",cost:1e3,unlockLevel:6,effects:{speedMultiplier:1.15}},{id:"smart_prioritization",name:"Smart Prioritization",description:"Automation prioritizes orders based on profit and urgency",type:"intelligence",cost:1500,unlockLevel:8,effects:{smartPrioritization:!0}},{id:"efficiency_boost_2",name:"Efficiency Boost II",description:"Automated equipment uses 20% fewer ingredients",type:"efficiency",cost:2e3,unlockLevel:10,effects:{efficiencyBonus:.2}},{id:"speed_boost_2",name:"Speed Boost II",description:"Automated equipment works 30% faster",type:"speed",cost:2500,unlockLevel:12,effects:{speedMultiplier:1.3}}],h=(0,a.createContext)(void 0);function g(e){let{children:s}=e,[t,n]=(0,a.useState)({level:1,experience:0,money:100,maxExperience:100,skillPoints:0,totalMoneyEarned:0,totalOrdersCompleted:0,totalItemsBaked:0,unlockedRecipes:["chocolate_chip_cookies","vanilla_muffins"],automationUpgrades:[]}),[g,v]=(0,a.useState)([{id:"oven1",name:"Basic Oven",type:"oven",isActive:!1,level:1,efficiency:1,automationLevel:0},{id:"mixer1",name:"Hand Mixer",type:"mixer",isActive:!1,level:1,efficiency:1,automationLevel:0},{id:"counter1",name:"Work Counter",type:"counter",isActive:!1,level:1,efficiency:1,automationLevel:0}]),[y,f]=(0,a.useState)([{name:"Flour",quantity:15,cost:5,icon:"\uD83C\uDF3E"},{name:"Sugar",quantity:12,cost:8,icon:"\uD83C\uDF6F"},{name:"Eggs",quantity:10,cost:12,icon:"\uD83E\uDD5A"},{name:"Butter",quantity:8,cost:15,icon:"\uD83E\uDDC8"},{name:"Chocolate Chips",quantity:6,cost:20,icon:"\uD83C\uDF6B"},{name:"Vanilla",quantity:5,cost:25,icon:"\uD83C\uDF3F"},{name:"Salt",quantity:10,cost:3,icon:"\uD83E\uDDC2"}]),[b,j]=(0,a.useState)([{id:"1",customerName:"Alice Johnson",items:["Chocolate Chip Cookies"],timeLimit:300,reward:25,status:"pending",difficulty:1}]),[N,k]=(0,a.useState)(u),[w,C]=(0,a.useState)(x),[_,q]=(0,a.useState)([]),[S,D]=(0,a.useState)(!1),[L,E]=(0,a.useState)({enabled:!1,autoStart:!1,preferredRecipes:[],maxConcurrentJobs:2,priorityMode:"efficiency",ingredientThreshold:5}),[M,z]=(0,a.useState)([]),[B,A]=(0,a.useState)([]);(0,a.useEffect)(()=>{let e=setInterval(()=>{v(e=>e.map(e=>e.isActive&&e.timeRemaining&&e.timeRemaining>0?{...e,timeRemaining:e.timeRemaining-1}:e.isActive&&0===e.timeRemaining?{...e,isActive:!1,timeRemaining:void 0,currentRecipe:void 0}:e))},1e3);return()=>clearInterval(e)},[]),(0,a.useEffect)(()=>{let e=setInterval(()=>{j(e=>e.map(e=>{if(("accepted"===e.status||"in_progress"===e.status)&&e.timeLimit>0){let s=e.timeLimit-1;return 0===s?{...e,status:"failed",timeLimit:0}:{...e,timeLimit:s}}return e}))},1e3);return()=>clearInterval(e)},[]);let P=(e,s)=>{v(t=>t.map(t=>t.id===e?{...t,...s}:t))},$=e=>{n(s=>{let t=s.experience+e,i=function(e){let s=1,t=0;for(;;){let i=m(s+1);if(t+i>e)break;t+=i,s++}let i=m(s+1);return{level:s,experience:e-t,experienceRequired:i,totalExperience:e,rewards:function(e){let s=[];s.push({type:"money",id:"money_".concat(e),name:"Level Bonus",description:"Bonus money for reaching level ".concat(e),value:25*e});let t={2:["cinnamon_rolls"],3:["chocolate_brownies","sourdough_bread"],4:["croissants"],5:["cheesecake"],6:["macarons"],7:["honey_glazed_donuts"],8:["sourdough_bread"],9:["chocolate_souffle"],10:["croquembouche"],12:["opera_cake"],15:["artisan_pizza_dough"]};t[e]&&t[e].forEach(e=>{s.push({type:"recipe",id:e,name:"New Recipe Unlocked",description:"You can now bake ".concat(e.replace(/_/g," "))})});let i={3:["professional_oven"],4:["auto_mixer"],5:["stand_mixer"],6:["auto_oven"],7:["conveyor_belt"],8:["advanced_auto_mixer"],10:["industrial_oven"],12:["smart_conveyor_system"]};return i[e]&&i[e].forEach(e=>{s.push({type:"equipment",id:e,name:"New Equipment Available",description:"".concat(e.replace(/_/g," ")," is now available for purchase")})}),e%2==0&&s.push({type:"skill_point",id:"skill_point_".concat(e),name:"Skill Point",description:"Use this to upgrade your skills in the technology tree",value:1}),s}(s)}}(t);if(i.level>s.level){q(i.rewards),D(!0);let e=+(i.level%2==0);return setTimeout(()=>R(),100),{...s,level:i.level,experience:t,maxExperience:i.experienceRequired,skillPoints:s.skillPoints+e}}return{...s,experience:t,maxExperience:i.experienceRequired}})},F=e=>{n(s=>({...s,money:s.money+e,totalMoneyEarned:s.totalMoneyEarned+e})),setTimeout(()=>R(),100)},O=e=>t.money>=e&&(n(s=>({...s,money:s.money-e})),!0),T=(e,s)=>{let t=y.find(s=>s.name===e);return!!t&&t.quantity>=s&&(f(t=>t.map(t=>t.name===e?{...t,quantity:t.quantity-s}:t)),!0)},R=()=>{k(e=>e.map(e=>{if(e.completed)return e;let s=e.requirements.map(e=>{let s=0;switch(e.type){case"orders_completed":s=t.totalOrdersCompleted;break;case"money_earned":s=t.totalMoneyEarned;break;case"recipes_unlocked":s=t.unlockedRecipes.length;break;case"level_reached":s=t.level;break;case"items_baked":s=t.totalItemsBaked;break;case"equipment_owned":s=g.length}return{...e,current:s}}),i=s.every(e=>e.current>=e.target);return i&&!e.completed&&(showSuccess("Achievement Unlocked!","\uD83C\uDFC6 ".concat(e.name)),"money"===e.reward.type&&e.reward.value?F(e.reward.value):"skill_point"===e.reward.type&&e.reward.value&&n(s=>({...s,skillPoints:s.skillPoints+e.reward.value}))),{...e,requirements:s,completed:i}}))};return(0,i.jsx)(h.Provider,{value:{player:t,equipment:g,inventory:y,orders:b,achievements:N,skills:w,levelUpRewards:_,showLevelUp:S,automationSettings:L,automationJobs:M,conveyorBelts:B,updatePlayer:e=>{n(s=>({...s,...e}))},updateEquipment:P,addEquipment:e=>{let s={...e,id:Date.now().toString()+Math.random().toString(36).substring(2,11)};v(e=>[...e,s]),setTimeout(()=>R(),100)},addExperience:$,addMoney:F,spendMoney:O,useIngredient:T,addIngredient:(e,s)=>{f(t=>t.map(t=>t.name===e?{...t,quantity:t.quantity+s}:t))},acceptOrder:e=>{j(s=>s.map(s=>s.id===e?{...s,status:"accepted"}:s))},completeOrder:e=>{let s=b.find(s=>s.id===e);if(s&&s.items.every(e=>{let s=d(e.toLowerCase().replace(/\s+/g,"_"));return!!s&&c(s,y)})){s.items.forEach(e=>{let s=d(e.toLowerCase().replace(/\s+/g,"_"));s&&s.ingredients.forEach(e=>{T(e.name,e.quantity)})}),j(s=>s.map(s=>s.id===e?{...s,status:"completed"}:s)),n(e=>({...e,totalOrdersCompleted:e.totalOrdersCompleted+1,totalItemsBaked:e.totalItemsBaked+s.items.length}));let t=s.timeLimit>60,i=function(e){let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=10*e,i=s?Math.floor(.5*t):0;return t+i}(s.difficulty,t);F(s.reward),$(i),setTimeout(()=>R(),100)}},declineOrder:e=>{j(s=>s.filter(s=>s.id!==e))},generateNewOrder:()=>{let e=function(e){let s=l.filter(s=>s.unlockLevel<=e);0===s.length&&s.push(l[0]);let t=.7>Math.random()?1:.9>Math.random()?2:3,i=[];for(let e=0;e<t;e++){let e=s[Math.floor(Math.random()*s.length)];i.push(e)}let a=Math.ceil(i.reduce((e,s)=>e+s.difficulty,0)/i.length),n=i.reduce((e,s)=>e+s.basePrice,0),c=1.5+ +Math.random(),d=Math.floor(n*(.8+.4*Math.random())),o=Math.floor(i.reduce((e,s)=>e+s.bakingTime,0)*c);return{id:Date.now().toString()+Math.random().toString(36).substr(2,9),customerName:r[Math.floor(Math.random()*r.length)],items:i.map(e=>e.name),timeLimit:o,reward:d,status:"pending",difficulty:Math.min(5,a)}}(t.level);j(s=>[...s,e])},upgradeSkill:e=>{let s=w.find(s=>s.id===e);!s||s.level>=s.maxLevel||t.skillPoints<s.cost||(C(s=>s.map(s=>s.id===e?{...s,level:s.level+1}:s)),n(e=>({...e,skillPoints:e.skillPoints-s.cost})))},checkAchievements:R,dismissLevelUp:()=>{D(!1),q([])},updateAutomationSettings:e=>{E(s=>({...s,...e}))},purchaseAutomationUpgrade:e=>{let s=p.find(s=>s.id===e);s&&!(t.money<s.cost)&&O(s.cost)&&n(s=>({...s,automationUpgrades:[...s.automationUpgrades,e]}))},startAutomationJob:e=>{if(!L.enabled)return;let s=g.find(s=>s.id===e);if(!s||s.isActive||0===s.automationLevel)return;let i=function(e,s,t,i){let a=e.filter(e=>e.ingredients.every(e=>{let t=s.find(s=>s.name===e.name);return t&&t.quantity>=e.quantity}));if(0===a.length)return null;switch(t){case"speed":return a.reduce((e,s)=>s.bakingTime<e.bakingTime?s:e).id;case"profit":return a.reduce((e,s)=>s.basePrice>e.basePrice?s:e).id;case"efficiency":let n=i.flatMap(e=>e.items),l=a.filter(e=>n.includes(e.name));if(l.length>0)return l[0].id;return a[0].id;default:return a[0].id}}(o(t.level),y,L.priorityMode,b);if(!i)return;let a=d(i);if(!a||!c(a,y))return;let n=function(e,s,t,i){let a=Math.floor(t.bakingTime/i),n=t.ingredients.map(e=>({...e,quantity:Math.ceil(e.quantity*(1-(i-1)*.1))}));return{id:Date.now().toString()+Math.random().toString(36).substr(2,9),equipmentId:e,recipeId:s,startTime:Date.now(),duration:a,status:"queued",ingredients:n,efficiency:i}}(e,i,a,function(e,s,t){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=e;return a*=1+.1*s,t.forEach(e=>{let s=p.find(s=>s.id===e);(null==s?void 0:s.effects.efficiencyBonus)&&(a*=1+s.effects.efficiencyBonus)}),Math.min(a*=1+i,2)}(s.efficiency,s.automationLevel,t.automationUpgrades));z(e=>[...e,{...n,status:"running"}]),P(e,{isActive:!0,timeRemaining:n.duration,currentRecipe:a.name}),n.ingredients.forEach(e=>{T(e.name,e.quantity)})}},children:s})}function v(){let e=(0,a.useContext)(h);if(void 0===e)throw Error("useGame must be used within a GameProvider");return e}var y=t(3741),f=t(9419),b=t(2163);function j(e){let{isOpen:s,onClose:t}=e,{player:l,inventory:r}=v(),{t:c}=(0,n.o)(),[d,m]=(0,a.useState)("all");if(!s)return null;let u=o(l.level),x="all"===d?u:u.filter(e=>e.category===d),p=e=>e.ingredients.every(e=>{let s=r.find(s=>s.name===e.name);return s&&s.quantity>=e.quantity}),h=[{id:"all",name:c("recipes.all"),icon:"\uD83C\uDF7D️"},{id:"cookies",name:c("recipes.cookies"),icon:"\uD83C\uDF6A"},{id:"cakes",name:c("recipes.cakes"),icon:"\uD83E\uDDC1"},{id:"bread",name:c("recipes.bread"),icon:"\uD83C\uDF5E"},{id:"pastries",name:c("recipes.pastries"),icon:"\uD83E\uDD50"}];return(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,i.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:c("modal.recipes.title")}),(0,i.jsx)(y.$,{variant:"secondary",onClick:t,children:c("game.close")})]})}),(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:h.map(e=>(0,i.jsxs)(y.$,{variant:d===e.id?"primary":"secondary",size:"sm",onClick:()=>m(e.id),children:[e.icon," ",e.name]},e.id))}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto",children:x.map(e=>{let s;return(0,i.jsxs)("div",{className:"p-4 rounded-lg border-2 ".concat(p(e)?"border-green-300 bg-green-50":"border-gray-300 bg-gray-50"),children:[(0,i.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,i.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name}),(0,i.jsxs)("span",{className:"text-sm text-green-600",children:["$",e.basePrice]})]}),(0,i.jsxs)("div",{className:"text-xs text-gray-500 mb-2",children:[(s=e.difficulty,"⭐".repeat(s)+"☆".repeat(5-s))," • ⏱️ ",(e=>{let s=Math.floor(e/60);return"".concat(s,":").concat((e%60).toString().padStart(2,"0"))})(e.bakingTime)]}),(0,i.jsxs)("div",{className:"space-y-1 mb-3",children:[(0,i.jsx)("div",{className:"text-sm font-medium text-gray-700",children:c("recipes.ingredients")}),e.ingredients.map((e,s)=>{let t=r.find(s=>s.name===e.name),a=t&&t.quantity>=e.quantity;return(0,i.jsxs)("div",{className:"text-xs flex justify-between ".concat(a?"text-green-600":"text-red-600"),children:[(0,i.jsx)("span",{children:e.name}),(0,i.jsxs)("span",{children:[e.quantity,t&&(0,i.jsxs)("span",{className:"ml-1",children:["(",t.quantity," available)"]})]})]},s)})]}),(0,i.jsx)("div",{className:"text-xs text-gray-500",children:c("recipes.unlockLevel",{level:e.unlockLevel.toString()})}),p(e)&&(0,i.jsx)("div",{className:"mt-2",children:(0,i.jsx)(y.$,{size:"sm",variant:"success",className:"w-full",children:c("recipes.canCraft")})})]},e.id)})}),0===x.length&&(0,i.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,i.jsx)("div",{className:"text-4xl mb-2",children:"\uD83D\uDCDD"}),(0,i.jsx)("p",{children:c("recipes.noRecipes")}),(0,i.jsx)("p",{className:"text-sm",children:c("recipes.levelUpToUnlock")})]})]})]})})}function N(e){let{isOpen:s,onClose:t}=e,{player:l,inventory:r,spendMoney:c,addIngredient:d}=v(),{t:o}=(0,n.o)(),[m,u]=(0,a.useState)({});if(!s)return null;let x=(e,s)=>{u(t=>({...t,[e]:Math.max(0,s)}))},p=(e,s)=>s*(m[e]||1),h=(e,s)=>l.money>=p(e,s);return(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden",children:[(0,i.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:o("modal.shop.title")}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,i.jsx)("span",{className:"text-green-800 font-medium",children:o("ui.money",{amount:l.money.toString()})})}),(0,i.jsx)(y.$,{variant:"secondary",onClick:t,children:o("game.close")})]})]})}),(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsx)("div",{className:"space-y-4 max-h-[60vh] overflow-y-auto",children:r.map(e=>{let s=m[e.name]||1,t=p(e.name,e.cost),a=h(e.name,e.cost);return(0,i.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("div",{className:"text-2xl",children:e.icon}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-medium text-gray-800",children:e.name}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:o("shop.currentStock",{quantity:e.quantity.toString()})}),(0,i.jsx)("p",{className:"text-sm text-green-600",children:o("inventory.cost",{cost:e.cost.toString()})})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(y.$,{size:"sm",variant:"secondary",onClick:()=>x(e.name,s-1),disabled:s<=1,children:"-"}),(0,i.jsx)("span",{className:"w-12 text-center font-mono",children:s}),(0,i.jsx)(y.$,{size:"sm",variant:"secondary",onClick:()=>x(e.name,s+1),disabled:!h(e.name,e.cost)&&s>=1,children:"+"})]}),(0,i.jsxs)("div",{className:"text-right min-w-[80px]",children:[(0,i.jsxs)("div",{className:"font-medium ".concat(a?"text-green-600":"text-red-600"),children:["$",t]}),(0,i.jsx)(y.$,{size:"sm",variant:a?"success":"secondary",onClick:()=>((e,s)=>{let t=m[e]||1;c(s*t)&&(d(e,t),u(s=>({...s,[e]:0})))})(e.name,e.cost),disabled:!a,className:"mt-1",children:a?o("shop.buy"):o("shop.tooExpensive")})]})]})]},e.name)})}),(0,i.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,i.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:o("shop.tips.title")}),(0,i.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,i.jsx)("li",{children:o("shop.tips.bulk")}),(0,i.jsx)("li",{children:o("shop.tips.stock")}),(0,i.jsx)("li",{children:o("shop.tips.rare")}),(0,i.jsx)("li",{children:o("shop.tips.prices")})]})]})]})]})})}function k(e){let{isOpen:s,onClose:t,equipmentId:n,equipmentName:l}=e,{player:r,inventory:d,updateEquipment:m,useIngredient:u}=v(),[x,p]=(0,a.useState)(null);if(!s)return null;let h=o(r.level).filter(e=>c(e,d)),g=e=>{let s=Math.floor(e/60);return"".concat(s,":").concat((e%60).toString().padStart(2,"0"))},f=e=>"⭐".repeat(e)+"☆".repeat(5-e);return(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden",children:[(0,i.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("h2",{className:"text-2xl font-bold text-orange-800",children:["\uD83D\uDD25 ",l," - Select Recipe"]}),(0,i.jsx)(y.$,{variant:"secondary",onClick:t,children:"✕ Close"})]})}),(0,i.jsxs)("div",{className:"p-6",children:[0===h.length?(0,i.jsxs)("div",{className:"text-center py-8",children:[(0,i.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDE14"}),(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-2",children:"No recipes available"}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have enough ingredients to craft any recipes."}),(0,i.jsx)(y.$,{variant:"primary",onClick:t,children:"Buy Ingredients"})]}):(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[60vh] overflow-y-auto",children:h.map(e=>(0,i.jsxs)("div",{className:"p-4 rounded-lg border-2 cursor-pointer transition-all ".concat((null==x?void 0:x.id)===e.id?"border-orange-400 bg-orange-50":"border-gray-300 bg-gray-50 hover:border-orange-300"),onClick:()=>p(e),children:[(0,i.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{className:"text-2xl",children:(e=>{switch(e){case"cookies":return"\uD83C\uDF6A";case"cakes":return"\uD83E\uDDC1";case"bread":return"\uD83C\uDF5E";case"pastries":return"\uD83E\uDD50";default:return"\uD83C\uDF7D️"}})(e.category)}),(0,i.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name})]}),(0,i.jsxs)("span",{className:"text-sm text-green-600",children:["$",e.basePrice]})]}),(0,i.jsxs)("div",{className:"text-xs text-gray-500 mb-2",children:[f(e.difficulty)," • ⏱️ ",g(e.bakingTime)]}),(0,i.jsxs)("div",{className:"space-y-1 mb-3",children:[(0,i.jsx)("div",{className:"text-sm font-medium text-gray-700",children:"Ingredients:"}),e.ingredients.map((e,s)=>{let t=d.find(s=>s.name===e.name);return(0,i.jsxs)("div",{className:"text-xs flex justify-between text-green-600",children:[(0,i.jsx)("span",{children:e.name}),(0,i.jsxs)("span",{children:[e.quantity,(0,i.jsxs)("span",{className:"ml-1",children:["(",(null==t?void 0:t.quantity)||0," available)"]})]})]},s)})]}),(null==x?void 0:x.id)===e.id&&(0,i.jsx)(y.$,{variant:"success",size:"sm",className:"w-full",onClick:()=>{var s;c(s=e,d)&&s.ingredients.every(e=>{let s=d.find(s=>s.name===e.name);return s&&s.quantity>=e.quantity})&&(s.ingredients.forEach(e=>{u(e.name,e.quantity)}),m(n,{isActive:!0,timeRemaining:s.bakingTime,currentRecipe:s.name}),t())},children:"\uD83D\uDD25 Start Baking"})]},e.id))}),x&&h.length>0&&(0,i.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,i.jsxs)("h3",{className:"font-medium text-blue-800 mb-2",children:["\uD83D\uDCCB Baking Instructions for ",x.name]}),(0,i.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,i.jsxs)("p",{children:["• Baking time: ",g(x.bakingTime)]}),(0,i.jsxs)("p",{children:["• Difficulty: ",f(x.difficulty)]}),(0,i.jsxs)("p",{children:["• Expected reward: $",x.basePrice]}),(0,i.jsx)("p",{children:"• Make sure you have all ingredients before starting!"})]})]})]})]})})}function w(e){let{notifications:s,onRemove:t}=e;return((0,a.useEffect)(()=>{s.forEach(e=>{if(e.duration){let s=setTimeout(()=>{t(e.id)},e.duration);return()=>clearTimeout(s)}})},[s,t]),0===s.length)?null:(0,i.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2 max-w-sm",children:s.map(e=>(0,i.jsx)("div",{className:"p-4 rounded-lg border-l-4 shadow-lg transition-all duration-300 ".concat((e=>{switch(e){case"success":return"bg-green-100 border-green-400 text-green-800";case"error":return"bg-red-100 border-red-400 text-red-800";case"warning":return"bg-yellow-100 border-yellow-400 text-yellow-800";case"info":return"bg-blue-100 border-blue-400 text-blue-800";default:return"bg-gray-100 border-gray-400 text-gray-800"}})(e.type)),children:(0,i.jsxs)("div",{className:"flex items-start justify-between",children:[(0,i.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,i.jsx)("span",{className:"text-lg",children:(e=>{switch(e){case"success":return"✅";case"error":return"❌";case"warning":return"⚠️";case"info":return"ℹ️";default:return"\uD83D\uDCE2"}})(e.type)}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium",children:e.title}),(0,i.jsx)("p",{className:"text-sm opacity-90",children:e.message})]})]}),(0,i.jsx)("button",{onClick:()=>t(e.id),className:"text-lg opacity-60 hover:opacity-100 transition-opacity",children:"\xd7"})]})},e.id))})}function C(e){let{isOpen:s,onClose:t,newLevel:a,rewards:n}=e;return s?(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full overflow-hidden",children:[(0,i.jsxs)("div",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 p-6 text-center",children:[(0,i.jsx)("div",{className:"text-6xl mb-2",children:"\uD83C\uDF89"}),(0,i.jsx)("h2",{className:"text-3xl font-bold text-white mb-2",children:"Level Up!"}),(0,i.jsxs)("p",{className:"text-xl text-yellow-100",children:["You reached Level ",a,"!"]})]}),(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"\uD83C\uDF81 Level Rewards"}),(0,i.jsx)("div",{className:"space-y-3 mb-6",children:n.map((e,s)=>(0,i.jsx)("div",{className:"p-3 rounded-lg border ".concat((e=>{switch(e){case"recipe":return"bg-blue-50 border-blue-300 text-blue-800";case"equipment":return"bg-purple-50 border-purple-300 text-purple-800";case"money":return"bg-green-50 border-green-300 text-green-800";case"skill_point":return"bg-yellow-50 border-yellow-300 text-yellow-800";case"achievement":return"bg-orange-50 border-orange-300 text-orange-800";default:return"bg-gray-50 border-gray-300 text-gray-800"}})(e.type)),children:(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("span",{className:"text-2xl",children:(e=>{switch(e){case"recipe":return"\uD83D\uDCD6";case"equipment":return"⚙️";case"money":return"\uD83D\uDCB0";case"skill_point":return"⭐";case"achievement":return"\uD83C\uDFC6";default:return"\uD83C\uDF81"}})(e.type)}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("h4",{className:"font-medium",children:e.name}),(0,i.jsx)("p",{className:"text-sm opacity-80",children:e.description}),e.value&&(0,i.jsx)("p",{className:"text-sm font-semibold",children:"money"===e.type?"$".concat(e.value):"+".concat(e.value)})]})]})},s))}),(0,i.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg mb-6",children:[(0,i.jsx)("h4",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 What's Next?"}),(0,i.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,i.jsx)("li",{children:"• Check out new recipes in your recipe book"}),(0,i.jsx)("li",{children:"• Visit the shop for new equipment"}),(0,i.jsx)("li",{children:"• Take on more challenging orders"}),(0,i.jsx)("li",{children:"• Invest in skill upgrades"})]})]}),(0,i.jsx)(y.$,{variant:"primary",size:"lg",className:"w-full",onClick:t,children:"\uD83D\uDE80 Continue Playing"})]})]})}):null}function _(e){let{isOpen:s,onClose:t,achievements:n}=e,[l,r]=(0,a.useState)("all");if(!s)return null;let c="all"===l?n:n.filter(e=>e.category===l),d=n.filter(e=>e.completed).length,o=n.length;return(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,i.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83C\uDFC6 Achievements"}),(0,i.jsxs)("p",{className:"text-gray-600",children:[d," of ",o," achievements completed"]})]}),(0,i.jsx)(y.$,{variant:"secondary",onClick:t,children:"✕ Close"})]})}),(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,i.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Overall Progress"}),(0,i.jsxs)("span",{className:"text-sm text-gray-500",children:[Math.round(d/o*100),"%"]})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,i.jsx)("div",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 h-3 rounded-full transition-all duration-500",style:{width:"".concat(d/o*100,"%")}})})]}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:[{id:"all",name:"All",icon:"\uD83C\uDFC6"},{id:"baking",name:"Baking",icon:"\uD83D\uDC68‍\uD83C\uDF73"},{id:"business",name:"Business",icon:"\uD83D\uDCBC"},{id:"efficiency",name:"Efficiency",icon:"⚡"},{id:"collection",name:"Collection",icon:"\uD83D\uDCDA"},{id:"special",name:"Special",icon:"⭐"}].map(e=>(0,i.jsxs)(y.$,{variant:l===e.id?"primary":"secondary",size:"sm",onClick:()=>r(e.id),children:[e.icon," ",e.name]},e.id))}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[50vh] overflow-y-auto",children:c.map(e=>{let s=e.completed?100:Math.min(...e.requirements.map(e=>e.current?Math.min(100,e.current/e.target*100):0)),t=e.completed,a=e.unlocked;return(0,i.jsx)("div",{className:"p-4 rounded-lg border-2 ".concat(t?"border-green-400 bg-green-50":a?"border-gray-300 bg-white":"border-gray-200 bg-gray-50 opacity-60"),children:(0,i.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,i.jsx)("div",{className:"text-3xl ".concat(t?"grayscale-0":"grayscale"),children:e.icon}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsxs)("h3",{className:"font-semibold ".concat(t?"text-green-800":"text-gray-800"),children:[e.name,t&&(0,i.jsx)("span",{className:"ml-2",children:"✅"})]}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.description}),a&&!t&&(0,i.jsxs)("div",{className:"mb-2",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,i.jsx)("span",{className:"text-xs text-gray-500",children:"Progress"}),(0,i.jsx)("span",{className:"text-xs text-gray-500",children:(e=>{if(e.completed)return"Completed!";let s=e.requirements[0];return"".concat(s.current||0," / ").concat(s.target)})(e)})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(s,"%")}})})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,i.jsx)("span",{className:"text-gray-500",children:"Reward:"}),(0,i.jsx)("span",{className:"text-lg",children:(e=>{switch(e){case"recipe":return"\uD83D\uDCD6";case"equipment":return"⚙️";case"money":return"\uD83D\uDCB0";case"skill_point":return"⭐";default:return"\uD83C\uDF81"}})(e.reward.type)}),(0,i.jsx)("span",{className:"text-gray-700",children:e.reward.name}),e.reward.value&&(0,i.jsx)("span",{className:"text-green-600 font-medium",children:"money"===e.reward.type?"$".concat(e.reward.value):"+".concat(e.reward.value)})]})]})]})},e.id)})}),0===c.length&&(0,i.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,i.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFC6"}),(0,i.jsx)("p",{children:"No achievements in this category."})]})]})]})})}function q(e){let{isOpen:s,onClose:t,skills:n,skillPoints:l,playerLevel:r,onUpgradeSkill:c}=e,[d,o]=(0,a.useState)("all");if(!s)return null;let m="all"===d?n:n.filter(e=>e.category===d),u=e=>!(e.level>=e.maxLevel)&&!(l<e.cost)&&(!e.requirements.playerLevel||!(r<e.requirements.playerLevel))&&(!e.requirements.skills||e.requirements.skills.every(e=>{let s=n.find(s=>s.id===e);return s&&s.level>0}));return(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] overflow-hidden",children:[(0,i.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83C\uDF1F Skill Tree"}),(0,i.jsxs)("p",{className:"text-gray-600",children:["Available Skill Points: ",(0,i.jsx)("span",{className:"font-semibold text-blue-600",children:l})]})]}),(0,i.jsx)(y.$,{variant:"secondary",onClick:t,children:"✕ Close"})]})}),(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:[{id:"all",name:"All",icon:"\uD83C\uDF1F"},{id:"efficiency",name:"Efficiency",icon:"⚡"},{id:"automation",name:"Automation",icon:"\uD83E\uDD16"},{id:"quality",name:"Quality",icon:"\uD83D\uDC8E"},{id:"business",name:"Business",icon:"\uD83D\uDCBC"}].map(e=>(0,i.jsxs)(y.$,{variant:d===e.id?"primary":"secondary",size:"sm",onClick:()=>o(e.id),children:[e.icon," ",e.name]},e.id))}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto",children:m.map(e=>{let s=e.level>=e.maxLevel?"maxed":u(e)?"available":"locked",t=u(e);return(0,i.jsxs)("div",{className:"p-4 rounded-lg border-2 ".concat((e=>{switch(e){case"maxed":return"border-green-400 bg-green-50";case"available":return"border-blue-400 bg-blue-50";default:return"border-gray-300 bg-gray-50"}})(s)),children:[(0,i.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{className:"text-2xl",children:e.icon}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name}),(0,i.jsx)("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:e.category})]})]}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsxs)("div",{className:"text-sm font-medium text-gray-700",children:["Level ",e.level,"/",e.maxLevel]}),"maxed"!==s&&(0,i.jsxs)("div",{className:"text-xs text-blue-600",children:["Cost: ",e.cost," SP"]})]})]}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,i.jsxs)("div",{className:"mb-3",children:[(0,i.jsx)("h4",{className:"text-xs font-medium text-gray-700 mb-1",children:"Effects:"}),e.effects.map((e,s)=>(0,i.jsxs)("div",{className:"text-xs text-green-600",children:["• ",(e=>{let s=Math.round(100*e.value);switch(e.type){case"baking_speed":return"+".concat(s,"% baking speed");case"money_multiplier":return"+".concat(s,"% money earned");case"xp_multiplier":return"+".concat(s,"% experience gained");case"ingredient_efficiency":return"".concat(s,"% less ingredients used");case"automation_unlock":return"Unlock automation features";default:return"+".concat(s,"% bonus")}})(e)]},s))]}),e.requirements.playerLevel&&r<e.requirements.playerLevel&&(0,i.jsx)("div",{className:"mb-3",children:(0,i.jsxs)("div",{className:"text-xs text-red-600",children:["Requires Level ",e.requirements.playerLevel]})}),e.requirements.skills&&(0,i.jsx)("div",{className:"mb-3",children:(0,i.jsxs)("div",{className:"text-xs text-gray-600",children:["Requires: ",e.requirements.skills.map(e=>{let s=n.find(s=>s.id===e);return null==s?void 0:s.name}).join(", ")]})}),(0,i.jsx)("div",{className:"mb-3",children:(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(e.level/e.maxLevel*100,"%")}})})}),"maxed"===s?(0,i.jsx)(y.$,{variant:"success",size:"sm",className:"w-full",disabled:!0,children:"✅ Maxed"}):t?(0,i.jsxs)(y.$,{variant:"primary",size:"sm",className:"w-full",onClick:()=>c(e.id),children:["⬆️ Upgrade (",e.cost," SP)"]}):(0,i.jsx)(y.$,{variant:"secondary",size:"sm",className:"w-full",disabled:!0,children:"\uD83D\uDD12 Locked"})]},e.id)})}),0===m.length&&(0,i.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,i.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDF1F"}),(0,i.jsx)("p",{children:"No skills in this category."})]}),(0,i.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,i.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 Skill Tips"}),(0,i.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,i.jsx)("li",{children:"• Earn skill points by leveling up (1 point every 2 levels)"}),(0,i.jsx)("li",{children:"• Some skills require other skills to be unlocked first"}),(0,i.jsx)("li",{children:"• Focus on skills that match your playstyle"}),(0,i.jsx)("li",{children:"• Efficiency skills help with resource management"})]})]})]})]})})}function S(e){var s;let{isOpen:t,onClose:n}=e,{player:l,equipment:r,automationSettings:c,updateAutomationSettings:d,purchaseAutomationUpgrade:o}=v(),[m,u]=(0,a.useState)("settings");if(!t)return null;let x=r.filter(e=>e.automationLevel>0),h=p.filter(e=>{var s;return l.level>=e.unlockLevel&&!(null==(s=l.automationUpgrades)?void 0:s.includes(e.id))}),g=(e,s)=>{d({[e]:s})};return(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,i.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83E\uDD16 Automation Control"}),(0,i.jsx)(y.$,{variant:"secondary",onClick:n,children:"✕ Close"})]})}),(0,i.jsx)("div",{className:"border-b border-gray-200",children:(0,i.jsx)("div",{className:"flex space-x-0",children:[{id:"settings",name:"Settings",icon:"⚙️"},{id:"upgrades",name:"Upgrades",icon:"\uD83D\uDD27"},{id:"status",name:"Status",icon:"\uD83D\uDCCA"}].map(e=>(0,i.jsxs)("button",{onClick:()=>u(e.id),className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(m===e.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.icon," ",e.name]},e.id))})}),(0,i.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["settings"===m&&(0,i.jsx)("div",{className:"space-y-6",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,i.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"\uD83C\uDF9B️ Master Control"}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"checkbox",checked:(null==c?void 0:c.enabled)||!1,onChange:e=>g("enabled",e.target.checked),className:"rounded"}),(0,i.jsx)("span",{className:"text-sm",children:"Enable Automation"})]}),(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"checkbox",checked:(null==c?void 0:c.autoStart)||!1,onChange:e=>g("autoStart",e.target.checked),className:"rounded"}),(0,i.jsx)("span",{className:"text-sm",children:"Auto-start Equipment"})]})]})]}),(0,i.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,i.jsx)("h3",{className:"font-semibold text-green-800 mb-3",children:"\uD83C\uDFAF Priority Mode"}),(0,i.jsxs)("select",{value:(null==c?void 0:c.priorityMode)||"efficiency",onChange:e=>g("priorityMode",e.target.value),className:"w-full p-2 border rounded-lg",children:[(0,i.jsx)("option",{value:"efficiency",children:"Efficiency (Orders First)"}),(0,i.jsx)("option",{value:"profit",children:"Profit (Highest Value)"}),(0,i.jsx)("option",{value:"speed",children:"Speed (Fastest Recipes)"})]}),(0,i.jsx)("p",{className:"text-xs text-green-600 mt-1",children:"How automation chooses what to bake"})]}),(0,i.jsxs)("div",{className:"bg-purple-50 p-4 rounded-lg",children:[(0,i.jsx)("h3",{className:"font-semibold text-purple-800 mb-3",children:"⚡ Performance"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"block text-sm",children:["Max Concurrent Jobs: ",(null==c?void 0:c.maxConcurrentJobs)||2]}),(0,i.jsx)("input",{type:"range",min:"1",max:"5",value:(null==c?void 0:c.maxConcurrentJobs)||2,onChange:e=>g("maxConcurrentJobs",parseInt(e.target.value)),className:"w-full"})]})]}),(0,i.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,i.jsx)("h3",{className:"font-semibold text-yellow-800 mb-3",children:"\uD83D\uDEE1️ Safety"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"block text-sm",children:["Stop when ingredients below: ",(null==c?void 0:c.ingredientThreshold)||5]}),(0,i.jsx)("input",{type:"range",min:"0",max:"20",value:(null==c?void 0:c.ingredientThreshold)||5,onChange:e=>g("ingredientThreshold",parseInt(e.target.value)),className:"w-full"})]})]})]})}),"upgrades"===m&&(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg mb-4",children:[(0,i.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 Automation Upgrades"}),(0,i.jsx)("p",{className:"text-sm text-blue-700",children:"Improve your automation efficiency, speed, and intelligence with these upgrades."})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:h.map(e=>(0,i.jsxs)("div",{className:"p-4 border rounded-lg bg-white hover:bg-gray-50 transition-colors",children:[(0,i.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,i.jsx)("h4",{className:"font-semibold text-gray-800",children:e.name}),(0,i.jsxs)("span",{className:"text-sm text-green-600 font-medium",children:["$",e.cost]})]}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{className:"text-xs text-gray-500 uppercase tracking-wide",children:e.type}),(0,i.jsx)(y.$,{size:"sm",variant:l.money>=e.cost?"primary":"secondary",disabled:l.money<e.cost,onClick:()=>o(e.id),children:l.money>=e.cost?"Purchase":"Too Expensive"})]})]},e.id))}),0===h.length&&(0,i.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,i.jsx)("div",{className:"text-4xl mb-2",children:"\uD83D\uDD27"}),(0,i.jsx)("p",{children:"No upgrades available at your current level."}),(0,i.jsx)("p",{className:"text-sm",children:"Level up to unlock more automation upgrades!"})]})]}),"status"===m&&(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg text-center",children:[(0,i.jsx)("div",{className:"text-2xl text-green-600 mb-1",children:x.length}),(0,i.jsx)("div",{className:"text-sm text-green-800",children:"Automated Equipment"})]}),(0,i.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg text-center",children:[(0,i.jsx)("div",{className:"text-2xl text-blue-600 mb-1",children:(null==c?void 0:c.enabled)?"✅":"❌"}),(0,i.jsx)("div",{className:"text-sm text-blue-800",children:"Automation Status"})]}),(0,i.jsxs)("div",{className:"bg-purple-50 p-4 rounded-lg text-center",children:[(0,i.jsx)("div",{className:"text-2xl text-purple-600 mb-1",children:(null==(s=l.automationUpgrades)?void 0:s.length)||0}),(0,i.jsx)("div",{className:"text-sm text-purple-800",children:"Active Upgrades"})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"font-semibold text-gray-800",children:"\uD83C\uDFED Equipment Status"}),x.length>0?(0,i.jsx)("div",{className:"space-y-2",children:x.map(e=>(0,i.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium",children:e.name}),(0,i.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["Level ",e.automationLevel," • ",e.efficiency,"x efficiency"]})]}),(0,i.jsx)("div",{className:"px-2 py-1 rounded text-xs ".concat(e.isActive?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"),children:e.isActive?"Running":"Idle"})]},e.id))}):(0,i.jsxs)("div",{className:"text-center py-4 text-gray-500",children:[(0,i.jsx)("p",{children:"No automated equipment available."}),(0,i.jsx)("p",{className:"text-sm",children:"Purchase auto-equipment from the shop to get started!"})]})]})]})]})]})})}let D=[{id:"professional_oven",name:"Professional Oven",type:"oven",description:"Faster and more efficient than basic oven",cost:500,unlockLevel:3,automationLevel:0,efficiency:1.3,icon:"\uD83D\uDD25",category:"basic"},{id:"auto_oven",name:"Automated Oven",type:"auto_oven",description:"Fully automated oven that can run without supervision",cost:1500,unlockLevel:5,automationLevel:2,efficiency:1.5,icon:"\uD83E\uDD16",category:"automated"},{id:"industrial_mixer",name:"Industrial Mixer",type:"mixer",description:"High-capacity mixer for large batches",cost:750,unlockLevel:4,automationLevel:0,efficiency:1.4,icon:"\uD83E\uDD44",category:"basic"},{id:"auto_mixer",name:"Automated Mixer",type:"auto_mixer",description:"Self-operating mixer with ingredient dispensing",cost:2e3,unlockLevel:6,automationLevel:2,efficiency:1.6,icon:"\uD83E\uDD16",category:"automated"},{id:"conveyor_belt_basic",name:"Basic Conveyor Belt",type:"conveyor",description:"Moves items between equipment automatically",cost:1e3,unlockLevel:7,automationLevel:1,efficiency:1.2,icon:"\uD83D\uDD04",category:"automated"},{id:"smart_conveyor",name:"Smart Conveyor System",type:"conveyor",description:"Intelligent conveyor with sorting and routing",cost:3e3,unlockLevel:10,automationLevel:3,efficiency:1.8,icon:"\uD83E\uDDE0",category:"advanced"},{id:"master_oven",name:"Master Oven",type:"oven",description:"The ultimate baking machine with AI assistance",cost:5e3,unlockLevel:12,automationLevel:3,efficiency:2,icon:"\uD83D\uDC51",category:"advanced"}];function L(e){let{isOpen:s,onClose:t,onShowSuccess:n}=e,{player:l,equipment:r,spendMoney:c,addEquipment:d}=v(),[o,m]=(0,a.useState)("all");if(!s)return null;let u=D.filter(e=>l.level>=e.unlockLevel&&("all"===o||e.category===o));return(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,i.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83C\uDFEA Equipment Shop"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Upgrade your bakery with professional equipment"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,i.jsxs)("span",{className:"text-green-800 font-medium",children:["$",l.money]})}),(0,i.jsx)(y.$,{variant:"secondary",onClick:t,children:"✕ Close"})]})]})}),(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:[{id:"all",name:"All",icon:"\uD83C\uDFEA"},{id:"basic",name:"Basic",icon:"\uD83D\uDD27"},{id:"automated",name:"Automated",icon:"\uD83E\uDD16"},{id:"advanced",name:"Advanced",icon:"⚡"}].map(e=>(0,i.jsxs)(y.$,{variant:o===e.id?"primary":"secondary",size:"sm",onClick:()=>m(e.id),children:[e.icon," ",e.name]},e.id))}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto",children:u.map(e=>{var s;return(0,i.jsxs)("div",{className:"p-4 rounded-lg border-2 ".concat((e=>{switch(e){case"basic":return"border-gray-300 bg-gray-50";case"automated":return"border-blue-300 bg-blue-50";case"advanced":return"border-purple-300 bg-purple-50";default:return"border-gray-300 bg-white"}})(e.category)),children:[(0,i.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{className:"text-2xl",children:e.icon}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name}),(0,i.jsx)("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:e.category})]})]}),(0,i.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["$",e.cost]})]}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,i.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,i.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Efficiency:"}),(0,i.jsxs)("span",{className:"font-medium",children:[e.efficiency,"x"]})]}),e.automationLevel>0&&(0,i.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Automation:"}),0===(s=e.automationLevel)?null:(0,i.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:["\uD83E\uDD16 Auto Level ",s]})]}),(0,i.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Unlock Level:"}),(0,i.jsx)("span",{className:"font-medium",children:e.unlockLevel})]})]}),(0,i.jsx)(y.$,{variant:l.money>=e.cost?"success":"secondary",size:"sm",className:"w-full",disabled:l.money<e.cost,onClick:()=>{!(l.money<e.cost)&&c(e.cost)&&(d({name:e.name,type:e.type,isActive:!1,level:1,efficiency:e.efficiency,automationLevel:e.automationLevel}),n&&n("Equipment Purchased!","You bought ".concat(e.name,"!")))},children:l.money>=e.cost?"\uD83D\uDCB0 Purchase":"\uD83D\uDCB8 Too Expensive"})]},e.id)})}),0===u.length&&(0,i.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,i.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFEA"}),(0,i.jsx)("p",{children:"No equipment available in this category."}),(0,i.jsx)("p",{className:"text-sm",children:"Level up to unlock more equipment!"})]}),(0,i.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,i.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 Equipment Tips"}),(0,i.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,i.jsx)("li",{children:"• Automated equipment can run without your supervision"}),(0,i.jsx)("li",{children:"• Higher efficiency means faster production and better quality"}),(0,i.jsx)("li",{children:"• Conveyor belts connect equipment for seamless workflow"}),(0,i.jsx)("li",{children:"• Advanced equipment unlocks at higher levels"})]})]})]})]})})}var E=t(4382);let M=[{name:"Downtown Delights",location:"City Center",specialization:"general",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:0},{name:"Cookie Corner",location:"Shopping Mall",specialization:"cookies",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:2500},{name:"Cake Castle",location:"Wedding District",specialization:"cakes",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:3500},{name:"Bread Basket",location:"Farmers Market",specialization:"bread",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:3e3},{name:"Pastry Palace",location:"French Quarter",specialization:"pastries",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:4e3}];function z(e){let{isOpen:s,onClose:t,bakeries:l,currentBakeryId:r,onSwitchBakery:c,onPurchaseBakery:d,playerMoney:o}=e,{t:m}=(0,n.o)(),[u,x]=(0,a.useState)("owned");if(!s)return null;let p=l.filter(e=>e.unlocked),h=M.filter(e=>!l.some(s=>s.name===e.name&&s.unlocked)),g=e=>{switch(e){case"cookies":return"\uD83C\uDF6A";case"cakes":return"\uD83E\uDDC1";case"bread":return"\uD83C\uDF5E";case"pastries":return"\uD83E\uDD50";default:return"\uD83C\uDFEA"}},v=e=>{switch(e){case"cookies":return"+20% Cookie Production Speed";case"cakes":return"+25% Cake Profit Margin";case"bread":return"+15% Bread Ingredient Efficiency";case"pastries":return"+30% Pastry Experience Gain";default:return"Balanced Production"}},f=[{id:"owned",name:m("bakeries.owned")||"My Bakeries",icon:"\uD83C\uDFEA"},{id:"available",name:m("bakeries.available")||"Available",icon:"\uD83D\uDED2"}];return(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,i.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:m("bakeries.title")||"\uD83C\uDFEA Bakery Manager"}),(0,i.jsx)("p",{className:"text-gray-600",children:m("bakeries.subtitle")||"Manage your bakery empire"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,i.jsxs)("span",{className:"text-green-800 font-medium",children:["$",o]})}),(0,i.jsx)(y.$,{variant:"secondary",onClick:t,children:m("game.close")||"✕ Close"})]})]})}),(0,i.jsx)("div",{className:"border-b border-gray-200",children:(0,i.jsx)("div",{className:"flex space-x-0",children:f.map(e=>(0,i.jsxs)("button",{onClick:()=>x(e.id),className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(u===e.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.icon," ",e.name]},e.id))})}),(0,i.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["owned"===u&&(0,i.jsx)("div",{className:"space-y-4",children:p.length>0?(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:p.map(e=>(0,i.jsxs)("div",{className:"p-4 rounded-lg border-2 cursor-pointer transition-all ".concat(e.id===r?"border-orange-400 bg-orange-50":"border-gray-300 bg-white hover:border-orange-300"),onClick:()=>c(e.id),children:[(0,i.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{className:"text-2xl",children:g(e.specialization)}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:e.location})]})]}),e.id===r&&(0,i.jsx)("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full",children:m("bakeries.current")||"Current"})]}),(0,i.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsxs)("span",{className:"text-gray-600",children:[m("bakeries.level")||"Level",":"]}),(0,i.jsx)("span",{className:"font-medium",children:e.level})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsxs)("span",{className:"text-gray-600",children:[m("bakeries.specialization")||"Specialization",":"]}),(0,i.jsx)("span",{className:"font-medium capitalize",children:e.specialization})]}),(0,i.jsx)("div",{className:"text-xs text-blue-600",children:v(e.specialization)})]}),(0,i.jsxs)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:[(0,i.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,i.jsxs)("span",{className:"text-gray-600",children:[m("bakeries.equipment")||"Equipment",":"]}),(0,i.jsx)("span",{children:e.equipment.length})]}),(0,i.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,i.jsxs)("span",{className:"text-gray-600",children:[m("bakeries.orders")||"Active Orders",":"]}),(0,i.jsx)("span",{children:e.orders.length})]})]}),e.id!==r&&(0,i.jsx)(y.$,{variant:"primary",size:"sm",className:"w-full mt-3",onClick:s=>{s.stopPropagation(),c(e.id)},children:m("bakeries.switchTo")||"Switch To"})]},e.id))}):(0,i.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,i.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFEA"}),(0,i.jsx)("p",{children:m("bakeries.noOwned")||"You don't own any bakeries yet."})]})}),"available"===u&&(0,i.jsx)("div",{className:"space-y-4",children:h.length>0?(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:h.map((e,s)=>(0,i.jsxs)("div",{className:"p-4 rounded-lg border-2 border-gray-300 bg-white",children:[(0,i.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{className:"text-2xl",children:g(e.specialization)}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:e.location})]})]}),(0,i.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["$",e.purchaseCost]})]}),(0,i.jsxs)("div",{className:"space-y-2 text-sm mb-4",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsxs)("span",{className:"text-gray-600",children:[m("bakeries.specialization")||"Specialization",":"]}),(0,i.jsx)("span",{className:"font-medium capitalize",children:e.specialization})]}),(0,i.jsx)("div",{className:"text-xs text-blue-600",children:v(e.specialization)})]}),(0,i.jsx)(y.$,{variant:o>=e.purchaseCost?"success":"secondary",size:"sm",className:"w-full",disabled:o<e.purchaseCost,onClick:()=>{o>=e.purchaseCost&&d({...e,id:"bakery_".concat(Date.now()),unlocked:!0})},children:o>=e.purchaseCost?m("bakeries.purchase")||"\uD83D\uDCB0 Purchase":m("bakeries.tooExpensive")||"\uD83D\uDCB8 Too Expensive"})]},s))}):(0,i.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,i.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDF89"}),(0,i.jsx)("p",{children:m("bakeries.allOwned")||"You own all available bakeries!"})]})})]}),(0,i.jsxs)("div",{className:"p-4 bg-blue-50 border-t border-gray-200",children:[(0,i.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:m("bakeries.tips")||"\uD83D\uDCA1 Bakery Tips"}),(0,i.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,i.jsxs)("li",{children:["• ",m("bakeries.tip1")||"Each bakery specializes in different products for bonus efficiency"]}),(0,i.jsxs)("li",{children:["• ",m("bakeries.tip2")||"Switch between bakeries to manage multiple locations"]}),(0,i.jsxs)("li",{children:["• ",m("bakeries.tip3")||"Specialized bakeries attract customers looking for specific items"]}),(0,i.jsxs)("li",{children:["• ",m("bakeries.tip4")||"Upgrade each bakery independently for maximum profit"]})]})]})]})})}function B(e){let{player:s,onOpenMenu:t,onOpenAchievements:l,onOpenSkills:r,onOpenBakeries:c,onOpenSettings:d}=e,{t:o}=(0,n.o)(),[m,u]=(0,a.useState)(!1),x=s.experience/s.maxExperience*100;return(0,i.jsx)("div",{className:"bg-white shadow-lg border-b border-gray-200 relative",children:(0,i.jsxs)("div",{className:"px-6 py-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)(y.$,{variant:"secondary",size:"sm",className:"bg-orange-100 hover:bg-orange-200 text-orange-800",onClick:t,children:["☰ ",o("toolbar.menu","Menu")]}),(0,i.jsx)("div",{className:"hidden md:block",children:(0,i.jsx)("h1",{className:"text-xl font-bold text-orange-800",children:"\uD83E\uDD56 Bake It Out"})})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-sm font-medium text-gray-800",children:s.name}),(0,i.jsxs)("div",{className:"text-xs text-gray-500",children:["Level ",s.level]})]}),(0,i.jsxs)("div",{className:"hidden sm:block",children:[(0,i.jsx)("div",{className:"w-32 bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(x,"%")}})}),(0,i.jsxs)("div",{className:"text-xs text-center text-gray-500 mt-1",children:[s.experience,"/",s.maxExperience," XP"]})]}),(0,i.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,i.jsxs)("span",{className:"text-green-800 font-medium",children:["$",s.money]})}),s.skillPoints>0&&(0,i.jsxs)("div",{className:"bg-yellow-100 px-3 py-1 rounded-full relative",children:[(0,i.jsxs)("span",{className:"text-yellow-800 font-medium",children:["⭐ ",s.skillPoints]}),(0,i.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(y.$,{variant:"secondary",size:"sm",className:"md:hidden",onClick:()=>u(!m),children:"⚡"}),(0,i.jsxs)("div",{className:"hidden md:flex items-center space-x-2",children:[(0,i.jsx)(y.$,{variant:"secondary",size:"sm",onClick:c,children:"\uD83C\uDFEA"}),(0,i.jsx)(y.$,{variant:"secondary",size:"sm",onClick:l,className:"relative",children:"\uD83C\uDFC6"}),(0,i.jsxs)(y.$,{variant:"secondary",size:"sm",onClick:r,className:s.skillPoints>0?"bg-yellow-100 hover:bg-yellow-200":"",children:["\uD83C\uDF1F",s.skillPoints>0&&(0,i.jsx)("span",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]}),(0,i.jsx)(y.$,{variant:"secondary",size:"sm",onClick:d,children:"⚙️"})]})]})]}),m&&(0,i.jsxs)("div",{className:"md:hidden mt-4 pt-4 border-t border-gray-200",children:[(0,i.jsxs)("div",{className:"grid grid-cols-4 gap-2",children:[(0,i.jsxs)(y.$,{variant:"secondary",size:"sm",className:"flex flex-col items-center py-3",onClick:()=>{c(),u(!1)},children:[(0,i.jsx)("span",{className:"text-lg",children:"\uD83C\uDFEA"}),(0,i.jsx)("span",{className:"text-xs",children:o("toolbar.bakeries","Bakeries")})]}),(0,i.jsxs)(y.$,{variant:"secondary",size:"sm",className:"flex flex-col items-center py-3",onClick:()=>{l(),u(!1)},children:[(0,i.jsx)("span",{className:"text-lg",children:"\uD83C\uDFC6"}),(0,i.jsx)("span",{className:"text-xs",children:o("toolbar.achievements","Achievements")})]}),(0,i.jsxs)(y.$,{variant:"secondary",size:"sm",className:"flex flex-col items-center py-3 relative ".concat(s.skillPoints>0?"bg-yellow-100 hover:bg-yellow-200":""),onClick:()=>{r(),u(!1)},children:[(0,i.jsx)("span",{className:"text-lg",children:"\uD83C\uDF1F"}),(0,i.jsx)("span",{className:"text-xs",children:o("toolbar.skills","Skills")}),s.skillPoints>0&&(0,i.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]}),(0,i.jsxs)(y.$,{variant:"secondary",size:"sm",className:"flex flex-col items-center py-3",onClick:()=>{d(),u(!1)},children:[(0,i.jsx)("span",{className:"text-lg",children:"⚙️"}),(0,i.jsx)("span",{className:"text-xs",children:o("toolbar.settings","Settings")})]})]}),(0,i.jsxs)("div",{className:"mt-3",children:[(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(x,"%")}})}),(0,i.jsxs)("div",{className:"text-xs text-center text-gray-500 mt-1",children:[s.experience,"/",s.maxExperience," XP"]})]})]})]})})}function A(e){let{isOpen:s,onClose:t,onSaveGame:a,onLoadGame:l,onSettings:r,onMainMenu:c,onExit:d}=e,{t:o}=(0,n.o)();return s?(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden",children:[(0,i.jsx)("div",{className:"bg-gradient-to-r from-orange-500 to-yellow-500 p-6 text-white",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h2",{className:"text-2xl font-bold",children:["\uD83C\uDFAE ",o("gameMenu.title","Game Menu")]}),(0,i.jsx)("p",{className:"text-orange-100 text-sm",children:o("gameMenu.subtitle","Manage your game")})]}),(0,i.jsx)(y.$,{variant:"secondary",size:"sm",className:"bg-white/20 hover:bg-white/30 text-white border-white/30",onClick:t,children:"✕"})]})}),(0,i.jsxs)("div",{className:"p-6 space-y-3",children:[(0,i.jsxs)(y.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-orange-50",onClick:()=>{t()},children:[(0,i.jsx)("span",{className:"text-2xl mr-3",children:"▶️"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-semibold",children:o("gameMenu.resume","Resume Game")}),(0,i.jsx)("div",{className:"text-sm text-gray-500",children:o("gameMenu.resumeDesc","Continue playing")})]})]}),(0,i.jsxs)(y.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-green-50",onClick:()=>{a(),t()},children:[(0,i.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDCBE"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-semibold",children:o("gameMenu.save","Save Game")}),(0,i.jsx)("div",{className:"text-sm text-gray-500",children:o("gameMenu.saveDesc","Save your progress")})]})]}),(0,i.jsxs)(y.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-blue-50",onClick:()=>{l(),t()},children:[(0,i.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDCC1"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-semibold",children:o("gameMenu.load","Load Game")}),(0,i.jsx)("div",{className:"text-sm text-gray-500",children:o("gameMenu.loadDesc","Load saved progress")})]})]}),(0,i.jsxs)(y.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-purple-50",onClick:()=>{r(),t()},children:[(0,i.jsx)("span",{className:"text-2xl mr-3",children:"⚙️"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-semibold",children:o("gameMenu.settings","Settings")}),(0,i.jsx)("div",{className:"text-sm text-gray-500",children:o("gameMenu.settingsDesc","Game preferences")})]})]}),(0,i.jsxs)("div",{className:"border-t pt-3 mt-4",children:[(0,i.jsxs)(y.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-yellow-50",onClick:()=>{c(),t()},children:[(0,i.jsx)("span",{className:"text-2xl mr-3",children:"\uD83C\uDFE0"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-semibold",children:o("gameMenu.mainMenu","Main Menu")}),(0,i.jsx)("div",{className:"text-sm text-gray-500",children:o("gameMenu.mainMenuDesc","Return to main menu")})]})]}),d&&(0,i.jsxs)(y.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-red-50 text-red-600",onClick:()=>{d(),t()},children:[(0,i.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDEAA"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-semibold",children:o("gameMenu.exit","Exit Game")}),(0,i.jsx)("div",{className:"text-sm text-red-400",children:o("gameMenu.exitDesc","Close the application")})]})]})]})]}),(0,i.jsx)("div",{className:"bg-gray-50 px-6 py-3 text-center text-sm text-gray-500",children:o("gameMenu.tip","Press ESC to open this menu anytime")})]})}):null}function P(){var e;let{t:s}=(0,n.o)(),{player:t,equipment:l,inventory:r,orders:c,achievements:d,skills:o,levelUpRewards:m,showLevelUp:u,updateEquipment:x,acceptOrder:p,completeOrder:h,declineOrder:g,generateNewOrder:D,upgradeSkill:M,checkAchievements:P,dismissLevelUp:$,spendMoney:F}=v(),[O,T]=(0,a.useState)(!1),[R,I]=(0,a.useState)(!1),[U,G]=(0,a.useState)(!1),[J,Y]=(0,a.useState)(!1),[H,Q]=(0,a.useState)(!1),[V,W]=(0,a.useState)(!1),[X,K]=(0,a.useState)(!1),[Z,ee]=(0,a.useState)(!1),[es,et]=(0,a.useState)(!1),[ei,ea]=(0,a.useState)(!1),[en,el]=(0,a.useState)(null),[er,ec]=(0,a.useState)({language:"en",soundEnabled:!0,musicEnabled:!0,notificationsEnabled:!0,autoSaveEnabled:!0,graphicsQuality:"medium",animationSpeed:1,showTutorials:!0}),[ed,eo]=(0,a.useState)([{id:"main",name:"Downtown Delights",location:"City Center",specialization:"general",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],unlocked:!0,purchaseCost:0}]),[em,eu]=(0,a.useState)("main"),{notifications:ex,removeNotification:ep,showSuccess:eh,showError:eg,showInfo:ev}=function(){let[e,s]=(0,a.useState)([]),t=e=>{let t=Date.now().toString()+Math.random().toString(36).substr(2,9),i={...e,id:t,duration:e.duration||5e3};s(e=>[...e,i])};return{notifications:e,addNotification:t,removeNotification:e=>{s(s=>s.filter(s=>s.id!==e))},showSuccess:(e,s)=>{t({type:"success",title:e,message:s})},showError:(e,s)=>{t({type:"error",title:e,message:s})},showWarning:(e,s)=>{t({type:"warning",title:e,message:s})},showInfo:(e,s)=>{t({type:"info",title:e,message:s})}}}(),ey=(e,s)=>{el({id:e,name:s}),G(!0)},ef=e=>{p(e),ev("Order Accepted","You have accepted a new order!")},eb=e=>{let s=c.find(s=>s.id===e);s&&(h(e),P(),eh("Order Completed!","You earned $".concat(s.reward," and gained experience!")))},ej=e=>{g(e),ev("Order Declined","Order has been removed from your queue.")};return window.addEventListener("keydown",e=>{"Escape"===e.key&&ea(!ei)}),(0,i.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-orange-50 to-yellow-50",children:[(0,i.jsx)(B,{player:t,onOpenMenu:()=>ea(!0),onOpenAchievements:()=>Y(!0),onOpenSkills:()=>Q(!0),onOpenBakeries:()=>et(!0),onOpenSettings:()=>ee(!0)}),(0,i.jsxs)("div",{className:"max-w-7xl mx-auto p-6 grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,i.jsxs)("div",{className:"lg:col-span-3 space-y-6",children:[(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,i.jsx)("h2",{className:"text-xl font-semibold text-orange-800",children:s("kitchen.title")}),(0,i.jsxs)("div",{className:"text-sm text-gray-600",children:["Current: ",null==(e=ed.find(e=>e.id===em))?void 0:e.name]})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:l.map(e=>(0,i.jsx)(f.$,{equipment:e,onClick:ey},e.id))})]}),(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,i.jsx)("h2",{className:"text-xl font-semibold text-orange-800 mb-4",children:s("inventory.title")}),(0,i.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:r.map(e=>(0,i.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,i.jsx)("div",{className:"text-2xl mb-1",children:e.icon}),(0,i.jsx)("div",{className:"font-medium text-gray-800",children:e.name}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:s("inventory.quantity",{qty:e.quantity.toString()})}),(0,i.jsx)("div",{className:"text-xs text-green-600",children:s("inventory.cost",{cost:e.cost.toString()})})]},e.name))})]})]}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,i.jsx)("h2",{className:"text-xl font-semibold text-orange-800",children:s("orders.title")}),(0,i.jsx)(y.$,{size:"sm",variant:"primary",onClick:D,children:s("orders.newOrder")})]}),(0,i.jsx)("div",{className:"space-y-4",children:c.map(e=>(0,i.jsx)(b.p,{order:e,onAccept:ef,onDecline:ej,onComplete:eb},e.id))})]}),(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,i.jsx)("h2",{className:"text-xl font-semibold text-orange-800 mb-4",children:s("actions.title")}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.$,{variant:"secondary",size:"sm",className:"w-full",onClick:()=>I(!0),children:s("actions.buyIngredients")}),(0,i.jsx)(y.$,{variant:"secondary",size:"sm",className:"w-full",onClick:()=>T(!0),children:s("actions.viewRecipes")}),(0,i.jsx)(y.$,{variant:"secondary",size:"sm",className:"w-full",onClick:()=>K(!0),children:s("actions.equipmentShop")})]})]})]})]}),(0,i.jsx)(j,{isOpen:O,onClose:()=>T(!1)}),(0,i.jsx)(N,{isOpen:R,onClose:()=>I(!1)}),(0,i.jsx)(k,{isOpen:U,onClose:()=>G(!1),equipmentId:(null==en?void 0:en.id)||"",equipmentName:(null==en?void 0:en.name)||""}),(0,i.jsx)(_,{isOpen:J,onClose:()=>Y(!1),achievements:d}),(0,i.jsx)(q,{isOpen:H,onClose:()=>Q(!1),skills:o,skillPoints:t.skillPoints,playerLevel:t.level,onUpgradeSkill:M}),(0,i.jsx)(C,{isOpen:u,onClose:$,newLevel:t.level,rewards:m}),(0,i.jsx)(S,{isOpen:V,onClose:()=>W(!1)}),(0,i.jsx)(L,{isOpen:X,onClose:()=>K(!1),onShowSuccess:eh}),(0,i.jsx)(E.b,{isOpen:Z,onClose:()=>ee(!1),settings:er,onSettingsChange:e=>{ec(s=>({...s,...e}))}}),(0,i.jsx)(z,{isOpen:es,onClose:()=>et(!1),bakeries:ed,currentBakeryId:em,onSwitchBakery:e=>{var s;eu(e),ev("Bakery Switched","Switched to ".concat(null==(s=ed.find(s=>s.id===e))?void 0:s.name))},onPurchaseBakery:e=>{if(F(e.purchaseCost)){let s={id:Date.now().toString(),name:e.name,location:"Downtown",specialization:"general",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],unlocked:!0,purchaseCost:e.purchaseCost};eo(e=>[...e,s]),eh("Bakery Purchased!","You now own ".concat(e.name,"!"))}},playerMoney:t.money}),(0,i.jsx)(A,{isOpen:ei,onClose:()=>ea(!1),onSaveGame:()=>{eh("Game Saved!","Your progress has been saved.")},onLoadGame:()=>{eh("Game Loaded!","Your saved progress has been loaded.")},onSettings:()=>ee(!0),onMainMenu:()=>{window.location.href="/"},onExit:window.electronAPI?()=>{window.electronAPI&&window.electronAPI.quit()}:void 0}),(0,i.jsx)(w,{notifications:ex,onRemove:ep})]})}function $(){return(0,i.jsx)(g,{children:(0,i.jsx)(P,{})})}}}]);