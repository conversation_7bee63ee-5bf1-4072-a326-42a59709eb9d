exports.id=871,exports.ids=[871],exports.modules={440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(1658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},985:(a,b,c)=>{"use strict";c.d(b,{MultiplayerProvider:()=>j,K:()=>k});var d=c(687),e=c(3210),f=c(7405);class g{constructor(){this.socket=null,this.isConnected=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.currentRoom=null,this.currentPlayer=null}connect(){this.socket?.connected||(this.socket=(0,f.io)(process.env.NEXT_PUBLIC_SOCKET_URL||"http://localhost:3001",{transports:["websocket","polling"],timeout:2e4,forceNew:!0}),this.setupEventListeners())}setupEventListeners(){this.socket&&(this.socket.on("connect",()=>{console.log("Connected to multiplayer server"),this.isConnected=!0,this.reconnectAttempts=0}),this.socket.on("disconnect",a=>{console.log("Disconnected from multiplayer server:",a),this.isConnected=!1,"io server disconnect"===a&&this.handleReconnect()}),this.socket.on("connect_error",a=>{console.error("Connection error:",a),this.handleReconnect()}),this.socket.on("error",a=>{console.error("Socket error:",a)}))}handleReconnect(){this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`),setTimeout(()=>{this.connect()},1e3*Math.pow(2,this.reconnectAttempts))):console.error("Max reconnection attempts reached")}createRoom(a){return new Promise((b,c)=>{if(!this.socket?.connected)return void c(Error("Not connected to server"));this.socket.emit("create_room",a),this.socket.once("room_created",a=>{this.currentRoom=a,b(a)}),this.socket.once("error",a=>{c(Error(a.message))})})}joinRoom(a,b){return new Promise((c,d)=>{if(!this.socket?.connected)return void d(Error("Not connected to server"));this.socket.emit("join_room",a,b),this.socket.once("room_joined",(a,b)=>{this.currentRoom=a,this.currentPlayer=b,c({room:a,player:b})}),this.socket.once("error",a=>{d(Error(a.message))})})}leaveRoom(){this.socket?.connected&&this.currentRoom&&(this.socket.emit("leave_room",this.currentRoom.id),this.currentRoom=null,this.currentPlayer=null)}sendPlayerAction(a){this.socket?.connected&&this.currentPlayer&&this.socket.emit("player_action",{...a,playerId:this.currentPlayer.id,timestamp:Date.now()})}sendMessage(a){this.socket?.connected&&this.currentPlayer&&this.socket.emit("send_message",{playerId:this.currentPlayer.id,playerName:this.currentPlayer.name,content:a,timestamp:Date.now()})}on(a,b){this.socket?.on(a,b)}off(a,b){this.socket?.off(a,b)}once(a,b){this.socket?.once(a,b)}isSocketConnected(){return this.isConnected&&this.socket?.connected===!0}getCurrentRoom(){return this.currentRoom}getCurrentPlayer(){return this.currentPlayer}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null,this.isConnected=!1,this.currentRoom=null,this.currentPlayer=null)}}let h=new g,i=(0,e.createContext)(void 0);function j({children:a}){let[b,c]=(0,e.useState)(!1),[f,g]=(0,e.useState)(!1),[j,k]=(0,e.useState)(null),[l,m]=(0,e.useState)(null),[n,o]=(0,e.useState)(null),[p,q]=(0,e.useState)([]),[r,s]=(0,e.useState)("waiting"),[t,u]=(0,e.useState)(null),[v,w]=(0,e.useState)([]),x=(0,e.useCallback)(async(a,b)=>{try{k(null),await h.createRoom({...a,hostName:b.name,hostAvatar:b.avatar,hostLevel:b.level})}catch(a){throw k(a.message),a}},[]),y=(0,e.useCallback)(async(a,b)=>{try{k(null);let{room:c,player:d}=await h.joinRoom(a,b)}catch(a){throw k(a.message),a}},[]),z=(0,e.useCallback)(()=>{h.leaveRoom()},[]),A=(0,e.useCallback)(()=>{l&&n?.isHost&&h.sendPlayerAction({type:"start_game",data:{roomId:l.id}})},[l,n]),B=(0,e.useCallback)(a=>{h.sendMessage(a)},[]),C=(0,e.useCallback)(a=>{h.sendPlayerAction(a)},[]),D=(0,e.useCallback)(a=>{n&&C({type:"player_ready",data:{ready:a}})},[n,C]),E=(0,e.useCallback)(a=>{n?.isHost&&C({type:"kick_player",data:{playerId:a}})},[n,C]),F=(0,e.useCallback)(a=>{n?.isHost&&C({type:"update_room_settings",data:{settings:a}})},[n,C]);return(0,d.jsx)(i.Provider,{value:{isConnected:b,isInRoom:f,connectionError:j,currentRoom:l,currentPlayer:n,players:p,gameState:r,sharedGameState:t,messages:v,createRoom:x,joinRoom:y,leaveRoom:z,startGame:A,sendMessage:B,sendPlayerAction:C,setPlayerReady:D,kickPlayer:E,updateRoomSettings:F},children:a})}function k(){let a=(0,e.useContext)(i);return void 0===a?(console.warn("useMultiplayer called outside of MultiplayerProvider, using fallback"),{isConnected:!1,connectionStatus:"disconnected",currentRoom:null,gameState:null,createRoom:async()=>{},joinRoom:async()=>{},leaveRoom:()=>{},sendChatMessage:()=>{},updateGameState:()=>{},setPlayerReady:()=>{}}):a}},1135:()=>{},3630:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},4393:(a,b,c)=>{"use strict";c.d(b,{LanguageProvider:()=>h,o:()=>i});var d=c(687),e=c(3210);let f=(0,e.createContext)(void 0),g={en:{"game.title":"Bake It Out","game.subtitle":"Master the art of bakery management in this engaging multiplayer game. Complete orders, unlock recipes, automate your processes, and compete with friends!","game.play":"\uD83C\uDFAE Start Playing","game.multiplayer":"\uD83D\uDC65 Multiplayer","game.english":"\uD83C\uDDFA\uD83C\uDDF8 English","game.czech":"\uD83C\uDDE8\uD83C\uDDFF Čeština","game.home":"\uD83C\uDFE0 Home","game.close":"✕ Close","game.continue":"\uD83D\uDE80 Continue Playing","features.manage.title":"Manage Your Bakery","features.manage.description":"Take orders, bake delicious goods, and serve happy customers","features.levelup.title":"Level Up & Automate","features.levelup.description":"Unlock new recipes, buy equipment, and automate your processes","features.multiplayer.title":"Play Together","features.multiplayer.description":"Cooperative and competitive multiplayer modes with friends","status.development":"\uD83D\uDEA7 Game in Development - Phase 5: Multilayer Support! \uD83D\uDEA7","ui.level":"Level {{level}}","ui.money":"${{amount}}","ui.experience":"XP: {{current}}/{{max}}","ui.skillPoints":"SP: {{points}}","ui.achievements":"\uD83C\uDFC6 Achievements","ui.skills":"\uD83C\uDF1F Skills","ui.automation":"\uD83E\uDD16 Automation","kitchen.title":"\uD83C\uDFEA Kitchen","kitchen.clickToUse":"Click to use","kitchen.making":"Making: {{recipe}}","kitchen.timeRemaining":"Time: {{time}}","inventory.title":"\uD83D\uDCE6 Inventory","inventory.quantity":"Qty: {{qty}}","inventory.cost":"${{cost}} each","orders.title":"\uD83D\uDCCB Orders","orders.newOrder":"+ New Order","orders.accept":"Accept","orders.decline":"Decline","orders.complete":"Complete","orders.inProgress":"In Progress","orders.timeLimit":"Time: {{time}}","orders.reward":"${{amount}}","orders.customer":"Customer: {{name}}","actions.title":"⚡ Quick Actions","actions.buyIngredients":"\uD83D\uDED2 Buy Ingredients","actions.viewRecipes":"\uD83D\uDCD6 View Recipes","actions.equipmentShop":"\uD83D\uDD27 Equipment Shop","modal.recipes.title":"\uD83D\uDCD6 Recipe Book","modal.shop.title":"\uD83D\uDED2 Ingredient Shop","modal.baking.title":"\uD83D\uDD25 {{equipment}} - Select Recipe","modal.achievements.title":"\uD83C\uDFC6 Achievements","modal.skills.title":"\uD83C\uDF1F Skill Tree","modal.automation.title":"\uD83E\uDD16 Automation Control","modal.equipmentShop.title":"\uD83C\uDFEA Equipment Shop","modal.settings.title":"⚙️ Settings","modal.bakeries.title":"\uD83C\uDFEA Bakery Manager","modal.levelUp.title":"Level Up!","modal.levelUp.subtitle":"You reached Level {{level}}!","recipes.all":"All","recipes.cookies":"Cookies","recipes.cakes":"Cakes","recipes.bread":"Bread","recipes.pastries":"Pastries","recipes.ingredients":"Ingredients:","recipes.difficulty":"Difficulty:","recipes.time":"Time:","recipes.canCraft":"✅ Can Craft","recipes.unlockLevel":"Unlocked at Level {{level}}","recipes.noRecipes":"No recipes available in this category.","recipes.levelUpToUnlock":"Level up to unlock more recipes!","shop.currentStock":"Current stock: {{quantity}}","shop.buy":"Buy","shop.tooExpensive":"Too Expensive","shop.tips.title":"\uD83D\uDCA1 Shopping Tips","shop.tips.bulk":"• Buy ingredients in bulk to save time","shop.tips.stock":"• Keep an eye on your stock levels","shop.tips.rare":"• Some recipes require rare ingredients","shop.tips.prices":"• Prices may vary based on availability","baking.selectRecipe":"Select Recipe","baking.noRecipes":"No recipes available","baking.noIngredients":"You don't have enough ingredients to craft any recipes.","baking.buyIngredients":"Buy Ingredients","baking.startBaking":"\uD83D\uDD25 Start Baking","baking.instructions":"\uD83D\uDCCB Baking Instructions for {{recipe}}","baking.expectedReward":"Expected reward: ${{amount}}","baking.makesSure":"Make sure you have all ingredients before starting!","achievements.completed":"{{completed}} of {{total}} achievements completed","achievements.overallProgress":"Overall Progress","achievements.progress":"Progress","achievements.reward":"Reward:","achievements.noAchievements":"No achievements in this category.","skills.availablePoints":"Available Skill Points: {{points}}","skills.efficiency":"Efficiency","skills.automation":"Automation","skills.quality":"Quality","skills.business":"Business","skills.effects":"Effects:","skills.requires":"Requires: {{requirements}}","skills.requiresLevel":"Requires Level {{level}}","skills.maxed":"✅ Maxed","skills.upgrade":"⬆️ Upgrade ({{cost}} SP)","skills.locked":"\uD83D\uDD12 Locked","skills.noSkills":"No skills in this category.","skills.tips.title":"\uD83D\uDCA1 Skill Tips","skills.tips.earnPoints":"• Earn skill points by leveling up (1 point every 2 levels)","skills.tips.prerequisites":"• Some skills require other skills to be unlocked first","skills.tips.playstyle":"• Focus on skills that match your playstyle","skills.tips.efficiency":"• Efficiency skills help with resource management","automation.masterControl":"\uD83C\uDF9B️ Master Control","automation.enableAutomation":"Enable Automation","automation.autoStart":"Auto-start Equipment","automation.priorityMode":"\uD83C\uDFAF Priority Mode","automation.efficiency":"Efficiency (Orders First)","automation.profit":"Profit (Highest Value)","automation.speed":"Speed (Fastest Recipes)","automation.priorityDescription":"How automation chooses what to bake","automation.performance":"⚡ Performance","automation.maxJobs":"Max Concurrent Jobs: {{jobs}}","automation.safety":"\uD83D\uDEE1️ Safety","automation.stopWhenLow":"Stop when ingredients below: {{threshold}}","automation.upgrades":"\uD83D\uDCA1 Automation Upgrades","automation.upgradesDescription":"Improve your automation efficiency, speed, and intelligence with these upgrades.","automation.purchase":"Purchase","automation.noUpgrades":"No upgrades available at your current level.","automation.levelUpForUpgrades":"Level up to unlock more automation upgrades!","automation.automatedEquipment":"Automated Equipment","automation.activeUpgrades":"Active Upgrades","automation.automationStatus":"Automation Status","automation.equipmentStatus":"\uD83C\uDFED Equipment Status","automation.running":"Running","automation.idle":"Idle","automation.noAutomatedEquipment":"No automated equipment available.","automation.purchaseAutoEquipment":"Purchase auto-equipment from the shop to get started!","equipmentShop.upgradeYourBakery":"Upgrade your bakery with professional equipment","equipmentShop.basic":"Basic","equipmentShop.automated":"Automated","equipmentShop.advanced":"Advanced","equipmentShop.efficiency":"Efficiency: {{efficiency}}x","equipmentShop.automation":"Automation:","equipmentShop.unlockLevel":"Unlock Level: {{level}}","equipmentShop.purchase":"\uD83D\uDCB0 Purchase","equipmentShop.noEquipment":"No equipment available in this category.","equipmentShop.levelUpForEquipment":"Level up to unlock more equipment!","equipmentShop.tips.title":"\uD83D\uDCA1 Equipment Tips","equipmentShop.tips.automated":"• Automated equipment can run without your supervision","equipmentShop.tips.efficiency":"• Higher efficiency means faster production and better quality","equipmentShop.tips.conveyor":"• Conveyor belts connect equipment for seamless workflow","equipmentShop.tips.advanced":"• Advanced equipment unlocks at higher levels","levelUp.levelRewards":"\uD83C\uDF81 Level Rewards","levelUp.whatsNext":"\uD83D\uDCA1 What's Next?","levelUp.checkRecipes":"• Check out new recipes in your recipe book","levelUp.visitShop":"• Visit the shop for new equipment","levelUp.challengingOrders":"• Take on more challenging orders","levelUp.investSkills":"• Invest in skill upgrades","settings.title":"⚙️ Settings","settings.general":"General","settings.audio":"Audio","settings.graphics":"Graphics","settings.save":"Save & Data","settings.language":"\uD83C\uDF0D Language","settings.gameplay":"\uD83C\uDFAE Gameplay","settings.notifications":"Enable Notifications","settings.tutorials":"Show Tutorials","settings.animationSpeed":"Animation Speed","settings.sound":"Sound Effects","settings.music":"Background Music","settings.quality":"\uD83C\uDFA8 Graphics Quality","settings.autoSave":"\uD83D\uDCBE Auto-Save","settings.enableAutoSave":"Enable Auto-Save","settings.dataManagement":"\uD83D\uDCC1 Data Management","settings.exportSave":"\uD83D\uDCE4 Export Save","settings.importSave":"\uD83D\uDCE5 Import Save","settings.cloudSync":"☁️ Cloud Sync","settings.cloudSyncDescription":"Cloud sync allows you to save your progress online and play across multiple devices.","settings.comingSoon":"Coming Soon","bakeries.title":"\uD83C\uDFEA Bakery Manager","bakeries.subtitle":"Manage your bakery empire","bakeries.owned":"My Bakeries","bakeries.available":"Available","bakeries.current":"Current","bakeries.level":"Level","bakeries.specialization":"Specialization","bakeries.equipment":"Equipment","bakeries.orders":"Active Orders","bakeries.switchTo":"Switch To","bakeries.noOwned":"You don't own any bakeries yet.","bakeries.purchase":"\uD83D\uDCB0 Purchase","bakeries.tooExpensive":"\uD83D\uDCB8 Too Expensive","bakeries.allOwned":"You own all available bakeries!","bakeries.tips":"\uD83D\uDCA1 Bakery Tips","bakeries.tip1":"Each bakery specializes in different products for bonus efficiency","bakeries.tip2":"Switch between bakeries to manage multiple locations","bakeries.tip3":"Specialized bakeries attract customers looking for specific items","bakeries.tip4":"Upgrade each bakery independently for maximum profit","notifications.orderAccepted":"Order Accepted","notifications.orderAcceptedMessage":"You have accepted a new order!","notifications.orderCompleted":"Order Completed!","notifications.orderCompletedMessage":"You earned ${{reward}} and gained experience!","notifications.orderDeclined":"Order Declined","notifications.orderDeclinedMessage":"Order has been removed from your queue.","notifications.bakeryPurchased":"Bakery Purchased!","notifications.bakeryPurchasedMessage":"You now own {{name}}!","notifications.bakerySwitched":"Bakery Switched","notifications.bakerySwitchedMessage":"Switched to {{name}}","common.accept":"Accept","common.decline":"Decline","common.complete":"Complete","common.purchase":"Purchase","common.upgrade":"Upgrade","common.cancel":"Cancel","common.confirm":"Confirm","common.save":"Save","common.load":"Load","common.delete":"Delete","common.edit":"Edit","common.back":"Back","common.next":"Next","common.previous":"Previous","common.yes":"Yes","common.no":"No","common.create":"Create","common.join":"Join","common.leave":"Leave","common.start":"Start","common.ready":"Ready","common.notReady":"Not Ready","common.send":"Send","multiplayer.lobby":"\uD83D\uDC65 Multiplayer Lobby","multiplayer.connected":"\uD83D\uDFE2 Connected","multiplayer.disconnected":"\uD83D\uDD34 Disconnected","multiplayer.createRoom":"Create Room","multiplayer.joinRoom":"Join Room","multiplayer.room":"Room","multiplayer.yourName":"Your Name","multiplayer.enterName":"Enter your name","multiplayer.roomName":"Room Name","multiplayer.enterRoomName":"Enter room name","multiplayer.gameMode":"Game Mode","multiplayer.cooperative":"\uD83E\uDD1D Cooperative","multiplayer.competitive":"⚔️ Competitive","multiplayer.maxPlayers":"Max Players: {{count}}","multiplayer.roomId":"Room ID","multiplayer.enterRoomId":"Enter room ID","multiplayer.players":"Players ({{count}})","multiplayer.host":"HOST","multiplayer.level":"Level {{level}}","multiplayer.chat":"Chat","multiplayer.typeMessage":"Type a message...","multiplayer.gameTime":"Game Time: {{time}}","multiplayer.teamStats":"\uD83D\uDCCA Team Stats","multiplayer.ordersCompleted":"Orders Completed:","multiplayer.totalRevenue":"Total Revenue:","multiplayer.teamExperience":"Team Experience:","multiplayer.sharedKitchen":"\uD83C\uDFEA Shared Kitchen","multiplayer.sharedOrders":"\uD83D\uDCCB Shared Orders","multiplayer.sharedInventory":"\uD83D\uDCE6 Shared Inventory","multiplayer.contribution":"Contribution:","multiplayer.online":"\uD83D\uDFE2 Online","multiplayer.status":"Status:","multiplayer.you":"(You)","multiplayer.teamChat":"\uD83D\uDCAC Team Chat","multiplayer.chatPlaceholder":"Chat messages will appear here...","multiplayer.mode.cooperative.description":"\uD83E\uDD1D Cooperative Mode: Work together to complete orders and grow your shared bakery!","multiplayer.mode.competitive.description":"⚔️ Competitive Mode: Compete against other players to complete the most orders!","multiplayer.game.title":"\uD83C\uDFAE Multiplayer Game - {{roomName}}","multiplayer.game.mode":"Mode: {{mode}}","multiplayer.game.playersCount":"Players: {{count}}","multiplayer.game.playing":"\uD83D\uDFE2 Playing","multiplayer.game.leaveGame":"\uD83D\uDEAA Leave Game","multiplayer.game.tabs.game":"Game","multiplayer.game.tabs.players":"Players","multiplayer.game.tabs.chat":"Chat","multiplayer.create.title":"\uD83C\uDFD7️ Create Room","multiplayer.join.title":"\uD83D\uDEAA Join Room","multiplayer.room.info":"Mode: {{mode}} • Players: {{current}}/{{max}}","multiplayer.room.readyUp":"✅ Ready","multiplayer.room.notReady":"⏳ Not Ready","multiplayer.room.startGame":"\uD83D\uDE80 Start Game","multiplayer.room.leaveRoom":"\uD83D\uDEAA Leave","multiplayer.connection.connecting":"Connecting...","multiplayer.connection.reconnecting":"Reconnecting...","multiplayer.connection.failed":"Connection failed","multiplayer.connection.error":"⚠️ {{error}}","multiplayer.system.playerJoined":"{{name}} joined the room","multiplayer.system.playerLeft":"{{name}} left the room","multiplayer.system.gameStarted":"Game started!","multiplayer.system.gameEnded":"Game ended!","multiplayer.system.roomCreated":"Room created successfully","multiplayer.system.roomJoined":"Joined room successfully"},cs:{"game.title":"Bake It Out","game.subtitle":"Ovl\xe1dněte uměn\xed ř\xedzen\xed pek\xe1rny v t\xe9to poutav\xe9 multiplayerov\xe9 hře. Plňte objedn\xe1vky, odemykejte recepty, automatizujte procesy a soutěžte s př\xe1teli!","game.play":"\uD83C\uDFAE Zač\xedt hr\xe1t","game.multiplayer":"\uD83D\uDC65 Multiplayer","game.english":"\uD83C\uDDFA\uD83C\uDDF8 English","game.czech":"\uD83C\uDDE8\uD83C\uDDFF Čeština","game.home":"\uD83C\uDFE0 Domů","game.close":"✕ Zavř\xedt","game.continue":"\uD83D\uDE80 Pokračovat ve hře","features.manage.title":"Spravujte svou pek\xe1rnu","features.manage.description":"Přij\xedmejte objedn\xe1vky, pečte lahodn\xe9 v\xfdrobky a obsluhujte spokojen\xe9 z\xe1kazn\xedky","features.levelup.title":"Postupujte a automatizujte","features.levelup.description":"Odemykejte nov\xe9 recepty, kupujte vybaven\xed a automatizujte sv\xe9 procesy","features.multiplayer.title":"Hrajte společně","features.multiplayer.description":"Kooperativn\xed a soutěžn\xed multiplayerov\xe9 režimy s př\xe1teli","status.development":"\uD83D\uDEA7 Hra ve v\xfdvoji - F\xe1ze 5: V\xedcevrstv\xe1 podpora! \uD83D\uDEA7","ui.level":"\xdaroveň {{level}}","ui.money":"{{amount}} Kč","ui.experience":"XP: {{current}}/{{max}}","ui.skillPoints":"SP: {{points}}","ui.achievements":"\uD83C\uDFC6 \xdaspěchy","ui.skills":"\uD83C\uDF1F Dovednosti","ui.automation":"\uD83E\uDD16 Automatizace","kitchen.title":"\uD83C\uDFEA Kuchyně","kitchen.clickToUse":"Klikněte pro použit\xed","kitchen.making":"Připravuje: {{recipe}}","kitchen.timeRemaining":"Čas: {{time}}","inventory.title":"\uD83D\uDCE6 Sklad","inventory.quantity":"Množstv\xed: {{qty}}","inventory.cost":"{{cost}} Kč za kus","orders.title":"\uD83D\uDCCB Objedn\xe1vky","orders.newOrder":"+ Nov\xe1 objedn\xe1vka","orders.accept":"Přijmout","orders.decline":"Odm\xedtnout","orders.complete":"Dokončit","orders.inProgress":"Prob\xedh\xe1","orders.timeLimit":"Čas: {{time}}","orders.reward":"{{amount}} Kč","orders.customer":"Z\xe1kazn\xedk: {{name}}","actions.title":"⚡ Rychl\xe9 akce","actions.buyIngredients":"\uD83D\uDED2 Koupit suroviny","actions.viewRecipes":"\uD83D\uDCD6 Zobrazit recepty","actions.equipmentShop":"\uD83D\uDD27 Obchod s vybaven\xedm","modal.recipes.title":"\uD83D\uDCD6 Kniha receptů","modal.shop.title":"\uD83D\uDED2 Obchod se surovinami","modal.baking.title":"\uD83D\uDD25 {{equipment}} - Vyberte recept","modal.achievements.title":"\uD83C\uDFC6 \xdaspěchy","modal.skills.title":"\uD83C\uDF1F Strom dovednost\xed","modal.automation.title":"\uD83E\uDD16 Ovl\xe1d\xe1n\xed automatizace","modal.equipmentShop.title":"\uD83C\uDFEA Obchod s vybaven\xedm","modal.settings.title":"⚙️ Nastaven\xed","modal.bakeries.title":"\uD83C\uDFEA Spr\xe1vce pek\xe1ren","modal.levelUp.title":"Postup na vyšš\xed \xfaroveň!","modal.levelUp.subtitle":"Dos\xe1hli jste \xfarovně {{level}}!","recipes.all":"Vše","recipes.cookies":"Sušenky","recipes.cakes":"Dorty","recipes.bread":"Chl\xe9b","recipes.pastries":"Pečivo","recipes.ingredients":"Suroviny:","recipes.difficulty":"Obt\xedžnost:","recipes.time":"Čas:","recipes.canCraft":"✅ Lze vyrobit","recipes.unlockLevel":"Odemčeno na \xfarovni {{level}}","recipes.noRecipes":"V t\xe9to kategorii nejsou k dispozici ž\xe1dn\xe9 recepty.","recipes.levelUpToUnlock":"Postupte na vyšš\xed \xfaroveň pro odemčen\xed dalš\xedch receptů!","shop.currentStock":"Aktu\xe1ln\xed z\xe1soba: {{quantity}}","shop.buy":"Koupit","shop.tooExpensive":"Př\xedliš drah\xe9","shop.tips.title":"\uD83D\uDCA1 Tipy pro nakupov\xe1n\xed","shop.tips.bulk":"• Kupujte suroviny ve velk\xe9m množstv\xed pro \xfasporu času","shop.tips.stock":"• Sledujte \xfaroveň sv\xfdch z\xe1sob","shop.tips.rare":"• Někter\xe9 recepty vyžaduj\xed vz\xe1cn\xe9 suroviny","shop.tips.prices":"• Ceny se mohou lišit podle dostupnosti","baking.selectRecipe":"Vyberte recept","baking.noRecipes":"Ž\xe1dn\xe9 recepty k dispozici","baking.noIngredients":"Nem\xe1te dostatek surovin pro v\xfdrobu jak\xe9hokoli receptu.","baking.buyIngredients":"Koupit suroviny","baking.startBaking":"\uD83D\uDD25 Zač\xedt p\xe9ct","baking.instructions":"\uD83D\uDCCB Pokyny pro pečen\xed {{recipe}}","baking.expectedReward":"Oček\xe1van\xe1 odměna: {{amount}} Kč","baking.makesSure":"Ujistěte se, že m\xe1te všechny suroviny před zač\xe1tkem!","achievements.completed":"{{completed}} z {{total}} \xfaspěchů dokončeno","achievements.overallProgress":"Celkov\xfd pokrok","achievements.progress":"Pokrok","achievements.reward":"Odměna:","achievements.noAchievements":"V t\xe9to kategorii nejsou ž\xe1dn\xe9 \xfaspěchy.","skills.availablePoints":"Dostupn\xe9 body dovednost\xed: {{points}}","skills.efficiency":"Efektivita","skills.automation":"Automatizace","skills.quality":"Kvalita","skills.business":"Podnik\xe1n\xed","skills.effects":"Efekty:","skills.requires":"Vyžaduje: {{requirements}}","skills.requiresLevel":"Vyžaduje \xfaroveň {{level}}","skills.maxed":"✅ Maxim\xe1ln\xed","skills.upgrade":"⬆️ Vylepšit ({{cost}} SP)","skills.locked":"\uD83D\uDD12 Uzamčeno","skills.noSkills":"V t\xe9to kategorii nejsou ž\xe1dn\xe9 dovednosti.","skills.tips.title":"\uD83D\uDCA1 Tipy pro dovednosti","skills.tips.earnPoints":"• Z\xedsk\xe1vejte body dovednost\xed postupem na vyšš\xed \xfaroveň (1 bod každ\xe9 2 \xfarovně)","skills.tips.prerequisites":"• Někter\xe9 dovednosti vyžaduj\xed nejprve odemčen\xed jin\xfdch dovednost\xed","skills.tips.playstyle":"• Zaměřte se na dovednosti, kter\xe9 odpov\xeddaj\xed vašemu stylu hry","skills.tips.efficiency":"• Dovednosti efektivity pom\xe1haj\xed se spr\xe1vou zdrojů","automation.masterControl":"\uD83C\uDF9B️ Hlavn\xed ovl\xe1d\xe1n\xed","automation.enableAutomation":"Povolit automatizaci","automation.autoStart":"Automatick\xe9 spuštěn\xed vybaven\xed","automation.priorityMode":"\uD83C\uDFAF Režim priority","automation.efficiency":"Efektivita (objedn\xe1vky prvn\xed)","automation.profit":"Zisk (nejvyšš\xed hodnota)","automation.speed":"Rychlost (nejrychlejš\xed recepty)","automation.priorityDescription":"Jak automatizace vyb\xedr\xe1, co p\xe9ct","automation.performance":"⚡ V\xfdkon","automation.maxJobs":"Max současn\xfdch \xfaloh: {{jobs}}","automation.safety":"\uD83D\uDEE1️ Bezpečnost","automation.stopWhenLow":"Zastavit, když suroviny klesnou pod: {{threshold}}","automation.upgrades":"\uD83D\uDCA1 Vylepšen\xed automatizace","automation.upgradesDescription":"Vylepšete efektivitu, rychlost a inteligenci vaš\xed automatizace.","automation.purchase":"Koupit","automation.noUpgrades":"Na vaš\xed současn\xe9 \xfarovni nejsou k dispozici ž\xe1dn\xe1 vylepšen\xed.","automation.levelUpForUpgrades":"Postupte na vyšš\xed \xfaroveň pro odemčen\xed dalš\xedch vylepšen\xed automatizace!","automation.automatedEquipment":"Automatizovan\xe9 vybaven\xed","automation.activeUpgrades":"Aktivn\xed vylepšen\xed","automation.automationStatus":"Stav automatizace","automation.equipmentStatus":"\uD83C\uDFED Stav vybaven\xed","automation.running":"Běž\xed","automation.idle":"Nečinn\xe9","automation.noAutomatedEquipment":"Ž\xe1dn\xe9 automatizovan\xe9 vybaven\xed k dispozici.","automation.purchaseAutoEquipment":"Kupte si auto-vybaven\xed z obchodu pro zač\xe1tek!","equipmentShop.upgradeYourBakery":"Vylepšete svou pek\xe1rnu profesion\xe1ln\xedm vybaven\xedm","equipmentShop.basic":"Z\xe1kladn\xed","equipmentShop.automated":"Automatizovan\xe9","equipmentShop.advanced":"Pokročil\xe9","equipmentShop.efficiency":"Efektivita: {{efficiency}}x","equipmentShop.automation":"Automatizace:","equipmentShop.unlockLevel":"\xdaroveň odemčen\xed: {{level}}","equipmentShop.purchase":"\uD83D\uDCB0 Koupit","equipmentShop.noEquipment":"V t\xe9to kategorii nen\xed k dispozici ž\xe1dn\xe9 vybaven\xed.","equipmentShop.levelUpForEquipment":"Postupte na vyšš\xed \xfaroveň pro odemčen\xed dalš\xedho vybaven\xed!","equipmentShop.tips.title":"\uD83D\uDCA1 Tipy pro vybaven\xed","equipmentShop.tips.automated":"• Automatizovan\xe9 vybaven\xed může běžet bez vašeho dohledu","equipmentShop.tips.efficiency":"• Vyšš\xed efektivita znamen\xe1 rychlejš\xed v\xfdrobu a lepš\xed kvalitu","equipmentShop.tips.conveyor":"• Dopravn\xed p\xe1sy spojuj\xed vybaven\xed pro bezprobl\xe9mov\xfd pracovn\xed tok","equipmentShop.tips.advanced":"• Pokročil\xe9 vybaven\xed se odemyk\xe1 na vyšš\xedch \xfarovn\xedch","levelUp.levelRewards":"\uD83C\uDF81 Odměny za \xfaroveň","levelUp.whatsNext":"\uD83D\uDCA1 Co d\xe1l?","levelUp.checkRecipes":"• Pod\xedvejte se na nov\xe9 recepty ve sv\xe9 knize receptů","levelUp.visitShop":"• Navštivte obchod pro nov\xe9 vybaven\xed","levelUp.challengingOrders":"• Přijměte n\xe1ročnějš\xed objedn\xe1vky","levelUp.investSkills":"• Investujte do vylepšen\xed dovednost\xed","settings.title":"⚙️ Nastaven\xed","settings.general":"Obecn\xe9","settings.audio":"Zvuk","settings.graphics":"Grafika","settings.save":"Uložen\xed a data","settings.language":"\uD83C\uDF0D Jazyk","settings.gameplay":"\uD83C\uDFAE Hratelnost","settings.notifications":"Povolit ozn\xe1men\xed","settings.tutorials":"Zobrazit n\xe1vody","settings.animationSpeed":"Rychlost animace","settings.sound":"Zvukov\xe9 efekty","settings.music":"Hudba na pozad\xed","settings.quality":"\uD83C\uDFA8 Kvalita grafiky","settings.autoSave":"\uD83D\uDCBE Automatick\xe9 ukl\xe1d\xe1n\xed","settings.enableAutoSave":"Povolit automatick\xe9 ukl\xe1d\xe1n\xed","settings.dataManagement":"\uD83D\uDCC1 Spr\xe1va dat","settings.exportSave":"\uD83D\uDCE4 Exportovat uložen\xed","settings.importSave":"\uD83D\uDCE5 Importovat uložen\xed","settings.cloudSync":"☁️ Cloudov\xe1 synchronizace","settings.cloudSyncDescription":"Cloudov\xe1 synchronizace v\xe1m umožňuje uložit pokrok online a hr\xe1t na v\xedce zař\xedzen\xedch.","settings.comingSoon":"Již brzy","bakeries.title":"\uD83C\uDFEA Spr\xe1vce pek\xe1ren","bakeries.subtitle":"Spravujte sv\xe9 pek\xe1rensk\xe9 imp\xe9rium","bakeries.owned":"Moje pek\xe1rny","bakeries.available":"Dostupn\xe9","bakeries.current":"Aktu\xe1ln\xed","bakeries.level":"\xdaroveň","bakeries.specialization":"Specializace","bakeries.equipment":"Vybaven\xed","bakeries.orders":"Aktivn\xed objedn\xe1vky","bakeries.switchTo":"Přepnout na","bakeries.noOwned":"Ještě nevlastn\xedte ž\xe1dn\xe9 pek\xe1rny.","bakeries.purchase":"\uD83D\uDCB0 Koupit","bakeries.tooExpensive":"\uD83D\uDCB8 Př\xedliš drah\xe9","bakeries.allOwned":"Vlastn\xedte všechny dostupn\xe9 pek\xe1rny!","bakeries.tips":"\uD83D\uDCA1 Tipy pro pek\xe1rny","bakeries.tip1":"Každ\xe1 pek\xe1rna se specializuje na různ\xe9 produkty pro bonusovou efektivitu","bakeries.tip2":"Přep\xednejte mezi pek\xe1rnami pro spr\xe1vu v\xedce lokalit","bakeries.tip3":"Specializovan\xe9 pek\xe1rny přitahuj\xed z\xe1kazn\xedky hledaj\xedc\xed konkr\xe9tn\xed položky","bakeries.tip4":"Vylepšujte každou pek\xe1rnu nez\xe1visle pro maxim\xe1ln\xed zisk","notifications.orderAccepted":"Objedn\xe1vka přijata","notifications.orderAcceptedMessage":"Přijali jste novou objedn\xe1vku!","notifications.orderCompleted":"Objedn\xe1vka dokončena!","notifications.orderCompletedMessage":"Z\xedskali jste {{reward}} Kč a zkušenosti!","notifications.orderDeclined":"Objedn\xe1vka odm\xedtnuta","notifications.orderDeclinedMessage":"Objedn\xe1vka byla odstraněna z vaš\xed fronty.","notifications.bakeryPurchased":"Pek\xe1rna zakoupena!","notifications.bakeryPurchasedMessage":"Nyn\xed vlastn\xedte {{name}}!","notifications.bakerySwitched":"Pek\xe1rna přepnuta","notifications.bakerySwitchedMessage":"Přepnuto na {{name}}","common.accept":"Přijmout","common.decline":"Odm\xedtnout","common.complete":"Dokončit","common.purchase":"Koupit","common.upgrade":"Vylepšit","common.cancel":"Zrušit","common.confirm":"Potvrdit","common.save":"Uložit","common.load":"Nač\xedst","common.delete":"Smazat","common.edit":"Upravit","common.back":"Zpět","common.next":"Dalš\xed","common.previous":"Předchoz\xed","common.yes":"Ano","common.no":"Ne","common.create":"Vytvořit","common.join":"Připojit se","common.leave":"Odej\xedt","common.start":"Zač\xedt","common.ready":"Připraven","common.notReady":"Nepřipraven","common.send":"Odeslat","multiplayer.lobby":"\uD83D\uDC65 Multiplayerov\xe1 lobby","multiplayer.connected":"\uD83D\uDFE2 Připojeno","multiplayer.disconnected":"\uD83D\uDD34 Odpojeno","multiplayer.createRoom":"Vytvořit m\xedstnost","multiplayer.joinRoom":"Připojit se k m\xedstnosti","multiplayer.room":"M\xedstnost","multiplayer.yourName":"Vaše jm\xe9no","multiplayer.enterName":"Zadejte sv\xe9 jm\xe9no","multiplayer.roomName":"N\xe1zev m\xedstnosti","multiplayer.enterRoomName":"Zadejte n\xe1zev m\xedstnosti","multiplayer.gameMode":"Hern\xed režim","multiplayer.cooperative":"\uD83E\uDD1D Kooperativn\xed","multiplayer.competitive":"⚔️ Soutěžn\xed","multiplayer.maxPlayers":"Max hr\xe1čů: {{count}}","multiplayer.roomId":"ID m\xedstnosti","multiplayer.enterRoomId":"Zadejte ID m\xedstnosti","multiplayer.players":"Hr\xe1či ({{count}})","multiplayer.host":"HOSTITEL","multiplayer.level":"\xdaroveň {{level}}","multiplayer.chat":"Chat","multiplayer.typeMessage":"Napište zpr\xe1vu...","multiplayer.gameTime":"Hern\xed čas: {{time}}","multiplayer.teamStats":"\uD83D\uDCCA T\xfdmov\xe9 statistiky","multiplayer.ordersCompleted":"Dokončen\xe9 objedn\xe1vky:","multiplayer.totalRevenue":"Celkov\xfd př\xedjem:","multiplayer.teamExperience":"T\xfdmov\xe9 zkušenosti:","multiplayer.sharedKitchen":"\uD83C\uDFEA Sd\xedlen\xe1 kuchyně","multiplayer.sharedOrders":"\uD83D\uDCCB Sd\xedlen\xe9 objedn\xe1vky","multiplayer.sharedInventory":"\uD83D\uDCE6 Sd\xedlen\xfd sklad","multiplayer.contribution":"Př\xedspěvek:","multiplayer.online":"\uD83D\uDFE2 Online","multiplayer.status":"Stav:","multiplayer.you":"(Vy)","multiplayer.teamChat":"\uD83D\uDCAC T\xfdmov\xfd chat","multiplayer.chatPlaceholder":"Zde se zobraz\xed zpr\xe1vy chatu...","multiplayer.mode.cooperative.description":"\uD83E\uDD1D Kooperativn\xed režim: Spolupracujte na dokončov\xe1n\xed objedn\xe1vek a rozvoji sd\xedlen\xe9 pek\xe1rny!","multiplayer.mode.competitive.description":"⚔️ Soutěžn\xed režim: Soutěžte s ostatn\xedmi hr\xe1či o dokončen\xed nejv\xedce objedn\xe1vek!","multiplayer.game.title":"\uD83C\uDFAE Multiplayerov\xe1 hra - {{roomName}}","multiplayer.game.mode":"Režim: {{mode}}","multiplayer.game.playersCount":"Hr\xe1či: {{count}}","multiplayer.game.playing":"\uD83D\uDFE2 Hraje se","multiplayer.game.leaveGame":"\uD83D\uDEAA Opustit hru","multiplayer.game.tabs.game":"Hra","multiplayer.game.tabs.players":"Hr\xe1či","multiplayer.game.tabs.chat":"Chat","multiplayer.create.title":"\uD83C\uDFD7️ Vytvořit m\xedstnost","multiplayer.join.title":"\uD83D\uDEAA Připojit se k m\xedstnosti","multiplayer.room.info":"Režim: {{mode}} • Hr\xe1či: {{current}}/{{max}}","multiplayer.room.readyUp":"✅ Připraven","multiplayer.room.notReady":"⏳ Nepřipraven","multiplayer.room.startGame":"\uD83D\uDE80 Zač\xedt hru","multiplayer.room.leaveRoom":"\uD83D\uDEAA Opustit","multiplayer.connection.connecting":"Připojov\xe1n\xed...","multiplayer.connection.reconnecting":"Znovu se připojuje...","multiplayer.connection.failed":"Připojen\xed selhalo","multiplayer.connection.error":"⚠️ {{error}}","multiplayer.system.playerJoined":"{{name}} se připojil do m\xedstnosti","multiplayer.system.playerLeft":"{{name}} opustil m\xedstnost","multiplayer.system.gameStarted":"Hra začala!","multiplayer.system.gameEnded":"Hra skončila!","multiplayer.system.roomCreated":"M\xedstnost byla \xfaspěšně vytvořena","multiplayer.system.roomJoined":"\xdaspěšně jste se připojili do m\xedstnosti"}};function h({children:a}){let[b,c]=(0,e.useState)("en");return(0,d.jsx)(f.Provider,{value:{language:b,setLanguage:a=>{c(a),localStorage.setItem("language",a)},t:(a,c)=>{let d=g[b][a]||a;return c&&Object.entries(c).forEach(([a,b])=>{d=d.replace(`{{${a}}}`,b)}),d}},children:a})}function i(){let a=(0,e.useContext)(f);return void 0===a?(console.warn("useLanguage called outside of LanguageProvider, using fallback"),{language:"en",setLanguage:()=>{},t:a=>a}):a}},4431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>l,metadata:()=>k});var d=c(7413),e=c(2376),f=c.n(e),g=c(8726),h=c.n(g);c(1135);var i=c(7043),j=c(6999);let k={title:"Bake It Out - Bakery Management Game",description:"Master the art of bakery management in this engaging multiplayer game with Czech and English support"};function l({children:a}){return(0,d.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,suppressHydrationWarning:!0,children:(0,d.jsx)(i.LanguageProvider,{children:(0,d.jsx)(j.MultiplayerProvider,{children:a})})})})}},6999:(a,b,c)=>{"use strict";c.d(b,{MultiplayerProvider:()=>e});var d=c(1369);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call MultiplayerProvider() from the server but MultiplayerProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\contexts\\MultiplayerContext.tsx","MultiplayerProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call useMultiplayer() from the server but useMultiplayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\contexts\\MultiplayerContext.tsx","useMultiplayer")},7043:(a,b,c)=>{"use strict";c.d(b,{LanguageProvider:()=>e});var d=c(1369);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call LanguageProvider() from the server but LanguageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\contexts\\LanguageContext.tsx","LanguageProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call useLanguage() from the server but useLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\contexts\\LanguageContext.tsx","useLanguage")},7472:(a,b,c)=>{Promise.resolve().then(c.bind(c,7043)),Promise.resolve().then(c.bind(c,6999))},7990:()=>{},9264:(a,b,c)=>{Promise.resolve().then(c.bind(c,4393)),Promise.resolve().then(c.bind(c,985))},9727:()=>{},9974:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,8243,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))}};