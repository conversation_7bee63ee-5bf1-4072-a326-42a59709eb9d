'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { SkillTree } from '@/lib/progressionSystem'

interface SkillTreeModalProps {
  isOpen: boolean
  onClose: () => void
  skills: SkillTree[]
  skillPoints: number
  playerLevel: number
  onUpgradeSkill: (skillId: string) => void
}

export function SkillTreeModal({ 
  isOpen, 
  onClose, 
  skills, 
  skillPoints, 
  playerLevel,
  onUpgradeSkill 
}: SkillTreeModalProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  if (!isOpen) return null

  const categories = [
    { id: 'all', name: 'All', icon: '🌟' },
    { id: 'efficiency', name: 'Efficiency', icon: '⚡' },
    { id: 'automation', name: 'Automation', icon: '🤖' },
    { id: 'quality', name: 'Quality', icon: '💎' },
    { id: 'business', name: 'Business', icon: '💼' }
  ]

  const filteredSkills = selectedCategory === 'all' 
    ? skills 
    : skills.filter(skill => skill.category === selectedCategory)

  const canUpgradeSkill = (skill: SkillTree) => {
    if (skill.level >= skill.maxLevel) return false
    if (skillPoints < skill.cost) return false
    if (skill.requirements.playerLevel && playerLevel < skill.requirements.playerLevel) return false
    if (skill.requirements.skills) {
      return skill.requirements.skills.every(requiredSkillId => {
        const requiredSkill = skills.find(s => s.id === requiredSkillId)
        return requiredSkill && requiredSkill.level > 0
      })
    }
    return true
  }

  const getSkillStatus = (skill: SkillTree) => {
    if (skill.level >= skill.maxLevel) return 'maxed'
    if (!canUpgradeSkill(skill)) return 'locked'
    return 'available'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'maxed': return 'border-green-400 bg-green-50'
      case 'available': return 'border-blue-400 bg-blue-50'
      case 'locked': return 'border-gray-300 bg-gray-50'
      default: return 'border-gray-300 bg-gray-50'
    }
  }

  const getEffectDescription = (effect: any) => {
    const percentage = Math.round(effect.value * 100)
    switch (effect.type) {
      case 'baking_speed': return `+${percentage}% baking speed`
      case 'money_multiplier': return `+${percentage}% money earned`
      case 'xp_multiplier': return `+${percentage}% experience gained`
      case 'ingredient_efficiency': return `${percentage}% less ingredients used`
      case 'automation_unlock': return 'Unlock automation features'
      default: return `+${percentage}% bonus`
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold text-orange-800">🌟 Skill Tree</h2>
              <p className="text-gray-600">
                Available Skill Points: <span className="font-semibold text-blue-600">{skillPoints}</span>
              </p>
            </div>
            <Button variant="secondary" onClick={onClose}>
              ✕ Close
            </Button>
          </div>
        </div>

        <div className="p-6">
          {/* Category Filter */}
          <div className="flex flex-wrap gap-2 mb-6">
            {categories.map(category => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? 'primary' : 'secondary'}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
              >
                {category.icon} {category.name}
              </Button>
            ))}
          </div>

          {/* Skills Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto">
            {filteredSkills.map(skill => {
              const status = getSkillStatus(skill)
              const canUpgrade = canUpgradeSkill(skill)

              return (
                <div
                  key={skill.id}
                  className={`p-4 rounded-lg border-2 ${getStatusColor(status)}`}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl">{skill.icon}</span>
                      <div>
                        <h3 className="font-semibold text-gray-800">{skill.name}</h3>
                        <p className="text-xs text-gray-500 uppercase tracking-wide">
                          {skill.category}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-700">
                        Level {skill.level}/{skill.maxLevel}
                      </div>
                      {status !== 'maxed' && (
                        <div className="text-xs text-blue-600">
                          Cost: {skill.cost} SP
                        </div>
                      )}
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 mb-3">
                    {skill.description}
                  </p>

                  {/* Effects */}
                  <div className="mb-3">
                    <h4 className="text-xs font-medium text-gray-700 mb-1">Effects:</h4>
                    {skill.effects.map((effect, index) => (
                      <div key={index} className="text-xs text-green-600">
                        • {getEffectDescription(effect)}
                      </div>
                    ))}
                  </div>

                  {/* Requirements */}
                  {skill.requirements.playerLevel && playerLevel < skill.requirements.playerLevel && (
                    <div className="mb-3">
                      <div className="text-xs text-red-600">
                        Requires Level {skill.requirements.playerLevel}
                      </div>
                    </div>
                  )}

                  {skill.requirements.skills && (
                    <div className="mb-3">
                      <div className="text-xs text-gray-600">
                        Requires: {skill.requirements.skills.map(skillId => {
                          const reqSkill = skills.find(s => s.id === skillId)
                          return reqSkill?.name
                        }).join(', ')}
                      </div>
                    </div>
                  )}

                  {/* Progress Bar */}
                  <div className="mb-3">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(skill.level / skill.maxLevel) * 100}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Upgrade Button */}
                  {status === 'maxed' ? (
                    <Button variant="success" size="sm" className="w-full" disabled>
                      ✅ Maxed
                    </Button>
                  ) : canUpgrade ? (
                    <Button 
                      variant="primary" 
                      size="sm" 
                      className="w-full"
                      onClick={() => onUpgradeSkill(skill.id)}
                    >
                      ⬆️ Upgrade ({skill.cost} SP)
                    </Button>
                  ) : (
                    <Button variant="secondary" size="sm" className="w-full" disabled>
                      🔒 Locked
                    </Button>
                  )}
                </div>
              )
            })}
          </div>

          {filteredSkills.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">🌟</div>
              <p>No skills in this category.</p>
            </div>
          )}

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-medium text-blue-800 mb-2">💡 Skill Tips</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Earn skill points by leveling up (1 point every 2 levels)</li>
              <li>• Some skills require other skills to be unlocked first</li>
              <li>• Focus on skills that match your playstyle</li>
              <li>• Efficiency skills help with resource management</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
