{"version": 3, "file": "NsisUpdater.js", "sourceRoot": "", "sources": ["../src/NsisUpdater.ts"], "names": [], "mappings": ";;;AAAA,+DAAmJ;AACnJ,6BAA4B;AAG5B,+CAA2D;AAE3D,4IAAwI;AACxI,mCAA2C;AAE3C,mDAAyD;AACzD,uCAAiC;AACjC,qGAA0E;AAC1E,6BAAyB;AAEzB,MAAa,WAAY,SAAQ,yBAAW;IAO1C,YAAY,OAAkC,EAAE,GAAgB;QAC9D,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;QAGX,+BAA0B,GAA8B,CAAC,cAA6B,EAAE,uBAA+B,EAAE,EAAE,CACnI,IAAA,wDAAe,EAAC,cAAc,EAAE,uBAAuB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;IAHxE,CAAC;IAKD;;;OAGG;IACH,IAAI,yBAAyB;QAC3B,OAAO,IAAI,CAAC,0BAA0B,CAAA;IACxC,CAAC;IAED,IAAI,yBAAyB,CAAC,KAAgC;QAC5D,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAA;QACzC,CAAC;IACH,CAAC;IAED,gBAAgB;IACN,gBAAgB,CAAC,qBAA4C;QACrE,MAAM,QAAQ,GAAG,qBAAqB,CAAC,qBAAqB,CAAC,QAAQ,CAAA;QACrE,MAAM,QAAQ,GAAG,IAAA,mBAAQ,EAAC,QAAQ,CAAC,YAAY,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,KAAK,CAAE,CAAA;QAC1G,OAAO,IAAI,CAAC,eAAe,CAAC;YAC1B,aAAa,EAAE,KAAK;YACpB,qBAAqB;YACrB,QAAQ;YACR,IAAI,EAAE,KAAK,EAAE,eAAe,EAAE,eAAe,EAAE,WAAW,EAAE,kBAAkB,EAAE,EAAE;gBAChF,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAA;gBACxC,MAAM,cAAc,GAAG,WAAW,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI,CAAA;gBACjE,IAAI,cAAc,IAAI,qBAAqB,CAAC,mBAAmB,EAAE,CAAC;oBAChE,MAAM,IAAA,+BAAQ,EACZ,kCAAkC,qBAAqB,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,+BAA+B,EACzH,oCAAoC,CACrC,CAAA;gBACH,CAAC;gBACD,IAAI,CAAC,cAAc,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,EAAE,CAAC;oBAClE,IAAI,CAAC,OAAO,CAAC,IAAI,CACf,4JAA4J,CAC7J,CAAA;gBACH,CAAC;gBACD,IACE,cAAc;oBACd,qBAAqB,CAAC,2BAA2B;oBACjD,CAAC,MAAM,IAAI,CAAC,6BAA6B,CAAC,QAAQ,EAAE,qBAAqB,EAAE,eAAe,EAAE,QAAQ,EAAE,sDAA+B,CAAC,CAAC,EACvI,CAAC;oBACD,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,eAAe,EAAE,eAAe,CAAC,CAAA;gBAClF,CAAC;gBAED,MAAM,2BAA2B,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAA;gBAC/E,IAAI,2BAA2B,IAAI,IAAI,EAAE,CAAC;oBACxC,MAAM,kBAAkB,EAAE,CAAA;oBAC1B,yCAAyC;oBACzC,MAAM,IAAA,+BAAQ,EACZ,eAAe,qBAAqB,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,4CAA4C,2BAA2B,EAAE,EAChJ,+BAA+B,CAChC,CAAA;gBACH,CAAC;gBAED,IAAI,cAAc,EAAE,CAAC;oBACnB,IAAI,MAAM,IAAI,CAAC,8BAA8B,CAAC,qBAAqB,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,CAAC,EAAE,CAAC;wBACzG,IAAI,CAAC;4BACH,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,SAAG,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE;gCACvE,OAAO,EAAE,qBAAqB,CAAC,cAAc;gCAC7C,iBAAiB,EAAE,qBAAqB,CAAC,iBAAiB;gCAC1D,MAAM,EAAE,WAAW,CAAC,MAAM;6BAC3B,CAAC,CAAA;wBACJ,CAAC;wBAAC,OAAO,CAAM,EAAE,CAAC;4BAChB,IAAI,CAAC;gCACH,MAAM,IAAA,iBAAM,EAAC,WAAW,CAAC,CAAA;4BAC3B,CAAC;4BAAC,OAAO,QAAQ,EAAE,CAAC;gCAClB,SAAS;4BACX,CAAC;4BAED,MAAM,CAAC,CAAA;wBACT,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;SACF,CAAC,CAAA;IACJ,CAAC;IAED,8DAA8D;IAC9D,kJAAkJ;IAClJ,kEAAkE;IAC1D,KAAK,CAAC,eAAe,CAAC,cAAsB;QAClD,IAAI,aAA4C,CAAA;QAChD,IAAI,CAAC;YACH,aAAa,GAAG,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,aAAa,CAAA;YAC7D,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;gBAC1B,OAAO,IAAI,CAAA;YACb,CAAC;QACH,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACxB,oBAAoB;gBACpB,OAAO,IAAI,CAAA;YACb,CAAC;YACD,MAAM,CAAC,CAAA;QACT,CAAC;QACD,OAAO,MAAM,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE,cAAc,CAAC,CAAA;IAC9H,CAAC;IAES,SAAS,CAAC,OAAuB;QACzC,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAA;QACxC,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;YAC1B,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC,CAAA;YAClF,OAAO,KAAK,CAAA;QACd,CAAC;QAED,MAAM,IAAI,GAAG,CAAC,WAAW,CAAC,CAAA;QAC1B,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACjB,CAAC;QAED,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC5B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;QAC1B,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,+BAA+B;YAC/B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAA;QAC1C,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAA;QACxG,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;YACxB,2BAA2B;YAC3B,IAAI,CAAC,IAAI,CAAC,kBAAkB,WAAW,EAAE,CAAC,CAAA;QAC5C,CAAC;QAED,MAAM,kBAAkB,GAAG,GAAS,EAAE;YACpC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAA;QAChI,CAAC,CAAA;QAED,IAAI,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAA;YAC1F,kBAAkB,EAAE,CAAA;YACpB,OAAO,IAAI,CAAA;QACb,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAQ,EAAE,EAAE;YACpD,oEAAoE;YACpE,4GAA4G;YAC5G,MAAM,SAAS,GAAI,CAA2B,CAAC,IAAI,CAAA;YACnD,IAAI,CAAC,OAAO,CAAC,IAAI,CACf,qCAAqC,SAAS,qBAAqB,CAAC,CAAC,OAAO,0GAA0G,CACvL,CAAA;YACD,IAAI,SAAS,KAAK,SAAS,IAAI,SAAS,KAAK,QAAQ,EAAE,CAAC;gBACtD,kBAAkB,EAAE,CAAA;YACtB,CAAC;iBAAM,IAAI,SAAS,KAAK,QAAQ,EAAE,CAAC;gBAClC,OAAO,CAAC,UAAU,CAAC;qBAChB,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC;qBAC7B,KAAK,CAAC,CAAC,GAAU,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAA;YACnD,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;YACvB,CAAC;QACH,CAAC,CAAC,CAAA;QACF,OAAO,IAAI,CAAA;IACb,CAAC;IAEO,KAAK,CAAC,8BAA8B,CAC1C,qBAA4C,EAC5C,WAA4B,EAC5B,WAAmB,EACnB,QAAuB;QAEvB,IAAI,WAAW,CAAC,YAAY,IAAI,IAAI,EAAE,CAAC;YACrC,OAAO,IAAI,CAAA;QACb,CAAC;QAED,IAAI,CAAC;YACH,MAAM,eAAe,GAAkC;gBACrD,MAAM,EAAE,IAAI,SAAG,CAAC,WAAW,CAAC,IAAI,CAAC;gBACjC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAuB,CAAC,QAAQ,EAAE,oDAA6B,CAAC;gBACxF,MAAM,EAAE,IAAI,CAAC,OAAO;gBACpB,OAAO,EAAE,WAAW;gBACpB,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,yBAAyB,EAAE,QAAQ,CAAC,yBAAyB;gBAC7D,iBAAiB,EAAE,qBAAqB,CAAC,iBAAiB;aAC3D,CAAA;YAED,IAAI,IAAI,CAAC,aAAa,CAAC,yBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9C,eAAe,CAAC,UAAU,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAiB,EAAE,EAAE,CAAC,CAAA;YACrE,CAAC;YAED,MAAM,IAAI,+FAA8C,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC,QAAQ,EAAE,CAAA;QACtH,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAA;YAChG,mEAAmE;YACnE,OAAO,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAA;QACrC,CAAC;QACD,OAAO,KAAK,CAAA;IACd,CAAC;CACF;AAzMD,kCAyMC", "sourcesContent": ["import { AllPublishOptions, newError, PackageFileInfo, CURRENT_APP_INSTALLER_FILE_NAME, CURRENT_APP_PACKAGE_FILE_NAME } from \"builder-util-runtime\"\nimport * as path from \"path\"\nimport { AppAdapter } from \"./AppAdapter\"\nimport { DownloadUpdateOptions } from \"./AppUpdater\"\nimport { BaseUpdater, InstallOptions } from \"./BaseUpdater\"\nimport { DifferentialDownloaderOptions } from \"./differentialDownloader/DifferentialDownloader\"\nimport { FileWithEmbeddedBlockMapDifferentialDownloader } from \"./differentialDownloader/FileWithEmbeddedBlockMapDifferentialDownloader\"\nimport { DOWNLOAD_PROGRESS } from \"./types\"\nimport { VerifyUpdateCodeSignature } from \"./main\"\nimport { findFile, Provider } from \"./providers/Provider\"\nimport { unlink } from \"fs-extra\"\nimport { verifySignature } from \"./windowsExecutableCodeSignatureVerifier\"\nimport { URL } from \"url\"\n\nexport class NsisUpdater extends BaseUpdater {\n  /**\n   * Specify custom install directory path\n   *\n   */\n  installDirectory?: string\n\n  constructor(options?: AllPublishOptions | null, app?: AppAdapter) {\n    super(options, app)\n  }\n\n  protected _verifyUpdateCodeSignature: VerifyUpdateCodeSignature = (publisherNames: Array<string>, unescapedTempUpdateFile: string) =>\n    verifySignature(publisherNames, unescapedTempUpdateFile, this._logger)\n\n  /**\n   * The verifyUpdateCodeSignature. You can pass [win-verify-signature](https://github.com/beyondkmp/win-verify-trust) or another custom verify function: ` (publisherName: string[], path: string) => Promise<string | null>`.\n   * The default verify function uses [windowsExecutableCodeSignatureVerifier](https://github.com/electron-userland/electron-builder/blob/master/packages/electron-updater/src/windowsExecutableCodeSignatureVerifier.ts)\n   */\n  get verifyUpdateCodeSignature(): VerifyUpdateCodeSignature {\n    return this._verifyUpdateCodeSignature\n  }\n\n  set verifyUpdateCodeSignature(value: VerifyUpdateCodeSignature) {\n    if (value) {\n      this._verifyUpdateCodeSignature = value\n    }\n  }\n\n  /*** @private */\n  protected doDownloadUpdate(downloadUpdateOptions: DownloadUpdateOptions): Promise<Array<string>> {\n    const provider = downloadUpdateOptions.updateInfoAndProvider.provider\n    const fileInfo = findFile(provider.resolveFiles(downloadUpdateOptions.updateInfoAndProvider.info), \"exe\")!\n    return this.executeDownload({\n      fileExtension: \"exe\",\n      downloadUpdateOptions,\n      fileInfo,\n      task: async (destinationFile, downloadOptions, packageFile, removeTempDirIfAny) => {\n        const packageInfo = fileInfo.packageInfo\n        const isWebInstaller = packageInfo != null && packageFile != null\n        if (isWebInstaller && downloadUpdateOptions.disableWebInstaller) {\n          throw newError(\n            `Unable to download new version ${downloadUpdateOptions.updateInfoAndProvider.info.version}. Web Installers are disabled`,\n            \"ERR_UPDATER_WEB_INSTALLER_DISABLED\"\n          )\n        }\n        if (!isWebInstaller && !downloadUpdateOptions.disableWebInstaller) {\n          this._logger.warn(\n            \"disableWebInstaller is set to false, you should set it to true if you do not plan on using a web installer. This will default to true in a future version.\"\n          )\n        }\n        if (\n          isWebInstaller ||\n          downloadUpdateOptions.disableDifferentialDownload ||\n          (await this.differentialDownloadInstaller(fileInfo, downloadUpdateOptions, destinationFile, provider, CURRENT_APP_INSTALLER_FILE_NAME))\n        ) {\n          await this.httpExecutor.download(fileInfo.url, destinationFile, downloadOptions)\n        }\n\n        const signatureVerificationStatus = await this.verifySignature(destinationFile)\n        if (signatureVerificationStatus != null) {\n          await removeTempDirIfAny()\n          // noinspection ThrowInsideFinallyBlockJS\n          throw newError(\n            `New version ${downloadUpdateOptions.updateInfoAndProvider.info.version} is not signed by the application owner: ${signatureVerificationStatus}`,\n            \"ERR_UPDATER_INVALID_SIGNATURE\"\n          )\n        }\n\n        if (isWebInstaller) {\n          if (await this.differentialDownloadWebPackage(downloadUpdateOptions, packageInfo, packageFile, provider)) {\n            try {\n              await this.httpExecutor.download(new URL(packageInfo.path), packageFile, {\n                headers: downloadUpdateOptions.requestHeaders,\n                cancellationToken: downloadUpdateOptions.cancellationToken,\n                sha512: packageInfo.sha512,\n              })\n            } catch (e: any) {\n              try {\n                await unlink(packageFile)\n              } catch (_ignored) {\n                // ignore\n              }\n\n              throw e\n            }\n          }\n        }\n      },\n    })\n  }\n\n  // $certificateInfo = (Get-AuthenticodeSignature 'xxx\\yyy.exe'\n  // | where {$_.Status.Equals([System.Management.Automation.SignatureStatus]::Valid) -and $_.SignerCertificate.Subject.Contains(\"CN=siemens.com\")})\n  // | Out-String ; if ($certificateInfo) { exit 0 } else { exit 1 }\n  private async verifySignature(tempUpdateFile: string): Promise<string | null> {\n    let publisherName: Array<string> | string | null\n    try {\n      publisherName = (await this.configOnDisk.value).publisherName\n      if (publisherName == null) {\n        return null\n      }\n    } catch (e: any) {\n      if (e.code === \"ENOENT\") {\n        // no app-update.yml\n        return null\n      }\n      throw e\n    }\n    return await this._verifyUpdateCodeSignature(Array.isArray(publisherName) ? publisherName : [publisherName], tempUpdateFile)\n  }\n\n  protected doInstall(options: InstallOptions): boolean {\n    const installerPath = this.installerPath\n    if (installerPath == null) {\n      this.dispatchError(new Error(\"No valid update available, can't quit and install\"))\n      return false\n    }\n\n    const args = [\"--updated\"]\n    if (options.isSilent) {\n      args.push(\"/S\")\n    }\n\n    if (options.isForceRunAfter) {\n      args.push(\"--force-run\")\n    }\n\n    if (this.installDirectory) {\n      // maybe check if folder exists\n      args.push(`/D=${this.installDirectory}`)\n    }\n\n    const packagePath = this.downloadedUpdateHelper == null ? null : this.downloadedUpdateHelper.packageFile\n    if (packagePath != null) {\n      // only = form is supported\n      args.push(`--package-file=${packagePath}`)\n    }\n\n    const callUsingElevation = (): void => {\n      this.spawnLog(path.join(process.resourcesPath, \"elevate.exe\"), [installerPath].concat(args)).catch(e => this.dispatchError(e))\n    }\n\n    if (options.isAdminRightsRequired) {\n      this._logger.info(\"isAdminRightsRequired is set to true, run installer using elevate.exe\")\n      callUsingElevation()\n      return true\n    }\n\n    this.spawnLog(installerPath, args).catch((e: Error) => {\n      // https://github.com/electron-userland/electron-builder/issues/1129\n      // Node 8 sends errors: https://nodejs.org/dist/latest-v8.x/docs/api/errors.html#errors_common_system_errors\n      const errorCode = (e as NodeJS.ErrnoException).code\n      this._logger.info(\n        `Cannot run installer: error code: ${errorCode}, error message: \"${e.message}\", will be executed again using elevate if EACCES, and will try to use electron.shell.openItem if ENOENT`\n      )\n      if (errorCode === \"UNKNOWN\" || errorCode === \"EACCES\") {\n        callUsingElevation()\n      } else if (errorCode === \"ENOENT\") {\n        require(\"electron\")\n          .shell.openPath(installerPath)\n          .catch((err: Error) => this.dispatchError(err))\n      } else {\n        this.dispatchError(e)\n      }\n    })\n    return true\n  }\n\n  private async differentialDownloadWebPackage(\n    downloadUpdateOptions: DownloadUpdateOptions,\n    packageInfo: PackageFileInfo,\n    packagePath: string,\n    provider: Provider<any>\n  ): Promise<boolean> {\n    if (packageInfo.blockMapSize == null) {\n      return true\n    }\n\n    try {\n      const downloadOptions: DifferentialDownloaderOptions = {\n        newUrl: new URL(packageInfo.path),\n        oldFile: path.join(this.downloadedUpdateHelper!.cacheDir, CURRENT_APP_PACKAGE_FILE_NAME),\n        logger: this._logger,\n        newFile: packagePath,\n        requestHeaders: this.requestHeaders,\n        isUseMultipleRangeRequest: provider.isUseMultipleRangeRequest,\n        cancellationToken: downloadUpdateOptions.cancellationToken,\n      }\n\n      if (this.listenerCount(DOWNLOAD_PROGRESS) > 0) {\n        downloadOptions.onProgress = it => this.emit(DOWNLOAD_PROGRESS, it)\n      }\n\n      await new FileWithEmbeddedBlockMapDifferentialDownloader(packageInfo, this.httpExecutor, downloadOptions).download()\n    } catch (e: any) {\n      this._logger.error(`Cannot download differentially, fallback to full download: ${e.stack || e}`)\n      // during test (developer machine mac or linux) we must throw error\n      return process.platform === \"win32\"\n    }\n    return false\n  }\n}\n"]}