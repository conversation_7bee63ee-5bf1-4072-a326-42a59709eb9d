(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[925],{2163:(e,t,s)=>{"use strict";s.d(t,{p:()=>a});var r=s(5155),i=s(3741),n=s(9283);function a(e){let{order:t,onAccept:s,onDecline:a,onComplete:c}=e,{t:l}=(0,n.o)();return(0,r.jsxs)("div",{className:"p-4 rounded-lg border ".concat((()=>{switch(t.status){case"pending":return"border-yellow-300 bg-yellow-50";case"accepted":case"in_progress":return"border-blue-300 bg-blue-50";case"completed":return"border-green-300 bg-green-50";case"failed":return"border-red-300 bg-red-50";default:return"border-gray-300 bg-gray-50"}})()),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-lg",children:(()=>{let e=["\uD83D\uDC69","\uD83D\uDC68","\uD83D\uDC75","\uD83D\uDC74","\uD83D\uDC67","\uD83D\uDC66"],s=t.customerName.length%e.length;return e[s]})()}),(0,r.jsx)("h3",{className:"font-medium text-gray-800",children:t.customerName})]}),(0,r.jsx)("span",{className:"text-sm font-semibold text-green-600",children:l("orders.reward",{amount:t.reward.toString()})})]}),(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:t.items.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("span",{children:"\uD83E\uDDC1"}),(0,r.jsx)("span",{children:e})]},t))}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["⏰ ",l("orders.timeLimit",{time:(e=>{let t=Math.floor(e/60);return"".concat(t,":").concat((e%60).toString().padStart(2,"0"))})(t.timeLimit)})]}),(0,r.jsx)("div",{className:"text-xs",title:"Difficulty: ".concat(t.difficulty,"/5"),children:"⭐".repeat(t.difficulty)+"☆".repeat(5-t.difficulty)})]}),"pending"===t.status&&(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(i.$,{size:"sm",variant:"success",onClick:()=>s(t.id),className:"flex-1",children:["✅ ",l("orders.accept")]}),(0,r.jsxs)(i.$,{size:"sm",variant:"danger",onClick:()=>a(t.id),className:"flex-1",children:["❌ ",l("orders.decline")]})]}),"accepted"===t.status&&(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-blue-600 text-sm font-medium mb-2",children:"\uD83D\uDCCB Order Accepted"}),(0,r.jsxs)(i.$,{size:"sm",variant:"primary",onClick:()=>c&&c(t.id),className:"w-full",children:["\uD83C\uDFAF ",l("orders.complete")]})]}),"in_progress"===t.status&&(0,r.jsxs)("div",{className:"text-center text-orange-600 text-sm font-medium",children:["\uD83D\uDD04 ",l("orders.inProgress")]}),"completed"===t.status&&(0,r.jsx)("div",{className:"text-center text-green-600 text-sm font-medium",children:"✅ Completed!"}),"failed"===t.status&&(0,r.jsx)("div",{className:"text-center text-red-600 text-sm font-medium",children:"❌ Failed"})]})}},3741:(e,t,s)=>{"use strict";s.d(t,{$:()=>i});var r=s(5155);s(2115);let i=e=>{let{variant:t="primary",size:s="md",className:i="",children:n,...a}=e,c=["font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2",{primary:"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500",secondary:"bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500",danger:"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500",success:"bg-green-600 hover:bg-green-700 text-white focus:ring-green-500"}[t],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"}[s],i].join(" ");return(0,r.jsx)("button",{className:c,...a,children:n})}},7141:(e,t,s)=>{Promise.resolve().then(s.bind(s,8731))},9419:(e,t,s)=>{"use strict";s.d(t,{$:()=>n});var r=s(5155),i=s(9283);function n(e){let{equipment:t,onClick:s}=e,{t:n}=(0,i.o)();return(0,r.jsx)("div",{className:"p-4 rounded-lg border-2 cursor-pointer transition-all ".concat(t.isActive?"border-green-400 bg-green-50":"border-gray-200 bg-gray-50 hover:border-orange-300 hover:bg-orange-50"),onClick:()=>!t.isActive&&s(t.id,t.name),children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl mb-2",children:(e=>{switch(e){case"oven":return"\uD83D\uDD25";case"mixer":return"\uD83E\uDD44";case"counter":return"\uD83C\uDF7D️";default:return"⚙️"}})(t.type)}),(0,r.jsx)("h3",{className:"font-medium text-gray-800",children:t.name}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["Level ",t.level]}),t.isActive&&t.timeRemaining?(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("div",{className:"text-sm text-green-600",children:n("kitchen.making",{recipe:t.currentRecipe||""})}),(0,r.jsx)("div",{className:"text-lg font-mono text-green-700",children:(e=>{let t=Math.floor(e/60);return"".concat(t,":").concat((e%60).toString().padStart(2,"0"))})(t.timeRemaining)}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,r.jsx)("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-1000",style:{width:"".concat(100-t.timeRemaining/60*100,"%")}})})]}):(0,r.jsx)("div",{className:"text-sm text-gray-500 mt-2",children:t.isActive?"Busy":n("kitchen.clickToUse")})]})})}}},e=>{e.O(0,[283,112,441,964,358],()=>e(e.s=7141)),_N_E=e.O()}]);