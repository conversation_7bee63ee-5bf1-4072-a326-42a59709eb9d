# 📦 Bake It Out - Installer Guide

## Overview

This guide explains how to build and distribute installers for "Bake It Out", the multiplayer bakery management game. The installer system uses Electron and electron-builder to create native desktop applications for Windows, macOS, and Linux.

## 🚀 Quick Start

### Prerequisites

- **Node.js** (v18 or higher)
- **npm** (v8 or higher)
- **Git** (for version control)

### Build Installer

```bash
# Install dependencies
npm install

# Build for Windows (default)
npm run dist

# Build for all platforms
node scripts/build-installer.js --all

# Build with verbose output
node scripts/build-installer.js --verbose --all
```

## 🛠 Build System

### Available Scripts

```bash
# Development
npm run electron-dev          # Run in development mode
npm run electron             # Run Electron with built app

# Building
npm run build               # Build Next.js app only
npm run dist               # Build installer for Windows
npm run dist-win           # Build Windows installer
npm run dist-mac           # Build macOS installer  
npm run dist-linux         # Build Linux installer
npm run pack              # Build app without installer

# Custom build script
node scripts/build-installer.js [options]
```

### Build Script Options

```bash
--all         # Build for all platforms (Windows, macOS, Linux)
--skip-build  # Skip Next.js build step
--verbose     # Show detailed output
--help        # Show help message
```

## 📁 Project Structure

```
bake-it-out/
├── electron/                 # Electron main process
│   ├── main.js              # Main Electron process
│   ├── preload.js           # Preload script for security
│   ├── updater.js           # Auto-updater functionality
│   └── assets/              # App icons and installer assets
│       ├── icon.png         # Linux icon
│       ├── icon.ico         # Windows icon
│       ├── icon.icns        # macOS icon
│       └── installer.nsh    # NSIS installer script
├── scripts/                 # Build scripts
│   └── build-installer.js   # Main build script
├── server/                  # Socket.IO server
│   └── socket-server.js     # Multiplayer server
├── out/                     # Next.js build output
└── dist/                    # Final installers
```

## 🎯 Platform-Specific Builds

### Windows

**Output Files:**
- `Bake It Out Setup.exe` - NSIS installer
- `Bake It Out.exe` - Portable executable

**Features:**
- Custom NSIS installer with branding
- Desktop and Start Menu shortcuts
- File associations for `.bakesave` files
- Uninstaller with registry cleanup
- Auto-updater support

**Requirements:**
- Windows 7 or higher
- 64-bit or 32-bit architecture support

### macOS

**Output Files:**
- `Bake It Out.dmg` - Disk image installer
- `Bake It Out.zip` - Zip archive

**Features:**
- Drag-and-drop DMG installer
- Code signing ready (requires certificates)
- Auto-updater support
- Universal binary (Intel + Apple Silicon)

**Requirements:**
- macOS 10.14 or higher
- Intel or Apple Silicon Mac

### Linux

**Output Files:**
- `Bake It Out.AppImage` - Portable application
- `bake-it-out.deb` - Debian package
- `bake-it-out.rpm` - Red Hat package

**Features:**
- AppImage for universal compatibility
- Native package managers support
- Desktop integration
- Auto-updater support

**Requirements:**
- Ubuntu 18.04+ / Debian 10+ / CentOS 8+ / Fedora 32+
- 64-bit architecture

## 🔧 Configuration

### Electron Builder Config

The build configuration is in `package.json` under the `build` section:

```json
{
  "build": {
    "appId": "com.bakeitout.app",
    "productName": "Bake It Out",
    "directories": {
      "output": "dist"
    },
    "files": [
      "out/**/*",
      "electron/**/*", 
      "server/**/*",
      "node_modules/**/*",
      "package.json"
    ]
  }
}
```

### Auto-Updater

The app includes automatic update functionality:

- **Development**: Updates disabled
- **Production**: Checks for updates on startup
- **Update Source**: GitHub Releases (configurable)
- **Update Process**: Background download, user notification

## 🎨 Customization

### App Icons

Replace the placeholder icons in `electron/assets/`:

- `icon.png` - 256x256 PNG for Linux
- `icon.ico` - Multi-size ICO for Windows
- `icon.icns` - Multi-size ICNS for macOS

### Installer Branding

Customize installer appearance:

- **Windows**: Edit `electron/assets/installer.nsh`
- **macOS**: Modify DMG background and layout
- **Linux**: Update desktop file and metadata

### App Metadata

Update app information in `package.json`:

```json
{
  "name": "bake-it-out",
  "version": "1.0.0",
  "description": "Multiplayer bakery management game",
  "author": "Your Name",
  "homepage": "https://your-website.com"
}
```

## 🚀 Distribution

### GitHub Releases

1. **Tag Release**: Create a git tag with version number
2. **Build Installers**: Run build script for all platforms
3. **Upload Assets**: Upload installers to GitHub Release
4. **Auto-Updates**: Users will be notified of new versions

### Manual Distribution

1. **Build**: Create installers using build scripts
2. **Test**: Verify installers on target platforms
3. **Distribute**: Share installers via website or other channels

## 🔒 Security

### Code Signing

For production releases, code signing is recommended:

**Windows:**
- Obtain code signing certificate
- Configure signing in electron-builder
- Sign both installer and executable

**macOS:**
- Obtain Apple Developer certificate
- Configure notarization
- Sign and notarize the app

### Security Features

- **Preload Script**: Secure communication between main and renderer
- **Context Isolation**: Prevents code injection
- **Node Integration**: Disabled in renderer for security
- **CSP Headers**: Content Security Policy protection

## 🧪 Testing

### Pre-Release Testing

1. **Build Test**: Verify build completes without errors
2. **Installation Test**: Install on clean systems
3. **Functionality Test**: Verify all features work
4. **Update Test**: Test auto-updater functionality
5. **Uninstall Test**: Verify clean uninstallation

### Automated Testing

```bash
# Run tests before building
npm test

# Lint code
npm run lint

# Build verification
node scripts/build-installer.js --verbose
```

## 📊 Build Metrics

The build script generates `dist/build-info.json` with:

- Build timestamp
- Version information
- Platform targets
- Node.js and Electron versions
- File sizes and checksums

## 🐛 Troubleshooting

### Common Issues

**Build Fails:**
- Check Node.js version (v18+ required)
- Clear `node_modules` and reinstall
- Verify all dependencies are installed

**Large File Size:**
- Review included files in build config
- Exclude unnecessary dependencies
- Use `npm run pack` to inspect contents

**Auto-Updater Not Working:**
- Verify GitHub release configuration
- Check network connectivity
- Review update server logs

**Icons Not Showing:**
- Verify icon file formats and sizes
- Check file paths in build config
- Rebuild with `--verbose` for details

### Debug Mode

```bash
# Enable debug logging
DEBUG=electron-builder npm run dist

# Verbose build output
node scripts/build-installer.js --verbose
```

## 📝 Release Checklist

- [ ] Update version in `package.json`
- [ ] Update changelog/release notes
- [ ] Test on all target platforms
- [ ] Verify auto-updater configuration
- [ ] Build installers for all platforms
- [ ] Test installation on clean systems
- [ ] Create GitHub release with assets
- [ ] Announce release to users

## 🤝 Contributing

When contributing to the installer system:

1. Test changes on multiple platforms
2. Update documentation for new features
3. Verify backward compatibility
4. Follow security best practices
5. Update build scripts as needed

## 📞 Support

For installer-related issues:

1. Check this documentation
2. Review build logs with `--verbose`
3. Search existing GitHub issues
4. Create new issue with build details
5. Include platform and version information

The installer system is designed to be robust and user-friendly, providing a professional installation experience across all supported platforms.
