{"version": 3, "file": "skipWhile.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/skipWhile.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,wBAAwB,EAAE,MAAM,sBAAsB,CAAC;AAiDhE,MAAM,UAAU,SAAS,CAAI,SAA+C;IAC1E,OAAO,OAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAChC,IAAI,MAAM,GAAG,KAAK,CAAC;QACnB,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,CAAC,SAAS,CACd,wBAAwB,CAAC,UAAU,EAAE,UAAC,KAAK,IAAK,OAAA,CAAC,MAAM,IAAI,CAAC,MAAM,GAAG,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAA3E,CAA2E,CAAC,CAC7H,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC"}