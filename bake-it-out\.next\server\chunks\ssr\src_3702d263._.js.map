{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/lib/gameLogic.ts"], "sourcesContent": ["// Game logic and data structures\n\nexport interface Recipe {\n  id: string\n  name: string\n  ingredients: { name: string; quantity: number }[]\n  bakingTime: number // in seconds\n  difficulty: number // 1-5\n  unlockLevel: number\n  basePrice: number\n  category: 'cookies' | 'cakes' | 'bread' | 'pastries'\n}\n\nexport const RECIPES: Recipe[] = [\n  {\n    id: 'chocolate_chip_cookies',\n    name: 'Chocolate Chip Cookies',\n    ingredients: [\n      { name: 'Flour', quantity: 2 },\n      { name: 'Sugar', quantity: 1 },\n      { name: 'Butter', quantity: 1 },\n      { name: 'Chocolate Chips', quantity: 1 }\n    ],\n    bakingTime: 45,\n    difficulty: 1,\n    unlockLevel: 1,\n    basePrice: 25,\n    category: 'cookies'\n  },\n  {\n    id: 'vanilla_muffins',\n    name: 'Vanilla Muffins',\n    ingredients: [\n      { name: 'Flour', quantity: 2 },\n      { name: 'Sugar', quantity: 1 },\n      { name: 'Eggs', quantity: 1 },\n      { name: 'Vanilla', quantity: 1 }\n    ],\n    bakingTime: 60,\n    difficulty: 1,\n    unlockLevel: 1,\n    basePrice: 20,\n    category: 'cakes'\n  },\n  {\n    id: 'cinnamon_rolls',\n    name: 'Cinnamon Rolls',\n    ingredients: [\n      { name: 'Flour', quantity: 3 },\n      { name: 'Sugar', quantity: 2 },\n      { name: 'Butter', quantity: 2 },\n      { name: 'Eggs', quantity: 1 }\n    ],\n    bakingTime: 90,\n    difficulty: 2,\n    unlockLevel: 2,\n    basePrice: 35,\n    category: 'pastries'\n  },\n  {\n    id: 'chocolate_brownies',\n    name: 'Chocolate Brownies',\n    ingredients: [\n      { name: 'Flour', quantity: 2 },\n      { name: 'Sugar', quantity: 2 },\n      { name: 'Butter', quantity: 1 },\n      { name: 'Chocolate Chips', quantity: 2 }\n    ],\n    bakingTime: 75,\n    difficulty: 2,\n    unlockLevel: 2,\n    basePrice: 30,\n    category: 'cakes'\n  },\n  {\n    id: 'sourdough_bread',\n    name: 'Sourdough Bread',\n    ingredients: [\n      { name: 'Flour', quantity: 4 },\n      { name: 'Salt', quantity: 1 }\n    ],\n    bakingTime: 180,\n    difficulty: 3,\n    unlockLevel: 3,\n    basePrice: 45,\n    category: 'bread'\n  }\n]\n\nexport const CUSTOMER_NAMES = [\n  'Alice Johnson', 'Bob Smith', 'Carol Davis', 'David Wilson',\n  'Emma Brown', 'Frank Miller', 'Grace Taylor', 'Henry Anderson',\n  'Ivy Thomas', 'Jack Martinez', 'Kate Garcia', 'Liam Rodriguez',\n  'Mia Lopez', 'Noah Gonzalez', 'Olivia Hernandez', 'Paul Perez',\n  'Quinn Turner', 'Ruby Phillips', 'Sam Campbell', 'Tina Parker'\n]\n\nexport function generateRandomOrder(playerLevel: number): {\n  id: string\n  customerName: string\n  items: string[]\n  timeLimit: number\n  reward: number\n  status: 'pending'\n  difficulty: number\n} {\n  // Filter recipes based on player level\n  const availableRecipes = RECIPES.filter(recipe => recipe.unlockLevel <= playerLevel)\n  \n  if (availableRecipes.length === 0) {\n    // Fallback to basic recipe\n    availableRecipes.push(RECIPES[0])\n  }\n\n  // Select random recipe(s)\n  const numItems = Math.random() < 0.7 ? 1 : Math.random() < 0.9 ? 2 : 3\n  const selectedRecipes: Recipe[] = []\n  \n  for (let i = 0; i < numItems; i++) {\n    const recipe = availableRecipes[Math.floor(Math.random() * availableRecipes.length)]\n    selectedRecipes.push(recipe)\n  }\n\n  // Calculate order properties\n  const totalDifficulty = selectedRecipes.reduce((sum, recipe) => sum + recipe.difficulty, 0)\n  const avgDifficulty = Math.ceil(totalDifficulty / selectedRecipes.length)\n  const totalBasePrice = selectedRecipes.reduce((sum, recipe) => sum + recipe.basePrice, 0)\n  \n  // Add some randomness to price and time\n  const priceMultiplier = 0.8 + Math.random() * 0.4 // 0.8 to 1.2\n  const timeMultiplier = 1.5 + Math.random() * 1.0 // 1.5 to 2.5\n  \n  const reward = Math.floor(totalBasePrice * priceMultiplier)\n  const baseTime = selectedRecipes.reduce((sum, recipe) => sum + recipe.bakingTime, 0)\n  const timeLimit = Math.floor(baseTime * timeMultiplier)\n\n  return {\n    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n    customerName: CUSTOMER_NAMES[Math.floor(Math.random() * CUSTOMER_NAMES.length)],\n    items: selectedRecipes.map(recipe => recipe.name),\n    timeLimit,\n    reward,\n    status: 'pending',\n    difficulty: Math.min(5, avgDifficulty)\n  }\n}\n\nexport function calculateExperienceReward(difficulty: number, timeBonus: boolean = false): number {\n  const baseExp = difficulty * 10\n  const bonus = timeBonus ? Math.floor(baseExp * 0.5) : 0\n  return baseExp + bonus\n}\n\nexport function calculateLevelRequirement(level: number): number {\n  return level * 100 + (level - 1) * 50\n}\n\nexport function canCraftRecipe(recipe: Recipe, inventory: { name: string; quantity: number }[]): boolean {\n  return recipe.ingredients.every(ingredient => {\n    const inventoryItem = inventory.find(item => item.name === ingredient.name)\n    return inventoryItem && inventoryItem.quantity >= ingredient.quantity\n  })\n}\n\nexport function getRecipeById(id: string): Recipe | undefined {\n  return RECIPES.find(recipe => recipe.id === id)\n}\n\nexport function getAvailableRecipes(playerLevel: number): Recipe[] {\n  return RECIPES.filter(recipe => recipe.unlockLevel <= playerLevel)\n}\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;;;;;;AAa1B,MAAM,UAAoB;IAC/B;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAU,UAAU;YAAE;YAC9B;gBAAE,MAAM;gBAAmB,UAAU;YAAE;SACxC;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAQ,UAAU;YAAE;YAC5B;gBAAE,MAAM;gBAAW,UAAU;YAAE;SAChC;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAU,UAAU;YAAE;YAC9B;gBAAE,MAAM;gBAAQ,UAAU;YAAE;SAC7B;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAU,UAAU;YAAE;YAC9B;gBAAE,MAAM;gBAAmB,UAAU;YAAE;SACxC;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAQ,UAAU;YAAE;SAC7B;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;CACD;AAEM,MAAM,iBAAiB;IAC5B;IAAiB;IAAa;IAAe;IAC7C;IAAc;IAAgB;IAAgB;IAC9C;IAAc;IAAiB;IAAe;IAC9C;IAAa;IAAiB;IAAoB;IAClD;IAAgB;IAAiB;IAAgB;CAClD;AAEM,SAAS,oBAAoB,WAAmB;IASrD,uCAAuC;IACvC,MAAM,mBAAmB,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,WAAW,IAAI;IAExE,IAAI,iBAAiB,MAAM,KAAK,GAAG;QACjC,2BAA2B;QAC3B,iBAAiB,IAAI,CAAC,OAAO,CAAC,EAAE;IAClC;IAEA,0BAA0B;IAC1B,MAAM,WAAW,KAAK,MAAM,KAAK,MAAM,IAAI,KAAK,MAAM,KAAK,MAAM,IAAI;IACrE,MAAM,kBAA4B,EAAE;IAEpC,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;QACjC,MAAM,SAAS,gBAAgB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM,EAAE;QACpF,gBAAgB,IAAI,CAAC;IACvB;IAEA,6BAA6B;IAC7B,MAAM,kBAAkB,gBAAgB,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,UAAU,EAAE;IACzF,MAAM,gBAAgB,KAAK,IAAI,CAAC,kBAAkB,gBAAgB,MAAM;IACxE,MAAM,iBAAiB,gBAAgB,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,SAAS,EAAE;IAEvF,wCAAwC;IACxC,MAAM,kBAAkB,MAAM,KAAK,MAAM,KAAK,IAAI,aAAa;;IAC/D,MAAM,iBAAiB,MAAM,KAAK,MAAM,KAAK,IAAI,aAAa;;IAE9D,MAAM,SAAS,KAAK,KAAK,CAAC,iBAAiB;IAC3C,MAAM,WAAW,gBAAgB,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,UAAU,EAAE;IAClF,MAAM,YAAY,KAAK,KAAK,CAAC,WAAW;IAExC,OAAO;QACL,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QACjE,cAAc,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,eAAe,MAAM,EAAE;QAC/E,OAAO,gBAAgB,GAAG,CAAC,CAAA,SAAU,OAAO,IAAI;QAChD;QACA;QACA,QAAQ;QACR,YAAY,KAAK,GAAG,CAAC,GAAG;IAC1B;AACF;AAEO,SAAS,0BAA0B,UAAkB,EAAE,YAAqB,KAAK;IACtF,MAAM,UAAU,aAAa;IAC7B,MAAM,QAAQ,YAAY,KAAK,KAAK,CAAC,UAAU,OAAO;IACtD,OAAO,UAAU;AACnB;AAEO,SAAS,0BAA0B,KAAa;IACrD,OAAO,QAAQ,MAAM,CAAC,QAAQ,CAAC,IAAI;AACrC;AAEO,SAAS,eAAe,MAAc,EAAE,SAA+C;IAC5F,OAAO,OAAO,WAAW,CAAC,KAAK,CAAC,CAAA;QAC9B,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;QAC1E,OAAO,iBAAiB,cAAc,QAAQ,IAAI,WAAW,QAAQ;IACvE;AACF;AAEO,SAAS,cAAc,EAAU;IACtC,OAAO,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;AAC9C;AAEO,SAAS,oBAAoB,WAAmB;IACrD,OAAO,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,WAAW,IAAI;AACxD", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/lib/progressionSystem.ts"], "sourcesContent": ["// Advanced progression system for Bake It Out\n\nexport interface LevelReward {\n  type: 'recipe' | 'equipment' | 'money' | 'skill_point' | 'achievement'\n  id: string\n  name: string\n  description: string\n  value?: number\n}\n\nexport interface PlayerLevel {\n  level: number\n  experience: number\n  experienceRequired: number\n  totalExperience: number\n  rewards: LevelReward[]\n}\n\nexport interface Achievement {\n  id: string\n  name: string\n  description: string\n  icon: string\n  category: 'baking' | 'business' | 'efficiency' | 'collection' | 'special'\n  requirements: {\n    type: 'orders_completed' | 'money_earned' | 'recipes_unlocked' | 'level_reached' | 'items_baked' | 'equipment_owned'\n    target: number\n    current?: number\n  }[]\n  reward: LevelReward\n  unlocked: boolean\n  completed: boolean\n}\n\nexport interface SkillTree {\n  id: string\n  name: string\n  description: string\n  icon: string\n  category: 'efficiency' | 'automation' | 'quality' | 'business'\n  level: number\n  maxLevel: number\n  cost: number\n  requirements: {\n    playerLevel?: number\n    skills?: string[]\n    achievements?: string[]\n  }\n  effects: {\n    type: 'baking_speed' | 'money_multiplier' | 'xp_multiplier' | 'ingredient_efficiency' | 'automation_unlock'\n    value: number\n  }[]\n}\n\n// Experience calculation with exponential growth\nexport function calculateExperienceRequired(level: number): number {\n  if (level <= 1) return 0\n  return Math.floor(100 * Math.pow(1.15, level - 1))\n}\n\nexport function calculateTotalExperienceForLevel(level: number): number {\n  let total = 0\n  for (let i = 1; i <= level; i++) {\n    total += calculateExperienceRequired(i)\n  }\n  return total\n}\n\nexport function getLevelFromExperience(experience: number): PlayerLevel {\n  let level = 1\n  let totalExp = 0\n  \n  while (true) {\n    const expRequired = calculateExperienceRequired(level + 1)\n    if (totalExp + expRequired > experience) {\n      break\n    }\n    totalExp += expRequired\n    level++\n  }\n  \n  const expRequired = calculateExperienceRequired(level + 1)\n  const currentLevelExp = experience - totalExp\n  \n  return {\n    level,\n    experience: currentLevelExp,\n    experienceRequired: expRequired,\n    totalExperience: experience,\n    rewards: getLevelRewards(level)\n  }\n}\n\nexport function getLevelRewards(level: number): LevelReward[] {\n  const rewards: LevelReward[] = []\n  \n  // Money rewards every level\n  rewards.push({\n    type: 'money',\n    id: `money_${level}`,\n    name: 'Level Bonus',\n    description: `Bonus money for reaching level ${level}`,\n    value: level * 25\n  })\n  \n  // Recipe unlocks at specific levels\n  const recipeUnlocks: Record<number, string[]> = {\n    2: ['cinnamon_rolls'],\n    3: ['chocolate_brownies', 'sourdough_bread'],\n    4: ['croissants'],\n    5: ['cheesecake'],\n    6: ['macarons'],\n    7: ['honey_glazed_donuts'],\n    8: ['sourdough_bread'],\n    9: ['chocolate_souffle'],\n    10: ['croquembouche'],\n    12: ['opera_cake'],\n    15: ['artisan_pizza_dough']\n  }\n  \n  if (recipeUnlocks[level]) {\n    recipeUnlocks[level].forEach(recipeId => {\n      rewards.push({\n        type: 'recipe',\n        id: recipeId,\n        name: 'New Recipe Unlocked',\n        description: `You can now bake ${recipeId.replace(/_/g, ' ')}`\n      })\n    })\n  }\n  \n  // Equipment unlocks\n  const equipmentUnlocks: Record<number, string[]> = {\n    3: ['professional_oven'],\n    4: ['auto_mixer'],\n    5: ['stand_mixer'],\n    6: ['auto_oven'],\n    7: ['conveyor_belt'],\n    8: ['advanced_auto_mixer'],\n    10: ['industrial_oven'],\n    12: ['smart_conveyor_system']\n  }\n  \n  if (equipmentUnlocks[level]) {\n    equipmentUnlocks[level].forEach(equipmentId => {\n      rewards.push({\n        type: 'equipment',\n        id: equipmentId,\n        name: 'New Equipment Available',\n        description: `${equipmentId.replace(/_/g, ' ')} is now available for purchase`\n      })\n    })\n  }\n  \n  // Skill points every 2 levels\n  if (level % 2 === 0) {\n    rewards.push({\n      type: 'skill_point',\n      id: `skill_point_${level}`,\n      name: 'Skill Point',\n      description: 'Use this to upgrade your skills in the technology tree',\n      value: 1\n    })\n  }\n  \n  return rewards\n}\n\nexport const ACHIEVEMENTS: Achievement[] = [\n  {\n    id: 'first_order',\n    name: 'First Customer',\n    description: 'Complete your first order',\n    icon: '🎯',\n    category: 'baking',\n    requirements: [{ type: 'orders_completed', target: 1 }],\n    reward: { type: 'money', id: 'first_order_bonus', name: 'First Order Bonus', description: 'Bonus for first order', value: 50 },\n    unlocked: true,\n    completed: false\n  },\n  {\n    id: 'baker_apprentice',\n    name: 'Baker Apprentice',\n    description: 'Complete 10 orders',\n    icon: '👨‍🍳',\n    category: 'baking',\n    requirements: [{ type: 'orders_completed', target: 10 }],\n    reward: { type: 'recipe', id: 'special_cookies', name: 'Special Recipe', description: 'Unlock special cookie recipe' },\n    unlocked: true,\n    completed: false\n  },\n  {\n    id: 'money_maker',\n    name: 'Money Maker',\n    description: 'Earn $1000 total',\n    icon: '💰',\n    category: 'business',\n    requirements: [{ type: 'money_earned', target: 1000 }],\n    reward: { type: 'skill_point', id: 'money_maker_skill', name: 'Business Skill Point', description: 'Extra skill point for business success', value: 1 },\n    unlocked: true,\n    completed: false\n  },\n  {\n    id: 'recipe_collector',\n    name: 'Recipe Collector',\n    description: 'Unlock 5 different recipes',\n    icon: '📚',\n    category: 'collection',\n    requirements: [{ type: 'recipes_unlocked', target: 5 }],\n    reward: { type: 'money', id: 'recipe_bonus', name: 'Recipe Collection Bonus', description: 'Bonus for collecting recipes', value: 200 },\n    unlocked: true,\n    completed: false\n  },\n  {\n    id: 'level_master',\n    name: 'Level Master',\n    description: 'Reach level 10',\n    icon: '⭐',\n    category: 'special',\n    requirements: [{ type: 'level_reached', target: 10 }],\n    reward: { type: 'equipment', id: 'master_oven', name: 'Master Oven', description: 'Unlock the legendary Master Oven' },\n    unlocked: true,\n    completed: false\n  }\n]\n\nexport const SKILL_TREE: SkillTree[] = [\n  {\n    id: 'baking_speed_1',\n    name: 'Quick Hands',\n    description: 'Increase baking speed by 10%',\n    icon: '⚡',\n    category: 'efficiency',\n    level: 0,\n    maxLevel: 3,\n    cost: 1,\n    requirements: { playerLevel: 2 },\n    effects: [{ type: 'baking_speed', value: 0.1 }]\n  },\n  {\n    id: 'money_bonus_1',\n    name: 'Business Sense',\n    description: 'Increase money earned by 15%',\n    icon: '💼',\n    category: 'business',\n    level: 0,\n    maxLevel: 3,\n    cost: 1,\n    requirements: { playerLevel: 3 },\n    effects: [{ type: 'money_multiplier', value: 0.15 }]\n  },\n  {\n    id: 'xp_bonus_1',\n    name: 'Fast Learner',\n    description: 'Increase experience gained by 20%',\n    icon: '📈',\n    category: 'efficiency',\n    level: 0,\n    maxLevel: 2,\n    cost: 2,\n    requirements: { playerLevel: 4 },\n    effects: [{ type: 'xp_multiplier', value: 0.2 }]\n  },\n  {\n    id: 'ingredient_efficiency_1',\n    name: 'Efficient Baker',\n    description: 'Use 10% fewer ingredients',\n    icon: '🌾',\n    category: 'efficiency',\n    level: 0,\n    maxLevel: 2,\n    cost: 2,\n    requirements: { playerLevel: 5, skills: ['baking_speed_1'] },\n    effects: [{ type: 'ingredient_efficiency', value: 0.1 }]\n  },\n  {\n    id: 'automation_unlock_1',\n    name: 'Automation Expert',\n    description: 'Unlock advanced automation features',\n    icon: '🤖',\n    category: 'automation',\n    level: 0,\n    maxLevel: 1,\n    cost: 3,\n    requirements: { playerLevel: 8, achievements: ['baker_apprentice'] },\n    effects: [{ type: 'automation_unlock', value: 1 }]\n  }\n]\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;;;;;AAuDvC,SAAS,4BAA4B,KAAa;IACvD,IAAI,SAAS,GAAG,OAAO;IACvB,OAAO,KAAK,KAAK,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,QAAQ;AACjD;AAEO,SAAS,iCAAiC,KAAa;IAC5D,IAAI,QAAQ;IACZ,IAAK,IAAI,IAAI,GAAG,KAAK,OAAO,IAAK;QAC/B,SAAS,4BAA4B;IACvC;IACA,OAAO;AACT;AAEO,SAAS,uBAAuB,UAAkB;IACvD,IAAI,QAAQ;IACZ,IAAI,WAAW;IAEf,MAAO,KAAM;QACX,MAAM,cAAc,4BAA4B,QAAQ;QACxD,IAAI,WAAW,cAAc,YAAY;YACvC;QACF;QACA,YAAY;QACZ;IACF;IAEA,MAAM,cAAc,4BAA4B,QAAQ;IACxD,MAAM,kBAAkB,aAAa;IAErC,OAAO;QACL;QACA,YAAY;QACZ,oBAAoB;QACpB,iBAAiB;QACjB,SAAS,gBAAgB;IAC3B;AACF;AAEO,SAAS,gBAAgB,KAAa;IAC3C,MAAM,UAAyB,EAAE;IAEjC,4BAA4B;IAC5B,QAAQ,IAAI,CAAC;QACX,MAAM;QACN,IAAI,CAAC,MAAM,EAAE,OAAO;QACpB,MAAM;QACN,aAAa,CAAC,+BAA+B,EAAE,OAAO;QACtD,OAAO,QAAQ;IACjB;IAEA,oCAAoC;IACpC,MAAM,gBAA0C;QAC9C,GAAG;YAAC;SAAiB;QACrB,GAAG;YAAC;YAAsB;SAAkB;QAC5C,GAAG;YAAC;SAAa;QACjB,GAAG;YAAC;SAAa;QACjB,GAAG;YAAC;SAAW;QACf,GAAG;YAAC;SAAsB;QAC1B,GAAG;YAAC;SAAkB;QACtB,GAAG;YAAC;SAAoB;QACxB,IAAI;YAAC;SAAgB;QACrB,IAAI;YAAC;SAAa;QAClB,IAAI;YAAC;SAAsB;IAC7B;IAEA,IAAI,aAAa,CAAC,MAAM,EAAE;QACxB,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;YAC3B,QAAQ,IAAI,CAAC;gBACX,MAAM;gBACN,IAAI;gBACJ,MAAM;gBACN,aAAa,CAAC,iBAAiB,EAAE,SAAS,OAAO,CAAC,MAAM,MAAM;YAChE;QACF;IACF;IAEA,oBAAoB;IACpB,MAAM,mBAA6C;QACjD,GAAG;YAAC;SAAoB;QACxB,GAAG;YAAC;SAAa;QACjB,GAAG;YAAC;SAAc;QAClB,GAAG;YAAC;SAAY;QAChB,GAAG;YAAC;SAAgB;QACpB,GAAG;YAAC;SAAsB;QAC1B,IAAI;YAAC;SAAkB;QACvB,IAAI;YAAC;SAAwB;IAC/B;IAEA,IAAI,gBAAgB,CAAC,MAAM,EAAE;QAC3B,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;YAC9B,QAAQ,IAAI,CAAC;gBACX,MAAM;gBACN,IAAI;gBACJ,MAAM;gBACN,aAAa,GAAG,YAAY,OAAO,CAAC,MAAM,KAAK,8BAA8B,CAAC;YAChF;QACF;IACF;IAEA,8BAA8B;IAC9B,IAAI,QAAQ,MAAM,GAAG;QACnB,QAAQ,IAAI,CAAC;YACX,MAAM;YACN,IAAI,CAAC,YAAY,EAAE,OAAO;YAC1B,MAAM;YACN,aAAa;YACb,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEO,MAAM,eAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAoB,QAAQ;YAAE;SAAE;QACvD,QAAQ;YAAE,MAAM;YAAS,IAAI;YAAqB,MAAM;YAAqB,aAAa;YAAyB,OAAO;QAAG;QAC7H,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAoB,QAAQ;YAAG;SAAE;QACxD,QAAQ;YAAE,MAAM;YAAU,IAAI;YAAmB,MAAM;YAAkB,aAAa;QAA+B;QACrH,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAgB,QAAQ;YAAK;SAAE;QACtD,QAAQ;YAAE,MAAM;YAAe,IAAI;YAAqB,MAAM;YAAwB,aAAa;YAA0C,OAAO;QAAE;QACtJ,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAoB,QAAQ;YAAE;SAAE;QACvD,QAAQ;YAAE,MAAM;YAAS,IAAI;YAAgB,MAAM;YAA2B,aAAa;YAAgC,OAAO;QAAI;QACtI,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAiB,QAAQ;YAAG;SAAE;QACrD,QAAQ;YAAE,MAAM;YAAa,IAAI;YAAe,MAAM;YAAe,aAAa;QAAmC;QACrH,UAAU;QACV,WAAW;IACb;CACD;AAEM,MAAM,aAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,MAAM;QACN,cAAc;YAAE,aAAa;QAAE;QAC/B,SAAS;YAAC;gBAAE,MAAM;gBAAgB,OAAO;YAAI;SAAE;IACjD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,MAAM;QACN,cAAc;YAAE,aAAa;QAAE;QAC/B,SAAS;YAAC;gBAAE,MAAM;gBAAoB,OAAO;YAAK;SAAE;IACtD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,MAAM;QACN,cAAc;YAAE,aAAa;QAAE;QAC/B,SAAS;YAAC;gBAAE,MAAM;gBAAiB,OAAO;YAAI;SAAE;IAClD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,MAAM;QACN,cAAc;YAAE,aAAa;YAAG,QAAQ;gBAAC;aAAiB;QAAC;QAC3D,SAAS;YAAC;gBAAE,MAAM;gBAAyB,OAAO;YAAI;SAAE;IAC1D;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,MAAM;QACN,cAAc;YAAE,aAAa;YAAG,cAAc;gBAAC;aAAmB;QAAC;QACnE,SAAS;YAAC;gBAAE,MAAM;gBAAqB,OAAO;YAAE;SAAE;IACpD;CACD", "debugId": null}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/lib/automationSystem.ts"], "sourcesContent": ["// Automation system for Bake It Out\n\nexport interface AutomationSettings {\n  enabled: boolean\n  autoStart: boolean\n  preferredRecipes: string[]\n  maxConcurrentJobs: number\n  priorityMode: 'speed' | 'efficiency' | 'profit'\n  ingredientThreshold: number // Minimum ingredients before stopping\n}\n\nexport interface AutomationJob {\n  id: string\n  equipmentId: string\n  recipeId: string\n  startTime: number\n  duration: number\n  status: 'queued' | 'running' | 'completed' | 'failed'\n  ingredients: { name: string; quantity: number }[]\n  efficiency: number\n}\n\nexport interface ConveyorBelt {\n  id: string\n  name: string\n  level: number\n  speed: number // items per minute\n  capacity: number // max items on belt\n  connections: string[] // connected equipment IDs\n  items: ConveyorItem[]\n  isActive: boolean\n}\n\nexport interface ConveyorItem {\n  id: string\n  recipeId: string\n  position: number // 0-1 along the belt\n  targetEquipmentId: string\n}\n\nexport interface AutomationUpgrade {\n  id: string\n  name: string\n  description: string\n  type: 'speed' | 'efficiency' | 'capacity' | 'intelligence'\n  cost: number\n  unlockLevel: number\n  effects: {\n    speedMultiplier?: number\n    efficiencyBonus?: number\n    capacityIncrease?: number\n    autoQueueing?: boolean\n    smartPrioritization?: boolean\n  }\n}\n\nexport const AUTOMATION_UPGRADES: AutomationUpgrade[] = [\n  {\n    id: 'auto_queue_basic',\n    name: 'Basic Auto-Queue',\n    description: 'Equipment automatically starts the next recipe when finished',\n    type: 'intelligence',\n    cost: 500,\n    unlockLevel: 4,\n    effects: { autoQueueing: true }\n  },\n  {\n    id: 'efficiency_boost_1',\n    name: 'Efficiency Boost I',\n    description: 'Automated equipment uses 10% fewer ingredients',\n    type: 'efficiency',\n    cost: 750,\n    unlockLevel: 5,\n    effects: { efficiencyBonus: 0.1 }\n  },\n  {\n    id: 'speed_boost_1',\n    name: 'Speed Boost I',\n    description: 'Automated equipment works 15% faster',\n    type: 'speed',\n    cost: 1000,\n    unlockLevel: 6,\n    effects: { speedMultiplier: 1.15 }\n  },\n  {\n    id: 'smart_prioritization',\n    name: 'Smart Prioritization',\n    description: 'Automation prioritizes orders based on profit and urgency',\n    type: 'intelligence',\n    cost: 1500,\n    unlockLevel: 8,\n    effects: { smartPrioritization: true }\n  },\n  {\n    id: 'efficiency_boost_2',\n    name: 'Efficiency Boost II',\n    description: 'Automated equipment uses 20% fewer ingredients',\n    type: 'efficiency',\n    cost: 2000,\n    unlockLevel: 10,\n    effects: { efficiencyBonus: 0.2 }\n  },\n  {\n    id: 'speed_boost_2',\n    name: 'Speed Boost II',\n    description: 'Automated equipment works 30% faster',\n    type: 'speed',\n    cost: 2500,\n    unlockLevel: 12,\n    effects: { speedMultiplier: 1.3 }\n  }\n]\n\nexport function calculateAutomationEfficiency(\n  baseEfficiency: number,\n  automationLevel: number,\n  upgrades: string[],\n  skillBonuses: number = 0\n): number {\n  let efficiency = baseEfficiency\n\n  // Automation level bonus\n  efficiency *= (1 + automationLevel * 0.1)\n\n  // Upgrade bonuses\n  upgrades.forEach(upgradeId => {\n    const upgrade = AUTOMATION_UPGRADES.find(u => u.id === upgradeId)\n    if (upgrade?.effects.efficiencyBonus) {\n      efficiency *= (1 + upgrade.effects.efficiencyBonus)\n    }\n  })\n\n  // Skill bonuses\n  efficiency *= (1 + skillBonuses)\n\n  return Math.min(efficiency, 2.0) // Cap at 200% efficiency\n}\n\nexport function calculateAutomationSpeed(\n  baseSpeed: number,\n  automationLevel: number,\n  upgrades: string[],\n  skillBonuses: number = 0\n): number {\n  let speed = baseSpeed\n\n  // Automation level bonus\n  speed *= (1 + automationLevel * 0.05)\n\n  // Upgrade bonuses\n  upgrades.forEach(upgradeId => {\n    const upgrade = AUTOMATION_UPGRADES.find(u => u.id === upgradeId)\n    if (upgrade?.effects.speedMultiplier) {\n      speed *= upgrade.effects.speedMultiplier\n    }\n  })\n\n  // Skill bonuses\n  speed *= (1 + skillBonuses)\n\n  return speed\n}\n\nexport function canAutomate(equipmentType: string, automationLevel: number): boolean {\n  const automationRequirements: Record<string, number> = {\n    'oven': 1,\n    'mixer': 1,\n    'counter': 2,\n    'auto_oven': 0,\n    'auto_mixer': 0,\n    'conveyor': 0\n  }\n\n  return automationLevel >= (automationRequirements[equipmentType] || 999)\n}\n\nexport function generateAutomationJob(\n  equipmentId: string,\n  recipeId: string,\n  recipe: any,\n  efficiency: number\n): AutomationJob {\n  const adjustedDuration = Math.floor(recipe.bakingTime / efficiency)\n  const adjustedIngredients = recipe.ingredients.map((ing: any) => ({\n    ...ing,\n    quantity: Math.ceil(ing.quantity * (1 - (efficiency - 1) * 0.1))\n  }))\n\n  return {\n    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n    equipmentId,\n    recipeId,\n    startTime: Date.now(),\n    duration: adjustedDuration,\n    status: 'queued',\n    ingredients: adjustedIngredients,\n    efficiency\n  }\n}\n\nexport function selectOptimalRecipe(\n  availableRecipes: any[],\n  inventory: any[],\n  priorityMode: 'speed' | 'efficiency' | 'profit',\n  currentOrders: any[]\n): string | null {\n  const craftableRecipes = availableRecipes.filter(recipe =>\n    recipe.ingredients.every((ingredient: any) => {\n      const inventoryItem = inventory.find(item => item.name === ingredient.name)\n      return inventoryItem && inventoryItem.quantity >= ingredient.quantity\n    })\n  )\n\n  if (craftableRecipes.length === 0) return null\n\n  switch (priorityMode) {\n    case 'speed':\n      return craftableRecipes.reduce((fastest, recipe) =>\n        recipe.bakingTime < fastest.bakingTime ? recipe : fastest\n      ).id\n\n    case 'profit':\n      return craftableRecipes.reduce((mostProfitable, recipe) =>\n        recipe.basePrice > mostProfitable.basePrice ? recipe : mostProfitable\n      ).id\n\n    case 'efficiency':\n      // Prioritize recipes needed for current orders\n      const neededRecipes = currentOrders.flatMap(order => order.items)\n      const neededCraftable = craftableRecipes.filter(recipe =>\n        neededRecipes.includes(recipe.name)\n      )\n      \n      if (neededCraftable.length > 0) {\n        return neededCraftable[0].id\n      }\n      \n      return craftableRecipes[0].id\n\n    default:\n      return craftableRecipes[0].id\n  }\n}\n\nexport function updateConveyorBelt(\n  belt: ConveyorBelt,\n  deltaTime: number\n): ConveyorBelt {\n  const speed = belt.speed / 60 // convert to items per second\n  const moveDistance = speed * deltaTime\n\n  const updatedItems = belt.items.map(item => ({\n    ...item,\n    position: Math.min(1, item.position + moveDistance)\n  }))\n\n  // Remove items that reached the end\n  const activeItems = updatedItems.filter(item => item.position < 1)\n\n  return {\n    ...belt,\n    items: activeItems\n  }\n}\n\nexport function addItemToConveyor(\n  belt: ConveyorBelt,\n  item: Omit<ConveyorItem, 'position'>\n): ConveyorBelt {\n  if (belt.items.length >= belt.capacity) {\n    return belt // Belt is full\n  }\n\n  const newItem: ConveyorItem = {\n    ...item,\n    position: 0\n  }\n\n  return {\n    ...belt,\n    items: [...belt.items, newItem]\n  }\n}\n"], "names": [], "mappings": "AAAA,oCAAoC;;;;;;;;;;;AAwD7B,MAAM,sBAA2C;IACtD;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,aAAa;QACb,SAAS;YAAE,cAAc;QAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,aAAa;QACb,SAAS;YAAE,iBAAiB;QAAI;IAClC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,aAAa;QACb,SAAS;YAAE,iBAAiB;QAAK;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,aAAa;QACb,SAAS;YAAE,qBAAqB;QAAK;IACvC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,aAAa;QACb,SAAS;YAAE,iBAAiB;QAAI;IAClC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,aAAa;QACb,SAAS;YAAE,iBAAiB;QAAI;IAClC;CACD;AAEM,SAAS,8BACd,cAAsB,EACtB,eAAuB,EACvB,QAAkB,EAClB,eAAuB,CAAC;IAExB,IAAI,aAAa;IAEjB,yBAAyB;IACzB,cAAe,IAAI,kBAAkB;IAErC,kBAAkB;IAClB,SAAS,OAAO,CAAC,CAAA;QACf,MAAM,UAAU,oBAAoB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACvD,IAAI,SAAS,QAAQ,iBAAiB;YACpC,cAAe,IAAI,QAAQ,OAAO,CAAC,eAAe;QACpD;IACF;IAEA,gBAAgB;IAChB,cAAe,IAAI;IAEnB,OAAO,KAAK,GAAG,CAAC,YAAY,KAAK,yBAAyB;;AAC5D;AAEO,SAAS,yBACd,SAAiB,EACjB,eAAuB,EACvB,QAAkB,EAClB,eAAuB,CAAC;IAExB,IAAI,QAAQ;IAEZ,yBAAyB;IACzB,SAAU,IAAI,kBAAkB;IAEhC,kBAAkB;IAClB,SAAS,OAAO,CAAC,CAAA;QACf,MAAM,UAAU,oBAAoB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACvD,IAAI,SAAS,QAAQ,iBAAiB;YACpC,SAAS,QAAQ,OAAO,CAAC,eAAe;QAC1C;IACF;IAEA,gBAAgB;IAChB,SAAU,IAAI;IAEd,OAAO;AACT;AAEO,SAAS,YAAY,aAAqB,EAAE,eAAuB;IACxE,MAAM,yBAAiD;QACrD,QAAQ;QACR,SAAS;QACT,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;IACd;IAEA,OAAO,mBAAmB,CAAC,sBAAsB,CAAC,cAAc,IAAI,GAAG;AACzE;AAEO,SAAS,sBACd,WAAmB,EACnB,QAAgB,EAChB,MAAW,EACX,UAAkB;IAElB,MAAM,mBAAmB,KAAK,KAAK,CAAC,OAAO,UAAU,GAAG;IACxD,MAAM,sBAAsB,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,MAAa,CAAC;YAChE,GAAG,GAAG;YACN,UAAU,KAAK,IAAI,CAAC,IAAI,QAAQ,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG;QAChE,CAAC;IAED,OAAO;QACL,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QACjE;QACA;QACA,WAAW,KAAK,GAAG;QACnB,UAAU;QACV,QAAQ;QACR,aAAa;QACb;IACF;AACF;AAEO,SAAS,oBACd,gBAAuB,EACvB,SAAgB,EAChB,YAA+C,EAC/C,aAAoB;IAEpB,MAAM,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,SAC/C,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC;YACxB,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;YAC1E,OAAO,iBAAiB,cAAc,QAAQ,IAAI,WAAW,QAAQ;QACvE;IAGF,IAAI,iBAAiB,MAAM,KAAK,GAAG,OAAO;IAE1C,OAAQ;QACN,KAAK;YACH,OAAO,iBAAiB,MAAM,CAAC,CAAC,SAAS,SACvC,OAAO,UAAU,GAAG,QAAQ,UAAU,GAAG,SAAS,SAClD,EAAE;QAEN,KAAK;YACH,OAAO,iBAAiB,MAAM,CAAC,CAAC,gBAAgB,SAC9C,OAAO,SAAS,GAAG,eAAe,SAAS,GAAG,SAAS,gBACvD,EAAE;QAEN,KAAK;YACH,+CAA+C;YAC/C,MAAM,gBAAgB,cAAc,OAAO,CAAC,CAAA,QAAS,MAAM,KAAK;YAChE,MAAM,kBAAkB,iBAAiB,MAAM,CAAC,CAAA,SAC9C,cAAc,QAAQ,CAAC,OAAO,IAAI;YAGpC,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,OAAO,eAAe,CAAC,EAAE,CAAC,EAAE;YAC9B;YAEA,OAAO,gBAAgB,CAAC,EAAE,CAAC,EAAE;QAE/B;YACE,OAAO,gBAAgB,CAAC,EAAE,CAAC,EAAE;IACjC;AACF;AAEO,SAAS,mBACd,IAAkB,EAClB,SAAiB;IAEjB,MAAM,QAAQ,KAAK,KAAK,GAAG,GAAG,8BAA8B;;IAC5D,MAAM,eAAe,QAAQ;IAE7B,MAAM,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;YAC3C,GAAG,IAAI;YACP,UAAU,KAAK,GAAG,CAAC,GAAG,KAAK,QAAQ,GAAG;QACxC,CAAC;IAED,oCAAoC;IACpC,MAAM,cAAc,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,GAAG;IAEhE,OAAO;QACL,GAAG,IAAI;QACP,OAAO;IACT;AACF;AAEO,SAAS,kBACd,IAAkB,EAClB,IAAoC;IAEpC,IAAI,KAAK,KAAK,CAAC,MAAM,IAAI,KAAK,QAAQ,EAAE;QACtC,OAAO,KAAK,eAAe;;IAC7B;IAEA,MAAM,UAAwB;QAC5B,GAAG,IAAI;QACP,UAAU;IACZ;IAEA,OAAO;QACL,GAAG,IAAI;QACP,OAAO;eAAI,KAAK,KAAK;YAAE;SAAQ;IACjC;AACF", "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/contexts/GameContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect } from 'react'\nimport { EquipmentData } from '@/components/game/Equipment'\nimport { OrderData } from '@/components/game/Order'\nimport { generateRandomOrder, calculateExperienceReward, canCraftRecipe, getRecipeById, getAvailableRecipes } from '@/lib/gameLogic'\nimport {\n  getLevelFromExperience,\n  LevelReward,\n  Achievement,\n  SkillTree,\n  ACHIEVEMENTS,\n  SKILL_TREE\n} from '@/lib/progressionSystem'\nimport {\n  AutomationSettings,\n  AutomationJob,\n  ConveyorBelt,\n  AUTOMATION_UPGRADES,\n  calculateAutomationEfficiency,\n  calculateAutomationSpeed,\n  selectOptimalRecipe,\n  generateAutomationJob\n} from '@/lib/automationSystem'\n\nexport interface Player {\n  level: number\n  experience: number\n  money: number\n  maxExperience: number\n  skillPoints: number\n  totalMoneyEarned: number\n  totalOrdersCompleted: number\n  totalItemsBaked: number\n  unlockedRecipes: string[]\n  automationUpgrades: string[]\n}\n\nexport interface Ingredient {\n  name: string\n  quantity: number\n  cost: number\n  icon: string\n}\n\ninterface GameContextType {\n  player: Player\n  equipment: EquipmentData[]\n  inventory: Ingredient[]\n  orders: OrderData[]\n  achievements: Achievement[]\n  skills: SkillTree[]\n  levelUpRewards: LevelReward[]\n  showLevelUp: boolean\n  automationSettings: AutomationSettings\n  automationJobs: AutomationJob[]\n  conveyorBelts: ConveyorBelt[]\n  updatePlayer: (updates: Partial<Player>) => void\n  updateEquipment: (equipmentId: string, updates: Partial<EquipmentData>) => void\n  addExperience: (amount: number) => void\n  addMoney: (amount: number) => void\n  spendMoney: (amount: number) => boolean\n  useIngredient: (name: string, quantity: number) => boolean\n  addIngredient: (name: string, quantity: number) => void\n  acceptOrder: (orderId: string) => void\n  completeOrder: (orderId: string) => void\n  declineOrder: (orderId: string) => void\n  generateNewOrder: () => void\n  upgradeSkill: (skillId: string) => void\n  checkAchievements: () => void\n  dismissLevelUp: () => void\n  updateAutomationSettings: (updates: Partial<AutomationSettings>) => void\n  purchaseAutomationUpgrade: (upgradeId: string) => void\n  startAutomationJob: (equipmentId: string) => void\n}\n\nconst GameContext = createContext<GameContextType | undefined>(undefined)\n\nconst RECIPES = [\n  'Chocolate Chip Cookies',\n  'Vanilla Muffins',\n  'Cinnamon Rolls',\n  'Brownies',\n  'Croissants',\n  'Bread Loaf',\n  'Cupcakes',\n  'Apple Pie'\n]\n\nconst CUSTOMER_NAMES = [\n  'Alice Johnson', 'Bob Smith', 'Carol Davis', 'David Wilson',\n  'Emma Brown', 'Frank Miller', 'Grace Taylor', 'Henry Anderson',\n  'Ivy Thomas', 'Jack Martinez', 'Kate Garcia', 'Liam Rodriguez'\n]\n\nexport function GameProvider({ children }: { children: React.ReactNode }) {\n  const [player, setPlayer] = useState<Player>({\n    level: 1,\n    experience: 0,\n    money: 100,\n    maxExperience: 100,\n    skillPoints: 0,\n    totalMoneyEarned: 0,\n    totalOrdersCompleted: 0,\n    totalItemsBaked: 0,\n    unlockedRecipes: ['chocolate_chip_cookies', 'vanilla_muffins'],\n    automationUpgrades: []\n  })\n\n  const [equipment, setEquipment] = useState<EquipmentData[]>([\n    { id: 'oven1', name: 'Basic Oven', type: 'oven', isActive: false, level: 1, efficiency: 1.0, automationLevel: 0 },\n    { id: 'mixer1', name: 'Hand Mixer', type: 'mixer', isActive: false, level: 1, efficiency: 1.0, automationLevel: 0 },\n    { id: 'counter1', name: 'Work Counter', type: 'counter', isActive: false, level: 1, efficiency: 1.0, automationLevel: 0 }\n  ])\n\n  const [inventory, setInventory] = useState<Ingredient[]>([\n    { name: 'Flour', quantity: 15, cost: 5, icon: '🌾' },\n    { name: 'Sugar', quantity: 12, cost: 8, icon: '🍯' },\n    { name: 'Eggs', quantity: 10, cost: 12, icon: '🥚' },\n    { name: 'Butter', quantity: 8, cost: 15, icon: '🧈' },\n    { name: 'Chocolate Chips', quantity: 6, cost: 20, icon: '🍫' },\n    { name: 'Vanilla', quantity: 5, cost: 25, icon: '🌿' },\n    { name: 'Salt', quantity: 10, cost: 3, icon: '🧂' }\n  ])\n\n  const [orders, setOrders] = useState<OrderData[]>([\n    {\n      id: '1',\n      customerName: 'Alice Johnson',\n      items: ['Chocolate Chip Cookies'],\n      timeLimit: 300,\n      reward: 25,\n      status: 'pending',\n      difficulty: 1\n    }\n  ])\n\n  const [achievements, setAchievements] = useState<Achievement[]>(ACHIEVEMENTS)\n  const [skills, setSkills] = useState<SkillTree[]>(SKILL_TREE)\n  const [levelUpRewards, setLevelUpRewards] = useState<LevelReward[]>([])\n  const [showLevelUp, setShowLevelUp] = useState(false)\n\n  const [automationSettings, setAutomationSettings] = useState<AutomationSettings>({\n    enabled: false,\n    autoStart: false,\n    preferredRecipes: [],\n    maxConcurrentJobs: 2,\n    priorityMode: 'efficiency',\n    ingredientThreshold: 5\n  })\n\n  const [automationJobs, setAutomationJobs] = useState<AutomationJob[]>([])\n  const [conveyorBelts, setConveyorBelts] = useState<ConveyorBelt[]>([])\n\n  // Equipment timer effect\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setEquipment(prev => prev.map(eq => {\n        if (eq.isActive && eq.timeRemaining && eq.timeRemaining > 0) {\n          return { ...eq, timeRemaining: eq.timeRemaining - 1 }\n        } else if (eq.isActive && eq.timeRemaining === 0) {\n          // Baking completed - could add notification here\n          return { ...eq, isActive: false, timeRemaining: undefined, currentRecipe: undefined }\n        }\n        return eq\n      }))\n    }, 1000)\n\n    return () => clearInterval(interval)\n  }, [])\n\n  // Order timer effect\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setOrders(prev => prev.map(order => {\n        if ((order.status === 'accepted' || order.status === 'in_progress') && order.timeLimit > 0) {\n          const newTimeLimit = order.timeLimit - 1\n          if (newTimeLimit === 0) {\n            return { ...order, status: 'failed', timeLimit: 0 }\n          }\n          return { ...order, timeLimit: newTimeLimit }\n        }\n        return order\n      }))\n    }, 1000)\n\n    return () => clearInterval(interval)\n  }, [])\n\n  const updatePlayer = (updates: Partial<Player>) => {\n    setPlayer(prev => ({ ...prev, ...updates }))\n  }\n\n  const updateEquipment = (equipmentId: string, updates: Partial<EquipmentData>) => {\n    setEquipment(prev => prev.map(eq => \n      eq.id === equipmentId ? { ...eq, ...updates } : eq\n    ))\n  }\n\n  const addExperience = (amount: number) => {\n    setPlayer(prev => {\n      const newTotalExp = prev.experience + amount\n      const levelData = getLevelFromExperience(newTotalExp)\n      const leveledUp = levelData.level > prev.level\n\n      if (leveledUp) {\n        setLevelUpRewards(levelData.rewards)\n        setShowLevelUp(true)\n\n        // Add skill points for level up\n        const skillPointsGained = levelData.level % 2 === 0 ? 1 : 0\n\n        return {\n          ...prev,\n          level: levelData.level,\n          experience: newTotalExp,\n          maxExperience: levelData.experienceRequired,\n          skillPoints: prev.skillPoints + skillPointsGained\n        }\n      }\n\n      return {\n        ...prev,\n        experience: newTotalExp,\n        maxExperience: levelData.experienceRequired\n      }\n    })\n  }\n\n  const addMoney = (amount: number) => {\n    setPlayer(prev => ({\n      ...prev,\n      money: prev.money + amount,\n      totalMoneyEarned: prev.totalMoneyEarned + amount\n    }))\n  }\n\n  const spendMoney = (amount: number): boolean => {\n    if (player.money >= amount) {\n      setPlayer(prev => ({ ...prev, money: prev.money - amount }))\n      return true\n    }\n    return false\n  }\n\n  const useIngredient = (name: string, quantity: number): boolean => {\n    const ingredient = inventory.find(ing => ing.name === name)\n    if (ingredient && ingredient.quantity >= quantity) {\n      setInventory(prev => prev.map(ing => \n        ing.name === name \n          ? { ...ing, quantity: ing.quantity - quantity }\n          : ing\n      ))\n      return true\n    }\n    return false\n  }\n\n  const addIngredient = (name: string, quantity: number) => {\n    setInventory(prev => prev.map(ing => \n      ing.name === name \n        ? { ...ing, quantity: ing.quantity + quantity }\n        : ing\n    ))\n  }\n\n  const acceptOrder = (orderId: string) => {\n    setOrders(prev => prev.map(order => \n      order.id === orderId \n        ? { ...order, status: 'accepted' }\n        : order\n    ))\n  }\n\n  const completeOrder = (orderId: string) => {\n    const order = orders.find(o => o.id === orderId)\n    if (order) {\n      // Check if player has required ingredients\n      const canComplete = order.items.every(itemName => {\n        const recipe = getRecipeById(itemName.toLowerCase().replace(/\\s+/g, '_'))\n        return recipe ? canCraftRecipe(recipe, inventory) : false\n      })\n\n      if (canComplete) {\n        // Consume ingredients\n        order.items.forEach(itemName => {\n          const recipe = getRecipeById(itemName.toLowerCase().replace(/\\s+/g, '_'))\n          if (recipe) {\n            recipe.ingredients.forEach(ingredient => {\n              useIngredient(ingredient.name, ingredient.quantity)\n            })\n          }\n        })\n\n        // Complete order\n        setOrders(prev => prev.map(o =>\n          o.id === orderId\n            ? { ...o, status: 'completed' }\n            : o\n        ))\n\n        // Calculate rewards\n        const timeBonus = order.timeLimit > 60 // Bonus if completed with time to spare\n        const expReward = calculateExperienceReward(order.difficulty, timeBonus)\n\n        addMoney(order.reward)\n        addExperience(expReward)\n      }\n    }\n  }\n\n  const declineOrder = (orderId: string) => {\n    setOrders(prev => prev.filter(order => order.id !== orderId))\n  }\n\n  const generateNewOrder = () => {\n    const newOrder = generateRandomOrder(player.level)\n    setOrders(prev => [...prev, newOrder])\n  }\n\n  const upgradeSkill = (skillId: string) => {\n    const skill = skills.find(s => s.id === skillId)\n    if (!skill || skill.level >= skill.maxLevel || player.skillPoints < skill.cost) {\n      return\n    }\n\n    setSkills(prev => prev.map(s =>\n      s.id === skillId\n        ? { ...s, level: s.level + 1 }\n        : s\n    ))\n\n    setPlayer(prev => ({\n      ...prev,\n      skillPoints: prev.skillPoints - skill.cost\n    }))\n  }\n\n  const checkAchievements = () => {\n    setAchievements(prev => prev.map(achievement => {\n      if (achievement.completed) return achievement\n\n      const requirement = achievement.requirements[0]\n      let current = 0\n\n      switch (requirement.type) {\n        case 'orders_completed':\n          current = player.totalOrdersCompleted\n          break\n        case 'money_earned':\n          current = player.totalMoneyEarned\n          break\n        case 'recipes_unlocked':\n          current = player.unlockedRecipes.length\n          break\n        case 'level_reached':\n          current = player.level\n          break\n        case 'items_baked':\n          current = player.totalItemsBaked\n          break\n      }\n\n      const completed = current >= requirement.target\n\n      return {\n        ...achievement,\n        requirements: [{ ...requirement, current }],\n        completed\n      }\n    }))\n  }\n\n  const dismissLevelUp = () => {\n    setShowLevelUp(false)\n    setLevelUpRewards([])\n  }\n\n  const updateAutomationSettings = (updates: Partial<AutomationSettings>) => {\n    setAutomationSettings(prev => ({ ...prev, ...updates }))\n  }\n\n  const purchaseAutomationUpgrade = (upgradeId: string) => {\n    const upgrade = AUTOMATION_UPGRADES.find(u => u.id === upgradeId)\n    if (!upgrade || player.money < upgrade.cost) return\n\n    if (spendMoney(upgrade.cost)) {\n      setPlayer(prev => ({\n        ...prev,\n        automationUpgrades: [...prev.automationUpgrades, upgradeId]\n      }))\n    }\n  }\n\n  const startAutomationJob = (equipmentId: string) => {\n    if (!automationSettings.enabled) return\n\n    const targetEquipment = equipment.find(eq => eq.id === equipmentId)\n    if (!targetEquipment || targetEquipment.isActive || targetEquipment.automationLevel === 0) return\n\n    const availableRecipes = getAvailableRecipes(player.level)\n    const optimalRecipeId = selectOptimalRecipe(\n      availableRecipes,\n      inventory,\n      automationSettings.priorityMode,\n      orders\n    )\n\n    if (!optimalRecipeId) return\n\n    const recipe = getRecipeById(optimalRecipeId)\n    if (!recipe || !canCraftRecipe(recipe, inventory)) return\n\n    const efficiency = calculateAutomationEfficiency(\n      targetEquipment.efficiency,\n      targetEquipment.automationLevel,\n      player.automationUpgrades\n    )\n\n    const job = generateAutomationJob(equipmentId, optimalRecipeId, recipe, efficiency)\n\n    // Start the job\n    setAutomationJobs(prev => [...prev, { ...job, status: 'running' }])\n    updateEquipment(equipmentId, {\n      isActive: true,\n      timeRemaining: job.duration,\n      currentRecipe: recipe.name\n    })\n\n    // Consume ingredients\n    job.ingredients.forEach(ingredient => {\n      useIngredient(ingredient.name, ingredient.quantity)\n    })\n  }\n\n  return (\n    <GameContext.Provider value={{\n      player,\n      equipment,\n      inventory,\n      orders,\n      achievements,\n      skills,\n      levelUpRewards,\n      showLevelUp,\n      automationSettings,\n      automationJobs,\n      conveyorBelts,\n      updatePlayer,\n      updateEquipment,\n      addExperience,\n      addMoney,\n      spendMoney,\n      useIngredient,\n      addIngredient,\n      acceptOrder,\n      completeOrder,\n      declineOrder,\n      generateNewOrder,\n      upgradeSkill,\n      checkAchievements,\n      dismissLevelUp,\n      updateAutomationSettings,\n      purchaseAutomationUpgrade,\n      startAutomationJob\n    }}>\n      {children}\n    </GameContext.Provider>\n  )\n}\n\nexport function useGame() {\n  const context = useContext(GameContext)\n  if (context === undefined) {\n    throw new Error('useGame must be used within a GameProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAGA;AACA;AAQA;AAdA;;;;;;AA4EA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAE/D,MAAM,UAAU;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,iBAAiB;IACrB;IAAiB;IAAa;IAAe;IAC7C;IAAc;IAAgB;IAAgB;IAC9C;IAAc;IAAiB;IAAe;CAC/C;AAEM,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;QAC3C,OAAO;QACP,YAAY;QACZ,OAAO;QACP,eAAe;QACf,aAAa;QACb,kBAAkB;QAClB,sBAAsB;QACtB,iBAAiB;QACjB,iBAAiB;YAAC;YAA0B;SAAkB;QAC9D,oBAAoB,EAAE;IACxB;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QAC1D;YAAE,IAAI;YAAS,MAAM;YAAc,MAAM;YAAQ,UAAU;YAAO,OAAO;YAAG,YAAY;YAAK,iBAAiB;QAAE;QAChH;YAAE,IAAI;YAAU,MAAM;YAAc,MAAM;YAAS,UAAU;YAAO,OAAO;YAAG,YAAY;YAAK,iBAAiB;QAAE;QAClH;YAAE,IAAI;YAAY,MAAM;YAAgB,MAAM;YAAW,UAAU;YAAO,OAAO;YAAG,YAAY;YAAK,iBAAiB;QAAE;KACzH;IAED,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QACvD;YAAE,MAAM;YAAS,UAAU;YAAI,MAAM;YAAG,MAAM;QAAK;QACnD;YAAE,MAAM;YAAS,UAAU;YAAI,MAAM;YAAG,MAAM;QAAK;QACnD;YAAE,MAAM;YAAQ,UAAU;YAAI,MAAM;YAAI,MAAM;QAAK;QACnD;YAAE,MAAM;YAAU,UAAU;YAAG,MAAM;YAAI,MAAM;QAAK;QACpD;YAAE,MAAM;YAAmB,UAAU;YAAG,MAAM;YAAI,MAAM;QAAK;QAC7D;YAAE,MAAM;YAAW,UAAU;YAAG,MAAM;YAAI,MAAM;QAAK;QACrD;YAAE,MAAM;YAAQ,UAAU;YAAI,MAAM;YAAG,MAAM;QAAK;KACnD;IAED,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAChD;YACE,IAAI;YACJ,cAAc;YACd,OAAO;gBAAC;aAAyB;YACjC,WAAW;YACX,QAAQ;YACR,QAAQ;YACR,YAAY;QACd;KACD;IAED,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,+HAAA,CAAA,eAAY;IAC5E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,+HAAA,CAAA,aAAU;IAC5D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;QAC/E,SAAS;QACT,WAAW;QACX,kBAAkB,EAAE;QACpB,mBAAmB;QACnB,cAAc;QACd,qBAAqB;IACvB;IAEA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;oBAC5B,IAAI,GAAG,QAAQ,IAAI,GAAG,aAAa,IAAI,GAAG,aAAa,GAAG,GAAG;wBAC3D,OAAO;4BAAE,GAAG,EAAE;4BAAE,eAAe,GAAG,aAAa,GAAG;wBAAE;oBACtD,OAAO,IAAI,GAAG,QAAQ,IAAI,GAAG,aAAa,KAAK,GAAG;wBAChD,iDAAiD;wBACjD,OAAO;4BAAE,GAAG,EAAE;4BAAE,UAAU;4BAAO,eAAe;4BAAW,eAAe;wBAAU;oBACtF;oBACA,OAAO;gBACT;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;oBACzB,IAAI,CAAC,MAAM,MAAM,KAAK,cAAc,MAAM,MAAM,KAAK,aAAa,KAAK,MAAM,SAAS,GAAG,GAAG;wBAC1F,MAAM,eAAe,MAAM,SAAS,GAAG;wBACvC,IAAI,iBAAiB,GAAG;4BACtB,OAAO;gCAAE,GAAG,KAAK;gCAAE,QAAQ;gCAAU,WAAW;4BAAE;wBACpD;wBACA,OAAO;4BAAE,GAAG,KAAK;4BAAE,WAAW;wBAAa;oBAC7C;oBACA,OAAO;gBACT;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC,CAAC;IAC5C;IAEA,MAAM,kBAAkB,CAAC,aAAqB;QAC5C,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,KAC5B,GAAG,EAAE,KAAK,cAAc;oBAAE,GAAG,EAAE;oBAAE,GAAG,OAAO;gBAAC,IAAI;IAEpD;IAEA,MAAM,gBAAgB,CAAC;QACrB,UAAU,CAAA;YACR,MAAM,cAAc,KAAK,UAAU,GAAG;YACtC,MAAM,YAAY,CAAA,GAAA,+HAAA,CAAA,yBAAsB,AAAD,EAAE;YACzC,MAAM,YAAY,UAAU,KAAK,GAAG,KAAK,KAAK;YAE9C,IAAI,WAAW;gBACb,kBAAkB,UAAU,OAAO;gBACnC,eAAe;gBAEf,gCAAgC;gBAChC,MAAM,oBAAoB,UAAU,KAAK,GAAG,MAAM,IAAI,IAAI;gBAE1D,OAAO;oBACL,GAAG,IAAI;oBACP,OAAO,UAAU,KAAK;oBACtB,YAAY;oBACZ,eAAe,UAAU,kBAAkB;oBAC3C,aAAa,KAAK,WAAW,GAAG;gBAClC;YACF;YAEA,OAAO;gBACL,GAAG,IAAI;gBACP,YAAY;gBACZ,eAAe,UAAU,kBAAkB;YAC7C;QACF;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,UAAU,CAAA,OAAQ,CAAC;gBACjB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,GAAG;gBACpB,kBAAkB,KAAK,gBAAgB,GAAG;YAC5C,CAAC;IACH;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,OAAO,KAAK,IAAI,QAAQ;YAC1B,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO,KAAK,KAAK,GAAG;gBAAO,CAAC;YAC1D,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC,MAAc;QACnC,MAAM,aAAa,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QACtD,IAAI,cAAc,WAAW,QAAQ,IAAI,UAAU;YACjD,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MAC5B,IAAI,IAAI,KAAK,OACT;wBAAE,GAAG,GAAG;wBAAE,UAAU,IAAI,QAAQ,GAAG;oBAAS,IAC5C;YAEN,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC,MAAc;QACnC,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MAC5B,IAAI,IAAI,KAAK,OACT;oBAAE,GAAG,GAAG;oBAAE,UAAU,IAAI,QAAQ,GAAG;gBAAS,IAC5C;IAER;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,QACzB,MAAM,EAAE,KAAK,UACT;oBAAE,GAAG,KAAK;oBAAE,QAAQ;gBAAW,IAC/B;IAER;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACxC,IAAI,OAAO;YACT,2CAA2C;YAC3C,MAAM,cAAc,MAAM,KAAK,CAAC,KAAK,CAAC,CAAA;gBACpC,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,QAAQ;gBACpE,OAAO,SAAS,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,aAAa;YACtD;YAEA,IAAI,aAAa;gBACf,sBAAsB;gBACtB,MAAM,KAAK,CAAC,OAAO,CAAC,CAAA;oBAClB,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,QAAQ;oBACpE,IAAI,QAAQ;wBACV,OAAO,WAAW,CAAC,OAAO,CAAC,CAAA;4BACzB,cAAc,WAAW,IAAI,EAAE,WAAW,QAAQ;wBACpD;oBACF;gBACF;gBAEA,iBAAiB;gBACjB,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACzB,EAAE,EAAE,KAAK,UACL;4BAAE,GAAG,CAAC;4BAAE,QAAQ;wBAAY,IAC5B;gBAGN,oBAAoB;gBACpB,MAAM,YAAY,MAAM,SAAS,GAAG,GAAG,wCAAwC;;gBAC/E,MAAM,YAAY,CAAA,GAAA,uHAAA,CAAA,4BAAyB,AAAD,EAAE,MAAM,UAAU,EAAE;gBAE9D,SAAS,MAAM,MAAM;gBACrB,cAAc;YAChB;QACF;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,mBAAmB;QACvB,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,KAAK;QACjD,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAS;IACvC;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACxC,IAAI,CAAC,SAAS,MAAM,KAAK,IAAI,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,MAAM,IAAI,EAAE;YAC9E;QACF;QAEA,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACzB,EAAE,EAAE,KAAK,UACL;oBAAE,GAAG,CAAC;oBAAE,OAAO,EAAE,KAAK,GAAG;gBAAE,IAC3B;QAGN,UAAU,CAAA,OAAQ,CAAC;gBACjB,GAAG,IAAI;gBACP,aAAa,KAAK,WAAW,GAAG,MAAM,IAAI;YAC5C,CAAC;IACH;IAEA,MAAM,oBAAoB;QACxB,gBAAgB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;gBAC/B,IAAI,YAAY,SAAS,EAAE,OAAO;gBAElC,MAAM,cAAc,YAAY,YAAY,CAAC,EAAE;gBAC/C,IAAI,UAAU;gBAEd,OAAQ,YAAY,IAAI;oBACtB,KAAK;wBACH,UAAU,OAAO,oBAAoB;wBACrC;oBACF,KAAK;wBACH,UAAU,OAAO,gBAAgB;wBACjC;oBACF,KAAK;wBACH,UAAU,OAAO,eAAe,CAAC,MAAM;wBACvC;oBACF,KAAK;wBACH,UAAU,OAAO,KAAK;wBACtB;oBACF,KAAK;wBACH,UAAU,OAAO,eAAe;wBAChC;gBACJ;gBAEA,MAAM,YAAY,WAAW,YAAY,MAAM;gBAE/C,OAAO;oBACL,GAAG,WAAW;oBACd,cAAc;wBAAC;4BAAE,GAAG,WAAW;4BAAE;wBAAQ;qBAAE;oBAC3C;gBACF;YACF;IACF;IAEA,MAAM,iBAAiB;QACrB,eAAe;QACf,kBAAkB,EAAE;IACtB;IAEA,MAAM,2BAA2B,CAAC;QAChC,sBAAsB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC,CAAC;IACxD;IAEA,MAAM,4BAA4B,CAAC;QACjC,MAAM,UAAU,8HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACvD,IAAI,CAAC,WAAW,OAAO,KAAK,GAAG,QAAQ,IAAI,EAAE;QAE7C,IAAI,WAAW,QAAQ,IAAI,GAAG;YAC5B,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,oBAAoB;2BAAI,KAAK,kBAAkB;wBAAE;qBAAU;gBAC7D,CAAC;QACH;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAEjC,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK;QACvD,IAAI,CAAC,mBAAmB,gBAAgB,QAAQ,IAAI,gBAAgB,eAAe,KAAK,GAAG;QAE3F,MAAM,mBAAmB,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,KAAK;QACzD,MAAM,kBAAkB,CAAA,GAAA,8HAAA,CAAA,sBAAmB,AAAD,EACxC,kBACA,WACA,mBAAmB,YAAY,EAC/B;QAGF,IAAI,CAAC,iBAAiB;QAEtB,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE;QAC7B,IAAI,CAAC,UAAU,CAAC,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,YAAY;QAEnD,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,gCAA6B,AAAD,EAC7C,gBAAgB,UAAU,EAC1B,gBAAgB,eAAe,EAC/B,OAAO,kBAAkB;QAG3B,MAAM,MAAM,CAAA,GAAA,8HAAA,CAAA,wBAAqB,AAAD,EAAE,aAAa,iBAAiB,QAAQ;QAExE,gBAAgB;QAChB,kBAAkB,CAAA,OAAQ;mBAAI;gBAAM;oBAAE,GAAG,GAAG;oBAAE,QAAQ;gBAAU;aAAE;QAClE,gBAAgB,aAAa;YAC3B,UAAU;YACV,eAAe,IAAI,QAAQ;YAC3B,eAAe,OAAO,IAAI;QAC5B;QAEA,sBAAsB;QACtB,IAAI,WAAW,CAAC,OAAO,CAAC,CAAA;YACtB,cAAc,WAAW,IAAI,EAAE,WAAW,QAAQ;QACpD;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'danger' | 'success'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport const Button: React.FC<ButtonProps> = ({\n  variant = 'primary',\n  size = 'md',\n  className = '',\n  children,\n  ...props\n}) => {\n  const baseClasses = 'font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2'\n\n  const variantClasses = {\n    primary: 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500',\n    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500',\n    danger: 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500',\n    success: 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500',\n  }\n\n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-base',\n    lg: 'px-6 py-3 text-lg',\n  }\n\n  const combinedClasses = [\n    baseClasses,\n    variantClasses[variant],\n    sizeClasses[size],\n    className\n  ].join(' ')\n\n  return (\n    <button\n      className={combinedClasses}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAQO,MAAM,SAAgC,CAAC,EAC5C,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB;QACA,cAAc,CAAC,QAAQ;QACvB,WAAW,CAAC,KAAK;QACjB;KACD,CAAC,IAAI,CAAC;IAEP,qBACE,8OAAC;QACC,WAAW;QACV,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1297, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/Equipment.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useLanguage } from '@/contexts/LanguageContext'\n\nexport interface EquipmentData {\n  id: string\n  name: string\n  type: 'oven' | 'mixer' | 'counter' | 'auto_oven' | 'auto_mixer' | 'conveyor'\n  isActive: boolean\n  timeRemaining?: number\n  currentRecipe?: string\n  level: number\n  efficiency: number\n  automationLevel: number\n  isAutomated?: boolean\n  queuedRecipes?: string[]\n}\n\ninterface EquipmentProps {\n  equipment: EquipmentData\n  onClick: (equipmentId: string, equipmentName: string) => void\n}\n\nexport function Equipment({ equipment, onClick }: EquipmentProps) {\n  const { t } = useLanguage()\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const getEquipmentIcon = (type: string) => {\n    switch (type) {\n      case 'oven': return '🔥'\n      case 'mixer': return '🥄'\n      case 'counter': return '🍽️'\n      default: return '⚙️'\n    }\n  }\n\n  const getStatusColor = () => {\n    if (equipment.isActive) {\n      return 'border-green-400 bg-green-50'\n    }\n    return 'border-gray-200 bg-gray-50 hover:border-orange-300 hover:bg-orange-50'\n  }\n\n  return (\n    <div\n      className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${getStatusColor()}`}\n      onClick={() => !equipment.isActive && onClick(equipment.id, equipment.name)}\n    >\n      <div className=\"text-center\">\n        <div className=\"text-3xl mb-2\">\n          {getEquipmentIcon(equipment.type)}\n        </div>\n        <h3 className=\"font-medium text-gray-800\">{equipment.name}</h3>\n        <div className=\"text-xs text-gray-500\">Level {equipment.level}</div>\n        \n        {equipment.isActive && equipment.timeRemaining ? (\n          <div className=\"mt-2\">\n            <div className=\"text-sm text-green-600\">\n              {t('kitchen.making', { recipe: equipment.currentRecipe || '' })}\n            </div>\n            <div className=\"text-lg font-mono text-green-700\">\n              {formatTime(equipment.timeRemaining)}\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-2 mt-2\">\n              <div \n                className=\"bg-green-500 h-2 rounded-full transition-all duration-1000\"\n                style={{ \n                  width: `${100 - (equipment.timeRemaining / 60) * 100}%` \n                }}\n              ></div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"text-sm text-gray-500 mt-2\">\n            {equipment.isActive ? 'Busy' : t('kitchen.clickToUse')}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAwBO,SAAS,UAAU,EAAE,SAAS,EAAE,OAAO,EAAkB;IAC9D,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IAExB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,UAAU,QAAQ,EAAE;YACtB,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,sDAAsD,EAAE,kBAAkB;QACtF,SAAS,IAAM,CAAC,UAAU,QAAQ,IAAI,QAAQ,UAAU,EAAE,EAAE,UAAU,IAAI;kBAE1E,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,UAAU,IAAI;;;;;;8BAElC,8OAAC;oBAAG,WAAU;8BAA6B,UAAU,IAAI;;;;;;8BACzD,8OAAC;oBAAI,WAAU;;wBAAwB;wBAAO,UAAU,KAAK;;;;;;;gBAE5D,UAAU,QAAQ,IAAI,UAAU,aAAa,iBAC5C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,EAAE,kBAAkB;gCAAE,QAAQ,UAAU,aAAa,IAAI;4BAAG;;;;;;sCAE/D,8OAAC;4BAAI,WAAU;sCACZ,WAAW,UAAU,aAAa;;;;;;sCAErC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO,GAAG,MAAM,AAAC,UAAU,aAAa,GAAG,KAAM,IAAI,CAAC,CAAC;gCACzD;;;;;;;;;;;;;;;;yCAKN,8OAAC;oBAAI,WAAU;8BACZ,UAAU,QAAQ,GAAG,SAAS,EAAE;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 1430, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/Order.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { useLanguage } from '@/contexts/LanguageContext'\n\nexport interface OrderData {\n  id: string\n  customerName: string\n  items: string[]\n  timeLimit: number\n  reward: number\n  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'failed'\n  difficulty: number\n}\n\ninterface OrderProps {\n  order: OrderData\n  onAccept: (orderId: string) => void\n  onDecline: (orderId: string) => void\n  onComplete?: (orderId: string) => void\n}\n\nexport function Order({ order, onAccept, onDecline, onComplete }: OrderProps) {\n  const { t } = useLanguage()\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const getStatusStyle = () => {\n    switch (order.status) {\n      case 'pending':\n        return 'border-yellow-300 bg-yellow-50'\n      case 'accepted':\n      case 'in_progress':\n        return 'border-blue-300 bg-blue-50'\n      case 'completed':\n        return 'border-green-300 bg-green-50'\n      case 'failed':\n        return 'border-red-300 bg-red-50'\n      default:\n        return 'border-gray-300 bg-gray-50'\n    }\n  }\n\n  const getDifficultyStars = () => {\n    return '⭐'.repeat(order.difficulty) + '☆'.repeat(5 - order.difficulty)\n  }\n\n  const getCustomerAvatar = () => {\n    const avatars = ['👩', '👨', '👵', '👴', '👧', '👦']\n    const index = order.customerName.length % avatars.length\n    return avatars[index]\n  }\n\n  return (\n    <div className={`p-4 rounded-lg border ${getStatusStyle()}`}>\n      <div className=\"flex items-center justify-between mb-2\">\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-lg\">{getCustomerAvatar()}</span>\n          <h3 className=\"font-medium text-gray-800\">{order.customerName}</h3>\n        </div>\n        <span className=\"text-sm font-semibold text-green-600\">{t('orders.reward', { amount: order.reward.toString() })}</span>\n      </div>\n      \n      <div className=\"text-sm text-gray-600 mb-2\">\n        {order.items.map((item, index) => (\n          <div key={index} className=\"flex items-center space-x-1\">\n            <span>🧁</span>\n            <span>{item}</span>\n          </div>\n        ))}\n      </div>\n      \n      <div className=\"flex justify-between items-center mb-3\">\n        <div className=\"text-xs text-gray-500\">\n          ⏰ {t('orders.timeLimit', { time: formatTime(order.timeLimit) })}\n        </div>\n        <div className=\"text-xs\" title={`Difficulty: ${order.difficulty}/5`}>\n          {getDifficultyStars()}\n        </div>\n      </div>\n\n      {order.status === 'pending' && (\n        <div className=\"flex space-x-2\">\n          <Button\n            size=\"sm\"\n            variant=\"success\"\n            onClick={() => onAccept(order.id)}\n            className=\"flex-1\"\n          >\n            ✅ {t('orders.accept')}\n          </Button>\n          <Button \n            size=\"sm\" \n            variant=\"danger\" \n            onClick={() => onDecline(order.id)}\n            className=\"flex-1\"\n          >\n            ❌ {t('orders.decline')}\n          </Button>\n        </div>\n      )}\n      \n      {order.status === 'accepted' && (\n        <div className=\"text-center\">\n          <div className=\"text-blue-600 text-sm font-medium mb-2\">\n            📋 Order Accepted\n          </div>\n          <Button\n            size=\"sm\"\n            variant=\"primary\"\n            onClick={() => onComplete && onComplete(order.id)}\n            className=\"w-full\"\n          >\n            🎯 {t('orders.complete')}\n          </Button>\n        </div>\n      )}\n      \n      {order.status === 'in_progress' && (\n        <div className=\"text-center text-orange-600 text-sm font-medium\">\n          🔄 {t('orders.inProgress')}\n        </div>\n      )}\n      \n      {order.status === 'completed' && (\n        <div className=\"text-center text-green-600 text-sm font-medium\">\n          ✅ Completed!\n        </div>\n      )}\n      \n      {order.status === 'failed' && (\n        <div className=\"text-center text-red-600 text-sm font-medium\">\n          ❌ Failed\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAsBO,SAAS,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAc;IAC1E,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IAExB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,MAAM,iBAAiB;QACrB,OAAQ,MAAM,MAAM;YAClB,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,OAAO,IAAI,MAAM,CAAC,MAAM,UAAU,IAAI,IAAI,MAAM,CAAC,IAAI,MAAM,UAAU;IACvE;IAEA,MAAM,oBAAoB;QACxB,MAAM,UAAU;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;SAAK;QACpD,MAAM,QAAQ,MAAM,YAAY,CAAC,MAAM,GAAG,QAAQ,MAAM;QACxD,OAAO,OAAO,CAAC,MAAM;IACvB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,sBAAsB,EAAE,kBAAkB;;0BACzD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAW;;;;;;0CAC3B,8OAAC;gCAAG,WAAU;0CAA6B,MAAM,YAAY;;;;;;;;;;;;kCAE/D,8OAAC;wBAAK,WAAU;kCAAwC,EAAE,iBAAiB;4BAAE,QAAQ,MAAM,MAAM,CAAC,QAAQ;wBAAG;;;;;;;;;;;;0BAG/G,8OAAC;gBAAI,WAAU;0BACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAM;;;;;;;uBAFC;;;;;;;;;;0BAOd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BAAwB;4BAClC,EAAE,oBAAoB;gCAAE,MAAM,WAAW,MAAM,SAAS;4BAAE;;;;;;;kCAE/D,8OAAC;wBAAI,WAAU;wBAAU,OAAO,CAAC,YAAY,EAAE,MAAM,UAAU,CAAC,EAAE,CAAC;kCAChE;;;;;;;;;;;;YAIJ,MAAM,MAAM,KAAK,2BAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,SAAS,MAAM,EAAE;wBAChC,WAAU;;4BACX;4BACI,EAAE;;;;;;;kCAEP,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,UAAU,MAAM,EAAE;wBACjC,WAAU;;4BACX;4BACI,EAAE;;;;;;;;;;;;;YAKV,MAAM,MAAM,KAAK,4BAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAyC;;;;;;kCAGxD,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,cAAc,WAAW,MAAM,EAAE;wBAChD,WAAU;;4BACX;4BACK,EAAE;;;;;;;;;;;;;YAKX,MAAM,MAAM,KAAK,+BAChB,8OAAC;gBAAI,WAAU;;oBAAkD;oBAC3D,EAAE;;;;;;;YAIT,MAAM,MAAM,KAAK,6BAChB,8OAAC;gBAAI,WAAU;0BAAiD;;;;;;YAKjE,MAAM,MAAM,KAAK,0BAChB,8OAAC;gBAAI,WAAU;0BAA+C;;;;;;;;;;;;AAMtE", "debugId": null}}, {"offset": {"line": 1691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/RecipeModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Recipe, getAvailableRecipes } from '@/lib/gameLogic'\nimport { useGame } from '@/contexts/GameContext'\nimport { useLanguage } from '@/contexts/LanguageContext'\n\ninterface RecipeModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport function RecipeModal({ isOpen, onClose }: RecipeModalProps) {\n  const { player, inventory } = useGame()\n  const { t } = useLanguage()\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n  \n  if (!isOpen) return null\n\n  const availableRecipes = getAvailableRecipes(player.level)\n  const filteredRecipes = selectedCategory === 'all' \n    ? availableRecipes \n    : availableRecipes.filter(recipe => recipe.category === selectedCategory)\n\n  const canCraft = (recipe: Recipe) => {\n    return recipe.ingredients.every(ingredient => {\n      const inventoryItem = inventory.find(item => item.name === ingredient.name)\n      return inventoryItem && inventoryItem.quantity >= ingredient.quantity\n    })\n  }\n\n  const getDifficultyStars = (difficulty: number) => {\n    return '⭐'.repeat(difficulty) + '☆'.repeat(5 - difficulty)\n  }\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const categories = [\n    { id: 'all', name: t('recipes.all'), icon: '🍽️' },\n    { id: 'cookies', name: t('recipes.cookies'), icon: '🍪' },\n    { id: 'cakes', name: t('recipes.cakes'), icon: '🧁' },\n    { id: 'bread', name: t('recipes.bread'), icon: '🍞' },\n    { id: 'pastries', name: t('recipes.pastries'), icon: '🥐' }\n  ]\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-2xl font-bold text-orange-800\">{t('modal.recipes.title')}</h2>\n            <Button variant=\"secondary\" onClick={onClose}>\n              {t('game.close')}\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Category Filter */}\n          <div className=\"flex flex-wrap gap-2 mb-6\">\n            {categories.map(category => (\n              <Button\n                key={category.id}\n                variant={selectedCategory === category.id ? 'primary' : 'secondary'}\n                size=\"sm\"\n                onClick={() => setSelectedCategory(category.id)}\n              >\n                {category.icon} {category.name}\n              </Button>\n            ))}\n          </div>\n\n          {/* Recipes Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto\">\n            {filteredRecipes.map(recipe => (\n              <div\n                key={recipe.id}\n                className={`p-4 rounded-lg border-2 ${\n                  canCraft(recipe)\n                    ? 'border-green-300 bg-green-50'\n                    : 'border-gray-300 bg-gray-50'\n                }`}\n              >\n                <div className=\"flex justify-between items-start mb-2\">\n                  <h3 className=\"font-semibold text-gray-800\">{recipe.name}</h3>\n                  <span className=\"text-sm text-green-600\">${recipe.basePrice}</span>\n                </div>\n\n                <div className=\"text-xs text-gray-500 mb-2\">\n                  {getDifficultyStars(recipe.difficulty)} • ⏱️ {formatTime(recipe.bakingTime)}\n                </div>\n\n                <div className=\"space-y-1 mb-3\">\n                  <div className=\"text-sm font-medium text-gray-700\">{t('recipes.ingredients')}</div>\n                  {recipe.ingredients.map((ingredient, index) => {\n                    const inventoryItem = inventory.find(item => item.name === ingredient.name)\n                    const hasEnough = inventoryItem && inventoryItem.quantity >= ingredient.quantity\n\n                    return (\n                      <div\n                        key={index}\n                        className={`text-xs flex justify-between ${\n                          hasEnough ? 'text-green-600' : 'text-red-600'\n                        }`}\n                      >\n                        <span>{ingredient.name}</span>\n                        <span>\n                          {ingredient.quantity}\n                          {inventoryItem && (\n                            <span className=\"ml-1\">\n                              ({inventoryItem.quantity} available)\n                            </span>\n                          )}\n                        </span>\n                      </div>\n                    )\n                  })}\n                </div>\n\n                <div className=\"text-xs text-gray-500\">\n                  {t('recipes.unlockLevel', { level: recipe.unlockLevel.toString() })}\n                </div>\n\n                {canCraft(recipe) && (\n                  <div className=\"mt-2\">\n                    <Button size=\"sm\" variant=\"success\" className=\"w-full\">\n                      {t('recipes.canCraft')}\n                    </Button>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n\n          {filteredRecipes.length === 0 && (\n            <div className=\"text-center py-8 text-gray-500\">\n              <div className=\"text-4xl mb-2\">📝</div>\n              <p>{t('recipes.noRecipes')}</p>\n              <p className=\"text-sm\">{t('recipes.levelUpToUnlock')}</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAaO,SAAS,YAAY,EAAE,MAAM,EAAE,OAAO,EAAoB;IAC/D,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACpC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IACxB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,mBAAmB,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,KAAK;IACzD,MAAM,kBAAkB,qBAAqB,QACzC,mBACA,iBAAiB,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,KAAK;IAE1D,MAAM,WAAW,CAAC;QAChB,OAAO,OAAO,WAAW,CAAC,KAAK,CAAC,CAAA;YAC9B,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;YAC1E,OAAO,iBAAiB,cAAc,QAAQ,IAAI,WAAW,QAAQ;QACvE;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAO,IAAI,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI;IACjD;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM,EAAE;YAAgB,MAAM;QAAM;QACjD;YAAE,IAAI;YAAW,MAAM,EAAE;YAAoB,MAAM;QAAK;QACxD;YAAE,IAAI;YAAS,MAAM,EAAE;YAAkB,MAAM;QAAK;QACpD;YAAE,IAAI;YAAS,MAAM,EAAE;YAAkB,MAAM;QAAK;QACpD;YAAE,IAAI;YAAY,MAAM,EAAE;YAAqB,MAAM;QAAK;KAC3D;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC,EAAE;;;;;;0CACtD,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,SAAS;0CAClC,EAAE;;;;;;;;;;;;;;;;;8BAKT,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC,kIAAA,CAAA,SAAM;oCAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;oCACxD,MAAK;oCACL,SAAS,IAAM,oBAAoB,SAAS,EAAE;;wCAE7C,SAAS,IAAI;wCAAC;wCAAE,SAAS,IAAI;;mCALzB,SAAS,EAAE;;;;;;;;;;sCAWtB,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAA,uBACnB,8OAAC;oCAEC,WAAW,CAAC,wBAAwB,EAClC,SAAS,UACL,iCACA,8BACJ;;sDAEF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA+B,OAAO,IAAI;;;;;;8DACxD,8OAAC;oDAAK,WAAU;;wDAAyB;wDAAE,OAAO,SAAS;;;;;;;;;;;;;sDAG7D,8OAAC;4CAAI,WAAU;;gDACZ,mBAAmB,OAAO,UAAU;gDAAE;gDAAO,WAAW,OAAO,UAAU;;;;;;;sDAG5E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAqC,EAAE;;;;;;gDACrD,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY;oDACnC,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;oDAC1E,MAAM,YAAY,iBAAiB,cAAc,QAAQ,IAAI,WAAW,QAAQ;oDAEhF,qBACE,8OAAC;wDAEC,WAAW,CAAC,6BAA6B,EACvC,YAAY,mBAAmB,gBAC/B;;0EAEF,8OAAC;0EAAM,WAAW,IAAI;;;;;;0EACtB,8OAAC;;oEACE,WAAW,QAAQ;oEACnB,+BACC,8OAAC;wEAAK,WAAU;;4EAAO;4EACnB,cAAc,QAAQ;4EAAC;;;;;;;;;;;;;;uDAV1B;;;;;gDAgBX;;;;;;;sDAGF,8OAAC;4CAAI,WAAU;sDACZ,EAAE,uBAAuB;gDAAE,OAAO,OAAO,WAAW,CAAC,QAAQ;4CAAG;;;;;;wCAGlE,SAAS,yBACR,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAQ;gDAAU,WAAU;0DAC3C,EAAE;;;;;;;;;;;;mCAlDJ,OAAO,EAAE;;;;;;;;;;wBA0DnB,gBAAgB,MAAM,KAAK,mBAC1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;8CAAG,EAAE;;;;;;8CACN,8OAAC;oCAAE,WAAU;8CAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC", "debugId": null}}, {"offset": {"line": 2012, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/ShopModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { useGame } from '@/contexts/GameContext'\nimport { useLanguage } from '@/contexts/LanguageContext'\n\ninterface ShopModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport function ShopModal({ isOpen, onClose }: ShopModalProps) {\n  const { player, inventory, spendMoney, addIngredient } = useGame()\n  const { t } = useLanguage()\n  const [quantities, setQuantities] = useState<Record<string, number>>({})\n\n  if (!isOpen) return null\n\n  const handleQuantityChange = (ingredientName: string, quantity: number) => {\n    setQuantities(prev => ({\n      ...prev,\n      [ingredientName]: Math.max(0, quantity)\n    }))\n  }\n\n  const buyIngredient = (ingredientName: string, cost: number) => {\n    const quantity = quantities[ingredientName] || 1\n    const totalCost = cost * quantity\n    \n    if (spendMoney(totalCost)) {\n      addIngredient(ingredientName, quantity)\n      setQuantities(prev => ({ ...prev, [ingredientName]: 0 }))\n    }\n  }\n\n  const getTotalCost = (ingredientName: string, cost: number) => {\n    const quantity = quantities[ingredientName] || 1\n    return cost * quantity\n  }\n\n  const canAfford = (ingredientName: string, cost: number) => {\n    return player.money >= getTotalCost(ingredientName, cost)\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-2xl font-bold text-orange-800\">{t('modal.shop.title')}</h2>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"bg-green-100 px-3 py-1 rounded-full\">\n                <span className=\"text-green-800 font-medium\">{t('ui.money', { amount: player.money.toString() })}</span>\n              </div>\n              <Button variant=\"secondary\" onClick={onClose}>\n                {t('game.close')}\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          <div className=\"space-y-4 max-h-[60vh] overflow-y-auto\">\n            {inventory.map(ingredient => {\n              const quantity = quantities[ingredient.name] || 1\n              const totalCost = getTotalCost(ingredient.name, ingredient.cost)\n              const affordable = canAfford(ingredient.name, ingredient.cost)\n\n              return (\n                <div\n                  key={ingredient.name}\n                  className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\"\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"text-2xl\">{ingredient.icon}</div>\n                    <div>\n                      <h3 className=\"font-medium text-gray-800\">{ingredient.name}</h3>\n                      <p className=\"text-sm text-gray-600\">\n                        {t('shop.currentStock', { quantity: ingredient.quantity.toString() })}\n                      </p>\n                      <p className=\"text-sm text-green-600\">\n                        {t('inventory.cost', { cost: ingredient.cost.toString() })}\n                      </p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"flex items-center space-x-2\">\n                      <Button\n                        size=\"sm\"\n                        variant=\"secondary\"\n                        onClick={() => handleQuantityChange(ingredient.name, quantity - 1)}\n                        disabled={quantity <= 1}\n                      >\n                        -\n                      </Button>\n                      <span className=\"w-12 text-center font-mono\">{quantity}</span>\n                      <Button\n                        size=\"sm\"\n                        variant=\"secondary\"\n                        onClick={() => handleQuantityChange(ingredient.name, quantity + 1)}\n                        disabled={!canAfford(ingredient.name, ingredient.cost) && quantity >= 1}\n                      >\n                        +\n                      </Button>\n                    </div>\n\n                    <div className=\"text-right min-w-[80px]\">\n                      <div className={`font-medium ${affordable ? 'text-green-600' : 'text-red-600'}`}>\n                        ${totalCost}\n                      </div>\n                      <Button\n                        size=\"sm\"\n                        variant={affordable ? 'success' : 'secondary'}\n                        onClick={() => buyIngredient(ingredient.name, ingredient.cost)}\n                        disabled={!affordable}\n                        className=\"mt-1\"\n                      >\n                        {affordable ? t('shop.buy') : t('shop.tooExpensive')}\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n              )\n            })}\n          </div>\n\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n            <h3 className=\"font-medium text-blue-800 mb-2\">{t('shop.tips.title')}</h3>\n            <ul className=\"text-sm text-blue-700 space-y-1\">\n              <li>{t('shop.tips.bulk')}</li>\n              <li>{t('shop.tips.stock')}</li>\n              <li>{t('shop.tips.rare')}</li>\n              <li>{t('shop.tips.prices')}</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS,UAAU,EAAE,MAAM,EAAE,OAAO,EAAkB;IAC3D,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/D,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IACxB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAEtE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,uBAAuB,CAAC,gBAAwB;QACpD,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,eAAe,EAAE,KAAK,GAAG,CAAC,GAAG;YAChC,CAAC;IACH;IAEA,MAAM,gBAAgB,CAAC,gBAAwB;QAC7C,MAAM,WAAW,UAAU,CAAC,eAAe,IAAI;QAC/C,MAAM,YAAY,OAAO;QAEzB,IAAI,WAAW,YAAY;YACzB,cAAc,gBAAgB;YAC9B,cAAc,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,eAAe,EAAE;gBAAE,CAAC;QACzD;IACF;IAEA,MAAM,eAAe,CAAC,gBAAwB;QAC5C,MAAM,WAAW,UAAU,CAAC,eAAe,IAAI;QAC/C,OAAO,OAAO;IAChB;IAEA,MAAM,YAAY,CAAC,gBAAwB;QACzC,OAAO,OAAO,KAAK,IAAI,aAAa,gBAAgB;IACtD;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC,EAAE;;;;;;0CACtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA8B,EAAE,YAAY;gDAAE,QAAQ,OAAO,KAAK,CAAC,QAAQ;4CAAG;;;;;;;;;;;kDAEhG,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAY,SAAS;kDAClC,EAAE;;;;;;;;;;;;;;;;;;;;;;;8BAMX,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAA;gCACb,MAAM,WAAW,UAAU,CAAC,WAAW,IAAI,CAAC,IAAI;gCAChD,MAAM,YAAY,aAAa,WAAW,IAAI,EAAE,WAAW,IAAI;gCAC/D,MAAM,aAAa,UAAU,WAAW,IAAI,EAAE,WAAW,IAAI;gCAE7D,qBACE,8OAAC;oCAEC,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAY,WAAW,IAAI;;;;;;8DAC1C,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA6B,WAAW,IAAI;;;;;;sEAC1D,8OAAC;4DAAE,WAAU;sEACV,EAAE,qBAAqB;gEAAE,UAAU,WAAW,QAAQ,CAAC,QAAQ;4DAAG;;;;;;sEAErE,8OAAC;4DAAE,WAAU;sEACV,EAAE,kBAAkB;gEAAE,MAAM,WAAW,IAAI,CAAC,QAAQ;4DAAG;;;;;;;;;;;;;;;;;;sDAK9D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,qBAAqB,WAAW,IAAI,EAAE,WAAW;4DAChE,UAAU,YAAY;sEACvB;;;;;;sEAGD,8OAAC;4DAAK,WAAU;sEAA8B;;;;;;sEAC9C,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,qBAAqB,WAAW,IAAI,EAAE,WAAW;4DAChE,UAAU,CAAC,UAAU,WAAW,IAAI,EAAE,WAAW,IAAI,KAAK,YAAY;sEACvE;;;;;;;;;;;;8DAKH,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,YAAY,EAAE,aAAa,mBAAmB,gBAAgB;;gEAAE;gEAC7E;;;;;;;sEAEJ,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS,aAAa,YAAY;4DAClC,SAAS,IAAM,cAAc,WAAW,IAAI,EAAE,WAAW,IAAI;4DAC7D,UAAU,CAAC;4DACX,WAAU;sEAET,aAAa,EAAE,cAAc,EAAE;;;;;;;;;;;;;;;;;;;mCAhDjC,WAAW,IAAI;;;;;4BAsD1B;;;;;;sCAGF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAkC,EAAE;;;;;;8CAClD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAI,EAAE;;;;;;sDACP,8OAAC;sDAAI,EAAE;;;;;;sDACP,8OAAC;sDAAI,EAAE;;;;;;sDACP,8OAAC;sDAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrB", "debugId": null}}, {"offset": {"line": 2352, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/BakingModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Recipe, getAvailableRecipes, canCraftRecipe } from '@/lib/gameLogic'\nimport { useGame } from '@/contexts/GameContext'\n\ninterface BakingModalProps {\n  isOpen: boolean\n  onClose: () => void\n  equipmentId: string\n  equipmentName: string\n}\n\nexport function BakingModal({ isOpen, onClose, equipmentId, equipmentName }: BakingModalProps) {\n  const { player, inventory, updateEquipment, useIngredient } = useGame()\n  const [selectedRecipe, setSelectedRecipe] = useState<Recipe | null>(null)\n  \n  if (!isOpen) return null\n\n  const availableRecipes = getAvailableRecipes(player.level)\n  const craftableRecipes = availableRecipes.filter(recipe => \n    canCraftRecipe(recipe, inventory)\n  )\n\n  const startBaking = (recipe: Recipe) => {\n    // Check if we can craft the recipe\n    if (!canCraftRecipe(recipe, inventory)) {\n      return\n    }\n\n    // Consume ingredients\n    recipe.ingredients.forEach(ingredient => {\n      useIngredient(ingredient.name, ingredient.quantity)\n    })\n\n    // Start the equipment\n    updateEquipment(equipmentId, {\n      isActive: true,\n      timeRemaining: recipe.bakingTime,\n      currentRecipe: recipe.name\n    })\n\n    onClose()\n  }\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const getDifficultyStars = (difficulty: number) => {\n    return '⭐'.repeat(difficulty) + '☆'.repeat(5 - difficulty)\n  }\n\n  const getRecipeIcon = (category: string) => {\n    switch (category) {\n      case 'cookies': return '🍪'\n      case 'cakes': return '🧁'\n      case 'bread': return '🍞'\n      case 'pastries': return '🥐'\n      default: return '🍽️'\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-2xl font-bold text-orange-800\">\n              🔥 {equipmentName} - Select Recipe\n            </h2>\n            <Button variant=\"secondary\" onClick={onClose}>\n              ✕ Close\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          {craftableRecipes.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <div className=\"text-4xl mb-4\">😔</div>\n              <h3 className=\"text-lg font-medium text-gray-800 mb-2\">\n                No recipes available\n              </h3>\n              <p className=\"text-gray-600 mb-4\">\n                You don't have enough ingredients to craft any recipes.\n              </p>\n              <Button variant=\"primary\" onClick={onClose}>\n                Buy Ingredients\n              </Button>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[60vh] overflow-y-auto\">\n              {craftableRecipes.map(recipe => (\n                <div\n                  key={recipe.id}\n                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${\n                    selectedRecipe?.id === recipe.id\n                      ? 'border-orange-400 bg-orange-50'\n                      : 'border-gray-300 bg-gray-50 hover:border-orange-300'\n                  }`}\n                  onClick={() => setSelectedRecipe(recipe)}\n                >\n                  <div className=\"flex items-start justify-between mb-2\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-2xl\">{getRecipeIcon(recipe.category)}</span>\n                      <h3 className=\"font-semibold text-gray-800\">{recipe.name}</h3>\n                    </div>\n                    <span className=\"text-sm text-green-600\">${recipe.basePrice}</span>\n                  </div>\n\n                  <div className=\"text-xs text-gray-500 mb-2\">\n                    {getDifficultyStars(recipe.difficulty)} • ⏱️ {formatTime(recipe.bakingTime)}\n                  </div>\n\n                  <div className=\"space-y-1 mb-3\">\n                    <div className=\"text-sm font-medium text-gray-700\">Ingredients:</div>\n                    {recipe.ingredients.map((ingredient, index) => {\n                      const inventoryItem = inventory.find(item => item.name === ingredient.name)\n                      \n                      return (\n                        <div\n                          key={index}\n                          className=\"text-xs flex justify-between text-green-600\"\n                        >\n                          <span>{ingredient.name}</span>\n                          <span>\n                            {ingredient.quantity} \n                            <span className=\"ml-1\">\n                              ({inventoryItem?.quantity || 0} available)\n                            </span>\n                          </span>\n                        </div>\n                      )\n                    })}\n                  </div>\n\n                  {selectedRecipe?.id === recipe.id && (\n                    <Button\n                      variant=\"success\"\n                      size=\"sm\"\n                      className=\"w-full\"\n                      onClick={() => startBaking(recipe)}\n                    >\n                      🔥 Start Baking\n                    </Button>\n                  )}\n                </div>\n              ))}\n            </div>\n          )}\n\n          {selectedRecipe && craftableRecipes.length > 0 && (\n            <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n              <h3 className=\"font-medium text-blue-800 mb-2\">\n                📋 Baking Instructions for {selectedRecipe.name}\n              </h3>\n              <div className=\"text-sm text-blue-700 space-y-1\">\n                <p>• Baking time: {formatTime(selectedRecipe.bakingTime)}</p>\n                <p>• Difficulty: {getDifficultyStars(selectedRecipe.difficulty)}</p>\n                <p>• Expected reward: ${selectedRecipe.basePrice}</p>\n                <p>• Make sure you have all ingredients before starting!</p>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAcO,SAAS,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAoB;IAC3F,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,mBAAmB,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,KAAK;IACzD,MAAM,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,SAC/C,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;IAGzB,MAAM,cAAc,CAAC;QACnB,mCAAmC;QACnC,IAAI,CAAC,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,YAAY;YACtC;QACF;QAEA,sBAAsB;QACtB,OAAO,WAAW,CAAC,OAAO,CAAC,CAAA;YACzB,cAAc,WAAW,IAAI,EAAE,WAAW,QAAQ;QACpD;QAEA,sBAAsB;QACtB,gBAAgB,aAAa;YAC3B,UAAU;YACV,eAAe,OAAO,UAAU;YAChC,eAAe,OAAO,IAAI;QAC5B;QAEA;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAO,IAAI,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI;IACjD;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAqC;oCAC7C;oCAAc;;;;;;;0CAEpB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,SAAS;0CAAS;;;;;;;;;;;;;;;;;8BAMlD,8OAAC;oBAAI,WAAU;;wBACZ,iBAAiB,MAAM,KAAK,kBAC3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;8CAAS;;;;;;;;;;;iDAK9C,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAA,uBACpB,8OAAC;oCAEC,WAAW,CAAC,sDAAsD,EAChE,gBAAgB,OAAO,OAAO,EAAE,GAC5B,mCACA,sDACJ;oCACF,SAAS,IAAM,kBAAkB;;sDAEjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAY,cAAc,OAAO,QAAQ;;;;;;sEACzD,8OAAC;4DAAG,WAAU;sEAA+B,OAAO,IAAI;;;;;;;;;;;;8DAE1D,8OAAC;oDAAK,WAAU;;wDAAyB;wDAAE,OAAO,SAAS;;;;;;;;;;;;;sDAG7D,8OAAC;4CAAI,WAAU;;gDACZ,mBAAmB,OAAO,UAAU;gDAAE;gDAAO,WAAW,OAAO,UAAU;;;;;;;sDAG5E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAoC;;;;;;gDAClD,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY;oDACnC,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;oDAE1E,qBACE,8OAAC;wDAEC,WAAU;;0EAEV,8OAAC;0EAAM,WAAW,IAAI;;;;;;0EACtB,8OAAC;;oEACE,WAAW,QAAQ;kFACpB,8OAAC;wEAAK,WAAU;;4EAAO;4EACnB,eAAe,YAAY;4EAAE;;;;;;;;;;;;;;uDAP9B;;;;;gDAYX;;;;;;;wCAGD,gBAAgB,OAAO,OAAO,EAAE,kBAC/B,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,YAAY;sDAC5B;;;;;;;mCAhDE,OAAO,EAAE;;;;;;;;;;wBAyDrB,kBAAkB,iBAAiB,MAAM,GAAG,mBAC3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAiC;wCACjB,eAAe,IAAI;;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAE;gDAAgB,WAAW,eAAe,UAAU;;;;;;;sDACvD,8OAAC;;gDAAE;gDAAe,mBAAmB,eAAe,UAAU;;;;;;;sDAC9D,8OAAC;;gDAAE;gDAAqB,eAAe,SAAS;;;;;;;sDAChD,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB", "debugId": null}}, {"offset": {"line": 2727, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/NotificationSystem.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\nexport interface Notification {\n  id: string\n  type: 'success' | 'error' | 'warning' | 'info'\n  title: string\n  message: string\n  duration?: number\n}\n\ninterface NotificationSystemProps {\n  notifications: Notification[]\n  onRemove: (id: string) => void\n}\n\nexport function NotificationSystem({ notifications, onRemove }: NotificationSystemProps) {\n  useEffect(() => {\n    notifications.forEach(notification => {\n      if (notification.duration) {\n        const timer = setTimeout(() => {\n          onRemove(notification.id)\n        }, notification.duration)\n        \n        return () => clearTimeout(timer)\n      }\n    })\n  }, [notifications, onRemove])\n\n  const getNotificationStyle = (type: string) => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-100 border-green-400 text-green-800'\n      case 'error':\n        return 'bg-red-100 border-red-400 text-red-800'\n      case 'warning':\n        return 'bg-yellow-100 border-yellow-400 text-yellow-800'\n      case 'info':\n        return 'bg-blue-100 border-blue-400 text-blue-800'\n      default:\n        return 'bg-gray-100 border-gray-400 text-gray-800'\n    }\n  }\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'success':\n        return '✅'\n      case 'error':\n        return '❌'\n      case 'warning':\n        return '⚠️'\n      case 'info':\n        return 'ℹ️'\n      default:\n        return '📢'\n    }\n  }\n\n  if (notifications.length === 0) return null\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-sm\">\n      {notifications.map(notification => (\n        <div\n          key={notification.id}\n          className={`p-4 rounded-lg border-l-4 shadow-lg transition-all duration-300 ${getNotificationStyle(notification.type)}`}\n        >\n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex items-start space-x-2\">\n              <span className=\"text-lg\">{getNotificationIcon(notification.type)}</span>\n              <div>\n                <h4 className=\"font-medium\">{notification.title}</h4>\n                <p className=\"text-sm opacity-90\">{notification.message}</p>\n              </div>\n            </div>\n            <button\n              onClick={() => onRemove(notification.id)}\n              className=\"text-lg opacity-60 hover:opacity-100 transition-opacity\"\n            >\n              ×\n            </button>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n\n// Hook for managing notifications\nexport function useNotifications() {\n  const [notifications, setNotifications] = useState<Notification[]>([])\n\n  const addNotification = (notification: Omit<Notification, 'id'>) => {\n    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9)\n    const newNotification: Notification = {\n      ...notification,\n      id,\n      duration: notification.duration || 5000\n    }\n    setNotifications(prev => [...prev, newNotification])\n  }\n\n  const removeNotification = (id: string) => {\n    setNotifications(prev => prev.filter(n => n.id !== id))\n  }\n\n  const showSuccess = (title: string, message: string) => {\n    addNotification({ type: 'success', title, message })\n  }\n\n  const showError = (title: string, message: string) => {\n    addNotification({ type: 'error', title, message })\n  }\n\n  const showWarning = (title: string, message: string) => {\n    addNotification({ type: 'warning', title, message })\n  }\n\n  const showInfo = (title: string, message: string) => {\n    addNotification({ type: 'info', title, message })\n  }\n\n  return {\n    notifications,\n    addNotification,\n    removeNotification,\n    showSuccess,\n    showError,\n    showWarning,\n    showInfo\n  }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAiBO,SAAS,mBAAmB,EAAE,aAAa,EAAE,QAAQ,EAA2B;IACrF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc,OAAO,CAAC,CAAA;YACpB,IAAI,aAAa,QAAQ,EAAE;gBACzB,MAAM,QAAQ,WAAW;oBACvB,SAAS,aAAa,EAAE;gBAC1B,GAAG,aAAa,QAAQ;gBAExB,OAAO,IAAM,aAAa;YAC5B;QACF;IACF,GAAG;QAAC;QAAe;KAAS;IAE5B,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IAEvC,qBACE,8OAAC;QAAI,WAAU;kBACZ,cAAc,GAAG,CAAC,CAAA,6BACjB,8OAAC;gBAEC,WAAW,CAAC,gEAAgE,EAAE,qBAAqB,aAAa,IAAI,GAAG;0BAEvH,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAW,oBAAoB,aAAa,IAAI;;;;;;8CAChE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAe,aAAa,KAAK;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;sDAAsB,aAAa,OAAO;;;;;;;;;;;;;;;;;;sCAG3D,8OAAC;4BACC,SAAS,IAAM,SAAS,aAAa,EAAE;4BACvC,WAAU;sCACX;;;;;;;;;;;;eAdE,aAAa,EAAE;;;;;;;;;;AAsB9B;AAGO,SAAS;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,MAAM,kBAAkB,CAAC;QACvB,MAAM,KAAK,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QACxE,MAAM,kBAAgC;YACpC,GAAG,YAAY;YACf;YACA,UAAU,aAAa,QAAQ,IAAI;QACrC;QACA,iBAAiB,CAAA,OAAQ;mBAAI;gBAAM;aAAgB;IACrD;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACrD;IAEA,MAAM,cAAc,CAAC,OAAe;QAClC,gBAAgB;YAAE,MAAM;YAAW;YAAO;QAAQ;IACpD;IAEA,MAAM,YAAY,CAAC,OAAe;QAChC,gBAAgB;YAAE,MAAM;YAAS;YAAO;QAAQ;IAClD;IAEA,MAAM,cAAc,CAAC,OAAe;QAClC,gBAAgB;YAAE,MAAM;YAAW;YAAO;QAAQ;IACpD;IAEA,MAAM,WAAW,CAAC,OAAe;QAC/B,gBAAgB;YAAE,MAAM;YAAQ;YAAO;QAAQ;IACjD;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 2912, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/LevelUpModal.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { LevelReward } from '@/lib/progressionSystem'\n\ninterface LevelUpModalProps {\n  isOpen: boolean\n  onClose: () => void\n  newLevel: number\n  rewards: LevelReward[]\n}\n\nexport function LevelUpModal({ isOpen, onClose, newLevel, rewards }: LevelUpModalProps) {\n  if (!isOpen) return null\n\n  const getRewardIcon = (type: string) => {\n    switch (type) {\n      case 'recipe': return '📖'\n      case 'equipment': return '⚙️'\n      case 'money': return '💰'\n      case 'skill_point': return '⭐'\n      case 'achievement': return '🏆'\n      default: return '🎁'\n    }\n  }\n\n  const getRewardColor = (type: string) => {\n    switch (type) {\n      case 'recipe': return 'bg-blue-50 border-blue-300 text-blue-800'\n      case 'equipment': return 'bg-purple-50 border-purple-300 text-purple-800'\n      case 'money': return 'bg-green-50 border-green-300 text-green-800'\n      case 'skill_point': return 'bg-yellow-50 border-yellow-300 text-yellow-800'\n      case 'achievement': return 'bg-orange-50 border-orange-300 text-orange-800'\n      default: return 'bg-gray-50 border-gray-300 text-gray-800'\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-md w-full overflow-hidden\">\n        {/* Header with celebration */}\n        <div className=\"bg-gradient-to-r from-yellow-400 to-orange-500 p-6 text-center\">\n          <div className=\"text-6xl mb-2\">🎉</div>\n          <h2 className=\"text-3xl font-bold text-white mb-2\">Level Up!</h2>\n          <p className=\"text-xl text-yellow-100\">\n            You reached Level {newLevel}!\n          </p>\n        </div>\n\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">\n            🎁 Level Rewards\n          </h3>\n\n          <div className=\"space-y-3 mb-6\">\n            {rewards.map((reward, index) => (\n              <div\n                key={index}\n                className={`p-3 rounded-lg border ${getRewardColor(reward.type)}`}\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <span className=\"text-2xl\">{getRewardIcon(reward.type)}</span>\n                  <div className=\"flex-1\">\n                    <h4 className=\"font-medium\">{reward.name}</h4>\n                    <p className=\"text-sm opacity-80\">{reward.description}</p>\n                    {reward.value && (\n                      <p className=\"text-sm font-semibold\">\n                        {reward.type === 'money' ? `$${reward.value}` : `+${reward.value}`}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          <div className=\"bg-blue-50 p-4 rounded-lg mb-6\">\n            <h4 className=\"font-medium text-blue-800 mb-2\">💡 What's Next?</h4>\n            <ul className=\"text-sm text-blue-700 space-y-1\">\n              <li>• Check out new recipes in your recipe book</li>\n              <li>• Visit the shop for new equipment</li>\n              <li>• Take on more challenging orders</li>\n              <li>• Invest in skill upgrades</li>\n            </ul>\n          </div>\n\n          <Button\n            variant=\"primary\"\n            size=\"lg\"\n            className=\"w-full\"\n            onClick={onClose}\n          >\n            🚀 Continue Playing\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYO,SAAS,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAqB;IACpF,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAE,WAAU;;gCAA0B;gCAClB;gCAAS;;;;;;;;;;;;;8BAIhC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAIzD,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;oCAEC,WAAW,CAAC,sBAAsB,EAAE,eAAe,OAAO,IAAI,GAAG;8CAEjE,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAY,cAAc,OAAO,IAAI;;;;;;0DACrD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAe,OAAO,IAAI;;;;;;kEACxC,8OAAC;wDAAE,WAAU;kEAAsB,OAAO,WAAW;;;;;;oDACpD,OAAO,KAAK,kBACX,8OAAC;wDAAE,WAAU;kEACV,OAAO,IAAI,KAAK,UAAU,CAAC,CAAC,EAAE,OAAO,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE,OAAO,KAAK,EAAE;;;;;;;;;;;;;;;;;;mCAVrE;;;;;;;;;;sCAmBX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAIR,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;sCACV;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 3158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/AchievementsModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Achievement } from '@/lib/progressionSystem'\n\ninterface AchievementsModalProps {\n  isOpen: boolean\n  onClose: () => void\n  achievements: Achievement[]\n}\n\nexport function AchievementsModal({ isOpen, onClose, achievements }: AchievementsModalProps) {\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n\n  if (!isOpen) return null\n\n  const categories = [\n    { id: 'all', name: 'All', icon: '🏆' },\n    { id: 'baking', name: 'Baking', icon: '👨‍🍳' },\n    { id: 'business', name: 'Business', icon: '💼' },\n    { id: 'efficiency', name: 'Efficiency', icon: '⚡' },\n    { id: 'collection', name: 'Collection', icon: '📚' },\n    { id: 'special', name: 'Special', icon: '⭐' }\n  ]\n\n  const filteredAchievements = selectedCategory === 'all' \n    ? achievements \n    : achievements.filter(achievement => achievement.category === selectedCategory)\n\n  const completedCount = achievements.filter(a => a.completed).length\n  const totalCount = achievements.length\n\n  const getProgressPercentage = (achievement: Achievement) => {\n    if (achievement.completed) return 100\n    if (!achievement.requirements[0].current) return 0\n    return Math.min(100, (achievement.requirements[0].current / achievement.requirements[0].target) * 100)\n  }\n\n  const getRewardIcon = (type: string) => {\n    switch (type) {\n      case 'recipe': return '📖'\n      case 'equipment': return '⚙️'\n      case 'money': return '💰'\n      case 'skill_point': return '⭐'\n      default: return '🎁'\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-orange-800\">🏆 Achievements</h2>\n              <p className=\"text-gray-600\">\n                {completedCount} of {totalCount} achievements completed\n              </p>\n            </div>\n            <Button variant=\"secondary\" onClick={onClose}>\n              ✕ Close\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Progress Bar */}\n          <div className=\"mb-6\">\n            <div className=\"flex justify-between items-center mb-2\">\n              <span className=\"text-sm font-medium text-gray-700\">Overall Progress</span>\n              <span className=\"text-sm text-gray-500\">\n                {Math.round((completedCount / totalCount) * 100)}%\n              </span>\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-3\">\n              <div \n                className=\"bg-gradient-to-r from-yellow-400 to-orange-500 h-3 rounded-full transition-all duration-500\"\n                style={{ width: `${(completedCount / totalCount) * 100}%` }}\n              ></div>\n            </div>\n          </div>\n\n          {/* Category Filter */}\n          <div className=\"flex flex-wrap gap-2 mb-6\">\n            {categories.map(category => (\n              <Button\n                key={category.id}\n                variant={selectedCategory === category.id ? 'primary' : 'secondary'}\n                size=\"sm\"\n                onClick={() => setSelectedCategory(category.id)}\n              >\n                {category.icon} {category.name}\n              </Button>\n            ))}\n          </div>\n\n          {/* Achievements Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[50vh] overflow-y-auto\">\n            {filteredAchievements.map(achievement => {\n              const progress = getProgressPercentage(achievement)\n              const isCompleted = achievement.completed\n              const isUnlocked = achievement.unlocked\n\n              return (\n                <div\n                  key={achievement.id}\n                  className={`p-4 rounded-lg border-2 ${\n                    isCompleted\n                      ? 'border-green-400 bg-green-50'\n                      : isUnlocked\n                      ? 'border-gray-300 bg-white'\n                      : 'border-gray-200 bg-gray-50 opacity-60'\n                  }`}\n                >\n                  <div className=\"flex items-start space-x-3\">\n                    <div className={`text-3xl ${isCompleted ? 'grayscale-0' : 'grayscale'}`}>\n                      {achievement.icon}\n                    </div>\n                    <div className=\"flex-1\">\n                      <h3 className={`font-semibold ${isCompleted ? 'text-green-800' : 'text-gray-800'}`}>\n                        {achievement.name}\n                        {isCompleted && <span className=\"ml-2\">✅</span>}\n                      </h3>\n                      <p className=\"text-sm text-gray-600 mb-2\">\n                        {achievement.description}\n                      </p>\n\n                      {/* Progress */}\n                      {isUnlocked && !isCompleted && (\n                        <div className=\"mb-2\">\n                          <div className=\"flex justify-between items-center mb-1\">\n                            <span className=\"text-xs text-gray-500\">Progress</span>\n                            <span className=\"text-xs text-gray-500\">\n                              {achievement.requirements[0].current || 0} / {achievement.requirements[0].target}\n                            </span>\n                          </div>\n                          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                            <div \n                              className=\"bg-blue-500 h-2 rounded-full transition-all duration-300\"\n                              style={{ width: `${progress}%` }}\n                            ></div>\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Reward */}\n                      <div className=\"flex items-center space-x-2 text-sm\">\n                        <span className=\"text-gray-500\">Reward:</span>\n                        <span className=\"text-lg\">{getRewardIcon(achievement.reward.type)}</span>\n                        <span className=\"text-gray-700\">{achievement.reward.name}</span>\n                        {achievement.reward.value && (\n                          <span className=\"text-green-600 font-medium\">\n                            {achievement.reward.type === 'money' ? `$${achievement.reward.value}` : `+${achievement.reward.value}`}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )\n            })}\n          </div>\n\n          {filteredAchievements.length === 0 && (\n            <div className=\"text-center py-8 text-gray-500\">\n              <div className=\"text-4xl mb-2\">🏆</div>\n              <p>No achievements in this category.</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYO,SAAS,kBAAkB,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAA0B;IACzF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM;YAAO,MAAM;QAAK;QACrC;YAAE,IAAI;YAAU,MAAM;YAAU,MAAM;QAAQ;QAC9C;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAK;QAC/C;YAAE,IAAI;YAAc,MAAM;YAAc,MAAM;QAAI;QAClD;YAAE,IAAI;YAAc,MAAM;YAAc,MAAM;QAAK;QACnD;YAAE,IAAI;YAAW,MAAM;YAAW,MAAM;QAAI;KAC7C;IAED,MAAM,uBAAuB,qBAAqB,QAC9C,eACA,aAAa,MAAM,CAAC,CAAA,cAAe,YAAY,QAAQ,KAAK;IAEhE,MAAM,iBAAiB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;IACnE,MAAM,aAAa,aAAa,MAAM;IAEtC,MAAM,wBAAwB,CAAC;QAC7B,IAAI,YAAY,SAAS,EAAE,OAAO;QAClC,IAAI,CAAC,YAAY,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO;QACjD,OAAO,KAAK,GAAG,CAAC,KAAK,AAAC,YAAY,YAAY,CAAC,EAAE,CAAC,OAAO,GAAG,YAAY,YAAY,CAAC,EAAE,CAAC,MAAM,GAAI;IACpG;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAE,WAAU;;4CACV;4CAAe;4CAAK;4CAAW;;;;;;;;;;;;;0CAGpC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,SAAS;0CAAS;;;;;;;;;;;;;;;;;8BAMlD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAoC;;;;;;sDACpD,8OAAC;4CAAK,WAAU;;gDACb,KAAK,KAAK,CAAC,AAAC,iBAAiB,aAAc;gDAAK;;;;;;;;;;;;;8CAGrD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,AAAC,iBAAiB,aAAc,IAAI,CAAC,CAAC;wCAAC;;;;;;;;;;;;;;;;;sCAMhE,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC,kIAAA,CAAA,SAAM;oCAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;oCACxD,MAAK;oCACL,SAAS,IAAM,oBAAoB,SAAS,EAAE;;wCAE7C,SAAS,IAAI;wCAAC;wCAAE,SAAS,IAAI;;mCALzB,SAAS,EAAE;;;;;;;;;;sCAWtB,8OAAC;4BAAI,WAAU;sCACZ,qBAAqB,GAAG,CAAC,CAAA;gCACxB,MAAM,WAAW,sBAAsB;gCACvC,MAAM,cAAc,YAAY,SAAS;gCACzC,MAAM,aAAa,YAAY,QAAQ;gCAEvC,qBACE,8OAAC;oCAEC,WAAW,CAAC,wBAAwB,EAClC,cACI,iCACA,aACA,6BACA,yCACJ;8CAEF,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,SAAS,EAAE,cAAc,gBAAgB,aAAa;0DACpE,YAAY,IAAI;;;;;;0DAEnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAW,CAAC,cAAc,EAAE,cAAc,mBAAmB,iBAAiB;;4DAC/E,YAAY,IAAI;4DAChB,6BAAe,8OAAC;gEAAK,WAAU;0EAAO;;;;;;;;;;;;kEAEzC,8OAAC;wDAAE,WAAU;kEACV,YAAY,WAAW;;;;;;oDAIzB,cAAc,CAAC,6BACd,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,8OAAC;wEAAK,WAAU;;4EACb,YAAY,YAAY,CAAC,EAAE,CAAC,OAAO,IAAI;4EAAE;4EAAI,YAAY,YAAY,CAAC,EAAE,CAAC,MAAM;;;;;;;;;;;;;0EAGpF,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,OAAO,GAAG,SAAS,CAAC,CAAC;oEAAC;;;;;;;;;;;;;;;;;kEAOvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAW,cAAc,YAAY,MAAM,CAAC,IAAI;;;;;;0EAChE,8OAAC;gEAAK,WAAU;0EAAiB,YAAY,MAAM,CAAC,IAAI;;;;;;4DACvD,YAAY,MAAM,CAAC,KAAK,kBACvB,8OAAC;gEAAK,WAAU;0EACb,YAAY,MAAM,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,EAAE,YAAY,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE,YAAY,MAAM,CAAC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;mCA/C3G,YAAY,EAAE;;;;;4BAuDzB;;;;;;wBAGD,qBAAqB,MAAM,KAAK,mBAC/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}, {"offset": {"line": 3575, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/SkillTreeModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { SkillTree } from '@/lib/progressionSystem'\n\ninterface SkillTreeModalProps {\n  isOpen: boolean\n  onClose: () => void\n  skills: SkillTree[]\n  skillPoints: number\n  playerLevel: number\n  onUpgradeSkill: (skillId: string) => void\n}\n\nexport function SkillTreeModal({ \n  isOpen, \n  onClose, \n  skills, \n  skillPoints, \n  playerLevel,\n  onUpgradeSkill \n}: SkillTreeModalProps) {\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n\n  if (!isOpen) return null\n\n  const categories = [\n    { id: 'all', name: 'All', icon: '🌟' },\n    { id: 'efficiency', name: 'Efficiency', icon: '⚡' },\n    { id: 'automation', name: 'Automation', icon: '🤖' },\n    { id: 'quality', name: 'Quality', icon: '💎' },\n    { id: 'business', name: 'Business', icon: '💼' }\n  ]\n\n  const filteredSkills = selectedCategory === 'all' \n    ? skills \n    : skills.filter(skill => skill.category === selectedCategory)\n\n  const canUpgradeSkill = (skill: SkillTree) => {\n    if (skill.level >= skill.maxLevel) return false\n    if (skillPoints < skill.cost) return false\n    if (skill.requirements.playerLevel && playerLevel < skill.requirements.playerLevel) return false\n    if (skill.requirements.skills) {\n      return skill.requirements.skills.every(requiredSkillId => {\n        const requiredSkill = skills.find(s => s.id === requiredSkillId)\n        return requiredSkill && requiredSkill.level > 0\n      })\n    }\n    return true\n  }\n\n  const getSkillStatus = (skill: SkillTree) => {\n    if (skill.level >= skill.maxLevel) return 'maxed'\n    if (!canUpgradeSkill(skill)) return 'locked'\n    return 'available'\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'maxed': return 'border-green-400 bg-green-50'\n      case 'available': return 'border-blue-400 bg-blue-50'\n      case 'locked': return 'border-gray-300 bg-gray-50'\n      default: return 'border-gray-300 bg-gray-50'\n    }\n  }\n\n  const getEffectDescription = (effect: any) => {\n    const percentage = Math.round(effect.value * 100)\n    switch (effect.type) {\n      case 'baking_speed': return `+${percentage}% baking speed`\n      case 'money_multiplier': return `+${percentage}% money earned`\n      case 'xp_multiplier': return `+${percentage}% experience gained`\n      case 'ingredient_efficiency': return `${percentage}% less ingredients used`\n      case 'automation_unlock': return 'Unlock automation features'\n      default: return `+${percentage}% bonus`\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-orange-800\">🌟 Skill Tree</h2>\n              <p className=\"text-gray-600\">\n                Available Skill Points: <span className=\"font-semibold text-blue-600\">{skillPoints}</span>\n              </p>\n            </div>\n            <Button variant=\"secondary\" onClick={onClose}>\n              ✕ Close\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Category Filter */}\n          <div className=\"flex flex-wrap gap-2 mb-6\">\n            {categories.map(category => (\n              <Button\n                key={category.id}\n                variant={selectedCategory === category.id ? 'primary' : 'secondary'}\n                size=\"sm\"\n                onClick={() => setSelectedCategory(category.id)}\n              >\n                {category.icon} {category.name}\n              </Button>\n            ))}\n          </div>\n\n          {/* Skills Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto\">\n            {filteredSkills.map(skill => {\n              const status = getSkillStatus(skill)\n              const canUpgrade = canUpgradeSkill(skill)\n\n              return (\n                <div\n                  key={skill.id}\n                  className={`p-4 rounded-lg border-2 ${getStatusColor(status)}`}\n                >\n                  <div className=\"flex items-start justify-between mb-3\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-2xl\">{skill.icon}</span>\n                      <div>\n                        <h3 className=\"font-semibold text-gray-800\">{skill.name}</h3>\n                        <p className=\"text-xs text-gray-500 uppercase tracking-wide\">\n                          {skill.category}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-sm font-medium text-gray-700\">\n                        Level {skill.level}/{skill.maxLevel}\n                      </div>\n                      {status !== 'maxed' && (\n                        <div className=\"text-xs text-blue-600\">\n                          Cost: {skill.cost} SP\n                        </div>\n                      )}\n                    </div>\n                  </div>\n\n                  <p className=\"text-sm text-gray-600 mb-3\">\n                    {skill.description}\n                  </p>\n\n                  {/* Effects */}\n                  <div className=\"mb-3\">\n                    <h4 className=\"text-xs font-medium text-gray-700 mb-1\">Effects:</h4>\n                    {skill.effects.map((effect, index) => (\n                      <div key={index} className=\"text-xs text-green-600\">\n                        • {getEffectDescription(effect)}\n                      </div>\n                    ))}\n                  </div>\n\n                  {/* Requirements */}\n                  {skill.requirements.playerLevel && playerLevel < skill.requirements.playerLevel && (\n                    <div className=\"mb-3\">\n                      <div className=\"text-xs text-red-600\">\n                        Requires Level {skill.requirements.playerLevel}\n                      </div>\n                    </div>\n                  )}\n\n                  {skill.requirements.skills && (\n                    <div className=\"mb-3\">\n                      <div className=\"text-xs text-gray-600\">\n                        Requires: {skill.requirements.skills.map(skillId => {\n                          const reqSkill = skills.find(s => s.id === skillId)\n                          return reqSkill?.name\n                        }).join(', ')}\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Progress Bar */}\n                  <div className=\"mb-3\">\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div \n                        className=\"bg-blue-500 h-2 rounded-full transition-all duration-300\"\n                        style={{ width: `${(skill.level / skill.maxLevel) * 100}%` }}\n                      ></div>\n                    </div>\n                  </div>\n\n                  {/* Upgrade Button */}\n                  {status === 'maxed' ? (\n                    <Button variant=\"success\" size=\"sm\" className=\"w-full\" disabled>\n                      ✅ Maxed\n                    </Button>\n                  ) : canUpgrade ? (\n                    <Button \n                      variant=\"primary\" \n                      size=\"sm\" \n                      className=\"w-full\"\n                      onClick={() => onUpgradeSkill(skill.id)}\n                    >\n                      ⬆️ Upgrade ({skill.cost} SP)\n                    </Button>\n                  ) : (\n                    <Button variant=\"secondary\" size=\"sm\" className=\"w-full\" disabled>\n                      🔒 Locked\n                    </Button>\n                  )}\n                </div>\n              )\n            })}\n          </div>\n\n          {filteredSkills.length === 0 && (\n            <div className=\"text-center py-8 text-gray-500\">\n              <div className=\"text-4xl mb-2\">🌟</div>\n              <p>No skills in this category.</p>\n            </div>\n          )}\n\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n            <h3 className=\"font-medium text-blue-800 mb-2\">💡 Skill Tips</h3>\n            <ul className=\"text-sm text-blue-700 space-y-1\">\n              <li>• Earn skill points by leveling up (1 point every 2 levels)</li>\n              <li>• Some skills require other skills to be unlocked first</li>\n              <li>• Focus on skills that match your playstyle</li>\n              <li>• Efficiency skills help with resource management</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAeO,SAAS,eAAe,EAC7B,MAAM,EACN,OAAO,EACP,MAAM,EACN,WAAW,EACX,WAAW,EACX,cAAc,EACM;IACpB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM;YAAO,MAAM;QAAK;QACrC;YAAE,IAAI;YAAc,MAAM;YAAc,MAAM;QAAI;QAClD;YAAE,IAAI;YAAc,MAAM;YAAc,MAAM;QAAK;QACnD;YAAE,IAAI;YAAW,MAAM;YAAW,MAAM;QAAK;QAC7C;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAK;KAChD;IAED,MAAM,iBAAiB,qBAAqB,QACxC,SACA,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;IAE9C,MAAM,kBAAkB,CAAC;QACvB,IAAI,MAAM,KAAK,IAAI,MAAM,QAAQ,EAAE,OAAO;QAC1C,IAAI,cAAc,MAAM,IAAI,EAAE,OAAO;QACrC,IAAI,MAAM,YAAY,CAAC,WAAW,IAAI,cAAc,MAAM,YAAY,CAAC,WAAW,EAAE,OAAO;QAC3F,IAAI,MAAM,YAAY,CAAC,MAAM,EAAE;YAC7B,OAAO,MAAM,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;gBACrC,MAAM,gBAAgB,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAChD,OAAO,iBAAiB,cAAc,KAAK,GAAG;YAChD;QACF;QACA,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,MAAM,KAAK,IAAI,MAAM,QAAQ,EAAE,OAAO;QAC1C,IAAI,CAAC,gBAAgB,QAAQ,OAAO;QACpC,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,aAAa,KAAK,KAAK,CAAC,OAAO,KAAK,GAAG;QAC7C,OAAQ,OAAO,IAAI;YACjB,KAAK;gBAAgB,OAAO,CAAC,CAAC,EAAE,WAAW,cAAc,CAAC;YAC1D,KAAK;gBAAoB,OAAO,CAAC,CAAC,EAAE,WAAW,cAAc,CAAC;YAC9D,KAAK;gBAAiB,OAAO,CAAC,CAAC,EAAE,WAAW,mBAAmB,CAAC;YAChE,KAAK;gBAAyB,OAAO,GAAG,WAAW,uBAAuB,CAAC;YAC3E,KAAK;gBAAqB,OAAO;YACjC;gBAAS,OAAO,CAAC,CAAC,EAAE,WAAW,OAAO,CAAC;QACzC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAE,WAAU;;4CAAgB;0DACH,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;0CAG3E,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,SAAS;0CAAS;;;;;;;;;;;;;;;;;8BAMlD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC,kIAAA,CAAA,SAAM;oCAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;oCACxD,MAAK;oCACL,SAAS,IAAM,oBAAoB,SAAS,EAAE;;wCAE7C,SAAS,IAAI;wCAAC;wCAAE,SAAS,IAAI;;mCALzB,SAAS,EAAE;;;;;;;;;;sCAWtB,8OAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAA;gCAClB,MAAM,SAAS,eAAe;gCAC9B,MAAM,aAAa,gBAAgB;gCAEnC,qBACE,8OAAC;oCAEC,WAAW,CAAC,wBAAwB,EAAE,eAAe,SAAS;;sDAE9D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAY,MAAM,IAAI;;;;;;sEACtC,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA+B,MAAM,IAAI;;;;;;8EACvD,8OAAC;oEAAE,WAAU;8EACV,MAAM,QAAQ;;;;;;;;;;;;;;;;;;8DAIrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEAAoC;gEAC1C,MAAM,KAAK;gEAAC;gEAAE,MAAM,QAAQ;;;;;;;wDAEpC,WAAW,yBACV,8OAAC;4DAAI,WAAU;;gEAAwB;gEAC9B,MAAM,IAAI;gEAAC;;;;;;;;;;;;;;;;;;;sDAM1B,8OAAC;4CAAE,WAAU;sDACV,MAAM,WAAW;;;;;;sDAIpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;gDACtD,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC1B,8OAAC;wDAAgB,WAAU;;4DAAyB;4DAC/C,qBAAqB;;uDADhB;;;;;;;;;;;wCAOb,MAAM,YAAY,CAAC,WAAW,IAAI,cAAc,MAAM,YAAY,CAAC,WAAW,kBAC7E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;oDAAuB;oDACpB,MAAM,YAAY,CAAC,WAAW;;;;;;;;;;;;wCAKnD,MAAM,YAAY,CAAC,MAAM,kBACxB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;oDAAwB;oDAC1B,MAAM,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;wDACvC,MAAM,WAAW,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;wDAC3C,OAAO,UAAU;oDACnB,GAAG,IAAI,CAAC;;;;;;;;;;;;sDAMd,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,AAAC,MAAM,KAAK,GAAG,MAAM,QAAQ,GAAI,IAAI,CAAC,CAAC;oDAAC;;;;;;;;;;;;;;;;wCAMhE,WAAW,wBACV,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;4CAAS,QAAQ;sDAAC;;;;;mDAG9D,2BACF,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,eAAe,MAAM,EAAE;;gDACvC;gDACc,MAAM,IAAI;gDAAC;;;;;;iEAG1B,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAY,MAAK;4CAAK,WAAU;4CAAS,QAAQ;sDAAC;;;;;;;mCApF/D,MAAM,EAAE;;;;;4BA0FnB;;;;;;wBAGD,eAAe,MAAM,KAAK,mBACzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;8CAAE;;;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}, {"offset": {"line": 4088, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/AutomationModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { AutomationSettings, AUTOMATION_UPGRADES } from '@/lib/automationSystem'\nimport { useGame } from '@/contexts/GameContext'\n\ninterface AutomationModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport function AutomationModal({ isOpen, onClose }: AutomationModalProps) {\n  const { player, equipment, automationSettings, updateAutomationSettings, purchaseAutomationUpgrade } = useGame()\n  const [selectedTab, setSelectedTab] = useState<'settings' | 'upgrades' | 'status'>('settings')\n\n  if (!isOpen) return null\n\n  const automatedEquipment = equipment.filter(eq => eq.automationLevel > 0)\n  const availableUpgrades = AUTOMATION_UPGRADES.filter(upgrade => \n    player.level >= upgrade.unlockLevel && !player.automationUpgrades?.includes(upgrade.id)\n  )\n\n  const handleSettingChange = (key: keyof AutomationSettings, value: any) => {\n    updateAutomationSettings({ [key]: value })\n  }\n\n  const tabs = [\n    { id: 'settings', name: 'Settings', icon: '⚙️' },\n    { id: 'upgrades', name: 'Upgrades', icon: '🔧' },\n    { id: 'status', name: 'Status', icon: '📊' }\n  ]\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-2xl font-bold text-orange-800\">🤖 Automation Control</h2>\n            <Button variant=\"secondary\" onClick={onClose}>\n              ✕ Close\n            </Button>\n          </div>\n        </div>\n\n        {/* Tab Navigation */}\n        <div className=\"border-b border-gray-200\">\n          <div className=\"flex space-x-0\">\n            {tabs.map(tab => (\n              <button\n                key={tab.id}\n                onClick={() => setSelectedTab(tab.id as any)}\n                className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${\n                  selectedTab === tab.id\n                    ? 'border-orange-500 text-orange-600 bg-orange-50'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'\n                }`}\n              >\n                {tab.icon} {tab.name}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"p-6 max-h-[60vh] overflow-y-auto\">\n          {/* Settings Tab */}\n          {selectedTab === 'settings' && (\n            <div className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {/* Master Control */}\n                <div className=\"bg-blue-50 p-4 rounded-lg\">\n                  <h3 className=\"font-semibold text-blue-800 mb-3\">🎛️ Master Control</h3>\n                  <div className=\"space-y-3\">\n                    <label className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"checkbox\"\n                        checked={automationSettings?.enabled || false}\n                        onChange={(e) => handleSettingChange('enabled', e.target.checked)}\n                        className=\"rounded\"\n                      />\n                      <span className=\"text-sm\">Enable Automation</span>\n                    </label>\n                    <label className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"checkbox\"\n                        checked={automationSettings?.autoStart || false}\n                        onChange={(e) => handleSettingChange('autoStart', e.target.checked)}\n                        className=\"rounded\"\n                      />\n                      <span className=\"text-sm\">Auto-start Equipment</span>\n                    </label>\n                  </div>\n                </div>\n\n                {/* Priority Settings */}\n                <div className=\"bg-green-50 p-4 rounded-lg\">\n                  <h3 className=\"font-semibold text-green-800 mb-3\">🎯 Priority Mode</h3>\n                  <select\n                    value={automationSettings?.priorityMode || 'efficiency'}\n                    onChange={(e) => handleSettingChange('priorityMode', e.target.value)}\n                    className=\"w-full p-2 border rounded-lg\"\n                  >\n                    <option value=\"efficiency\">Efficiency (Orders First)</option>\n                    <option value=\"profit\">Profit (Highest Value)</option>\n                    <option value=\"speed\">Speed (Fastest Recipes)</option>\n                  </select>\n                  <p className=\"text-xs text-green-600 mt-1\">\n                    How automation chooses what to bake\n                  </p>\n                </div>\n\n                {/* Capacity Settings */}\n                <div className=\"bg-purple-50 p-4 rounded-lg\">\n                  <h3 className=\"font-semibold text-purple-800 mb-3\">⚡ Performance</h3>\n                  <div className=\"space-y-2\">\n                    <label className=\"block text-sm\">\n                      Max Concurrent Jobs: {automationSettings?.maxConcurrentJobs || 2}\n                    </label>\n                    <input\n                      type=\"range\"\n                      min=\"1\"\n                      max=\"5\"\n                      value={automationSettings?.maxConcurrentJobs || 2}\n                      onChange={(e) => handleSettingChange('maxConcurrentJobs', parseInt(e.target.value))}\n                      className=\"w-full\"\n                    />\n                  </div>\n                </div>\n\n                {/* Safety Settings */}\n                <div className=\"bg-yellow-50 p-4 rounded-lg\">\n                  <h3 className=\"font-semibold text-yellow-800 mb-3\">🛡️ Safety</h3>\n                  <div className=\"space-y-2\">\n                    <label className=\"block text-sm\">\n                      Stop when ingredients below: {automationSettings?.ingredientThreshold || 5}\n                    </label>\n                    <input\n                      type=\"range\"\n                      min=\"0\"\n                      max=\"20\"\n                      value={automationSettings?.ingredientThreshold || 5}\n                      onChange={(e) => handleSettingChange('ingredientThreshold', parseInt(e.target.value))}\n                      className=\"w-full\"\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Upgrades Tab */}\n          {selectedTab === 'upgrades' && (\n            <div className=\"space-y-4\">\n              <div className=\"bg-blue-50 p-4 rounded-lg mb-4\">\n                <h3 className=\"font-medium text-blue-800 mb-2\">💡 Automation Upgrades</h3>\n                <p className=\"text-sm text-blue-700\">\n                  Improve your automation efficiency, speed, and intelligence with these upgrades.\n                </p>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                {availableUpgrades.map(upgrade => (\n                  <div\n                    key={upgrade.id}\n                    className=\"p-4 border rounded-lg bg-white hover:bg-gray-50 transition-colors\"\n                  >\n                    <div className=\"flex justify-between items-start mb-2\">\n                      <h4 className=\"font-semibold text-gray-800\">{upgrade.name}</h4>\n                      <span className=\"text-sm text-green-600 font-medium\">${upgrade.cost}</span>\n                    </div>\n                    <p className=\"text-sm text-gray-600 mb-3\">{upgrade.description}</p>\n                    \n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-500 uppercase tracking-wide\">\n                        {upgrade.type}\n                      </span>\n                      <Button\n                        size=\"sm\"\n                        variant={player.money >= upgrade.cost ? 'primary' : 'secondary'}\n                        disabled={player.money < upgrade.cost}\n                        onClick={() => purchaseAutomationUpgrade(upgrade.id)}\n                      >\n                        {player.money >= upgrade.cost ? 'Purchase' : 'Too Expensive'}\n                      </Button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              {availableUpgrades.length === 0 && (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <div className=\"text-4xl mb-2\">🔧</div>\n                  <p>No upgrades available at your current level.</p>\n                  <p className=\"text-sm\">Level up to unlock more automation upgrades!</p>\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* Status Tab */}\n          {selectedTab === 'status' && (\n            <div className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div className=\"bg-green-50 p-4 rounded-lg text-center\">\n                  <div className=\"text-2xl text-green-600 mb-1\">{automatedEquipment.length}</div>\n                  <div className=\"text-sm text-green-800\">Automated Equipment</div>\n                </div>\n                <div className=\"bg-blue-50 p-4 rounded-lg text-center\">\n                  <div className=\"text-2xl text-blue-600 mb-1\">\n                    {automationSettings?.enabled ? '✅' : '❌'}\n                  </div>\n                  <div className=\"text-sm text-blue-800\">Automation Status</div>\n                </div>\n                <div className=\"bg-purple-50 p-4 rounded-lg text-center\">\n                  <div className=\"text-2xl text-purple-600 mb-1\">\n                    {player.automationUpgrades?.length || 0}\n                  </div>\n                  <div className=\"text-sm text-purple-800\">Active Upgrades</div>\n                </div>\n              </div>\n\n              <div className=\"space-y-4\">\n                <h3 className=\"font-semibold text-gray-800\">🏭 Equipment Status</h3>\n                {automatedEquipment.length > 0 ? (\n                  <div className=\"space-y-2\">\n                    {automatedEquipment.map(eq => (\n                      <div key={eq.id} className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                        <div>\n                          <span className=\"font-medium\">{eq.name}</span>\n                          <span className=\"text-sm text-gray-500 ml-2\">\n                            Level {eq.automationLevel} • {eq.efficiency}x efficiency\n                          </span>\n                        </div>\n                        <div className={`px-2 py-1 rounded text-xs ${\n                          eq.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'\n                        }`}>\n                          {eq.isActive ? 'Running' : 'Idle'}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"text-center py-4 text-gray-500\">\n                    <p>No automated equipment available.</p>\n                    <p className=\"text-sm\">Purchase auto-equipment from the shop to get started!</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAwB;IACvE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,kBAAkB,EAAE,wBAAwB,EAAE,yBAAyB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC7G,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsC;IAEnF,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,qBAAqB,UAAU,MAAM,CAAC,CAAA,KAAM,GAAG,eAAe,GAAG;IACvE,MAAM,oBAAoB,8HAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,CAAA,UACnD,OAAO,KAAK,IAAI,QAAQ,WAAW,IAAI,CAAC,OAAO,kBAAkB,EAAE,SAAS,QAAQ,EAAE;IAGxF,MAAM,sBAAsB,CAAC,KAA+B;QAC1D,yBAAyB;YAAE,CAAC,IAAI,EAAE;QAAM;IAC1C;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAK;QAC/C;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAK;QAC/C;YAAE,IAAI;YAAU,MAAM;YAAU,MAAM;QAAK;KAC5C;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,SAAS;0CAAS;;;;;;;;;;;;;;;;;8BAOlD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAA,oBACR,8OAAC;gCAEC,SAAS,IAAM,eAAe,IAAI,EAAE;gCACpC,WAAW,CAAC,2DAA2D,EACrE,gBAAgB,IAAI,EAAE,GAClB,mDACA,yEACJ;;oCAED,IAAI,IAAI;oCAAC;oCAAE,IAAI,IAAI;;+BARf,IAAI,EAAE;;;;;;;;;;;;;;;8BAcnB,8OAAC;oBAAI,WAAU;;wBAEZ,gBAAgB,4BACf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEACC,MAAK;gEACL,SAAS,oBAAoB,WAAW;gEACxC,UAAU,CAAC,IAAM,oBAAoB,WAAW,EAAE,MAAM,CAAC,OAAO;gEAChE,WAAU;;;;;;0EAEZ,8OAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEACC,MAAK;gEACL,SAAS,oBAAoB,aAAa;gEAC1C,UAAU,CAAC,IAAM,oBAAoB,aAAa,EAAE,MAAM,CAAC,OAAO;gEAClE,WAAU;;;;;;0EAEZ,8OAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAMhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDACC,OAAO,oBAAoB,gBAAgB;gDAC3C,UAAU,CAAC,IAAM,oBAAoB,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDACnE,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAa;;;;;;kEAC3B,8OAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;;;;;;;0DAExB,8OAAC;gDAAE,WAAU;0DAA8B;;;;;;;;;;;;kDAM7C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;;4DAAgB;4DACT,oBAAoB,qBAAqB;;;;;;;kEAEjE,8OAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,OAAO,oBAAoB,qBAAqB;wDAChD,UAAU,CAAC,IAAM,oBAAoB,qBAAqB,SAAS,EAAE,MAAM,CAAC,KAAK;wDACjF,WAAU;;;;;;;;;;;;;;;;;;kDAMhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;;4DAAgB;4DACD,oBAAoB,uBAAuB;;;;;;;kEAE3E,8OAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,OAAO,oBAAoB,uBAAuB;wDAClD,UAAU,CAAC,IAAM,oBAAoB,uBAAuB,SAAS,EAAE,MAAM,CAAC,KAAK;wDACnF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASrB,gBAAgB,4BACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,8OAAC;oCAAI,WAAU;8CACZ,kBAAkB,GAAG,CAAC,CAAA,wBACrB,8OAAC;4CAEC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA+B,QAAQ,IAAI;;;;;;sEACzD,8OAAC;4DAAK,WAAU;;gEAAqC;gEAAE,QAAQ,IAAI;;;;;;;;;;;;;8DAErE,8OAAC;oDAAE,WAAU;8DAA8B,QAAQ,WAAW;;;;;;8DAE9D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,QAAQ,IAAI;;;;;;sEAEf,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS,OAAO,KAAK,IAAI,QAAQ,IAAI,GAAG,YAAY;4DACpD,UAAU,OAAO,KAAK,GAAG,QAAQ,IAAI;4DACrC,SAAS,IAAM,0BAA0B,QAAQ,EAAE;sEAElD,OAAO,KAAK,IAAI,QAAQ,IAAI,GAAG,aAAa;;;;;;;;;;;;;2CAnB5C,QAAQ,EAAE;;;;;;;;;;gCA0BpB,kBAAkB,MAAM,KAAK,mBAC5B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;sDAAE;;;;;;sDACH,8OAAC;4CAAE,WAAU;sDAAU;;;;;;;;;;;;;;;;;;wBAO9B,gBAAgB,0BACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgC,mBAAmB,MAAM;;;;;;8DACxE,8OAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,oBAAoB,UAAU,MAAM;;;;;;8DAEvC,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,OAAO,kBAAkB,EAAE,UAAU;;;;;;8DAExC,8OAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;8CAI7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;wCAC3C,mBAAmB,MAAM,GAAG,kBAC3B,8OAAC;4CAAI,WAAU;sDACZ,mBAAmB,GAAG,CAAC,CAAA,mBACtB,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAe,GAAG,IAAI;;;;;;8EACtC,8OAAC;oEAAK,WAAU;;wEAA6B;wEACpC,GAAG,eAAe;wEAAC;wEAAI,GAAG,UAAU;wEAAC;;;;;;;;;;;;;sEAGhD,8OAAC;4DAAI,WAAW,CAAC,0BAA0B,EACzC,GAAG,QAAQ,GAAG,gCAAgC,6BAC9C;sEACC,GAAG,QAAQ,GAAG,YAAY;;;;;;;mDAVrB,GAAG,EAAE;;;;;;;;;iEAgBnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAE;;;;;;8DACH,8OAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3C", "debugId": null}}, {"offset": {"line": 4807, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/EquipmentShopModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { useGame } from '@/contexts/GameContext'\n\ninterface EquipmentItem {\n  id: string\n  name: string\n  type: string\n  description: string\n  cost: number\n  unlockLevel: number\n  automationLevel: number\n  efficiency: number\n  icon: string\n  category: 'basic' | 'automated' | 'advanced'\n}\n\nconst EQUIPMENT_SHOP: EquipmentItem[] = [\n  {\n    id: 'professional_oven',\n    name: 'Professional Oven',\n    type: 'oven',\n    description: 'Faster and more efficient than basic oven',\n    cost: 500,\n    unlockLevel: 3,\n    automationLevel: 0,\n    efficiency: 1.3,\n    icon: '🔥',\n    category: 'basic'\n  },\n  {\n    id: 'auto_oven',\n    name: 'Automated Oven',\n    type: 'auto_oven',\n    description: 'Fully automated oven that can run without supervision',\n    cost: 1500,\n    unlockLevel: 5,\n    automationLevel: 2,\n    efficiency: 1.5,\n    icon: '🤖',\n    category: 'automated'\n  },\n  {\n    id: 'industrial_mixer',\n    name: 'Industrial Mixer',\n    type: 'mixer',\n    description: 'High-capacity mixer for large batches',\n    cost: 750,\n    unlockLevel: 4,\n    automationLevel: 0,\n    efficiency: 1.4,\n    icon: '🥄',\n    category: 'basic'\n  },\n  {\n    id: 'auto_mixer',\n    name: 'Automated Mixer',\n    type: 'auto_mixer',\n    description: 'Self-operating mixer with ingredient dispensing',\n    cost: 2000,\n    unlockLevel: 6,\n    automationLevel: 2,\n    efficiency: 1.6,\n    icon: '🤖',\n    category: 'automated'\n  },\n  {\n    id: 'conveyor_belt_basic',\n    name: 'Basic Conveyor Belt',\n    type: 'conveyor',\n    description: 'Moves items between equipment automatically',\n    cost: 1000,\n    unlockLevel: 7,\n    automationLevel: 1,\n    efficiency: 1.2,\n    icon: '🔄',\n    category: 'automated'\n  },\n  {\n    id: 'smart_conveyor',\n    name: 'Smart Conveyor System',\n    type: 'conveyor',\n    description: 'Intelligent conveyor with sorting and routing',\n    cost: 3000,\n    unlockLevel: 10,\n    automationLevel: 3,\n    efficiency: 1.8,\n    icon: '🧠',\n    category: 'advanced'\n  },\n  {\n    id: 'master_oven',\n    name: 'Master Oven',\n    type: 'oven',\n    description: 'The ultimate baking machine with AI assistance',\n    cost: 5000,\n    unlockLevel: 12,\n    automationLevel: 3,\n    efficiency: 2.0,\n    icon: '👑',\n    category: 'advanced'\n  }\n]\n\ninterface EquipmentShopModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport function EquipmentShopModal({ isOpen, onClose }: EquipmentShopModalProps) {\n  const { player, equipment, spendMoney, updateEquipment } = useGame()\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n\n  if (!isOpen) return null\n\n  const categories = [\n    { id: 'all', name: 'All', icon: '🏪' },\n    { id: 'basic', name: 'Basic', icon: '🔧' },\n    { id: 'automated', name: 'Automated', icon: '🤖' },\n    { id: 'advanced', name: 'Advanced', icon: '⚡' }\n  ]\n\n  const availableEquipment = EQUIPMENT_SHOP.filter(item => \n    player.level >= item.unlockLevel &&\n    (selectedCategory === 'all' || item.category === selectedCategory)\n  )\n\n  const purchaseEquipment = (item: EquipmentItem) => {\n    if (player.money < item.cost) return\n\n    if (spendMoney(item.cost)) {\n      const newEquipment = {\n        id: Date.now().toString(),\n        name: item.name,\n        type: item.type as any,\n        isActive: false,\n        level: 1,\n        efficiency: item.efficiency,\n        automationLevel: item.automationLevel\n      }\n\n      // Add to equipment list (this would need to be implemented in GameContext)\n      // For now, we'll just show success\n      console.log('Purchased equipment:', newEquipment)\n    }\n  }\n\n  const getAutomationBadge = (level: number) => {\n    if (level === 0) return null\n    return (\n      <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n        🤖 Auto Level {level}\n      </span>\n    )\n  }\n\n  const getCategoryColor = (category: string) => {\n    switch (category) {\n      case 'basic': return 'border-gray-300 bg-gray-50'\n      case 'automated': return 'border-blue-300 bg-blue-50'\n      case 'advanced': return 'border-purple-300 bg-purple-50'\n      default: return 'border-gray-300 bg-white'\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-orange-800\">🏪 Equipment Shop</h2>\n              <p className=\"text-gray-600\">\n                Upgrade your bakery with professional equipment\n              </p>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"bg-green-100 px-3 py-1 rounded-full\">\n                <span className=\"text-green-800 font-medium\">${player.money}</span>\n              </div>\n              <Button variant=\"secondary\" onClick={onClose}>\n                ✕ Close\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Category Filter */}\n          <div className=\"flex flex-wrap gap-2 mb-6\">\n            {categories.map(category => (\n              <Button\n                key={category.id}\n                variant={selectedCategory === category.id ? 'primary' : 'secondary'}\n                size=\"sm\"\n                onClick={() => setSelectedCategory(category.id)}\n              >\n                {category.icon} {category.name}\n              </Button>\n            ))}\n          </div>\n\n          {/* Equipment Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto\">\n            {availableEquipment.map(item => (\n              <div\n                key={item.id}\n                className={`p-4 rounded-lg border-2 ${getCategoryColor(item.category)}`}\n              >\n                <div className=\"flex items-start justify-between mb-3\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"text-2xl\">{item.icon}</span>\n                    <div>\n                      <h3 className=\"font-semibold text-gray-800\">{item.name}</h3>\n                      <p className=\"text-xs text-gray-500 uppercase tracking-wide\">\n                        {item.category}\n                      </p>\n                    </div>\n                  </div>\n                  <span className=\"text-lg font-bold text-green-600\">${item.cost}</span>\n                </div>\n\n                <p className=\"text-sm text-gray-600 mb-3\">{item.description}</p>\n\n                <div className=\"space-y-2 mb-4\">\n                  <div className=\"flex justify-between text-sm\">\n                    <span className=\"text-gray-600\">Efficiency:</span>\n                    <span className=\"font-medium\">{item.efficiency}x</span>\n                  </div>\n                  {item.automationLevel > 0 && (\n                    <div className=\"flex justify-between text-sm\">\n                      <span className=\"text-gray-600\">Automation:</span>\n                      {getAutomationBadge(item.automationLevel)}\n                    </div>\n                  )}\n                  <div className=\"flex justify-between text-sm\">\n                    <span className=\"text-gray-600\">Unlock Level:</span>\n                    <span className=\"font-medium\">{item.unlockLevel}</span>\n                  </div>\n                </div>\n\n                <Button\n                  variant={player.money >= item.cost ? 'success' : 'secondary'}\n                  size=\"sm\"\n                  className=\"w-full\"\n                  disabled={player.money < item.cost}\n                  onClick={() => purchaseEquipment(item)}\n                >\n                  {player.money >= item.cost ? '💰 Purchase' : '💸 Too Expensive'}\n                </Button>\n              </div>\n            ))}\n          </div>\n\n          {availableEquipment.length === 0 && (\n            <div className=\"text-center py-8 text-gray-500\">\n              <div className=\"text-4xl mb-2\">🏪</div>\n              <p>No equipment available in this category.</p>\n              <p className=\"text-sm\">Level up to unlock more equipment!</p>\n            </div>\n          )}\n\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n            <h3 className=\"font-medium text-blue-800 mb-2\">💡 Equipment Tips</h3>\n            <ul className=\"text-sm text-blue-700 space-y-1\">\n              <li>• Automated equipment can run without your supervision</li>\n              <li>• Higher efficiency means faster production and better quality</li>\n              <li>• Conveyor belts connect equipment for seamless workflow</li>\n              <li>• Advanced equipment unlocks at higher levels</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAmBA,MAAM,iBAAkC;IACtC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,MAAM;QACN,UAAU;IACZ;CACD;AAOM,SAAS,mBAAmB,EAAE,MAAM,EAAE,OAAO,EAA2B;IAC7E,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM;YAAO,MAAM;QAAK;QACrC;YAAE,IAAI;YAAS,MAAM;YAAS,MAAM;QAAK;QACzC;YAAE,IAAI;YAAa,MAAM;YAAa,MAAM;QAAK;QACjD;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAI;KAC/C;IAED,MAAM,qBAAqB,eAAe,MAAM,CAAC,CAAA,OAC/C,OAAO,KAAK,IAAI,KAAK,WAAW,IAChC,CAAC,qBAAqB,SAAS,KAAK,QAAQ,KAAK,gBAAgB;IAGnE,MAAM,oBAAoB,CAAC;QACzB,IAAI,OAAO,KAAK,GAAG,KAAK,IAAI,EAAE;QAE9B,IAAI,WAAW,KAAK,IAAI,GAAG;YACzB,MAAM,eAAe;gBACnB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,UAAU;gBACV,OAAO;gBACP,YAAY,KAAK,UAAU;gBAC3B,iBAAiB,KAAK,eAAe;YACvC;YAEA,2EAA2E;YAC3E,mCAAmC;YACnC,QAAQ,GAAG,CAAC,wBAAwB;QACtC;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,UAAU,GAAG,OAAO;QACxB,qBACE,8OAAC;YAAK,WAAU;;gBAAgG;gBAC/F;;;;;;;IAGrB;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;;gDAA6B;gDAAE,OAAO,KAAK;;;;;;;;;;;;kDAE7D,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAY,SAAS;kDAAS;;;;;;;;;;;;;;;;;;;;;;;8BAOpD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC,kIAAA,CAAA,SAAM;oCAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;oCACxD,MAAK;oCACL,SAAS,IAAM,oBAAoB,SAAS,EAAE;;wCAE7C,SAAS,IAAI;wCAAC;wCAAE,SAAS,IAAI;;mCALzB,SAAS,EAAE;;;;;;;;;;sCAWtB,8OAAC;4BAAI,WAAU;sCACZ,mBAAmB,GAAG,CAAC,CAAA,qBACtB,8OAAC;oCAEC,WAAW,CAAC,wBAAwB,EAAE,iBAAiB,KAAK,QAAQ,GAAG;;sDAEvE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAY,KAAK,IAAI;;;;;;sEACrC,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA+B,KAAK,IAAI;;;;;;8EACtD,8OAAC;oEAAE,WAAU;8EACV,KAAK,QAAQ;;;;;;;;;;;;;;;;;;8DAIpB,8OAAC;oDAAK,WAAU;;wDAAmC;wDAAE,KAAK,IAAI;;;;;;;;;;;;;sDAGhE,8OAAC;4CAAE,WAAU;sDAA8B,KAAK,WAAW;;;;;;sDAE3D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;;gEAAe,KAAK,UAAU;gEAAC;;;;;;;;;;;;;gDAEhD,KAAK,eAAe,GAAG,mBACtB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;wDAC/B,mBAAmB,KAAK,eAAe;;;;;;;8DAG5C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAe,KAAK,WAAW;;;;;;;;;;;;;;;;;;sDAInD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,YAAY;4CACjD,MAAK;4CACL,WAAU;4CACV,UAAU,OAAO,KAAK,GAAG,KAAK,IAAI;4CAClC,SAAS,IAAM,kBAAkB;sDAEhC,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,gBAAgB;;;;;;;mCA1C1C,KAAK,EAAE;;;;;;;;;;wBAgDjB,mBAAmB,MAAM,KAAK,mBAC7B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;;sCAI3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}, {"offset": {"line": 5365, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/lib/saveSystem.ts"], "sourcesContent": ["// Save/Load system for Bake It Out\n\nexport interface GameSave {\n  version: string\n  timestamp: number\n  player: {\n    level: number\n    experience: number\n    money: number\n    skillPoints: number\n    totalMoneyEarned: number\n    totalOrdersCompleted: number\n    totalItemsBaked: number\n    unlockedRecipes: string[]\n    automationUpgrades: string[]\n  }\n  equipment: any[]\n  inventory: any[]\n  achievements: any[]\n  skills: any[]\n  automationSettings: any\n  gameSettings: {\n    language: string\n    soundEnabled: boolean\n    musicEnabled: boolean\n    notificationsEnabled: boolean\n    autoSaveEnabled: boolean\n  }\n  bakeries: BakeryLocation[]\n  currentBakeryId: string\n}\n\nexport interface BakeryLocation {\n  id: string\n  name: string\n  location: string\n  specialization: 'general' | 'cookies' | 'cakes' | 'bread' | 'pastries'\n  level: number\n  equipment: any[]\n  inventory: any[]\n  orders: any[]\n  automationJobs: any[]\n  conveyorBelts: any[]\n  unlocked: boolean\n  purchaseCost: number\n}\n\nexport interface CloudSaveMetadata {\n  id: string\n  userId: string\n  deviceId: string\n  lastModified: number\n  gameVersion: string\n  bakeryCount: number\n  playerLevel: number\n}\n\nconst SAVE_VERSION = '1.0.0'\nconst LOCAL_STORAGE_KEY = 'bakeItOut_gameSave'\nconst AUTO_SAVE_INTERVAL = 30000 // 30 seconds\n\nexport class SaveSystem {\n  private autoSaveInterval: NodeJS.Timeout | null = null\n  private cloudSyncEnabled = false\n\n  constructor() {\n    this.initializeAutoSave()\n  }\n\n  // Local Storage Operations\n  saveToLocal(gameData: Partial<GameSave>): boolean {\n    try {\n      const save: GameSave = {\n        version: SAVE_VERSION,\n        timestamp: Date.now(),\n        ...gameData\n      } as GameSave\n\n      const saveString = JSON.stringify(save)\n      localStorage.setItem(LOCAL_STORAGE_KEY, saveString)\n      \n      console.log('Game saved to local storage')\n      return true\n    } catch (error) {\n      console.error('Failed to save game to local storage:', error)\n      return false\n    }\n  }\n\n  loadFromLocal(): GameSave | null {\n    try {\n      const saveString = localStorage.getItem(LOCAL_STORAGE_KEY)\n      if (!saveString) return null\n\n      const save: GameSave = JSON.parse(saveString)\n      \n      // Version compatibility check\n      if (save.version !== SAVE_VERSION) {\n        console.warn('Save version mismatch, attempting migration')\n        return this.migrateSave(save)\n      }\n\n      console.log('Game loaded from local storage')\n      return save\n    } catch (error) {\n      console.error('Failed to load game from local storage:', error)\n      return null\n    }\n  }\n\n  deleteLocalSave(): boolean {\n    try {\n      localStorage.removeItem(LOCAL_STORAGE_KEY)\n      console.log('Local save deleted')\n      return true\n    } catch (error) {\n      console.error('Failed to delete local save:', error)\n      return false\n    }\n  }\n\n  // Auto-save functionality\n  initializeAutoSave() {\n    if (typeof window !== 'undefined') {\n      this.autoSaveInterval = setInterval(() => {\n        this.triggerAutoSave()\n      }, AUTO_SAVE_INTERVAL)\n    }\n  }\n\n  private triggerAutoSave() {\n    // This would be called by the game context to auto-save\n    const event = new CustomEvent('autoSave')\n    window.dispatchEvent(event)\n  }\n\n  stopAutoSave() {\n    if (this.autoSaveInterval) {\n      clearInterval(this.autoSaveInterval)\n      this.autoSaveInterval = null\n    }\n  }\n\n  // Cloud Save Operations (placeholder for future implementation)\n  async saveToCloud(gameData: GameSave, userId: string): Promise<boolean> {\n    try {\n      // This would integrate with a cloud service like Firebase, Supabase, etc.\n      const metadata: CloudSaveMetadata = {\n        id: `${userId}_${Date.now()}`,\n        userId,\n        deviceId: this.getDeviceId(),\n        lastModified: Date.now(),\n        gameVersion: SAVE_VERSION,\n        bakeryCount: gameData.bakeries?.length || 1,\n        playerLevel: gameData.player.level\n      }\n\n      // Placeholder for cloud save implementation\n      console.log('Cloud save would be implemented here', { gameData, metadata })\n      return true\n    } catch (error) {\n      console.error('Failed to save to cloud:', error)\n      return false\n    }\n  }\n\n  async loadFromCloud(userId: string): Promise<GameSave | null> {\n    try {\n      // Placeholder for cloud load implementation\n      console.log('Cloud load would be implemented here', { userId })\n      return null\n    } catch (error) {\n      console.error('Failed to load from cloud:', error)\n      return null\n    }\n  }\n\n  async syncWithCloud(localSave: GameSave, userId: string): Promise<GameSave> {\n    try {\n      const cloudSave = await this.loadFromCloud(userId)\n      \n      if (!cloudSave) {\n        // No cloud save exists, upload local save\n        await this.saveToCloud(localSave, userId)\n        return localSave\n      }\n\n      // Compare timestamps and merge\n      if (cloudSave.timestamp > localSave.timestamp) {\n        console.log('Cloud save is newer, using cloud data')\n        return cloudSave\n      } else {\n        console.log('Local save is newer, uploading to cloud')\n        await this.saveToCloud(localSave, userId)\n        return localSave\n      }\n    } catch (error) {\n      console.error('Failed to sync with cloud:', error)\n      return localSave\n    }\n  }\n\n  // Save migration for version compatibility\n  private migrateSave(oldSave: any): GameSave | null {\n    try {\n      // Handle migration from older save versions\n      const migratedSave: GameSave = {\n        version: SAVE_VERSION,\n        timestamp: oldSave.timestamp || Date.now(),\n        player: {\n          level: oldSave.player?.level || 1,\n          experience: oldSave.player?.experience || 0,\n          money: oldSave.player?.money || 100,\n          skillPoints: oldSave.player?.skillPoints || 0,\n          totalMoneyEarned: oldSave.player?.totalMoneyEarned || 0,\n          totalOrdersCompleted: oldSave.player?.totalOrdersCompleted || 0,\n          totalItemsBaked: oldSave.player?.totalItemsBaked || 0,\n          unlockedRecipes: oldSave.player?.unlockedRecipes || ['chocolate_chip_cookies', 'vanilla_muffins'],\n          automationUpgrades: oldSave.player?.automationUpgrades || []\n        },\n        equipment: oldSave.equipment || [],\n        inventory: oldSave.inventory || [],\n        achievements: oldSave.achievements || [],\n        skills: oldSave.skills || [],\n        automationSettings: oldSave.automationSettings || {},\n        gameSettings: {\n          language: oldSave.gameSettings?.language || 'en',\n          soundEnabled: oldSave.gameSettings?.soundEnabled ?? true,\n          musicEnabled: oldSave.gameSettings?.musicEnabled ?? true,\n          notificationsEnabled: oldSave.gameSettings?.notificationsEnabled ?? true,\n          autoSaveEnabled: oldSave.gameSettings?.autoSaveEnabled ?? true\n        },\n        bakeries: oldSave.bakeries || [],\n        currentBakeryId: oldSave.currentBakeryId || 'main'\n      }\n\n      console.log('Save migrated successfully')\n      return migratedSave\n    } catch (error) {\n      console.error('Failed to migrate save:', error)\n      return null\n    }\n  }\n\n  // Utility functions\n  private getDeviceId(): string {\n    let deviceId = localStorage.getItem('deviceId')\n    if (!deviceId) {\n      deviceId = 'device_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)\n      localStorage.setItem('deviceId', deviceId)\n    }\n    return deviceId\n  }\n\n  exportSave(gameData: GameSave): string {\n    return JSON.stringify(gameData, null, 2)\n  }\n\n  importSave(saveString: string): GameSave | null {\n    try {\n      const save = JSON.parse(saveString)\n      return this.migrateSave(save)\n    } catch (error) {\n      console.error('Failed to import save:', error)\n      return null\n    }\n  }\n\n  // Backup management\n  createBackup(gameData: GameSave): boolean {\n    try {\n      const backupKey = `${LOCAL_STORAGE_KEY}_backup_${Date.now()}`\n      localStorage.setItem(backupKey, JSON.stringify(gameData))\n      \n      // Keep only the 5 most recent backups\n      this.cleanupOldBackups()\n      return true\n    } catch (error) {\n      console.error('Failed to create backup:', error)\n      return false\n    }\n  }\n\n  private cleanupOldBackups() {\n    const backupKeys = Object.keys(localStorage)\n      .filter(key => key.startsWith(`${LOCAL_STORAGE_KEY}_backup_`))\n      .sort()\n\n    while (backupKeys.length > 5) {\n      const oldestKey = backupKeys.shift()\n      if (oldestKey) {\n        localStorage.removeItem(oldestKey)\n      }\n    }\n  }\n\n  getBackups(): Array<{ key: string; timestamp: number; data: GameSave }> {\n    const backups: Array<{ key: string; timestamp: number; data: GameSave }> = []\n    \n    Object.keys(localStorage).forEach(key => {\n      if (key.startsWith(`${LOCAL_STORAGE_KEY}_backup_`)) {\n        try {\n          const data = JSON.parse(localStorage.getItem(key) || '{}')\n          const timestamp = parseInt(key.split('_').pop() || '0')\n          backups.push({ key, timestamp, data })\n        } catch (error) {\n          console.error('Failed to parse backup:', error)\n        }\n      }\n    })\n\n    return backups.sort((a, b) => b.timestamp - a.timestamp)\n  }\n}\n\nexport const saveSystem = new SaveSystem()\n"], "names": [], "mappings": "AAAA,mCAAmC;;;;;AAyDnC,MAAM,eAAe;AACrB,MAAM,oBAAoB;AAC1B,MAAM,qBAAqB,MAAM,aAAa;;AAEvC,MAAM;IACH,mBAA0C,KAAI;IAC9C,mBAAmB,MAAK;IAEhC,aAAc;QACZ,IAAI,CAAC,kBAAkB;IACzB;IAEA,2BAA2B;IAC3B,YAAY,QAA2B,EAAW;QAChD,IAAI;YACF,MAAM,OAAiB;gBACrB,SAAS;gBACT,WAAW,KAAK,GAAG;gBACnB,GAAG,QAAQ;YACb;YAEA,MAAM,aAAa,KAAK,SAAS,CAAC;YAClC,aAAa,OAAO,CAAC,mBAAmB;YAExC,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;QACT;IACF;IAEA,gBAAiC;QAC/B,IAAI;YACF,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,CAAC,YAAY,OAAO;YAExB,MAAM,OAAiB,KAAK,KAAK,CAAC;YAElC,8BAA8B;YAC9B,IAAI,KAAK,OAAO,KAAK,cAAc;gBACjC,QAAQ,IAAI,CAAC;gBACb,OAAO,IAAI,CAAC,WAAW,CAAC;YAC1B;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO;QACT;IACF;IAEA,kBAA2B;QACzB,IAAI;YACF,aAAa,UAAU,CAAC;YACxB,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;QACT;IACF;IAEA,0BAA0B;IAC1B,qBAAqB;QACnB;;IAKF;IAEQ,kBAAkB;QACxB,wDAAwD;QACxD,MAAM,QAAQ,IAAI,YAAY;QAC9B,OAAO,aAAa,CAAC;IACvB;IAEA,eAAe;QACb,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,cAAc,IAAI,CAAC,gBAAgB;YACnC,IAAI,CAAC,gBAAgB,GAAG;QAC1B;IACF;IAEA,gEAAgE;IAChE,MAAM,YAAY,QAAkB,EAAE,MAAc,EAAoB;QACtE,IAAI;YACF,0EAA0E;YAC1E,MAAM,WAA8B;gBAClC,IAAI,GAAG,OAAO,CAAC,EAAE,KAAK,GAAG,IAAI;gBAC7B;gBACA,UAAU,IAAI,CAAC,WAAW;gBAC1B,cAAc,KAAK,GAAG;gBACtB,aAAa;gBACb,aAAa,SAAS,QAAQ,EAAE,UAAU;gBAC1C,aAAa,SAAS,MAAM,CAAC,KAAK;YACpC;YAEA,4CAA4C;YAC5C,QAAQ,GAAG,CAAC,wCAAwC;gBAAE;gBAAU;YAAS;YACzE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF;IAEA,MAAM,cAAc,MAAc,EAA4B;QAC5D,IAAI;YACF,4CAA4C;YAC5C,QAAQ,GAAG,CAAC,wCAAwC;gBAAE;YAAO;YAC7D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;QACT;IACF;IAEA,MAAM,cAAc,SAAmB,EAAE,MAAc,EAAqB;QAC1E,IAAI;YACF,MAAM,YAAY,MAAM,IAAI,CAAC,aAAa,CAAC;YAE3C,IAAI,CAAC,WAAW;gBACd,0CAA0C;gBAC1C,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW;gBAClC,OAAO;YACT;YAEA,+BAA+B;YAC/B,IAAI,UAAU,SAAS,GAAG,UAAU,SAAS,EAAE;gBAC7C,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW;gBAClC,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;QACT;IACF;IAEA,2CAA2C;IACnC,YAAY,OAAY,EAAmB;QACjD,IAAI;YACF,4CAA4C;YAC5C,MAAM,eAAyB;gBAC7B,SAAS;gBACT,WAAW,QAAQ,SAAS,IAAI,KAAK,GAAG;gBACxC,QAAQ;oBACN,OAAO,QAAQ,MAAM,EAAE,SAAS;oBAChC,YAAY,QAAQ,MAAM,EAAE,cAAc;oBAC1C,OAAO,QAAQ,MAAM,EAAE,SAAS;oBAChC,aAAa,QAAQ,MAAM,EAAE,eAAe;oBAC5C,kBAAkB,QAAQ,MAAM,EAAE,oBAAoB;oBACtD,sBAAsB,QAAQ,MAAM,EAAE,wBAAwB;oBAC9D,iBAAiB,QAAQ,MAAM,EAAE,mBAAmB;oBACpD,iBAAiB,QAAQ,MAAM,EAAE,mBAAmB;wBAAC;wBAA0B;qBAAkB;oBACjG,oBAAoB,QAAQ,MAAM,EAAE,sBAAsB,EAAE;gBAC9D;gBACA,WAAW,QAAQ,SAAS,IAAI,EAAE;gBAClC,WAAW,QAAQ,SAAS,IAAI,EAAE;gBAClC,cAAc,QAAQ,YAAY,IAAI,EAAE;gBACxC,QAAQ,QAAQ,MAAM,IAAI,EAAE;gBAC5B,oBAAoB,QAAQ,kBAAkB,IAAI,CAAC;gBACnD,cAAc;oBACZ,UAAU,QAAQ,YAAY,EAAE,YAAY;oBAC5C,cAAc,QAAQ,YAAY,EAAE,gBAAgB;oBACpD,cAAc,QAAQ,YAAY,EAAE,gBAAgB;oBACpD,sBAAsB,QAAQ,YAAY,EAAE,wBAAwB;oBACpE,iBAAiB,QAAQ,YAAY,EAAE,mBAAmB;gBAC5D;gBACA,UAAU,QAAQ,QAAQ,IAAI,EAAE;gBAChC,iBAAiB,QAAQ,eAAe,IAAI;YAC9C;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;QACT;IACF;IAEA,oBAAoB;IACZ,cAAsB;QAC5B,IAAI,WAAW,aAAa,OAAO,CAAC;QACpC,IAAI,CAAC,UAAU;YACb,WAAW,YAAY,KAAK,GAAG,KAAK,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YAC/E,aAAa,OAAO,CAAC,YAAY;QACnC;QACA,OAAO;IACT;IAEA,WAAW,QAAkB,EAAU;QACrC,OAAO,KAAK,SAAS,CAAC,UAAU,MAAM;IACxC;IAEA,WAAW,UAAkB,EAAmB;QAC9C,IAAI;YACF,MAAM,OAAO,KAAK,KAAK,CAAC;YACxB,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,aAAa,QAAkB,EAAW;QACxC,IAAI;YACF,MAAM,YAAY,GAAG,kBAAkB,QAAQ,EAAE,KAAK,GAAG,IAAI;YAC7D,aAAa,OAAO,CAAC,WAAW,KAAK,SAAS,CAAC;YAE/C,sCAAsC;YACtC,IAAI,CAAC,iBAAiB;YACtB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF;IAEQ,oBAAoB;QAC1B,MAAM,aAAa,OAAO,IAAI,CAAC,cAC5B,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,GAAG,kBAAkB,QAAQ,CAAC,GAC3D,IAAI;QAEP,MAAO,WAAW,MAAM,GAAG,EAAG;YAC5B,MAAM,YAAY,WAAW,KAAK;YAClC,IAAI,WAAW;gBACb,aAAa,UAAU,CAAC;YAC1B;QACF;IACF;IAEA,aAAwE;QACtE,MAAM,UAAqE,EAAE;QAE7E,OAAO,IAAI,CAAC,cAAc,OAAO,CAAC,CAAA;YAChC,IAAI,IAAI,UAAU,CAAC,GAAG,kBAAkB,QAAQ,CAAC,GAAG;gBAClD,IAAI;oBACF,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,QAAQ;oBACrD,MAAM,YAAY,SAAS,IAAI,KAAK,CAAC,KAAK,GAAG,MAAM;oBACnD,QAAQ,IAAI,CAAC;wBAAE;wBAAK;wBAAW;oBAAK;gBACtC,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,2BAA2B;gBAC3C;YACF;QACF;QAEA,OAAO,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;IACzD;AACF;AAEO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 5609, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/SettingsModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { useLanguage } from '@/contexts/LanguageContext'\nimport { saveSystem } from '@/lib/saveSystem'\n\ninterface GameSettings {\n  language: 'en' | 'cs'\n  soundEnabled: boolean\n  musicEnabled: boolean\n  notificationsEnabled: boolean\n  autoSaveEnabled: boolean\n  graphicsQuality: 'low' | 'medium' | 'high'\n  animationSpeed: number\n  showTutorials: boolean\n}\n\ninterface SettingsModalProps {\n  isOpen: boolean\n  onClose: () => void\n  settings: GameSettings\n  onSettingsChange: (settings: Partial<GameSettings>) => void\n}\n\nexport function SettingsModal({ isOpen, onClose, settings, onSettingsChange }: SettingsModalProps) {\n  const { language, setLanguage, t } = useLanguage()\n  const [activeTab, setActiveTab] = useState<'general' | 'audio' | 'graphics' | 'save'>('general')\n\n  if (!isOpen) return null\n\n  const handleSettingChange = (key: keyof GameSettings, value: any) => {\n    onSettingsChange({ [key]: value })\n  }\n\n  const handleLanguageChange = (newLanguage: 'en' | 'cs') => {\n    setLanguage(newLanguage)\n    handleSettingChange('language', newLanguage)\n  }\n\n  const exportSave = () => {\n    // This would get the current game state and export it\n    const gameData = {\n      version: '1.0.0',\n      timestamp: Date.now(),\n      player: {}, // Would be filled with actual game data\n      equipment: [],\n      inventory: [],\n      achievements: [],\n      skills: [],\n      automationSettings: {},\n      gameSettings: settings,\n      bakeries: [],\n      currentBakeryId: 'main'\n    }\n    \n    const saveString = saveSystem.exportSave(gameData as any)\n    const blob = new Blob([saveString], { type: 'application/json' })\n    const url = URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = `bake-it-out-save-${Date.now()}.json`\n    a.click()\n    URL.revokeObjectURL(url)\n  }\n\n  const importSave = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (!file) return\n\n    const reader = new FileReader()\n    reader.onload = (e) => {\n      try {\n        const saveString = e.target?.result as string\n        const gameData = saveSystem.importSave(saveString)\n        if (gameData) {\n          // This would load the imported data into the game\n          console.log('Save imported successfully:', gameData)\n          alert('Save imported successfully!')\n        } else {\n          alert('Failed to import save file')\n        }\n      } catch (error) {\n        alert('Invalid save file')\n      }\n    }\n    reader.readAsText(file)\n  }\n\n  const tabs = [\n    { id: 'general', name: t('settings.general') || 'General', icon: '⚙️' },\n    { id: 'audio', name: t('settings.audio') || 'Audio', icon: '🔊' },\n    { id: 'graphics', name: t('settings.graphics') || 'Graphics', icon: '🎨' },\n    { id: 'save', name: t('settings.save') || 'Save & Data', icon: '💾' }\n  ]\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-2xl font-bold text-orange-800\">\n              {t('settings.title') || '⚙️ Settings'}\n            </h2>\n            <Button variant=\"secondary\" onClick={onClose}>\n              {t('game.close') || '✕ Close'}\n            </Button>\n          </div>\n        </div>\n\n        {/* Tab Navigation */}\n        <div className=\"border-b border-gray-200\">\n          <div className=\"flex space-x-0\">\n            {tabs.map(tab => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id as any)}\n                className={`px-4 py-3 font-medium text-sm border-b-2 transition-colors ${\n                  activeTab === tab.id\n                    ? 'border-orange-500 text-orange-600 bg-orange-50'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'\n                }`}\n              >\n                {tab.icon} {tab.name}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"p-6 max-h-[60vh] overflow-y-auto\">\n          {/* General Settings */}\n          {activeTab === 'general' && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"font-semibold text-gray-800 mb-3\">\n                  {t('settings.language') || '🌍 Language'}\n                </h3>\n                <div className=\"flex space-x-2\">\n                  <Button\n                    variant={language === 'en' ? 'primary' : 'secondary'}\n                    size=\"sm\"\n                    onClick={() => handleLanguageChange('en')}\n                  >\n                    🇺🇸 English\n                  </Button>\n                  <Button\n                    variant={language === 'cs' ? 'primary' : 'secondary'}\n                    size=\"sm\"\n                    onClick={() => handleLanguageChange('cs')}\n                  >\n                    🇨🇿 Čeština\n                  </Button>\n                </div>\n              </div>\n\n              <div>\n                <h3 className=\"font-semibold text-gray-800 mb-3\">\n                  {t('settings.gameplay') || '🎮 Gameplay'}\n                </h3>\n                <div className=\"space-y-3\">\n                  <label className=\"flex items-center justify-between\">\n                    <span>{t('settings.notifications') || 'Enable Notifications'}</span>\n                    <input\n                      type=\"checkbox\"\n                      checked={settings.notificationsEnabled}\n                      onChange={(e) => handleSettingChange('notificationsEnabled', e.target.checked)}\n                      className=\"rounded\"\n                    />\n                  </label>\n                  <label className=\"flex items-center justify-between\">\n                    <span>{t('settings.tutorials') || 'Show Tutorials'}</span>\n                    <input\n                      type=\"checkbox\"\n                      checked={settings.showTutorials}\n                      onChange={(e) => handleSettingChange('showTutorials', e.target.checked)}\n                      className=\"rounded\"\n                    />\n                  </label>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      {t('settings.animationSpeed') || 'Animation Speed'}: {settings.animationSpeed}x\n                    </label>\n                    <input\n                      type=\"range\"\n                      min=\"0.5\"\n                      max=\"2\"\n                      step=\"0.1\"\n                      value={settings.animationSpeed}\n                      onChange={(e) => handleSettingChange('animationSpeed', parseFloat(e.target.value))}\n                      className=\"w-full\"\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Audio Settings */}\n          {activeTab === 'audio' && (\n            <div className=\"space-y-6\">\n              <div className=\"space-y-4\">\n                <label className=\"flex items-center justify-between\">\n                  <span className=\"flex items-center space-x-2\">\n                    <span>🔊</span>\n                    <span>{t('settings.sound') || 'Sound Effects'}</span>\n                  </span>\n                  <input\n                    type=\"checkbox\"\n                    checked={settings.soundEnabled}\n                    onChange={(e) => handleSettingChange('soundEnabled', e.target.checked)}\n                    className=\"rounded\"\n                  />\n                </label>\n                <label className=\"flex items-center justify-between\">\n                  <span className=\"flex items-center space-x-2\">\n                    <span>🎵</span>\n                    <span>{t('settings.music') || 'Background Music'}</span>\n                  </span>\n                  <input\n                    type=\"checkbox\"\n                    checked={settings.musicEnabled}\n                    onChange={(e) => handleSettingChange('musicEnabled', e.target.checked)}\n                    className=\"rounded\"\n                  />\n                </label>\n              </div>\n            </div>\n          )}\n\n          {/* Graphics Settings */}\n          {activeTab === 'graphics' && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"font-semibold text-gray-800 mb-3\">\n                  {t('settings.quality') || '🎨 Graphics Quality'}\n                </h3>\n                <div className=\"space-x-2\">\n                  {['low', 'medium', 'high'].map(quality => (\n                    <Button\n                      key={quality}\n                      variant={settings.graphicsQuality === quality ? 'primary' : 'secondary'}\n                      size=\"sm\"\n                      onClick={() => handleSettingChange('graphicsQuality', quality)}\n                    >\n                      {quality.charAt(0).toUpperCase() + quality.slice(1)}\n                    </Button>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Save & Data Settings */}\n          {activeTab === 'save' && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"font-semibold text-gray-800 mb-3\">\n                  {t('settings.autoSave') || '💾 Auto-Save'}\n                </h3>\n                <label className=\"flex items-center justify-between\">\n                  <span>{t('settings.enableAutoSave') || 'Enable Auto-Save'}</span>\n                  <input\n                    type=\"checkbox\"\n                    checked={settings.autoSaveEnabled}\n                    onChange={(e) => handleSettingChange('autoSaveEnabled', e.target.checked)}\n                    className=\"rounded\"\n                  />\n                </label>\n              </div>\n\n              <div>\n                <h3 className=\"font-semibold text-gray-800 mb-3\">\n                  {t('settings.dataManagement') || '📁 Data Management'}\n                </h3>\n                <div className=\"space-y-3\">\n                  <Button variant=\"secondary\" onClick={exportSave} className=\"w-full\">\n                    {t('settings.exportSave') || '📤 Export Save'}\n                  </Button>\n                  <div>\n                    <input\n                      type=\"file\"\n                      accept=\".json\"\n                      onChange={importSave}\n                      className=\"hidden\"\n                      id=\"import-save\"\n                    />\n                    <Button\n                      variant=\"secondary\"\n                      onClick={() => document.getElementById('import-save')?.click()}\n                      className=\"w-full\"\n                    >\n                      {t('settings.importSave') || '📥 Import Save'}\n                    </Button>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-yellow-50 p-4 rounded-lg\">\n                <h4 className=\"font-medium text-yellow-800 mb-2\">\n                  {t('settings.cloudSync') || '☁️ Cloud Sync'}\n                </h4>\n                <p className=\"text-sm text-yellow-700 mb-3\">\n                  {t('settings.cloudSyncDescription') || 'Cloud sync allows you to save your progress online and play across multiple devices.'}\n                </p>\n                <Button variant=\"secondary\" size=\"sm\" disabled>\n                  {t('settings.comingSoon') || 'Coming Soon'}\n                </Button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAyBO,SAAS,cAAc,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,gBAAgB,EAAsB;IAC/F,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6C;IAEtF,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,sBAAsB,CAAC,KAAyB;QACpD,iBAAiB;YAAE,CAAC,IAAI,EAAE;QAAM;IAClC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY;QACZ,oBAAoB,YAAY;IAClC;IAEA,MAAM,aAAa;QACjB,sDAAsD;QACtD,MAAM,WAAW;YACf,SAAS;YACT,WAAW,KAAK,GAAG;YACnB,QAAQ,CAAC;YACT,WAAW,EAAE;YACb,WAAW,EAAE;YACb,cAAc,EAAE;YAChB,QAAQ,EAAE;YACV,oBAAoB,CAAC;YACrB,cAAc;YACd,UAAU,EAAE;YACZ,iBAAiB;QACnB;QAEA,MAAM,aAAa,wHAAA,CAAA,aAAU,CAAC,UAAU,CAAC;QACzC,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAmB;QAC/D,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,iBAAiB,EAAE,KAAK,GAAG,GAAG,KAAK,CAAC;QAClD,EAAE,KAAK;QACP,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,IAAI;gBACF,MAAM,aAAa,EAAE,MAAM,EAAE;gBAC7B,MAAM,WAAW,wHAAA,CAAA,aAAU,CAAC,UAAU,CAAC;gBACvC,IAAI,UAAU;oBACZ,kDAAkD;oBAClD,QAAQ,GAAG,CAAC,+BAA+B;oBAC3C,MAAM;gBACR,OAAO;oBACL,MAAM;gBACR;YACF,EAAE,OAAO,OAAO;gBACd,MAAM;YACR;QACF;QACA,OAAO,UAAU,CAAC;IACpB;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAW,MAAM,EAAE,uBAAuB;YAAW,MAAM;QAAK;QACtE;YAAE,IAAI;YAAS,MAAM,EAAE,qBAAqB;YAAS,MAAM;QAAK;QAChE;YAAE,IAAI;YAAY,MAAM,EAAE,wBAAwB;YAAY,MAAM;QAAK;QACzE;YAAE,IAAI;YAAQ,MAAM,EAAE,oBAAoB;YAAe,MAAM;QAAK;KACrE;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,EAAE,qBAAqB;;;;;;0CAE1B,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,SAAS;0CAClC,EAAE,iBAAiB;;;;;;;;;;;;;;;;;8BAM1B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAA,oBACR,8OAAC;gCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;gCAClC,WAAW,CAAC,2DAA2D,EACrE,cAAc,IAAI,EAAE,GAChB,mDACA,yEACJ;;oCAED,IAAI,IAAI;oCAAC;oCAAE,IAAI,IAAI;;+BARf,IAAI,EAAE;;;;;;;;;;;;;;;8BAcnB,8OAAC;oBAAI,WAAU;;wBAEZ,cAAc,2BACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,EAAE,wBAAwB;;;;;;sDAE7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,aAAa,OAAO,YAAY;oDACzC,MAAK;oDACL,SAAS,IAAM,qBAAqB;8DACrC;;;;;;8DAGD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,aAAa,OAAO,YAAY;oDACzC,MAAK;oDACL,SAAS,IAAM,qBAAqB;8DACrC;;;;;;;;;;;;;;;;;;8CAML,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,EAAE,wBAAwB;;;;;;sDAE7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;sEAAM,EAAE,6BAA6B;;;;;;sEACtC,8OAAC;4DACC,MAAK;4DACL,SAAS,SAAS,oBAAoB;4DACtC,UAAU,CAAC,IAAM,oBAAoB,wBAAwB,EAAE,MAAM,CAAC,OAAO;4DAC7E,WAAU;;;;;;;;;;;;8DAGd,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;sEAAM,EAAE,yBAAyB;;;;;;sEAClC,8OAAC;4DACC,MAAK;4DACL,SAAS,SAAS,aAAa;4DAC/B,UAAU,CAAC,IAAM,oBAAoB,iBAAiB,EAAE,MAAM,CAAC,OAAO;4DACtE,WAAU;;;;;;;;;;;;8DAGd,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;;gEACd,EAAE,8BAA8B;gEAAkB;gEAAG,SAAS,cAAc;gEAAC;;;;;;;sEAEhF,8OAAC;4DACC,MAAK;4DACL,KAAI;4DACJ,KAAI;4DACJ,MAAK;4DACL,OAAO,SAAS,cAAc;4DAC9B,UAAU,CAAC,IAAM,oBAAoB,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;4DAChF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASrB,cAAc,yBACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAK,WAAU;;kEACd,8OAAC;kEAAK;;;;;;kEACN,8OAAC;kEAAM,EAAE,qBAAqB;;;;;;;;;;;;0DAEhC,8OAAC;gDACC,MAAK;gDACL,SAAS,SAAS,YAAY;gDAC9B,UAAU,CAAC,IAAM,oBAAoB,gBAAgB,EAAE,MAAM,CAAC,OAAO;gDACrE,WAAU;;;;;;;;;;;;kDAGd,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAK,WAAU;;kEACd,8OAAC;kEAAK;;;;;;kEACN,8OAAC;kEAAM,EAAE,qBAAqB;;;;;;;;;;;;0DAEhC,8OAAC;gDACC,MAAK;gDACL,SAAS,SAAS,YAAY;gDAC9B,UAAU,CAAC,IAAM,oBAAoB,gBAAgB,EAAE,MAAM,CAAC,OAAO;gDACrE,WAAU;;;;;;;;;;;;;;;;;;;;;;;wBAQnB,cAAc,4BACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,EAAE,uBAAuB;;;;;;kDAE5B,8OAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAO;4CAAU;yCAAO,CAAC,GAAG,CAAC,CAAA,wBAC7B,8OAAC,kIAAA,CAAA,SAAM;gDAEL,SAAS,SAAS,eAAe,KAAK,UAAU,YAAY;gDAC5D,MAAK;gDACL,SAAS,IAAM,oBAAoB,mBAAmB;0DAErD,QAAQ,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,KAAK,CAAC;+CAL5C;;;;;;;;;;;;;;;;;;;;;wBAchB,cAAc,wBACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,EAAE,wBAAwB;;;;;;sDAE7B,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;8DAAM,EAAE,8BAA8B;;;;;;8DACvC,8OAAC;oDACC,MAAK;oDACL,SAAS,SAAS,eAAe;oDACjC,UAAU,CAAC,IAAM,oBAAoB,mBAAmB,EAAE,MAAM,CAAC,OAAO;oDACxE,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,EAAE,8BAA8B;;;;;;sDAEnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAY,SAAS;oDAAY,WAAU;8DACxD,EAAE,0BAA0B;;;;;;8DAE/B,8OAAC;;sEACC,8OAAC;4DACC,MAAK;4DACL,QAAO;4DACP,UAAU;4DACV,WAAU;4DACV,IAAG;;;;;;sEAEL,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,SAAS,IAAM,SAAS,cAAc,CAAC,gBAAgB;4DACvD,WAAU;sEAET,EAAE,0BAA0B;;;;;;;;;;;;;;;;;;;;;;;;8CAMrC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,EAAE,yBAAyB;;;;;;sDAE9B,8OAAC;4CAAE,WAAU;sDACV,EAAE,oCAAoC;;;;;;sDAEzC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAY,MAAK;4CAAK,QAAQ;sDAC3C,EAAE,0BAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C", "debugId": null}}, {"offset": {"line": 6256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/BakeryManagerModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { useLanguage } from '@/contexts/LanguageContext'\nimport { BakeryLocation } from '@/lib/saveSystem'\n\ninterface BakeryManagerModalProps {\n  isOpen: boolean\n  onClose: () => void\n  bakeries: BakeryLocation[]\n  currentBakeryId: string\n  onSwitchBakery: (bakeryId: string) => void\n  onPurchaseBakery: (bakery: BakeryLocation) => void\n  playerMoney: number\n}\n\nconst AVAILABLE_BAKERIES: Omit<BakeryLocation, 'id' | 'unlocked'>[] = [\n  {\n    name: 'Downtown Delights',\n    location: 'City Center',\n    specialization: 'general',\n    level: 1,\n    equipment: [],\n    inventory: [],\n    orders: [],\n    automationJobs: [],\n    conveyorBelts: [],\n    purchaseCost: 0 // Main bakery is free\n  },\n  {\n    name: 'Cookie Corner',\n    location: 'Shopping Mall',\n    specialization: 'cookies',\n    level: 1,\n    equipment: [],\n    inventory: [],\n    orders: [],\n    automationJobs: [],\n    conveyorBelts: [],\n    purchaseCost: 2500\n  },\n  {\n    name: 'Cake Castle',\n    location: 'Wedding District',\n    specialization: 'cakes',\n    level: 1,\n    equipment: [],\n    inventory: [],\n    orders: [],\n    automationJobs: [],\n    conveyorBelts: [],\n    purchaseCost: 3500\n  },\n  {\n    name: 'Bread Basket',\n    location: 'Farmers Market',\n    specialization: 'bread',\n    level: 1,\n    equipment: [],\n    inventory: [],\n    orders: [],\n    automationJobs: [],\n    conveyorBelts: [],\n    purchaseCost: 3000\n  },\n  {\n    name: 'Pastry Palace',\n    location: 'French Quarter',\n    specialization: 'pastries',\n    level: 1,\n    equipment: [],\n    inventory: [],\n    orders: [],\n    automationJobs: [],\n    conveyorBelts: [],\n    purchaseCost: 4000\n  }\n]\n\nexport function BakeryManagerModal({\n  isOpen,\n  onClose,\n  bakeries,\n  currentBakeryId,\n  onSwitchBakery,\n  onPurchaseBakery,\n  playerMoney\n}: BakeryManagerModalProps) {\n  const { t } = useLanguage()\n  const [selectedTab, setSelectedTab] = useState<'owned' | 'available'>('owned')\n\n  if (!isOpen) return null\n\n  const ownedBakeries = bakeries.filter(b => b.unlocked)\n  const availableBakeries = AVAILABLE_BAKERIES.filter(ab => \n    !bakeries.some(b => b.name === ab.name && b.unlocked)\n  )\n\n  const getSpecializationIcon = (specialization: string) => {\n    switch (specialization) {\n      case 'cookies': return '🍪'\n      case 'cakes': return '🧁'\n      case 'bread': return '🍞'\n      case 'pastries': return '🥐'\n      default: return '🏪'\n    }\n  }\n\n  const getSpecializationBonus = (specialization: string) => {\n    switch (specialization) {\n      case 'cookies': return '+20% Cookie Production Speed'\n      case 'cakes': return '+25% Cake Profit Margin'\n      case 'bread': return '+15% Bread Ingredient Efficiency'\n      case 'pastries': return '+30% Pastry Experience Gain'\n      default: return 'Balanced Production'\n    }\n  }\n\n  const handlePurchaseBakery = (bakery: Omit<BakeryLocation, 'id' | 'unlocked'>) => {\n    if (playerMoney >= bakery.purchaseCost) {\n      const newBakery: BakeryLocation = {\n        ...bakery,\n        id: `bakery_${Date.now()}`,\n        unlocked: true\n      }\n      onPurchaseBakery(newBakery)\n    }\n  }\n\n  const tabs = [\n    { id: 'owned', name: t('bakeries.owned') || 'My Bakeries', icon: '🏪' },\n    { id: 'available', name: t('bakeries.available') || 'Available', icon: '🛒' }\n  ]\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-orange-800\">\n                {t('bakeries.title') || '🏪 Bakery Manager'}\n              </h2>\n              <p className=\"text-gray-600\">\n                {t('bakeries.subtitle') || 'Manage your bakery empire'}\n              </p>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"bg-green-100 px-3 py-1 rounded-full\">\n                <span className=\"text-green-800 font-medium\">${playerMoney}</span>\n              </div>\n              <Button variant=\"secondary\" onClick={onClose}>\n                {t('game.close') || '✕ Close'}\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Tab Navigation */}\n        <div className=\"border-b border-gray-200\">\n          <div className=\"flex space-x-0\">\n            {tabs.map(tab => (\n              <button\n                key={tab.id}\n                onClick={() => setSelectedTab(tab.id as any)}\n                className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${\n                  selectedTab === tab.id\n                    ? 'border-orange-500 text-orange-600 bg-orange-50'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'\n                }`}\n              >\n                {tab.icon} {tab.name}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"p-6 max-h-[60vh] overflow-y-auto\">\n          {/* Owned Bakeries */}\n          {selectedTab === 'owned' && (\n            <div className=\"space-y-4\">\n              {ownedBakeries.length > 0 ? (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  {ownedBakeries.map(bakery => (\n                    <div\n                      key={bakery.id}\n                      className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${\n                        bakery.id === currentBakeryId\n                          ? 'border-orange-400 bg-orange-50'\n                          : 'border-gray-300 bg-white hover:border-orange-300'\n                      }`}\n                      onClick={() => onSwitchBakery(bakery.id)}\n                    >\n                      <div className=\"flex items-start justify-between mb-3\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-2xl\">{getSpecializationIcon(bakery.specialization)}</span>\n                          <div>\n                            <h3 className=\"font-semibold text-gray-800\">{bakery.name}</h3>\n                            <p className=\"text-sm text-gray-600\">{bakery.location}</p>\n                          </div>\n                        </div>\n                        {bakery.id === currentBakeryId && (\n                          <span className=\"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full\">\n                            {t('bakeries.current') || 'Current'}\n                          </span>\n                        )}\n                      </div>\n\n                      <div className=\"space-y-2 text-sm\">\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">{t('bakeries.level') || 'Level'}:</span>\n                          <span className=\"font-medium\">{bakery.level}</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">{t('bakeries.specialization') || 'Specialization'}:</span>\n                          <span className=\"font-medium capitalize\">{bakery.specialization}</span>\n                        </div>\n                        <div className=\"text-xs text-blue-600\">\n                          {getSpecializationBonus(bakery.specialization)}\n                        </div>\n                      </div>\n\n                      <div className=\"mt-3 pt-3 border-t border-gray-200\">\n                        <div className=\"flex justify-between text-sm\">\n                          <span className=\"text-gray-600\">{t('bakeries.equipment') || 'Equipment'}:</span>\n                          <span>{bakery.equipment.length}</span>\n                        </div>\n                        <div className=\"flex justify-between text-sm\">\n                          <span className=\"text-gray-600\">{t('bakeries.orders') || 'Active Orders'}:</span>\n                          <span>{bakery.orders.length}</span>\n                        </div>\n                      </div>\n\n                      {bakery.id !== currentBakeryId && (\n                        <Button\n                          variant=\"primary\"\n                          size=\"sm\"\n                          className=\"w-full mt-3\"\n                          onClick={(e) => {\n                            e.stopPropagation()\n                            onSwitchBakery(bakery.id)\n                          }}\n                        >\n                          {t('bakeries.switchTo') || 'Switch To'}\n                        </Button>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <div className=\"text-4xl mb-2\">🏪</div>\n                  <p>{t('bakeries.noOwned') || 'You don\\'t own any bakeries yet.'}</p>\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* Available Bakeries */}\n          {selectedTab === 'available' && (\n            <div className=\"space-y-4\">\n              {availableBakeries.length > 0 ? (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  {availableBakeries.map((bakery, index) => (\n                    <div\n                      key={index}\n                      className=\"p-4 rounded-lg border-2 border-gray-300 bg-white\"\n                    >\n                      <div className=\"flex items-start justify-between mb-3\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-2xl\">{getSpecializationIcon(bakery.specialization)}</span>\n                          <div>\n                            <h3 className=\"font-semibold text-gray-800\">{bakery.name}</h3>\n                            <p className=\"text-sm text-gray-600\">{bakery.location}</p>\n                          </div>\n                        </div>\n                        <span className=\"text-lg font-bold text-green-600\">${bakery.purchaseCost}</span>\n                      </div>\n\n                      <div className=\"space-y-2 text-sm mb-4\">\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">{t('bakeries.specialization') || 'Specialization'}:</span>\n                          <span className=\"font-medium capitalize\">{bakery.specialization}</span>\n                        </div>\n                        <div className=\"text-xs text-blue-600\">\n                          {getSpecializationBonus(bakery.specialization)}\n                        </div>\n                      </div>\n\n                      <Button\n                        variant={playerMoney >= bakery.purchaseCost ? 'success' : 'secondary'}\n                        size=\"sm\"\n                        className=\"w-full\"\n                        disabled={playerMoney < bakery.purchaseCost}\n                        onClick={() => handlePurchaseBakery(bakery)}\n                      >\n                        {playerMoney >= bakery.purchaseCost \n                          ? (t('bakeries.purchase') || '💰 Purchase')\n                          : (t('bakeries.tooExpensive') || '💸 Too Expensive')\n                        }\n                      </Button>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <div className=\"text-4xl mb-2\">🎉</div>\n                  <p>{t('bakeries.allOwned') || 'You own all available bakeries!'}</p>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n\n        <div className=\"p-4 bg-blue-50 border-t border-gray-200\">\n          <h3 className=\"font-medium text-blue-800 mb-2\">\n            {t('bakeries.tips') || '💡 Bakery Tips'}\n          </h3>\n          <ul className=\"text-sm text-blue-700 space-y-1\">\n            <li>• {t('bakeries.tip1') || 'Each bakery specializes in different products for bonus efficiency'}</li>\n            <li>• {t('bakeries.tip2') || 'Switch between bakeries to manage multiple locations'}</li>\n            <li>• {t('bakeries.tip3') || 'Specialized bakeries attract customers looking for specific items'}</li>\n            <li>• {t('bakeries.tip4') || 'Upgrade each bakery independently for maximum profit'}</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAiBA,MAAM,qBAAgE;IACpE;QACE,MAAM;QACN,UAAU;QACV,gBAAgB;QAChB,OAAO;QACP,WAAW,EAAE;QACb,WAAW,EAAE;QACb,QAAQ,EAAE;QACV,gBAAgB,EAAE;QAClB,eAAe,EAAE;QACjB,cAAc,EAAE,sBAAsB;IACxC;IACA;QACE,MAAM;QACN,UAAU;QACV,gBAAgB;QAChB,OAAO;QACP,WAAW,EAAE;QACb,WAAW,EAAE;QACb,QAAQ,EAAE;QACV,gBAAgB,EAAE;QAClB,eAAe,EAAE;QACjB,cAAc;IAChB;IACA;QACE,MAAM;QACN,UAAU;QACV,gBAAgB;QAChB,OAAO;QACP,WAAW,EAAE;QACb,WAAW,EAAE;QACb,QAAQ,EAAE;QACV,gBAAgB,EAAE;QAClB,eAAe,EAAE;QACjB,cAAc;IAChB;IACA;QACE,MAAM;QACN,UAAU;QACV,gBAAgB;QAChB,OAAO;QACP,WAAW,EAAE;QACb,WAAW,EAAE;QACb,QAAQ,EAAE;QACV,gBAAgB,EAAE;QAClB,eAAe,EAAE;QACjB,cAAc;IAChB;IACA;QACE,MAAM;QACN,UAAU;QACV,gBAAgB;QAChB,OAAO;QACP,WAAW,EAAE;QACb,WAAW,EAAE;QACb,QAAQ,EAAE;QACV,gBAAgB,EAAE;QAClB,eAAe,EAAE;QACjB,cAAc;IAChB;CACD;AAEM,SAAS,mBAAmB,EACjC,MAAM,EACN,OAAO,EACP,QAAQ,EACR,eAAe,EACf,cAAc,EACd,gBAAgB,EAChB,WAAW,EACa;IACxB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAEtE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ;IACrD,MAAM,oBAAoB,mBAAmB,MAAM,CAAC,CAAA,KAClD,CAAC,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,GAAG,IAAI,IAAI,EAAE,QAAQ;IAGtD,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,eAAe,OAAO,YAAY,EAAE;YACtC,MAAM,YAA4B;gBAChC,GAAG,MAAM;gBACT,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;gBAC1B,UAAU;YACZ;YACA,iBAAiB;QACnB;IACF;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAS,MAAM,EAAE,qBAAqB;YAAe,MAAM;QAAK;QACtE;YAAE,IAAI;YAAa,MAAM,EAAE,yBAAyB;YAAa,MAAM;QAAK;KAC7E;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,EAAE,qBAAqB;;;;;;kDAE1B,8OAAC;wCAAE,WAAU;kDACV,EAAE,wBAAwB;;;;;;;;;;;;0CAG/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;;gDAA6B;gDAAE;;;;;;;;;;;;kDAEjD,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAY,SAAS;kDAClC,EAAE,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;8BAO5B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAA,oBACR,8OAAC;gCAEC,SAAS,IAAM,eAAe,IAAI,EAAE;gCACpC,WAAW,CAAC,2DAA2D,EACrE,gBAAgB,IAAI,EAAE,GAClB,mDACA,yEACJ;;oCAED,IAAI,IAAI;oCAAC;oCAAE,IAAI,IAAI;;+BARf,IAAI,EAAE;;;;;;;;;;;;;;;8BAcnB,8OAAC;oBAAI,WAAU;;wBAEZ,gBAAgB,yBACf,8OAAC;4BAAI,WAAU;sCACZ,cAAc,MAAM,GAAG,kBACtB,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAA,uBACjB,8OAAC;wCAEC,WAAW,CAAC,sDAAsD,EAChE,OAAO,EAAE,KAAK,kBACV,mCACA,oDACJ;wCACF,SAAS,IAAM,eAAe,OAAO,EAAE;;0DAEvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAY,sBAAsB,OAAO,cAAc;;;;;;0EACvE,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA+B,OAAO,IAAI;;;;;;kFACxD,8OAAC;wEAAE,WAAU;kFAAyB,OAAO,QAAQ;;;;;;;;;;;;;;;;;;oDAGxD,OAAO,EAAE,KAAK,iCACb,8OAAC;wDAAK,WAAU;kEACb,EAAE,uBAAuB;;;;;;;;;;;;0DAKhC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;;oEAAiB,EAAE,qBAAqB;oEAAQ;;;;;;;0EAChE,8OAAC;gEAAK,WAAU;0EAAe,OAAO,KAAK;;;;;;;;;;;;kEAE7C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;;oEAAiB,EAAE,8BAA8B;oEAAiB;;;;;;;0EAClF,8OAAC;gEAAK,WAAU;0EAA0B,OAAO,cAAc;;;;;;;;;;;;kEAEjE,8OAAC;wDAAI,WAAU;kEACZ,uBAAuB,OAAO,cAAc;;;;;;;;;;;;0DAIjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;;oEAAiB,EAAE,yBAAyB;oEAAY;;;;;;;0EACxE,8OAAC;0EAAM,OAAO,SAAS,CAAC,MAAM;;;;;;;;;;;;kEAEhC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;;oEAAiB,EAAE,sBAAsB;oEAAgB;;;;;;;0EACzE,8OAAC;0EAAM,OAAO,MAAM,CAAC,MAAM;;;;;;;;;;;;;;;;;;4CAI9B,OAAO,EAAE,KAAK,iCACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,eAAe,OAAO,EAAE;gDAC1B;0DAEC,EAAE,wBAAwB;;;;;;;uCA1D1B,OAAO,EAAE;;;;;;;;;qDAiEpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;kDAAG,EAAE,uBAAuB;;;;;;;;;;;;;;;;;wBAOpC,gBAAgB,6BACf,8OAAC;4BAAI,WAAU;sCACZ,kBAAkB,MAAM,GAAG,kBAC1B,8OAAC;gCAAI,WAAU;0CACZ,kBAAkB,GAAG,CAAC,CAAC,QAAQ,sBAC9B,8OAAC;wCAEC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAY,sBAAsB,OAAO,cAAc;;;;;;0EACvE,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA+B,OAAO,IAAI;;;;;;kFACxD,8OAAC;wEAAE,WAAU;kFAAyB,OAAO,QAAQ;;;;;;;;;;;;;;;;;;kEAGzD,8OAAC;wDAAK,WAAU;;4DAAmC;4DAAE,OAAO,YAAY;;;;;;;;;;;;;0DAG1E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;;oEAAiB,EAAE,8BAA8B;oEAAiB;;;;;;;0EAClF,8OAAC;gEAAK,WAAU;0EAA0B,OAAO,cAAc;;;;;;;;;;;;kEAEjE,8OAAC;wDAAI,WAAU;kEACZ,uBAAuB,OAAO,cAAc;;;;;;;;;;;;0DAIjD,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,eAAe,OAAO,YAAY,GAAG,YAAY;gDAC1D,MAAK;gDACL,WAAU;gDACV,UAAU,cAAc,OAAO,YAAY;gDAC3C,SAAS,IAAM,qBAAqB;0DAEnC,eAAe,OAAO,YAAY,GAC9B,EAAE,wBAAwB,gBAC1B,EAAE,4BAA4B;;;;;;;uCAjChC;;;;;;;;;qDAwCX,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;kDAAG,EAAE,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;8BAOxC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,EAAE,oBAAoB;;;;;;sCAEzB,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;;wCAAG;wCAAG,EAAE,oBAAoB;;;;;;;8CAC7B,8OAAC;;wCAAG;wCAAG,EAAE,oBAAoB;;;;;;;8CAC7B,8OAAC;;wCAAG;wCAAG,EAAE,oBAAoB;;;;;;;8CAC7B,8OAAC;;wCAAG;wCAAG,EAAE,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}, {"offset": {"line": 7004, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/app/game/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useLanguage } from '@/contexts/LanguageContext'\nimport { GameProvider, useGame } from '@/contexts/GameContext'\nimport { Button } from '@/components/ui/Button'\nimport { Equipment } from '@/components/game/Equipment'\nimport { Order } from '@/components/game/Order'\nimport { RecipeModal } from '@/components/game/RecipeModal'\nimport { ShopModal } from '@/components/game/ShopModal'\nimport { BakingModal } from '@/components/game/BakingModal'\nimport { NotificationSystem, useNotifications } from '@/components/game/NotificationSystem'\nimport { LevelUpModal } from '@/components/game/LevelUpModal'\nimport { AchievementsModal } from '@/components/game/AchievementsModal'\nimport { SkillTreeModal } from '@/components/game/SkillTreeModal'\nimport { AutomationModal } from '@/components/game/AutomationModal'\nimport { EquipmentShopModal } from '@/components/game/EquipmentShopModal'\nimport { SettingsModal } from '@/components/game/SettingsModal'\nimport { BakeryManagerModal } from '@/components/game/BakeryManagerModal'\n\nfunction GameContent() {\n  const { t } = useLanguage()\n  const {\n    player,\n    equipment,\n    inventory,\n    orders,\n    achievements,\n    skills,\n    levelUpRewards,\n    showLevelUp,\n    updateEquipment,\n    acceptOrder,\n    completeOrder,\n    declineOrder,\n    generateNewOrder,\n    upgradeSkill,\n    checkAchievements,\n    dismissLevelUp\n  } = useGame()\n\n  const [showRecipeModal, setShowRecipeModal] = useState(false)\n  const [showShopModal, setShowShopModal] = useState(false)\n  const [showBakingModal, setShowBakingModal] = useState(false)\n  const [showAchievementsModal, setShowAchievementsModal] = useState(false)\n  const [showSkillTreeModal, setShowSkillTreeModal] = useState(false)\n  const [showAutomationModal, setShowAutomationModal] = useState(false)\n  const [showEquipmentShopModal, setShowEquipmentShopModal] = useState(false)\n  const [showSettingsModal, setShowSettingsModal] = useState(false)\n  const [showBakeryManagerModal, setShowBakeryManagerModal] = useState(false)\n  const [selectedEquipment, setSelectedEquipment] = useState<{id: string, name: string} | null>(null)\n\n  // Game settings state\n  const [gameSettings, setGameSettings] = useState({\n    language: 'en' as 'en' | 'cs',\n    soundEnabled: true,\n    musicEnabled: true,\n    notificationsEnabled: true,\n    autoSaveEnabled: true,\n    graphicsQuality: 'medium' as 'low' | 'medium' | 'high',\n    animationSpeed: 1,\n    showTutorials: true\n  })\n\n  // Bakery management state\n  const [bakeries, setBakeries] = useState([\n    {\n      id: 'main',\n      name: 'Downtown Delights',\n      location: 'City Center',\n      specialization: 'general' as const,\n      level: 1,\n      equipment: [],\n      inventory: [],\n      orders: [],\n      automationJobs: [],\n      conveyorBelts: [],\n      unlocked: true,\n      purchaseCost: 0\n    }\n  ])\n  const [currentBakeryId, setCurrentBakeryId] = useState('main')\n\n  const { notifications, removeNotification, showSuccess, showError, showInfo } = useNotifications()\n\n  const handleEquipmentClick = (equipmentId: string, equipmentName: string) => {\n    setSelectedEquipment({ id: equipmentId, name: equipmentName })\n    setShowBakingModal(true)\n  }\n\n  const handleOrderAccept = (orderId: string) => {\n    acceptOrder(orderId)\n    showInfo('Order Accepted', 'You have accepted a new order!')\n  }\n\n  const handleOrderComplete = (orderId: string) => {\n    const order = orders.find(o => o.id === orderId)\n    if (order) {\n      completeOrder(orderId)\n      checkAchievements()\n      showSuccess('Order Completed!', `You earned $${order.reward} and gained experience!`)\n    }\n  }\n\n  const handleOrderDecline = (orderId: string) => {\n    declineOrder(orderId)\n    showInfo('Order Declined', 'Order has been removed from your queue.')\n  }\n\n  const handleSettingsChange = (newSettings: Partial<typeof gameSettings>) => {\n    setGameSettings(prev => ({ ...prev, ...newSettings }))\n  }\n\n  const handleSwitchBakery = (bakeryId: string) => {\n    setCurrentBakeryId(bakeryId)\n    showInfo('Bakery Switched', `Switched to ${bakeries.find(b => b.id === bakeryId)?.name}`)\n  }\n\n  const handlePurchaseBakery = (bakery: any) => {\n    if (spendMoney(bakery.purchaseCost)) {\n      setBakeries(prev => [...prev, bakery])\n      showSuccess('Bakery Purchased!', `You now own ${bakery.name}!`)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-orange-50 to-yellow-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-orange-200 p-4\">\n        <div className=\"max-w-7xl mx-auto flex justify-between items-center\">\n          <div className=\"flex items-center space-x-6\">\n            <h1 className=\"text-2xl font-bold text-orange-800\">🥖 {t('game.title')}</h1>\n            <div className=\"flex items-center space-x-4 text-sm\">\n              <div className=\"bg-blue-100 px-3 py-1 rounded-full\">\n                <span className=\"text-blue-800\">{t('ui.level', { level: player.level.toString() })}</span>\n              </div>\n              <div className=\"bg-green-100 px-3 py-1 rounded-full\">\n                <span className=\"text-green-800\">{t('ui.money', { amount: player.money.toString() })}</span>\n              </div>\n              <div className=\"bg-purple-100 px-3 py-1 rounded-full\">\n                <span className=\"text-purple-800\">{t('ui.experience', { current: player.experience.toString(), max: player.maxExperience.toString() })}</span>\n              </div>\n              <div className=\"bg-yellow-100 px-3 py-1 rounded-full\">\n                <span className=\"text-yellow-800\">{t('ui.skillPoints', { points: player.skillPoints.toString() })}</span>\n              </div>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={() => setShowBakeryManagerModal(true)}\n              >\n                🏪 Bakeries\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={() => setShowAchievementsModal(true)}\n              >\n                {t('ui.achievements')}\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={() => setShowSkillTreeModal(true)}\n              >\n                {t('ui.skills')}\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={() => setShowAutomationModal(true)}\n              >\n                {t('ui.automation')}\n              </Button>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <Button\n              variant=\"secondary\"\n              size=\"sm\"\n              onClick={() => setShowSettingsModal(true)}\n            >\n              ⚙️ Settings\n            </Button>\n            <Button variant=\"secondary\" onClick={() => window.location.href = '/'}>\n              {t('game.home')}\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto p-6 grid grid-cols-1 lg:grid-cols-4 gap-6\">\n        {/* Kitchen Area */}\n        <div className=\"lg:col-span-3 space-y-6\">\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex justify-between items-center mb-4\">\n              <h2 className=\"text-xl font-semibold text-orange-800\">{t('kitchen.title')}</h2>\n              <div className=\"text-sm text-gray-600\">\n                Current: {bakeries.find(b => b.id === currentBakeryId)?.name}\n              </div>\n            </div>\n\n            {/* Equipment Grid */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              {equipment.map((eq) => (\n                <Equipment\n                  key={eq.id}\n                  equipment={eq}\n                  onClick={handleEquipmentClick}\n                />\n              ))}\n            </div>\n          </div>\n\n          {/* Inventory */}\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-semibold text-orange-800 mb-4\">{t('inventory.title')}</h2>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              {inventory.map((ingredient) => (\n                <div key={ingredient.name} className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                  <div className=\"text-2xl mb-1\">{ingredient.icon}</div>\n                  <div className=\"font-medium text-gray-800\">{ingredient.name}</div>\n                  <div className=\"text-sm text-gray-600\">{t('inventory.quantity', { qty: ingredient.quantity.toString() })}</div>\n                  <div className=\"text-xs text-green-600\">{t('inventory.cost', { cost: ingredient.cost.toString() })}</div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Orders Panel */}\n        <div className=\"space-y-6\">\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex justify-between items-center mb-4\">\n              <h2 className=\"text-xl font-semibold text-orange-800\">{t('orders.title')}</h2>\n              <Button\n                size=\"sm\"\n                variant=\"primary\"\n                onClick={generateNewOrder}\n              >\n                {t('orders.newOrder')}\n              </Button>\n            </div>\n            <div className=\"space-y-4\">\n              {orders.map((order) => (\n                <Order\n                  key={order.id}\n                  order={order}\n                  onAccept={handleOrderAccept}\n                  onDecline={handleOrderDecline}\n                  onComplete={handleOrderComplete}\n                />\n              ))}\n            </div>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-semibold text-orange-800 mb-4\">{t('actions.title')}</h2>\n            <div className=\"space-y-2\">\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                className=\"w-full\"\n                onClick={() => setShowShopModal(true)}\n              >\n                {t('actions.buyIngredients')}\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                className=\"w-full\"\n                onClick={() => setShowRecipeModal(true)}\n              >\n                {t('actions.viewRecipes')}\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                className=\"w-full\"\n                onClick={() => setShowEquipmentShopModal(true)}\n              >\n                {t('actions.equipmentShop')}\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Modals */}\n      <RecipeModal\n        isOpen={showRecipeModal}\n        onClose={() => setShowRecipeModal(false)}\n      />\n      <ShopModal\n        isOpen={showShopModal}\n        onClose={() => setShowShopModal(false)}\n      />\n      <BakingModal\n        isOpen={showBakingModal}\n        onClose={() => setShowBakingModal(false)}\n        equipmentId={selectedEquipment?.id || ''}\n        equipmentName={selectedEquipment?.name || ''}\n      />\n      <AchievementsModal\n        isOpen={showAchievementsModal}\n        onClose={() => setShowAchievementsModal(false)}\n        achievements={achievements}\n      />\n      <SkillTreeModal\n        isOpen={showSkillTreeModal}\n        onClose={() => setShowSkillTreeModal(false)}\n        skills={skills}\n        skillPoints={player.skillPoints}\n        playerLevel={player.level}\n        onUpgradeSkill={upgradeSkill}\n      />\n      <LevelUpModal\n        isOpen={showLevelUp}\n        onClose={dismissLevelUp}\n        newLevel={player.level}\n        rewards={levelUpRewards}\n      />\n      <AutomationModal\n        isOpen={showAutomationModal}\n        onClose={() => setShowAutomationModal(false)}\n      />\n      <EquipmentShopModal\n        isOpen={showEquipmentShopModal}\n        onClose={() => setShowEquipmentShopModal(false)}\n      />\n      <SettingsModal\n        isOpen={showSettingsModal}\n        onClose={() => setShowSettingsModal(false)}\n        settings={gameSettings}\n        onSettingsChange={handleSettingsChange}\n      />\n      <BakeryManagerModal\n        isOpen={showBakeryManagerModal}\n        onClose={() => setShowBakeryManagerModal(false)}\n        bakeries={bakeries}\n        currentBakeryId={currentBakeryId}\n        onSwitchBakery={handleSwitchBakery}\n        onPurchaseBakery={handlePurchaseBakery}\n        playerMoney={player.money}\n      />\n\n      {/* Notification System */}\n      <NotificationSystem\n        notifications={notifications}\n        onRemove={removeNotification}\n      />\n    </div>\n  )\n}\n\nexport default function GamePage() {\n  return (\n    <GameProvider>\n      <GameContent />\n    </GameProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlBA;;;;;;;;;;;;;;;;;;;AAoBA,SAAS;IACP,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IACxB,MAAM,EACJ,MAAM,EACN,SAAS,EACT,SAAS,EACT,MAAM,EACN,YAAY,EACZ,MAAM,EACN,cAAc,EACd,WAAW,EACX,eAAe,EACf,WAAW,EACX,aAAa,EACb,YAAY,EACZ,gBAAgB,EAChB,YAAY,EACZ,iBAAiB,EACjB,cAAc,EACf,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEV,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqC;IAE9F,sBAAsB;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,UAAU;QACV,cAAc;QACd,cAAc;QACd,sBAAsB;QACtB,iBAAiB;QACjB,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;IACjB;IAEA,0BAA0B;IAC1B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,gBAAgB;YAChB,OAAO;YACP,WAAW,EAAE;YACb,WAAW,EAAE;YACb,QAAQ,EAAE;YACV,gBAAgB,EAAE;YAClB,eAAe,EAAE;YACjB,UAAU;YACV,cAAc;QAChB;KACD;IACD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,EAAE,aAAa,EAAE,kBAAkB,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD;IAE/F,MAAM,uBAAuB,CAAC,aAAqB;QACjD,qBAAqB;YAAE,IAAI;YAAa,MAAM;QAAc;QAC5D,mBAAmB;IACrB;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY;QACZ,SAAS,kBAAkB;IAC7B;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACxC,IAAI,OAAO;YACT,cAAc;YACd;YACA,YAAY,oBAAoB,CAAC,YAAY,EAAE,MAAM,MAAM,CAAC,uBAAuB,CAAC;QACtF;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,aAAa;QACb,SAAS,kBAAkB;IAC7B;IAEA,MAAM,uBAAuB,CAAC;QAC5B,gBAAgB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,WAAW;YAAC,CAAC;IACtD;IAEA,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB;QACnB,SAAS,mBAAmB,CAAC,YAAY,EAAE,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,MAAM;IAC1F;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,WAAW,OAAO,YAAY,GAAG;YACnC,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAO;YACrC,YAAY,qBAAqB,CAAC,YAAY,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC;QAChE;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAqC;wCAAI,EAAE;;;;;;;8CACzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAiB,EAAE,YAAY;oDAAE,OAAO,OAAO,KAAK,CAAC,QAAQ;gDAAG;;;;;;;;;;;sDAElF,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAkB,EAAE,YAAY;oDAAE,QAAQ,OAAO,KAAK,CAAC,QAAQ;gDAAG;;;;;;;;;;;sDAEpF,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAmB,EAAE,iBAAiB;oDAAE,SAAS,OAAO,UAAU,CAAC,QAAQ;oDAAI,KAAK,OAAO,aAAa,CAAC,QAAQ;gDAAG;;;;;;;;;;;sDAEtI,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAmB,EAAE,kBAAkB;oDAAE,QAAQ,OAAO,WAAW,CAAC,QAAQ;gDAAG;;;;;;;;;;;sDAEjG,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,0BAA0B;sDAC1C;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,yBAAyB;sDAEvC,EAAE;;;;;;sDAEL,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,sBAAsB;sDAEpC,EAAE;;;;;;sDAEL,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,uBAAuB;sDAErC,EAAE;;;;;;;;;;;;;;;;;;sCAIT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,qBAAqB;8CACrC;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAY,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;8CAC/D,EAAE;;;;;;;;;;;;;;;;;;;;;;;0BAMX,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyC,EAAE;;;;;;0DACzD,8OAAC;gDAAI,WAAU;;oDAAwB;oDAC3B,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,kBAAkB;;;;;;;;;;;;;kDAK5D,8OAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAC,mBACd,8OAAC,uIAAA,CAAA,YAAS;gDAER,WAAW;gDACX,SAAS;+CAFJ,GAAG,EAAE;;;;;;;;;;;;;;;;0CASlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA8C,EAAE;;;;;;kDAC9D,8OAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAC,2BACd,8OAAC;gDAA0B,WAAU;;kEACnC,8OAAC;wDAAI,WAAU;kEAAiB,WAAW,IAAI;;;;;;kEAC/C,8OAAC;wDAAI,WAAU;kEAA6B,WAAW,IAAI;;;;;;kEAC3D,8OAAC;wDAAI,WAAU;kEAAyB,EAAE,sBAAsB;4DAAE,KAAK,WAAW,QAAQ,CAAC,QAAQ;wDAAG;;;;;;kEACtG,8OAAC;wDAAI,WAAU;kEAA0B,EAAE,kBAAkB;4DAAE,MAAM,WAAW,IAAI,CAAC,QAAQ;wDAAG;;;;;;;+CAJxF,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;kCAYjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyC,EAAE;;;;;;0DACzD,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;0DAER,EAAE;;;;;;;;;;;;kDAGP,8OAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC,mIAAA,CAAA,QAAK;gDAEJ,OAAO;gDACP,UAAU;gDACV,WAAW;gDACX,YAAY;+CAJP,MAAM,EAAE;;;;;;;;;;;;;;;;0CAWrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA8C,EAAE;;;;;;kDAC9D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,iBAAiB;0DAE/B,EAAE;;;;;;0DAEL,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,mBAAmB;0DAEjC,EAAE;;;;;;0DAEL,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,0BAA0B;0DAExC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQb,8OAAC,yIAAA,CAAA,cAAW;gBACV,QAAQ;gBACR,SAAS,IAAM,mBAAmB;;;;;;0BAEpC,8OAAC,uIAAA,CAAA,YAAS;gBACR,QAAQ;gBACR,SAAS,IAAM,iBAAiB;;;;;;0BAElC,8OAAC,yIAAA,CAAA,cAAW;gBACV,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,aAAa,mBAAmB,MAAM;gBACtC,eAAe,mBAAmB,QAAQ;;;;;;0BAE5C,8OAAC,+IAAA,CAAA,oBAAiB;gBAChB,QAAQ;gBACR,SAAS,IAAM,yBAAyB;gBACxC,cAAc;;;;;;0BAEhB,8OAAC,4IAAA,CAAA,iBAAc;gBACb,QAAQ;gBACR,SAAS,IAAM,sBAAsB;gBACrC,QAAQ;gBACR,aAAa,OAAO,WAAW;gBAC/B,aAAa,OAAO,KAAK;gBACzB,gBAAgB;;;;;;0BAElB,8OAAC,0IAAA,CAAA,eAAY;gBACX,QAAQ;gBACR,SAAS;gBACT,UAAU,OAAO,KAAK;gBACtB,SAAS;;;;;;0BAEX,8OAAC,6IAAA,CAAA,kBAAe;gBACd,QAAQ;gBACR,SAAS,IAAM,uBAAuB;;;;;;0BAExC,8OAAC,gJAAA,CAAA,qBAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,0BAA0B;;;;;;0BAE3C,8OAAC,2IAAA,CAAA,gBAAa;gBACZ,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,UAAU;gBACV,kBAAkB;;;;;;0BAEpB,8OAAC,gJAAA,CAAA,qBAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,0BAA0B;gBACzC,UAAU;gBACV,iBAAiB;gBACjB,gBAAgB;gBAChB,kBAAkB;gBAClB,aAAa,OAAO,KAAK;;;;;;0BAI3B,8OAAC,gJAAA,CAAA,qBAAkB;gBACjB,eAAe;gBACf,UAAU;;;;;;;;;;;;AAIlB;AAEe,SAAS;IACtB,qBACE,8OAAC,+HAAA,CAAA,eAAY;kBACX,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}