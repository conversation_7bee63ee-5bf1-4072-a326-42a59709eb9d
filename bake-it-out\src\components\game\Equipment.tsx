'use client'

import { useState } from 'react'

export interface EquipmentData {
  id: string
  name: string
  type: 'oven' | 'mixer' | 'counter'
  isActive: boolean
  timeRemaining?: number
  currentRecipe?: string
  level: number
  efficiency: number
}

interface EquipmentProps {
  equipment: EquipmentData
  onClick: (equipmentId: string, equipmentName: string) => void
}

export function Equipment({ equipment, onClick }: EquipmentProps) {
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getEquipmentIcon = (type: string) => {
    switch (type) {
      case 'oven': return '🔥'
      case 'mixer': return '🥄'
      case 'counter': return '🍽️'
      default: return '⚙️'
    }
  }

  const getStatusColor = () => {
    if (equipment.isActive) {
      return 'border-green-400 bg-green-50'
    }
    return 'border-gray-200 bg-gray-50 hover:border-orange-300 hover:bg-orange-50'
  }

  return (
    <div
      className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${getStatusColor()}`}
      onClick={() => !equipment.isActive && onClick(equipment.id, equipment.name)}
    >
      <div className="text-center">
        <div className="text-3xl mb-2">
          {getEquipmentIcon(equipment.type)}
        </div>
        <h3 className="font-medium text-gray-800">{equipment.name}</h3>
        <div className="text-xs text-gray-500">Level {equipment.level}</div>
        
        {equipment.isActive && equipment.timeRemaining ? (
          <div className="mt-2">
            <div className="text-sm text-green-600">
              Making: {equipment.currentRecipe}
            </div>
            <div className="text-lg font-mono text-green-700">
              {formatTime(equipment.timeRemaining)}
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div 
                className="bg-green-500 h-2 rounded-full transition-all duration-1000"
                style={{ 
                  width: `${100 - (equipment.timeRemaining / 60) * 100}%` 
                }}
              ></div>
            </div>
          </div>
        ) : (
          <div className="text-sm text-gray-500 mt-2">
            {equipment.isActive ? 'Busy' : 'Click to use'}
          </div>
        )}
      </div>
    </div>
  )
}
