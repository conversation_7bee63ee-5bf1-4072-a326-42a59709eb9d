{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/lib/gameLogic.ts"], "sourcesContent": ["// Game logic and data structures\n\nexport interface Recipe {\n  id: string\n  name: string\n  ingredients: { name: string; quantity: number }[]\n  bakingTime: number // in seconds\n  difficulty: number // 1-5\n  unlockLevel: number\n  basePrice: number\n  category: 'cookies' | 'cakes' | 'bread' | 'pastries'\n}\n\nexport const RECIPES: Recipe[] = [\n  {\n    id: 'chocolate_chip_cookies',\n    name: 'Chocolate Chip Cookies',\n    ingredients: [\n      { name: 'Flour', quantity: 2 },\n      { name: 'Sugar', quantity: 1 },\n      { name: 'Butter', quantity: 1 },\n      { name: 'Chocolate Chips', quantity: 1 }\n    ],\n    bakingTime: 45,\n    difficulty: 1,\n    unlockLevel: 1,\n    basePrice: 25,\n    category: 'cookies'\n  },\n  {\n    id: 'vanilla_muffins',\n    name: 'Vanilla Muffins',\n    ingredients: [\n      { name: 'Flour', quantity: 2 },\n      { name: 'Sugar', quantity: 1 },\n      { name: 'Eggs', quantity: 1 },\n      { name: 'Vanilla', quantity: 1 }\n    ],\n    bakingTime: 60,\n    difficulty: 1,\n    unlockLevel: 1,\n    basePrice: 20,\n    category: 'cakes'\n  },\n  {\n    id: 'cinnamon_rolls',\n    name: 'Cinnamon Rolls',\n    ingredients: [\n      { name: 'Flour', quantity: 3 },\n      { name: 'Sugar', quantity: 2 },\n      { name: 'Butter', quantity: 2 },\n      { name: 'Eggs', quantity: 1 }\n    ],\n    bakingTime: 90,\n    difficulty: 2,\n    unlockLevel: 2,\n    basePrice: 35,\n    category: 'pastries'\n  },\n  {\n    id: 'chocolate_brownies',\n    name: 'Chocolate Brownies',\n    ingredients: [\n      { name: 'Flour', quantity: 2 },\n      { name: 'Sugar', quantity: 2 },\n      { name: 'Butter', quantity: 1 },\n      { name: 'Chocolate Chips', quantity: 2 }\n    ],\n    bakingTime: 75,\n    difficulty: 2,\n    unlockLevel: 2,\n    basePrice: 30,\n    category: 'cakes'\n  },\n  {\n    id: 'sourdough_bread',\n    name: 'Sourdough Bread',\n    ingredients: [\n      { name: 'Flour', quantity: 4 },\n      { name: 'Salt', quantity: 1 }\n    ],\n    bakingTime: 180,\n    difficulty: 3,\n    unlockLevel: 3,\n    basePrice: 45,\n    category: 'bread'\n  }\n]\n\nexport const CUSTOMER_NAMES = [\n  'Alice Johnson', 'Bob Smith', 'Carol Davis', 'David Wilson',\n  'Emma Brown', 'Frank Miller', 'Grace Taylor', 'Henry Anderson',\n  'Ivy Thomas', 'Jack Martinez', 'Kate Garcia', 'Liam Rodriguez',\n  'Mia Lopez', 'Noah Gonzalez', 'Olivia Hernandez', 'Paul Perez',\n  'Quinn Turner', 'Ruby Phillips', 'Sam Campbell', 'Tina Parker'\n]\n\nexport function generateRandomOrder(playerLevel: number): {\n  id: string\n  customerName: string\n  items: string[]\n  timeLimit: number\n  reward: number\n  status: 'pending'\n  difficulty: number\n} {\n  // Filter recipes based on player level\n  const availableRecipes = RECIPES.filter(recipe => recipe.unlockLevel <= playerLevel)\n  \n  if (availableRecipes.length === 0) {\n    // Fallback to basic recipe\n    availableRecipes.push(RECIPES[0])\n  }\n\n  // Select random recipe(s)\n  const numItems = Math.random() < 0.7 ? 1 : Math.random() < 0.9 ? 2 : 3\n  const selectedRecipes: Recipe[] = []\n  \n  for (let i = 0; i < numItems; i++) {\n    const recipe = availableRecipes[Math.floor(Math.random() * availableRecipes.length)]\n    selectedRecipes.push(recipe)\n  }\n\n  // Calculate order properties\n  const totalDifficulty = selectedRecipes.reduce((sum, recipe) => sum + recipe.difficulty, 0)\n  const avgDifficulty = Math.ceil(totalDifficulty / selectedRecipes.length)\n  const totalBasePrice = selectedRecipes.reduce((sum, recipe) => sum + recipe.basePrice, 0)\n  \n  // Add some randomness to price and time\n  const priceMultiplier = 0.8 + Math.random() * 0.4 // 0.8 to 1.2\n  const timeMultiplier = 1.5 + Math.random() * 1.0 // 1.5 to 2.5\n  \n  const reward = Math.floor(totalBasePrice * priceMultiplier)\n  const baseTime = selectedRecipes.reduce((sum, recipe) => sum + recipe.bakingTime, 0)\n  const timeLimit = Math.floor(baseTime * timeMultiplier)\n\n  return {\n    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n    customerName: CUSTOMER_NAMES[Math.floor(Math.random() * CUSTOMER_NAMES.length)],\n    items: selectedRecipes.map(recipe => recipe.name),\n    timeLimit,\n    reward,\n    status: 'pending',\n    difficulty: Math.min(5, avgDifficulty)\n  }\n}\n\nexport function calculateExperienceReward(difficulty: number, timeBonus: boolean = false): number {\n  const baseExp = difficulty * 10\n  const bonus = timeBonus ? Math.floor(baseExp * 0.5) : 0\n  return baseExp + bonus\n}\n\nexport function calculateLevelRequirement(level: number): number {\n  return level * 100 + (level - 1) * 50\n}\n\nexport function canCraftRecipe(recipe: Recipe, inventory: { name: string; quantity: number }[]): boolean {\n  return recipe.ingredients.every(ingredient => {\n    const inventoryItem = inventory.find(item => item.name === ingredient.name)\n    return inventoryItem && inventoryItem.quantity >= ingredient.quantity\n  })\n}\n\nexport function getRecipeById(id: string): Recipe | undefined {\n  return RECIPES.find(recipe => recipe.id === id)\n}\n\nexport function getAvailableRecipes(playerLevel: number): Recipe[] {\n  return RECIPES.filter(recipe => recipe.unlockLevel <= playerLevel)\n}\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;;;;;;AAa1B,MAAM,UAAoB;IAC/B;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAU,UAAU;YAAE;YAC9B;gBAAE,MAAM;gBAAmB,UAAU;YAAE;SACxC;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAQ,UAAU;YAAE;YAC5B;gBAAE,MAAM;gBAAW,UAAU;YAAE;SAChC;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAU,UAAU;YAAE;YAC9B;gBAAE,MAAM;gBAAQ,UAAU;YAAE;SAC7B;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAU,UAAU;YAAE;YAC9B;gBAAE,MAAM;gBAAmB,UAAU;YAAE;SACxC;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAQ,UAAU;YAAE;SAC7B;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;CACD;AAEM,MAAM,iBAAiB;IAC5B;IAAiB;IAAa;IAAe;IAC7C;IAAc;IAAgB;IAAgB;IAC9C;IAAc;IAAiB;IAAe;IAC9C;IAAa;IAAiB;IAAoB;IAClD;IAAgB;IAAiB;IAAgB;CAClD;AAEM,SAAS,oBAAoB,WAAmB;IASrD,uCAAuC;IACvC,MAAM,mBAAmB,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,WAAW,IAAI;IAExE,IAAI,iBAAiB,MAAM,KAAK,GAAG;QACjC,2BAA2B;QAC3B,iBAAiB,IAAI,CAAC,OAAO,CAAC,EAAE;IAClC;IAEA,0BAA0B;IAC1B,MAAM,WAAW,KAAK,MAAM,KAAK,MAAM,IAAI,KAAK,MAAM,KAAK,MAAM,IAAI;IACrE,MAAM,kBAA4B,EAAE;IAEpC,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;QACjC,MAAM,SAAS,gBAAgB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM,EAAE;QACpF,gBAAgB,IAAI,CAAC;IACvB;IAEA,6BAA6B;IAC7B,MAAM,kBAAkB,gBAAgB,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,UAAU,EAAE;IACzF,MAAM,gBAAgB,KAAK,IAAI,CAAC,kBAAkB,gBAAgB,MAAM;IACxE,MAAM,iBAAiB,gBAAgB,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,SAAS,EAAE;IAEvF,wCAAwC;IACxC,MAAM,kBAAkB,MAAM,KAAK,MAAM,KAAK,IAAI,aAAa;;IAC/D,MAAM,iBAAiB,MAAM,KAAK,MAAM,KAAK,IAAI,aAAa;;IAE9D,MAAM,SAAS,KAAK,KAAK,CAAC,iBAAiB;IAC3C,MAAM,WAAW,gBAAgB,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,UAAU,EAAE;IAClF,MAAM,YAAY,KAAK,KAAK,CAAC,WAAW;IAExC,OAAO;QACL,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QACjE,cAAc,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,eAAe,MAAM,EAAE;QAC/E,OAAO,gBAAgB,GAAG,CAAC,CAAA,SAAU,OAAO,IAAI;QAChD;QACA;QACA,QAAQ;QACR,YAAY,KAAK,GAAG,CAAC,GAAG;IAC1B;AACF;AAEO,SAAS,0BAA0B,UAAkB,EAAE,YAAqB,KAAK;IACtF,MAAM,UAAU,aAAa;IAC7B,MAAM,QAAQ,YAAY,KAAK,KAAK,CAAC,UAAU,OAAO;IACtD,OAAO,UAAU;AACnB;AAEO,SAAS,0BAA0B,KAAa;IACrD,OAAO,QAAQ,MAAM,CAAC,QAAQ,CAAC,IAAI;AACrC;AAEO,SAAS,eAAe,MAAc,EAAE,SAA+C;IAC5F,OAAO,OAAO,WAAW,CAAC,KAAK,CAAC,CAAA;QAC9B,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;QAC1E,OAAO,iBAAiB,cAAc,QAAQ,IAAI,WAAW,QAAQ;IACvE;AACF;AAEO,SAAS,cAAc,EAAU;IACtC,OAAO,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;AAC9C;AAEO,SAAS,oBAAoB,WAAmB;IACrD,OAAO,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,WAAW,IAAI;AACxD", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/lib/progressionSystem.ts"], "sourcesContent": ["// Advanced progression system for Bake It Out\n\nexport interface LevelReward {\n  type: 'recipe' | 'equipment' | 'money' | 'skill_point' | 'achievement'\n  id: string\n  name: string\n  description: string\n  value?: number\n}\n\nexport interface PlayerLevel {\n  level: number\n  experience: number\n  experienceRequired: number\n  totalExperience: number\n  rewards: LevelReward[]\n}\n\nexport interface Achievement {\n  id: string\n  name: string\n  description: string\n  icon: string\n  category: 'baking' | 'business' | 'efficiency' | 'collection' | 'special'\n  requirements: {\n    type: 'orders_completed' | 'money_earned' | 'recipes_unlocked' | 'level_reached' | 'items_baked' | 'equipment_owned'\n    target: number\n    current?: number\n  }[]\n  reward: LevelReward\n  unlocked: boolean\n  completed: boolean\n}\n\nexport interface SkillTree {\n  id: string\n  name: string\n  description: string\n  icon: string\n  category: 'efficiency' | 'automation' | 'quality' | 'business'\n  level: number\n  maxLevel: number\n  cost: number\n  requirements: {\n    playerLevel?: number\n    skills?: string[]\n    achievements?: string[]\n  }\n  effects: {\n    type: 'baking_speed' | 'money_multiplier' | 'xp_multiplier' | 'ingredient_efficiency' | 'automation_unlock'\n    value: number\n  }[]\n}\n\n// Experience calculation with exponential growth\nexport function calculateExperienceRequired(level: number): number {\n  if (level <= 1) return 0\n  return Math.floor(100 * Math.pow(1.15, level - 1))\n}\n\nexport function calculateTotalExperienceForLevel(level: number): number {\n  let total = 0\n  for (let i = 1; i <= level; i++) {\n    total += calculateExperienceRequired(i)\n  }\n  return total\n}\n\nexport function getLevelFromExperience(experience: number): PlayerLevel {\n  let level = 1\n  let totalExp = 0\n  \n  while (true) {\n    const expRequired = calculateExperienceRequired(level + 1)\n    if (totalExp + expRequired > experience) {\n      break\n    }\n    totalExp += expRequired\n    level++\n  }\n  \n  const expRequired = calculateExperienceRequired(level + 1)\n  const currentLevelExp = experience - totalExp\n  \n  return {\n    level,\n    experience: currentLevelExp,\n    experienceRequired: expRequired,\n    totalExperience: experience,\n    rewards: getLevelRewards(level)\n  }\n}\n\nexport function getLevelRewards(level: number): LevelReward[] {\n  const rewards: LevelReward[] = []\n  \n  // Money rewards every level\n  rewards.push({\n    type: 'money',\n    id: `money_${level}`,\n    name: 'Level Bonus',\n    description: `Bonus money for reaching level ${level}`,\n    value: level * 25\n  })\n  \n  // Recipe unlocks at specific levels\n  const recipeUnlocks: Record<number, string[]> = {\n    2: ['cinnamon_rolls'],\n    3: ['chocolate_brownies', 'sourdough_bread'],\n    4: ['croissants'],\n    5: ['cheesecake'],\n    6: ['macarons'],\n    7: ['honey_glazed_donuts'],\n    8: ['sourdough_bread'],\n    9: ['chocolate_souffle'],\n    10: ['croquembouche'],\n    12: ['opera_cake'],\n    15: ['artisan_pizza_dough']\n  }\n  \n  if (recipeUnlocks[level]) {\n    recipeUnlocks[level].forEach(recipeId => {\n      rewards.push({\n        type: 'recipe',\n        id: recipeId,\n        name: 'New Recipe Unlocked',\n        description: `You can now bake ${recipeId.replace(/_/g, ' ')}`\n      })\n    })\n  }\n  \n  // Equipment unlocks\n  const equipmentUnlocks: Record<number, string[]> = {\n    3: ['professional_oven'],\n    4: ['auto_mixer'],\n    5: ['stand_mixer'],\n    6: ['auto_oven'],\n    7: ['conveyor_belt'],\n    8: ['advanced_auto_mixer'],\n    10: ['industrial_oven'],\n    12: ['smart_conveyor_system']\n  }\n  \n  if (equipmentUnlocks[level]) {\n    equipmentUnlocks[level].forEach(equipmentId => {\n      rewards.push({\n        type: 'equipment',\n        id: equipmentId,\n        name: 'New Equipment Available',\n        description: `${equipmentId.replace(/_/g, ' ')} is now available for purchase`\n      })\n    })\n  }\n  \n  // Skill points every 2 levels\n  if (level % 2 === 0) {\n    rewards.push({\n      type: 'skill_point',\n      id: `skill_point_${level}`,\n      name: 'Skill Point',\n      description: 'Use this to upgrade your skills in the technology tree',\n      value: 1\n    })\n  }\n  \n  return rewards\n}\n\nexport const ACHIEVEMENTS: Achievement[] = [\n  {\n    id: 'first_order',\n    name: 'First Customer',\n    description: 'Complete your first order',\n    icon: '🎯',\n    category: 'baking',\n    requirements: [{ type: 'orders_completed', target: 1 }],\n    reward: { type: 'money', id: 'first_order_bonus', name: 'First Order Bonus', description: 'Bonus for first order', value: 50 },\n    unlocked: true,\n    completed: false\n  },\n  {\n    id: 'baker_apprentice',\n    name: 'Baker Apprentice',\n    description: 'Complete 10 orders',\n    icon: '👨‍🍳',\n    category: 'baking',\n    requirements: [{ type: 'orders_completed', target: 10 }],\n    reward: { type: 'recipe', id: 'special_cookies', name: 'Special Recipe', description: 'Unlock special cookie recipe' },\n    unlocked: true,\n    completed: false\n  },\n  {\n    id: 'money_maker',\n    name: 'Money Maker',\n    description: 'Earn $1000 total',\n    icon: '💰',\n    category: 'business',\n    requirements: [{ type: 'money_earned', target: 1000 }],\n    reward: { type: 'skill_point', id: 'money_maker_skill', name: 'Business Skill Point', description: 'Extra skill point for business success', value: 1 },\n    unlocked: true,\n    completed: false\n  },\n  {\n    id: 'recipe_collector',\n    name: 'Recipe Collector',\n    description: 'Unlock 5 different recipes',\n    icon: '📚',\n    category: 'collection',\n    requirements: [{ type: 'recipes_unlocked', target: 5 }],\n    reward: { type: 'money', id: 'recipe_bonus', name: 'Recipe Collection Bonus', description: 'Bonus for collecting recipes', value: 200 },\n    unlocked: true,\n    completed: false\n  },\n  {\n    id: 'level_master',\n    name: 'Level Master',\n    description: 'Reach level 10',\n    icon: '⭐',\n    category: 'special',\n    requirements: [{ type: 'level_reached', target: 10 }],\n    reward: { type: 'equipment', id: 'master_oven', name: 'Master Oven', description: 'Unlock the legendary Master Oven' },\n    unlocked: true,\n    completed: false\n  }\n]\n\nexport const SKILL_TREE: SkillTree[] = [\n  {\n    id: 'baking_speed_1',\n    name: 'Quick Hands',\n    description: 'Increase baking speed by 10%',\n    icon: '⚡',\n    category: 'efficiency',\n    level: 0,\n    maxLevel: 3,\n    cost: 1,\n    requirements: { playerLevel: 2 },\n    effects: [{ type: 'baking_speed', value: 0.1 }]\n  },\n  {\n    id: 'money_bonus_1',\n    name: 'Business Sense',\n    description: 'Increase money earned by 15%',\n    icon: '💼',\n    category: 'business',\n    level: 0,\n    maxLevel: 3,\n    cost: 1,\n    requirements: { playerLevel: 3 },\n    effects: [{ type: 'money_multiplier', value: 0.15 }]\n  },\n  {\n    id: 'xp_bonus_1',\n    name: 'Fast Learner',\n    description: 'Increase experience gained by 20%',\n    icon: '📈',\n    category: 'efficiency',\n    level: 0,\n    maxLevel: 2,\n    cost: 2,\n    requirements: { playerLevel: 4 },\n    effects: [{ type: 'xp_multiplier', value: 0.2 }]\n  },\n  {\n    id: 'ingredient_efficiency_1',\n    name: 'Efficient Baker',\n    description: 'Use 10% fewer ingredients',\n    icon: '🌾',\n    category: 'efficiency',\n    level: 0,\n    maxLevel: 2,\n    cost: 2,\n    requirements: { playerLevel: 5, skills: ['baking_speed_1'] },\n    effects: [{ type: 'ingredient_efficiency', value: 0.1 }]\n  },\n  {\n    id: 'automation_unlock_1',\n    name: 'Automation Expert',\n    description: 'Unlock advanced automation features',\n    icon: '🤖',\n    category: 'automation',\n    level: 0,\n    maxLevel: 1,\n    cost: 3,\n    requirements: { playerLevel: 8, achievements: ['baker_apprentice'] },\n    effects: [{ type: 'automation_unlock', value: 1 }]\n  }\n]\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;;;;;AAuDvC,SAAS,4BAA4B,KAAa;IACvD,IAAI,SAAS,GAAG,OAAO;IACvB,OAAO,KAAK,KAAK,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,QAAQ;AACjD;AAEO,SAAS,iCAAiC,KAAa;IAC5D,IAAI,QAAQ;IACZ,IAAK,IAAI,IAAI,GAAG,KAAK,OAAO,IAAK;QAC/B,SAAS,4BAA4B;IACvC;IACA,OAAO;AACT;AAEO,SAAS,uBAAuB,UAAkB;IACvD,IAAI,QAAQ;IACZ,IAAI,WAAW;IAEf,MAAO,KAAM;QACX,MAAM,cAAc,4BAA4B,QAAQ;QACxD,IAAI,WAAW,cAAc,YAAY;YACvC;QACF;QACA,YAAY;QACZ;IACF;IAEA,MAAM,cAAc,4BAA4B,QAAQ;IACxD,MAAM,kBAAkB,aAAa;IAErC,OAAO;QACL;QACA,YAAY;QACZ,oBAAoB;QACpB,iBAAiB;QACjB,SAAS,gBAAgB;IAC3B;AACF;AAEO,SAAS,gBAAgB,KAAa;IAC3C,MAAM,UAAyB,EAAE;IAEjC,4BAA4B;IAC5B,QAAQ,IAAI,CAAC;QACX,MAAM;QACN,IAAI,CAAC,MAAM,EAAE,OAAO;QACpB,MAAM;QACN,aAAa,CAAC,+BAA+B,EAAE,OAAO;QACtD,OAAO,QAAQ;IACjB;IAEA,oCAAoC;IACpC,MAAM,gBAA0C;QAC9C,GAAG;YAAC;SAAiB;QACrB,GAAG;YAAC;YAAsB;SAAkB;QAC5C,GAAG;YAAC;SAAa;QACjB,GAAG;YAAC;SAAa;QACjB,GAAG;YAAC;SAAW;QACf,GAAG;YAAC;SAAsB;QAC1B,GAAG;YAAC;SAAkB;QACtB,GAAG;YAAC;SAAoB;QACxB,IAAI;YAAC;SAAgB;QACrB,IAAI;YAAC;SAAa;QAClB,IAAI;YAAC;SAAsB;IAC7B;IAEA,IAAI,aAAa,CAAC,MAAM,EAAE;QACxB,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;YAC3B,QAAQ,IAAI,CAAC;gBACX,MAAM;gBACN,IAAI;gBACJ,MAAM;gBACN,aAAa,CAAC,iBAAiB,EAAE,SAAS,OAAO,CAAC,MAAM,MAAM;YAChE;QACF;IACF;IAEA,oBAAoB;IACpB,MAAM,mBAA6C;QACjD,GAAG;YAAC;SAAoB;QACxB,GAAG;YAAC;SAAa;QACjB,GAAG;YAAC;SAAc;QAClB,GAAG;YAAC;SAAY;QAChB,GAAG;YAAC;SAAgB;QACpB,GAAG;YAAC;SAAsB;QAC1B,IAAI;YAAC;SAAkB;QACvB,IAAI;YAAC;SAAwB;IAC/B;IAEA,IAAI,gBAAgB,CAAC,MAAM,EAAE;QAC3B,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;YAC9B,QAAQ,IAAI,CAAC;gBACX,MAAM;gBACN,IAAI;gBACJ,MAAM;gBACN,aAAa,GAAG,YAAY,OAAO,CAAC,MAAM,KAAK,8BAA8B,CAAC;YAChF;QACF;IACF;IAEA,8BAA8B;IAC9B,IAAI,QAAQ,MAAM,GAAG;QACnB,QAAQ,IAAI,CAAC;YACX,MAAM;YACN,IAAI,CAAC,YAAY,EAAE,OAAO;YAC1B,MAAM;YACN,aAAa;YACb,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEO,MAAM,eAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAoB,QAAQ;YAAE;SAAE;QACvD,QAAQ;YAAE,MAAM;YAAS,IAAI;YAAqB,MAAM;YAAqB,aAAa;YAAyB,OAAO;QAAG;QAC7H,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAoB,QAAQ;YAAG;SAAE;QACxD,QAAQ;YAAE,MAAM;YAAU,IAAI;YAAmB,MAAM;YAAkB,aAAa;QAA+B;QACrH,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAgB,QAAQ;YAAK;SAAE;QACtD,QAAQ;YAAE,MAAM;YAAe,IAAI;YAAqB,MAAM;YAAwB,aAAa;YAA0C,OAAO;QAAE;QACtJ,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAoB,QAAQ;YAAE;SAAE;QACvD,QAAQ;YAAE,MAAM;YAAS,IAAI;YAAgB,MAAM;YAA2B,aAAa;YAAgC,OAAO;QAAI;QACtI,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAiB,QAAQ;YAAG;SAAE;QACrD,QAAQ;YAAE,MAAM;YAAa,IAAI;YAAe,MAAM;YAAe,aAAa;QAAmC;QACrH,UAAU;QACV,WAAW;IACb;CACD;AAEM,MAAM,aAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,MAAM;QACN,cAAc;YAAE,aAAa;QAAE;QAC/B,SAAS;YAAC;gBAAE,MAAM;gBAAgB,OAAO;YAAI;SAAE;IACjD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,MAAM;QACN,cAAc;YAAE,aAAa;QAAE;QAC/B,SAAS;YAAC;gBAAE,MAAM;gBAAoB,OAAO;YAAK;SAAE;IACtD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,MAAM;QACN,cAAc;YAAE,aAAa;QAAE;QAC/B,SAAS;YAAC;gBAAE,MAAM;gBAAiB,OAAO;YAAI;SAAE;IAClD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,MAAM;QACN,cAAc;YAAE,aAAa;YAAG,QAAQ;gBAAC;aAAiB;QAAC;QAC3D,SAAS;YAAC;gBAAE,MAAM;gBAAyB,OAAO;YAAI;SAAE;IAC1D;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,MAAM;QACN,cAAc;YAAE,aAAa;YAAG,cAAc;gBAAC;aAAmB;QAAC;QACnE,SAAS;YAAC;gBAAE,MAAM;gBAAqB,OAAO;YAAE;SAAE;IACpD;CACD", "debugId": null}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/lib/automationSystem.ts"], "sourcesContent": ["// Automation system for Bake It Out\n\nexport interface AutomationSettings {\n  enabled: boolean\n  autoStart: boolean\n  preferredRecipes: string[]\n  maxConcurrentJobs: number\n  priorityMode: 'speed' | 'efficiency' | 'profit'\n  ingredientThreshold: number // Minimum ingredients before stopping\n}\n\nexport interface AutomationJob {\n  id: string\n  equipmentId: string\n  recipeId: string\n  startTime: number\n  duration: number\n  status: 'queued' | 'running' | 'completed' | 'failed'\n  ingredients: { name: string; quantity: number }[]\n  efficiency: number\n}\n\nexport interface ConveyorBelt {\n  id: string\n  name: string\n  level: number\n  speed: number // items per minute\n  capacity: number // max items on belt\n  connections: string[] // connected equipment IDs\n  items: ConveyorItem[]\n  isActive: boolean\n}\n\nexport interface ConveyorItem {\n  id: string\n  recipeId: string\n  position: number // 0-1 along the belt\n  targetEquipmentId: string\n}\n\nexport interface AutomationUpgrade {\n  id: string\n  name: string\n  description: string\n  type: 'speed' | 'efficiency' | 'capacity' | 'intelligence'\n  cost: number\n  unlockLevel: number\n  effects: {\n    speedMultiplier?: number\n    efficiencyBonus?: number\n    capacityIncrease?: number\n    autoQueueing?: boolean\n    smartPrioritization?: boolean\n  }\n}\n\nexport const AUTOMATION_UPGRADES: AutomationUpgrade[] = [\n  {\n    id: 'auto_queue_basic',\n    name: 'Basic Auto-Queue',\n    description: 'Equipment automatically starts the next recipe when finished',\n    type: 'intelligence',\n    cost: 500,\n    unlockLevel: 4,\n    effects: { autoQueueing: true }\n  },\n  {\n    id: 'efficiency_boost_1',\n    name: 'Efficiency Boost I',\n    description: 'Automated equipment uses 10% fewer ingredients',\n    type: 'efficiency',\n    cost: 750,\n    unlockLevel: 5,\n    effects: { efficiencyBonus: 0.1 }\n  },\n  {\n    id: 'speed_boost_1',\n    name: 'Speed Boost I',\n    description: 'Automated equipment works 15% faster',\n    type: 'speed',\n    cost: 1000,\n    unlockLevel: 6,\n    effects: { speedMultiplier: 1.15 }\n  },\n  {\n    id: 'smart_prioritization',\n    name: 'Smart Prioritization',\n    description: 'Automation prioritizes orders based on profit and urgency',\n    type: 'intelligence',\n    cost: 1500,\n    unlockLevel: 8,\n    effects: { smartPrioritization: true }\n  },\n  {\n    id: 'efficiency_boost_2',\n    name: 'Efficiency Boost II',\n    description: 'Automated equipment uses 20% fewer ingredients',\n    type: 'efficiency',\n    cost: 2000,\n    unlockLevel: 10,\n    effects: { efficiencyBonus: 0.2 }\n  },\n  {\n    id: 'speed_boost_2',\n    name: 'Speed Boost II',\n    description: 'Automated equipment works 30% faster',\n    type: 'speed',\n    cost: 2500,\n    unlockLevel: 12,\n    effects: { speedMultiplier: 1.3 }\n  }\n]\n\nexport function calculateAutomationEfficiency(\n  baseEfficiency: number,\n  automationLevel: number,\n  upgrades: string[],\n  skillBonuses: number = 0\n): number {\n  let efficiency = baseEfficiency\n\n  // Automation level bonus\n  efficiency *= (1 + automationLevel * 0.1)\n\n  // Upgrade bonuses\n  upgrades.forEach(upgradeId => {\n    const upgrade = AUTOMATION_UPGRADES.find(u => u.id === upgradeId)\n    if (upgrade?.effects.efficiencyBonus) {\n      efficiency *= (1 + upgrade.effects.efficiencyBonus)\n    }\n  })\n\n  // Skill bonuses\n  efficiency *= (1 + skillBonuses)\n\n  return Math.min(efficiency, 2.0) // Cap at 200% efficiency\n}\n\nexport function calculateAutomationSpeed(\n  baseSpeed: number,\n  automationLevel: number,\n  upgrades: string[],\n  skillBonuses: number = 0\n): number {\n  let speed = baseSpeed\n\n  // Automation level bonus\n  speed *= (1 + automationLevel * 0.05)\n\n  // Upgrade bonuses\n  upgrades.forEach(upgradeId => {\n    const upgrade = AUTOMATION_UPGRADES.find(u => u.id === upgradeId)\n    if (upgrade?.effects.speedMultiplier) {\n      speed *= upgrade.effects.speedMultiplier\n    }\n  })\n\n  // Skill bonuses\n  speed *= (1 + skillBonuses)\n\n  return speed\n}\n\nexport function canAutomate(equipmentType: string, automationLevel: number): boolean {\n  const automationRequirements: Record<string, number> = {\n    'oven': 1,\n    'mixer': 1,\n    'counter': 2,\n    'auto_oven': 0,\n    'auto_mixer': 0,\n    'conveyor': 0\n  }\n\n  return automationLevel >= (automationRequirements[equipmentType] || 999)\n}\n\nexport function generateAutomationJob(\n  equipmentId: string,\n  recipeId: string,\n  recipe: any,\n  efficiency: number\n): AutomationJob {\n  const adjustedDuration = Math.floor(recipe.bakingTime / efficiency)\n  const adjustedIngredients = recipe.ingredients.map((ing: any) => ({\n    ...ing,\n    quantity: Math.ceil(ing.quantity * (1 - (efficiency - 1) * 0.1))\n  }))\n\n  return {\n    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n    equipmentId,\n    recipeId,\n    startTime: Date.now(),\n    duration: adjustedDuration,\n    status: 'queued',\n    ingredients: adjustedIngredients,\n    efficiency\n  }\n}\n\nexport function selectOptimalRecipe(\n  availableRecipes: any[],\n  inventory: any[],\n  priorityMode: 'speed' | 'efficiency' | 'profit',\n  currentOrders: any[]\n): string | null {\n  const craftableRecipes = availableRecipes.filter(recipe =>\n    recipe.ingredients.every((ingredient: any) => {\n      const inventoryItem = inventory.find(item => item.name === ingredient.name)\n      return inventoryItem && inventoryItem.quantity >= ingredient.quantity\n    })\n  )\n\n  if (craftableRecipes.length === 0) return null\n\n  switch (priorityMode) {\n    case 'speed':\n      return craftableRecipes.reduce((fastest, recipe) =>\n        recipe.bakingTime < fastest.bakingTime ? recipe : fastest\n      ).id\n\n    case 'profit':\n      return craftableRecipes.reduce((mostProfitable, recipe) =>\n        recipe.basePrice > mostProfitable.basePrice ? recipe : mostProfitable\n      ).id\n\n    case 'efficiency':\n      // Prioritize recipes needed for current orders\n      const neededRecipes = currentOrders.flatMap(order => order.items)\n      const neededCraftable = craftableRecipes.filter(recipe =>\n        neededRecipes.includes(recipe.name)\n      )\n      \n      if (neededCraftable.length > 0) {\n        return neededCraftable[0].id\n      }\n      \n      return craftableRecipes[0].id\n\n    default:\n      return craftableRecipes[0].id\n  }\n}\n\nexport function updateConveyorBelt(\n  belt: ConveyorBelt,\n  deltaTime: number\n): ConveyorBelt {\n  const speed = belt.speed / 60 // convert to items per second\n  const moveDistance = speed * deltaTime\n\n  const updatedItems = belt.items.map(item => ({\n    ...item,\n    position: Math.min(1, item.position + moveDistance)\n  }))\n\n  // Remove items that reached the end\n  const activeItems = updatedItems.filter(item => item.position < 1)\n\n  return {\n    ...belt,\n    items: activeItems\n  }\n}\n\nexport function addItemToConveyor(\n  belt: ConveyorBelt,\n  item: Omit<ConveyorItem, 'position'>\n): ConveyorBelt {\n  if (belt.items.length >= belt.capacity) {\n    return belt // Belt is full\n  }\n\n  const newItem: ConveyorItem = {\n    ...item,\n    position: 0\n  }\n\n  return {\n    ...belt,\n    items: [...belt.items, newItem]\n  }\n}\n"], "names": [], "mappings": "AAAA,oCAAoC;;;;;;;;;;;AAwD7B,MAAM,sBAA2C;IACtD;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,aAAa;QACb,SAAS;YAAE,cAAc;QAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,aAAa;QACb,SAAS;YAAE,iBAAiB;QAAI;IAClC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,aAAa;QACb,SAAS;YAAE,iBAAiB;QAAK;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,aAAa;QACb,SAAS;YAAE,qBAAqB;QAAK;IACvC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,aAAa;QACb,SAAS;YAAE,iBAAiB;QAAI;IAClC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,aAAa;QACb,SAAS;YAAE,iBAAiB;QAAI;IAClC;CACD;AAEM,SAAS,8BACd,cAAsB,EACtB,eAAuB,EACvB,QAAkB,EAClB,eAAuB,CAAC;IAExB,IAAI,aAAa;IAEjB,yBAAyB;IACzB,cAAe,IAAI,kBAAkB;IAErC,kBAAkB;IAClB,SAAS,OAAO,CAAC,CAAA;QACf,MAAM,UAAU,oBAAoB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACvD,IAAI,SAAS,QAAQ,iBAAiB;YACpC,cAAe,IAAI,QAAQ,OAAO,CAAC,eAAe;QACpD;IACF;IAEA,gBAAgB;IAChB,cAAe,IAAI;IAEnB,OAAO,KAAK,GAAG,CAAC,YAAY,KAAK,yBAAyB;;AAC5D;AAEO,SAAS,yBACd,SAAiB,EACjB,eAAuB,EACvB,QAAkB,EAClB,eAAuB,CAAC;IAExB,IAAI,QAAQ;IAEZ,yBAAyB;IACzB,SAAU,IAAI,kBAAkB;IAEhC,kBAAkB;IAClB,SAAS,OAAO,CAAC,CAAA;QACf,MAAM,UAAU,oBAAoB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACvD,IAAI,SAAS,QAAQ,iBAAiB;YACpC,SAAS,QAAQ,OAAO,CAAC,eAAe;QAC1C;IACF;IAEA,gBAAgB;IAChB,SAAU,IAAI;IAEd,OAAO;AACT;AAEO,SAAS,YAAY,aAAqB,EAAE,eAAuB;IACxE,MAAM,yBAAiD;QACrD,QAAQ;QACR,SAAS;QACT,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;IACd;IAEA,OAAO,mBAAmB,CAAC,sBAAsB,CAAC,cAAc,IAAI,GAAG;AACzE;AAEO,SAAS,sBACd,WAAmB,EACnB,QAAgB,EAChB,MAAW,EACX,UAAkB;IAElB,MAAM,mBAAmB,KAAK,KAAK,CAAC,OAAO,UAAU,GAAG;IACxD,MAAM,sBAAsB,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,MAAa,CAAC;YAChE,GAAG,GAAG;YACN,UAAU,KAAK,IAAI,CAAC,IAAI,QAAQ,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG;QAChE,CAAC;IAED,OAAO;QACL,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QACjE;QACA;QACA,WAAW,KAAK,GAAG;QACnB,UAAU;QACV,QAAQ;QACR,aAAa;QACb;IACF;AACF;AAEO,SAAS,oBACd,gBAAuB,EACvB,SAAgB,EAChB,YAA+C,EAC/C,aAAoB;IAEpB,MAAM,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,SAC/C,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC;YACxB,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;YAC1E,OAAO,iBAAiB,cAAc,QAAQ,IAAI,WAAW,QAAQ;QACvE;IAGF,IAAI,iBAAiB,MAAM,KAAK,GAAG,OAAO;IAE1C,OAAQ;QACN,KAAK;YACH,OAAO,iBAAiB,MAAM,CAAC,CAAC,SAAS,SACvC,OAAO,UAAU,GAAG,QAAQ,UAAU,GAAG,SAAS,SAClD,EAAE;QAEN,KAAK;YACH,OAAO,iBAAiB,MAAM,CAAC,CAAC,gBAAgB,SAC9C,OAAO,SAAS,GAAG,eAAe,SAAS,GAAG,SAAS,gBACvD,EAAE;QAEN,KAAK;YACH,+CAA+C;YAC/C,MAAM,gBAAgB,cAAc,OAAO,CAAC,CAAA,QAAS,MAAM,KAAK;YAChE,MAAM,kBAAkB,iBAAiB,MAAM,CAAC,CAAA,SAC9C,cAAc,QAAQ,CAAC,OAAO,IAAI;YAGpC,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,OAAO,eAAe,CAAC,EAAE,CAAC,EAAE;YAC9B;YAEA,OAAO,gBAAgB,CAAC,EAAE,CAAC,EAAE;QAE/B;YACE,OAAO,gBAAgB,CAAC,EAAE,CAAC,EAAE;IACjC;AACF;AAEO,SAAS,mBACd,IAAkB,EAClB,SAAiB;IAEjB,MAAM,QAAQ,KAAK,KAAK,GAAG,GAAG,8BAA8B;;IAC5D,MAAM,eAAe,QAAQ;IAE7B,MAAM,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;YAC3C,GAAG,IAAI;YACP,UAAU,KAAK,GAAG,CAAC,GAAG,KAAK,QAAQ,GAAG;QACxC,CAAC;IAED,oCAAoC;IACpC,MAAM,cAAc,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,GAAG;IAEhE,OAAO;QACL,GAAG,IAAI;QACP,OAAO;IACT;AACF;AAEO,SAAS,kBACd,IAAkB,EAClB,IAAoC;IAEpC,IAAI,KAAK,KAAK,CAAC,MAAM,IAAI,KAAK,QAAQ,EAAE;QACtC,OAAO,KAAK,eAAe;;IAC7B;IAEA,MAAM,UAAwB;QAC5B,GAAG,IAAI;QACP,UAAU;IACZ;IAEA,OAAO;QACL,GAAG,IAAI;QACP,OAAO;eAAI,KAAK,KAAK;YAAE;SAAQ;IACjC;AACF", "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/contexts/GameContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect } from 'react'\nimport { EquipmentData } from '@/components/game/Equipment'\nimport { OrderData } from '@/components/game/Order'\nimport { generateRandomOrder, calculateExperienceReward, canCraftRecipe, getRecipeById, getAvailableRecipes } from '@/lib/gameLogic'\nimport {\n  getLevelFromExperience,\n  LevelReward,\n  Achievement,\n  SkillTree,\n  ACHIEVEMENTS,\n  SKILL_TREE\n} from '@/lib/progressionSystem'\nimport {\n  AutomationSettings,\n  AutomationJob,\n  ConveyorBelt,\n  AUTOMATION_UPGRADES,\n  calculateAutomationEfficiency,\n  calculateAutomationSpeed,\n  selectOptimalRecipe,\n  generateAutomationJob\n} from '@/lib/automationSystem'\n\nexport interface Player {\n  level: number\n  experience: number\n  money: number\n  maxExperience: number\n  skillPoints: number\n  totalMoneyEarned: number\n  totalOrdersCompleted: number\n  totalItemsBaked: number\n  unlockedRecipes: string[]\n  automationUpgrades: string[]\n}\n\nexport interface Ingredient {\n  name: string\n  quantity: number\n  cost: number\n  icon: string\n}\n\ninterface GameContextType {\n  player: Player\n  equipment: EquipmentData[]\n  inventory: Ingredient[]\n  orders: OrderData[]\n  achievements: Achievement[]\n  skills: SkillTree[]\n  levelUpRewards: LevelReward[]\n  showLevelUp: boolean\n  automationSettings: AutomationSettings\n  automationJobs: AutomationJob[]\n  conveyorBelts: ConveyorBelt[]\n  updatePlayer: (updates: Partial<Player>) => void\n  updateEquipment: (equipmentId: string, updates: Partial<EquipmentData>) => void\n  addExperience: (amount: number) => void\n  addMoney: (amount: number) => void\n  spendMoney: (amount: number) => boolean\n  useIngredient: (name: string, quantity: number) => boolean\n  addIngredient: (name: string, quantity: number) => void\n  acceptOrder: (orderId: string) => void\n  completeOrder: (orderId: string) => void\n  declineOrder: (orderId: string) => void\n  generateNewOrder: () => void\n  upgradeSkill: (skillId: string) => void\n  checkAchievements: () => void\n  dismissLevelUp: () => void\n  updateAutomationSettings: (updates: Partial<AutomationSettings>) => void\n  purchaseAutomationUpgrade: (upgradeId: string) => void\n  startAutomationJob: (equipmentId: string) => void\n}\n\nconst GameContext = createContext<GameContextType | undefined>(undefined)\n\nconst RECIPES = [\n  'Chocolate Chip Cookies',\n  'Vanilla Muffins',\n  'Cinnamon Rolls',\n  'Brownies',\n  'Croissants',\n  'Bread Loaf',\n  'Cupcakes',\n  'Apple Pie'\n]\n\nconst CUSTOMER_NAMES = [\n  'Alice Johnson', 'Bob Smith', 'Carol Davis', 'David Wilson',\n  'Emma Brown', 'Frank Miller', 'Grace Taylor', 'Henry Anderson',\n  'Ivy Thomas', 'Jack Martinez', 'Kate Garcia', 'Liam Rodriguez'\n]\n\nexport function GameProvider({ children }: { children: React.ReactNode }) {\n  const [player, setPlayer] = useState<Player>({\n    level: 1,\n    experience: 0,\n    money: 100,\n    maxExperience: 100,\n    skillPoints: 0,\n    totalMoneyEarned: 0,\n    totalOrdersCompleted: 0,\n    totalItemsBaked: 0,\n    unlockedRecipes: ['chocolate_chip_cookies', 'vanilla_muffins'],\n    automationUpgrades: []\n  })\n\n  const [equipment, setEquipment] = useState<EquipmentData[]>([\n    { id: 'oven1', name: 'Basic Oven', type: 'oven', isActive: false, level: 1, efficiency: 1.0, automationLevel: 0 },\n    { id: 'mixer1', name: 'Hand Mixer', type: 'mixer', isActive: false, level: 1, efficiency: 1.0, automationLevel: 0 },\n    { id: 'counter1', name: 'Work Counter', type: 'counter', isActive: false, level: 1, efficiency: 1.0, automationLevel: 0 }\n  ])\n\n  const [inventory, setInventory] = useState<Ingredient[]>([\n    { name: 'Flour', quantity: 15, cost: 5, icon: '🌾' },\n    { name: 'Sugar', quantity: 12, cost: 8, icon: '🍯' },\n    { name: 'Eggs', quantity: 10, cost: 12, icon: '🥚' },\n    { name: 'Butter', quantity: 8, cost: 15, icon: '🧈' },\n    { name: 'Chocolate Chips', quantity: 6, cost: 20, icon: '🍫' },\n    { name: 'Vanilla', quantity: 5, cost: 25, icon: '🌿' },\n    { name: 'Salt', quantity: 10, cost: 3, icon: '🧂' }\n  ])\n\n  const [orders, setOrders] = useState<OrderData[]>([\n    {\n      id: '1',\n      customerName: 'Alice Johnson',\n      items: ['Chocolate Chip Cookies'],\n      timeLimit: 300,\n      reward: 25,\n      status: 'pending',\n      difficulty: 1\n    }\n  ])\n\n  const [achievements, setAchievements] = useState<Achievement[]>(ACHIEVEMENTS)\n  const [skills, setSkills] = useState<SkillTree[]>(SKILL_TREE)\n  const [levelUpRewards, setLevelUpRewards] = useState<LevelReward[]>([])\n  const [showLevelUp, setShowLevelUp] = useState(false)\n\n  const [automationSettings, setAutomationSettings] = useState<AutomationSettings>({\n    enabled: false,\n    autoStart: false,\n    preferredRecipes: [],\n    maxConcurrentJobs: 2,\n    priorityMode: 'efficiency',\n    ingredientThreshold: 5\n  })\n\n  const [automationJobs, setAutomationJobs] = useState<AutomationJob[]>([])\n  const [conveyorBelts, setConveyorBelts] = useState<ConveyorBelt[]>([])\n\n  // Equipment timer effect\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setEquipment(prev => prev.map(eq => {\n        if (eq.isActive && eq.timeRemaining && eq.timeRemaining > 0) {\n          return { ...eq, timeRemaining: eq.timeRemaining - 1 }\n        } else if (eq.isActive && eq.timeRemaining === 0) {\n          // Baking completed - could add notification here\n          return { ...eq, isActive: false, timeRemaining: undefined, currentRecipe: undefined }\n        }\n        return eq\n      }))\n    }, 1000)\n\n    return () => clearInterval(interval)\n  }, [])\n\n  // Order timer effect\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setOrders(prev => prev.map(order => {\n        if ((order.status === 'accepted' || order.status === 'in_progress') && order.timeLimit > 0) {\n          const newTimeLimit = order.timeLimit - 1\n          if (newTimeLimit === 0) {\n            return { ...order, status: 'failed', timeLimit: 0 }\n          }\n          return { ...order, timeLimit: newTimeLimit }\n        }\n        return order\n      }))\n    }, 1000)\n\n    return () => clearInterval(interval)\n  }, [])\n\n  const updatePlayer = (updates: Partial<Player>) => {\n    setPlayer(prev => ({ ...prev, ...updates }))\n  }\n\n  const updateEquipment = (equipmentId: string, updates: Partial<EquipmentData>) => {\n    setEquipment(prev => prev.map(eq => \n      eq.id === equipmentId ? { ...eq, ...updates } : eq\n    ))\n  }\n\n  const addExperience = (amount: number) => {\n    setPlayer(prev => {\n      const newTotalExp = prev.experience + amount\n      const levelData = getLevelFromExperience(newTotalExp)\n      const leveledUp = levelData.level > prev.level\n\n      if (leveledUp) {\n        setLevelUpRewards(levelData.rewards)\n        setShowLevelUp(true)\n\n        // Add skill points for level up\n        const skillPointsGained = levelData.level % 2 === 0 ? 1 : 0\n\n        return {\n          ...prev,\n          level: levelData.level,\n          experience: newTotalExp,\n          maxExperience: levelData.experienceRequired,\n          skillPoints: prev.skillPoints + skillPointsGained\n        }\n      }\n\n      return {\n        ...prev,\n        experience: newTotalExp,\n        maxExperience: levelData.experienceRequired\n      }\n    })\n  }\n\n  const addMoney = (amount: number) => {\n    setPlayer(prev => ({\n      ...prev,\n      money: prev.money + amount,\n      totalMoneyEarned: prev.totalMoneyEarned + amount\n    }))\n  }\n\n  const spendMoney = (amount: number): boolean => {\n    if (player.money >= amount) {\n      setPlayer(prev => ({ ...prev, money: prev.money - amount }))\n      return true\n    }\n    return false\n  }\n\n  const useIngredient = (name: string, quantity: number): boolean => {\n    const ingredient = inventory.find(ing => ing.name === name)\n    if (ingredient && ingredient.quantity >= quantity) {\n      setInventory(prev => prev.map(ing => \n        ing.name === name \n          ? { ...ing, quantity: ing.quantity - quantity }\n          : ing\n      ))\n      return true\n    }\n    return false\n  }\n\n  const addIngredient = (name: string, quantity: number) => {\n    setInventory(prev => prev.map(ing => \n      ing.name === name \n        ? { ...ing, quantity: ing.quantity + quantity }\n        : ing\n    ))\n  }\n\n  const acceptOrder = (orderId: string) => {\n    setOrders(prev => prev.map(order => \n      order.id === orderId \n        ? { ...order, status: 'accepted' }\n        : order\n    ))\n  }\n\n  const completeOrder = (orderId: string) => {\n    const order = orders.find(o => o.id === orderId)\n    if (order) {\n      // Check if player has required ingredients\n      const canComplete = order.items.every(itemName => {\n        const recipe = getRecipeById(itemName.toLowerCase().replace(/\\s+/g, '_'))\n        return recipe ? canCraftRecipe(recipe, inventory) : false\n      })\n\n      if (canComplete) {\n        // Consume ingredients\n        order.items.forEach(itemName => {\n          const recipe = getRecipeById(itemName.toLowerCase().replace(/\\s+/g, '_'))\n          if (recipe) {\n            recipe.ingredients.forEach(ingredient => {\n              useIngredient(ingredient.name, ingredient.quantity)\n            })\n          }\n        })\n\n        // Complete order\n        setOrders(prev => prev.map(o =>\n          o.id === orderId\n            ? { ...o, status: 'completed' }\n            : o\n        ))\n\n        // Calculate rewards\n        const timeBonus = order.timeLimit > 60 // Bonus if completed with time to spare\n        const expReward = calculateExperienceReward(order.difficulty, timeBonus)\n\n        addMoney(order.reward)\n        addExperience(expReward)\n      }\n    }\n  }\n\n  const declineOrder = (orderId: string) => {\n    setOrders(prev => prev.filter(order => order.id !== orderId))\n  }\n\n  const generateNewOrder = () => {\n    const newOrder = generateRandomOrder(player.level)\n    setOrders(prev => [...prev, newOrder])\n  }\n\n  const upgradeSkill = (skillId: string) => {\n    const skill = skills.find(s => s.id === skillId)\n    if (!skill || skill.level >= skill.maxLevel || player.skillPoints < skill.cost) {\n      return\n    }\n\n    setSkills(prev => prev.map(s =>\n      s.id === skillId\n        ? { ...s, level: s.level + 1 }\n        : s\n    ))\n\n    setPlayer(prev => ({\n      ...prev,\n      skillPoints: prev.skillPoints - skill.cost\n    }))\n  }\n\n  const checkAchievements = () => {\n    setAchievements(prev => prev.map(achievement => {\n      if (achievement.completed) return achievement\n\n      const requirement = achievement.requirements[0]\n      let current = 0\n\n      switch (requirement.type) {\n        case 'orders_completed':\n          current = player.totalOrdersCompleted\n          break\n        case 'money_earned':\n          current = player.totalMoneyEarned\n          break\n        case 'recipes_unlocked':\n          current = player.unlockedRecipes.length\n          break\n        case 'level_reached':\n          current = player.level\n          break\n        case 'items_baked':\n          current = player.totalItemsBaked\n          break\n      }\n\n      const completed = current >= requirement.target\n\n      return {\n        ...achievement,\n        requirements: [{ ...requirement, current }],\n        completed\n      }\n    }))\n  }\n\n  const dismissLevelUp = () => {\n    setShowLevelUp(false)\n    setLevelUpRewards([])\n  }\n\n  const updateAutomationSettings = (updates: Partial<AutomationSettings>) => {\n    setAutomationSettings(prev => ({ ...prev, ...updates }))\n  }\n\n  const purchaseAutomationUpgrade = (upgradeId: string) => {\n    const upgrade = AUTOMATION_UPGRADES.find(u => u.id === upgradeId)\n    if (!upgrade || player.money < upgrade.cost) return\n\n    if (spendMoney(upgrade.cost)) {\n      setPlayer(prev => ({\n        ...prev,\n        automationUpgrades: [...prev.automationUpgrades, upgradeId]\n      }))\n    }\n  }\n\n  const startAutomationJob = (equipmentId: string) => {\n    if (!automationSettings.enabled) return\n\n    const targetEquipment = equipment.find(eq => eq.id === equipmentId)\n    if (!targetEquipment || targetEquipment.isActive || targetEquipment.automationLevel === 0) return\n\n    const availableRecipes = getAvailableRecipes(player.level)\n    const optimalRecipeId = selectOptimalRecipe(\n      availableRecipes,\n      inventory,\n      automationSettings.priorityMode,\n      orders\n    )\n\n    if (!optimalRecipeId) return\n\n    const recipe = getRecipeById(optimalRecipeId)\n    if (!recipe || !canCraftRecipe(recipe, inventory)) return\n\n    const efficiency = calculateAutomationEfficiency(\n      targetEquipment.efficiency,\n      targetEquipment.automationLevel,\n      player.automationUpgrades\n    )\n\n    const job = generateAutomationJob(equipmentId, optimalRecipeId, recipe, efficiency)\n\n    // Start the job\n    setAutomationJobs(prev => [...prev, { ...job, status: 'running' }])\n    updateEquipment(equipmentId, {\n      isActive: true,\n      timeRemaining: job.duration,\n      currentRecipe: recipe.name\n    })\n\n    // Consume ingredients\n    job.ingredients.forEach(ingredient => {\n      useIngredient(ingredient.name, ingredient.quantity)\n    })\n  }\n\n  return (\n    <GameContext.Provider value={{\n      player,\n      equipment,\n      inventory,\n      orders,\n      achievements,\n      skills,\n      levelUpRewards,\n      showLevelUp,\n      automationSettings,\n      automationJobs,\n      conveyorBelts,\n      updatePlayer,\n      updateEquipment,\n      addExperience,\n      addMoney,\n      spendMoney,\n      useIngredient,\n      addIngredient,\n      acceptOrder,\n      completeOrder,\n      declineOrder,\n      generateNewOrder,\n      upgradeSkill,\n      checkAchievements,\n      dismissLevelUp,\n      updateAutomationSettings,\n      purchaseAutomationUpgrade,\n      startAutomationJob\n    }}>\n      {children}\n    </GameContext.Provider>\n  )\n}\n\nexport function useGame() {\n  const context = useContext(GameContext)\n  if (context === undefined) {\n    throw new Error('useGame must be used within a GameProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAGA;AACA;AAQA;AAdA;;;;;;AA4EA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAE/D,MAAM,UAAU;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,iBAAiB;IACrB;IAAiB;IAAa;IAAe;IAC7C;IAAc;IAAgB;IAAgB;IAC9C;IAAc;IAAiB;IAAe;CAC/C;AAEM,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;QAC3C,OAAO;QACP,YAAY;QACZ,OAAO;QACP,eAAe;QACf,aAAa;QACb,kBAAkB;QAClB,sBAAsB;QACtB,iBAAiB;QACjB,iBAAiB;YAAC;YAA0B;SAAkB;QAC9D,oBAAoB,EAAE;IACxB;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QAC1D;YAAE,IAAI;YAAS,MAAM;YAAc,MAAM;YAAQ,UAAU;YAAO,OAAO;YAAG,YAAY;YAAK,iBAAiB;QAAE;QAChH;YAAE,IAAI;YAAU,MAAM;YAAc,MAAM;YAAS,UAAU;YAAO,OAAO;YAAG,YAAY;YAAK,iBAAiB;QAAE;QAClH;YAAE,IAAI;YAAY,MAAM;YAAgB,MAAM;YAAW,UAAU;YAAO,OAAO;YAAG,YAAY;YAAK,iBAAiB;QAAE;KACzH;IAED,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QACvD;YAAE,MAAM;YAAS,UAAU;YAAI,MAAM;YAAG,MAAM;QAAK;QACnD;YAAE,MAAM;YAAS,UAAU;YAAI,MAAM;YAAG,MAAM;QAAK;QACnD;YAAE,MAAM;YAAQ,UAAU;YAAI,MAAM;YAAI,MAAM;QAAK;QACnD;YAAE,MAAM;YAAU,UAAU;YAAG,MAAM;YAAI,MAAM;QAAK;QACpD;YAAE,MAAM;YAAmB,UAAU;YAAG,MAAM;YAAI,MAAM;QAAK;QAC7D;YAAE,MAAM;YAAW,UAAU;YAAG,MAAM;YAAI,MAAM;QAAK;QACrD;YAAE,MAAM;YAAQ,UAAU;YAAI,MAAM;YAAG,MAAM;QAAK;KACnD;IAED,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAChD;YACE,IAAI;YACJ,cAAc;YACd,OAAO;gBAAC;aAAyB;YACjC,WAAW;YACX,QAAQ;YACR,QAAQ;YACR,YAAY;QACd;KACD;IAED,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,+HAAA,CAAA,eAAY;IAC5E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,+HAAA,CAAA,aAAU;IAC5D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;QAC/E,SAAS;QACT,WAAW;QACX,kBAAkB,EAAE;QACpB,mBAAmB;QACnB,cAAc;QACd,qBAAqB;IACvB;IAEA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;oBAC5B,IAAI,GAAG,QAAQ,IAAI,GAAG,aAAa,IAAI,GAAG,aAAa,GAAG,GAAG;wBAC3D,OAAO;4BAAE,GAAG,EAAE;4BAAE,eAAe,GAAG,aAAa,GAAG;wBAAE;oBACtD,OAAO,IAAI,GAAG,QAAQ,IAAI,GAAG,aAAa,KAAK,GAAG;wBAChD,iDAAiD;wBACjD,OAAO;4BAAE,GAAG,EAAE;4BAAE,UAAU;4BAAO,eAAe;4BAAW,eAAe;wBAAU;oBACtF;oBACA,OAAO;gBACT;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;oBACzB,IAAI,CAAC,MAAM,MAAM,KAAK,cAAc,MAAM,MAAM,KAAK,aAAa,KAAK,MAAM,SAAS,GAAG,GAAG;wBAC1F,MAAM,eAAe,MAAM,SAAS,GAAG;wBACvC,IAAI,iBAAiB,GAAG;4BACtB,OAAO;gCAAE,GAAG,KAAK;gCAAE,QAAQ;gCAAU,WAAW;4BAAE;wBACpD;wBACA,OAAO;4BAAE,GAAG,KAAK;4BAAE,WAAW;wBAAa;oBAC7C;oBACA,OAAO;gBACT;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC,CAAC;IAC5C;IAEA,MAAM,kBAAkB,CAAC,aAAqB;QAC5C,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,KAC5B,GAAG,EAAE,KAAK,cAAc;oBAAE,GAAG,EAAE;oBAAE,GAAG,OAAO;gBAAC,IAAI;IAEpD;IAEA,MAAM,gBAAgB,CAAC;QACrB,UAAU,CAAA;YACR,MAAM,cAAc,KAAK,UAAU,GAAG;YACtC,MAAM,YAAY,CAAA,GAAA,+HAAA,CAAA,yBAAsB,AAAD,EAAE;YACzC,MAAM,YAAY,UAAU,KAAK,GAAG,KAAK,KAAK;YAE9C,IAAI,WAAW;gBACb,kBAAkB,UAAU,OAAO;gBACnC,eAAe;gBAEf,gCAAgC;gBAChC,MAAM,oBAAoB,UAAU,KAAK,GAAG,MAAM,IAAI,IAAI;gBAE1D,OAAO;oBACL,GAAG,IAAI;oBACP,OAAO,UAAU,KAAK;oBACtB,YAAY;oBACZ,eAAe,UAAU,kBAAkB;oBAC3C,aAAa,KAAK,WAAW,GAAG;gBAClC;YACF;YAEA,OAAO;gBACL,GAAG,IAAI;gBACP,YAAY;gBACZ,eAAe,UAAU,kBAAkB;YAC7C;QACF;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,UAAU,CAAA,OAAQ,CAAC;gBACjB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,GAAG;gBACpB,kBAAkB,KAAK,gBAAgB,GAAG;YAC5C,CAAC;IACH;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,OAAO,KAAK,IAAI,QAAQ;YAC1B,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO,KAAK,KAAK,GAAG;gBAAO,CAAC;YAC1D,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC,MAAc;QACnC,MAAM,aAAa,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QACtD,IAAI,cAAc,WAAW,QAAQ,IAAI,UAAU;YACjD,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MAC5B,IAAI,IAAI,KAAK,OACT;wBAAE,GAAG,GAAG;wBAAE,UAAU,IAAI,QAAQ,GAAG;oBAAS,IAC5C;YAEN,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC,MAAc;QACnC,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MAC5B,IAAI,IAAI,KAAK,OACT;oBAAE,GAAG,GAAG;oBAAE,UAAU,IAAI,QAAQ,GAAG;gBAAS,IAC5C;IAER;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,QACzB,MAAM,EAAE,KAAK,UACT;oBAAE,GAAG,KAAK;oBAAE,QAAQ;gBAAW,IAC/B;IAER;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACxC,IAAI,OAAO;YACT,2CAA2C;YAC3C,MAAM,cAAc,MAAM,KAAK,CAAC,KAAK,CAAC,CAAA;gBACpC,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,QAAQ;gBACpE,OAAO,SAAS,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,aAAa;YACtD;YAEA,IAAI,aAAa;gBACf,sBAAsB;gBACtB,MAAM,KAAK,CAAC,OAAO,CAAC,CAAA;oBAClB,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,QAAQ;oBACpE,IAAI,QAAQ;wBACV,OAAO,WAAW,CAAC,OAAO,CAAC,CAAA;4BACzB,cAAc,WAAW,IAAI,EAAE,WAAW,QAAQ;wBACpD;oBACF;gBACF;gBAEA,iBAAiB;gBACjB,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACzB,EAAE,EAAE,KAAK,UACL;4BAAE,GAAG,CAAC;4BAAE,QAAQ;wBAAY,IAC5B;gBAGN,oBAAoB;gBACpB,MAAM,YAAY,MAAM,SAAS,GAAG,GAAG,wCAAwC;;gBAC/E,MAAM,YAAY,CAAA,GAAA,uHAAA,CAAA,4BAAyB,AAAD,EAAE,MAAM,UAAU,EAAE;gBAE9D,SAAS,MAAM,MAAM;gBACrB,cAAc;YAChB;QACF;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,mBAAmB;QACvB,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,KAAK;QACjD,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAS;IACvC;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACxC,IAAI,CAAC,SAAS,MAAM,KAAK,IAAI,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,MAAM,IAAI,EAAE;YAC9E;QACF;QAEA,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACzB,EAAE,EAAE,KAAK,UACL;oBAAE,GAAG,CAAC;oBAAE,OAAO,EAAE,KAAK,GAAG;gBAAE,IAC3B;QAGN,UAAU,CAAA,OAAQ,CAAC;gBACjB,GAAG,IAAI;gBACP,aAAa,KAAK,WAAW,GAAG,MAAM,IAAI;YAC5C,CAAC;IACH;IAEA,MAAM,oBAAoB;QACxB,gBAAgB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;gBAC/B,IAAI,YAAY,SAAS,EAAE,OAAO;gBAElC,MAAM,cAAc,YAAY,YAAY,CAAC,EAAE;gBAC/C,IAAI,UAAU;gBAEd,OAAQ,YAAY,IAAI;oBACtB,KAAK;wBACH,UAAU,OAAO,oBAAoB;wBACrC;oBACF,KAAK;wBACH,UAAU,OAAO,gBAAgB;wBACjC;oBACF,KAAK;wBACH,UAAU,OAAO,eAAe,CAAC,MAAM;wBACvC;oBACF,KAAK;wBACH,UAAU,OAAO,KAAK;wBACtB;oBACF,KAAK;wBACH,UAAU,OAAO,eAAe;wBAChC;gBACJ;gBAEA,MAAM,YAAY,WAAW,YAAY,MAAM;gBAE/C,OAAO;oBACL,GAAG,WAAW;oBACd,cAAc;wBAAC;4BAAE,GAAG,WAAW;4BAAE;wBAAQ;qBAAE;oBAC3C;gBACF;YACF;IACF;IAEA,MAAM,iBAAiB;QACrB,eAAe;QACf,kBAAkB,EAAE;IACtB;IAEA,MAAM,2BAA2B,CAAC;QAChC,sBAAsB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC,CAAC;IACxD;IAEA,MAAM,4BAA4B,CAAC;QACjC,MAAM,UAAU,8HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACvD,IAAI,CAAC,WAAW,OAAO,KAAK,GAAG,QAAQ,IAAI,EAAE;QAE7C,IAAI,WAAW,QAAQ,IAAI,GAAG;YAC5B,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,oBAAoB;2BAAI,KAAK,kBAAkB;wBAAE;qBAAU;gBAC7D,CAAC;QACH;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAEjC,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK;QACvD,IAAI,CAAC,mBAAmB,gBAAgB,QAAQ,IAAI,gBAAgB,eAAe,KAAK,GAAG;QAE3F,MAAM,mBAAmB,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,KAAK;QACzD,MAAM,kBAAkB,CAAA,GAAA,8HAAA,CAAA,sBAAmB,AAAD,EACxC,kBACA,WACA,mBAAmB,YAAY,EAC/B;QAGF,IAAI,CAAC,iBAAiB;QAEtB,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE;QAC7B,IAAI,CAAC,UAAU,CAAC,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,YAAY;QAEnD,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,gCAA6B,AAAD,EAC7C,gBAAgB,UAAU,EAC1B,gBAAgB,eAAe,EAC/B,OAAO,kBAAkB;QAG3B,MAAM,MAAM,CAAA,GAAA,8HAAA,CAAA,wBAAqB,AAAD,EAAE,aAAa,iBAAiB,QAAQ;QAExE,gBAAgB;QAChB,kBAAkB,CAAA,OAAQ;mBAAI;gBAAM;oBAAE,GAAG,GAAG;oBAAE,QAAQ;gBAAU;aAAE;QAClE,gBAAgB,aAAa;YAC3B,UAAU;YACV,eAAe,IAAI,QAAQ;YAC3B,eAAe,OAAO,IAAI;QAC5B;QAEA,sBAAsB;QACtB,IAAI,WAAW,CAAC,OAAO,CAAC,CAAA;YACtB,cAAc,WAAW,IAAI,EAAE,WAAW,QAAQ;QACpD;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'danger' | 'success'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport const Button: React.FC<ButtonProps> = ({\n  variant = 'primary',\n  size = 'md',\n  className = '',\n  children,\n  ...props\n}) => {\n  const baseClasses = 'font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2'\n\n  const variantClasses = {\n    primary: 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500',\n    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500',\n    danger: 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500',\n    success: 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500',\n  }\n\n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-base',\n    lg: 'px-6 py-3 text-lg',\n  }\n\n  const combinedClasses = [\n    baseClasses,\n    variantClasses[variant],\n    sizeClasses[size],\n    className\n  ].join(' ')\n\n  return (\n    <button\n      className={combinedClasses}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAQO,MAAM,SAAgC,CAAC,EAC5C,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB;QACA,cAAc,CAAC,QAAQ;QACvB,WAAW,CAAC,KAAK;QACjB;KACD,CAAC,IAAI,CAAC;IAEP,qBACE,8OAAC;QACC,WAAW;QACV,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1297, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/Equipment.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\n\nexport interface EquipmentData {\n  id: string\n  name: string\n  type: 'oven' | 'mixer' | 'counter' | 'auto_oven' | 'auto_mixer' | 'conveyor'\n  isActive: boolean\n  timeRemaining?: number\n  currentRecipe?: string\n  level: number\n  efficiency: number\n  automationLevel: number\n  isAutomated?: boolean\n  queuedRecipes?: string[]\n}\n\ninterface EquipmentProps {\n  equipment: EquipmentData\n  onClick: (equipmentId: string, equipmentName: string) => void\n}\n\nexport function Equipment({ equipment, onClick }: EquipmentProps) {\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const getEquipmentIcon = (type: string) => {\n    switch (type) {\n      case 'oven': return '🔥'\n      case 'mixer': return '🥄'\n      case 'counter': return '🍽️'\n      default: return '⚙️'\n    }\n  }\n\n  const getStatusColor = () => {\n    if (equipment.isActive) {\n      return 'border-green-400 bg-green-50'\n    }\n    return 'border-gray-200 bg-gray-50 hover:border-orange-300 hover:bg-orange-50'\n  }\n\n  return (\n    <div\n      className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${getStatusColor()}`}\n      onClick={() => !equipment.isActive && onClick(equipment.id, equipment.name)}\n    >\n      <div className=\"text-center\">\n        <div className=\"text-3xl mb-2\">\n          {getEquipmentIcon(equipment.type)}\n        </div>\n        <h3 className=\"font-medium text-gray-800\">{equipment.name}</h3>\n        <div className=\"text-xs text-gray-500\">Level {equipment.level}</div>\n        \n        {equipment.isActive && equipment.timeRemaining ? (\n          <div className=\"mt-2\">\n            <div className=\"text-sm text-green-600\">\n              Making: {equipment.currentRecipe}\n            </div>\n            <div className=\"text-lg font-mono text-green-700\">\n              {formatTime(equipment.timeRemaining)}\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-2 mt-2\">\n              <div \n                className=\"bg-green-500 h-2 rounded-full transition-all duration-1000\"\n                style={{ \n                  width: `${100 - (equipment.timeRemaining / 60) * 100}%` \n                }}\n              ></div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"text-sm text-gray-500 mt-2\">\n            {equipment.isActive ? 'Busy' : 'Click to use'}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAuBO,SAAS,UAAU,EAAE,SAAS,EAAE,OAAO,EAAkB;IAC9D,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,UAAU,QAAQ,EAAE;YACtB,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,sDAAsD,EAAE,kBAAkB;QACtF,SAAS,IAAM,CAAC,UAAU,QAAQ,IAAI,QAAQ,UAAU,EAAE,EAAE,UAAU,IAAI;kBAE1E,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,UAAU,IAAI;;;;;;8BAElC,8OAAC;oBAAG,WAAU;8BAA6B,UAAU,IAAI;;;;;;8BACzD,8OAAC;oBAAI,WAAU;;wBAAwB;wBAAO,UAAU,KAAK;;;;;;;gBAE5D,UAAU,QAAQ,IAAI,UAAU,aAAa,iBAC5C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCAAyB;gCAC7B,UAAU,aAAa;;;;;;;sCAElC,8OAAC;4BAAI,WAAU;sCACZ,WAAW,UAAU,aAAa;;;;;;sCAErC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO,GAAG,MAAM,AAAC,UAAU,aAAa,GAAG,KAAM,IAAI,CAAC,CAAC;gCACzD;;;;;;;;;;;;;;;;yCAKN,8OAAC;oBAAI,WAAU;8BACZ,UAAU,QAAQ,GAAG,SAAS;;;;;;;;;;;;;;;;;AAM3C", "debugId": null}}, {"offset": {"line": 1428, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/Order.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/Button'\n\nexport interface OrderData {\n  id: string\n  customerName: string\n  items: string[]\n  timeLimit: number\n  reward: number\n  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'failed'\n  difficulty: number\n}\n\ninterface OrderProps {\n  order: OrderData\n  onAccept: (orderId: string) => void\n  onDecline: (orderId: string) => void\n  onComplete?: (orderId: string) => void\n}\n\nexport function Order({ order, onAccept, onDecline, onComplete }: OrderProps) {\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const getStatusStyle = () => {\n    switch (order.status) {\n      case 'pending':\n        return 'border-yellow-300 bg-yellow-50'\n      case 'accepted':\n      case 'in_progress':\n        return 'border-blue-300 bg-blue-50'\n      case 'completed':\n        return 'border-green-300 bg-green-50'\n      case 'failed':\n        return 'border-red-300 bg-red-50'\n      default:\n        return 'border-gray-300 bg-gray-50'\n    }\n  }\n\n  const getDifficultyStars = () => {\n    return '⭐'.repeat(order.difficulty) + '☆'.repeat(5 - order.difficulty)\n  }\n\n  const getCustomerAvatar = () => {\n    const avatars = ['👩', '👨', '👵', '👴', '👧', '👦']\n    const index = order.customerName.length % avatars.length\n    return avatars[index]\n  }\n\n  return (\n    <div className={`p-4 rounded-lg border ${getStatusStyle()}`}>\n      <div className=\"flex items-center justify-between mb-2\">\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-lg\">{getCustomerAvatar()}</span>\n          <h3 className=\"font-medium text-gray-800\">{order.customerName}</h3>\n        </div>\n        <span className=\"text-sm font-semibold text-green-600\">${order.reward}</span>\n      </div>\n      \n      <div className=\"text-sm text-gray-600 mb-2\">\n        {order.items.map((item, index) => (\n          <div key={index} className=\"flex items-center space-x-1\">\n            <span>🧁</span>\n            <span>{item}</span>\n          </div>\n        ))}\n      </div>\n      \n      <div className=\"flex justify-between items-center mb-3\">\n        <div className=\"text-xs text-gray-500\">\n          ⏰ {formatTime(order.timeLimit)}\n        </div>\n        <div className=\"text-xs\" title={`Difficulty: ${order.difficulty}/5`}>\n          {getDifficultyStars()}\n        </div>\n      </div>\n\n      {order.status === 'pending' && (\n        <div className=\"flex space-x-2\">\n          <Button\n            size=\"sm\"\n            variant=\"success\"\n            onClick={() => onAccept(order.id)}\n            className=\"flex-1\"\n          >\n            ✅ Accept\n          </Button>\n          <Button \n            size=\"sm\" \n            variant=\"danger\" \n            onClick={() => onDecline(order.id)}\n            className=\"flex-1\"\n          >\n            ❌ Decline\n          </Button>\n        </div>\n      )}\n      \n      {order.status === 'accepted' && (\n        <div className=\"text-center\">\n          <div className=\"text-blue-600 text-sm font-medium mb-2\">\n            📋 Order Accepted\n          </div>\n          <Button\n            size=\"sm\"\n            variant=\"primary\"\n            onClick={() => onComplete && onComplete(order.id)}\n            className=\"w-full\"\n          >\n            🎯 Complete Order\n          </Button>\n        </div>\n      )}\n      \n      {order.status === 'in_progress' && (\n        <div className=\"text-center text-orange-600 text-sm font-medium\">\n          🔄 In Progress...\n        </div>\n      )}\n      \n      {order.status === 'completed' && (\n        <div className=\"text-center text-green-600 text-sm font-medium\">\n          ✅ Completed!\n        </div>\n      )}\n      \n      {order.status === 'failed' && (\n        <div className=\"text-center text-red-600 text-sm font-medium\">\n          ❌ Failed\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAqBO,SAAS,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAc;IAC1E,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,MAAM,iBAAiB;QACrB,OAAQ,MAAM,MAAM;YAClB,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,OAAO,IAAI,MAAM,CAAC,MAAM,UAAU,IAAI,IAAI,MAAM,CAAC,IAAI,MAAM,UAAU;IACvE;IAEA,MAAM,oBAAoB;QACxB,MAAM,UAAU;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;SAAK;QACpD,MAAM,QAAQ,MAAM,YAAY,CAAC,MAAM,GAAG,QAAQ,MAAM;QACxD,OAAO,OAAO,CAAC,MAAM;IACvB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,sBAAsB,EAAE,kBAAkB;;0BACzD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAW;;;;;;0CAC3B,8OAAC;gCAAG,WAAU;0CAA6B,MAAM,YAAY;;;;;;;;;;;;kCAE/D,8OAAC;wBAAK,WAAU;;4BAAuC;4BAAE,MAAM,MAAM;;;;;;;;;;;;;0BAGvE,8OAAC;gBAAI,WAAU;0BACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAM;;;;;;;uBAFC;;;;;;;;;;0BAOd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BAAwB;4BAClC,WAAW,MAAM,SAAS;;;;;;;kCAE/B,8OAAC;wBAAI,WAAU;wBAAU,OAAO,CAAC,YAAY,EAAE,MAAM,UAAU,CAAC,EAAE,CAAC;kCAChE;;;;;;;;;;;;YAIJ,MAAM,MAAM,KAAK,2BAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,SAAS,MAAM,EAAE;wBAChC,WAAU;kCACX;;;;;;kCAGD,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,UAAU,MAAM,EAAE;wBACjC,WAAU;kCACX;;;;;;;;;;;;YAMJ,MAAM,MAAM,KAAK,4BAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAyC;;;;;;kCAGxD,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,cAAc,WAAW,MAAM,EAAE;wBAChD,WAAU;kCACX;;;;;;;;;;;;YAMJ,MAAM,MAAM,KAAK,+BAChB,8OAAC;gBAAI,WAAU;0BAAkD;;;;;;YAKlE,MAAM,MAAM,KAAK,6BAChB,8OAAC;gBAAI,WAAU;0BAAiD;;;;;;YAKjE,MAAM,MAAM,KAAK,0BAChB,8OAAC;gBAAI,WAAU;0BAA+C;;;;;;;;;;;;AAMtE", "debugId": null}}, {"offset": {"line": 1673, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/RecipeModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Recipe, getAvailableRecipes } from '@/lib/gameLogic'\nimport { useGame } from '@/contexts/GameContext'\n\ninterface RecipeModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport function RecipeModal({ isOpen, onClose }: RecipeModalProps) {\n  const { player, inventory } = useGame()\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n  \n  if (!isOpen) return null\n\n  const availableRecipes = getAvailableRecipes(player.level)\n  const filteredRecipes = selectedCategory === 'all' \n    ? availableRecipes \n    : availableRecipes.filter(recipe => recipe.category === selectedCategory)\n\n  const canCraft = (recipe: Recipe) => {\n    return recipe.ingredients.every(ingredient => {\n      const inventoryItem = inventory.find(item => item.name === ingredient.name)\n      return inventoryItem && inventoryItem.quantity >= ingredient.quantity\n    })\n  }\n\n  const getDifficultyStars = (difficulty: number) => {\n    return '⭐'.repeat(difficulty) + '☆'.repeat(5 - difficulty)\n  }\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const categories = [\n    { id: 'all', name: 'All', icon: '🍽️' },\n    { id: 'cookies', name: 'Cookies', icon: '🍪' },\n    { id: 'cakes', name: 'Cakes', icon: '🧁' },\n    { id: 'bread', name: 'Bread', icon: '🍞' },\n    { id: 'pastries', name: 'Pastries', icon: '🥐' }\n  ]\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-2xl font-bold text-orange-800\">📖 Recipe Book</h2>\n            <Button variant=\"secondary\" onClick={onClose}>\n              ✕ Close\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Category Filter */}\n          <div className=\"flex flex-wrap gap-2 mb-6\">\n            {categories.map(category => (\n              <Button\n                key={category.id}\n                variant={selectedCategory === category.id ? 'primary' : 'secondary'}\n                size=\"sm\"\n                onClick={() => setSelectedCategory(category.id)}\n              >\n                {category.icon} {category.name}\n              </Button>\n            ))}\n          </div>\n\n          {/* Recipes Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto\">\n            {filteredRecipes.map(recipe => (\n              <div\n                key={recipe.id}\n                className={`p-4 rounded-lg border-2 ${\n                  canCraft(recipe)\n                    ? 'border-green-300 bg-green-50'\n                    : 'border-gray-300 bg-gray-50'\n                }`}\n              >\n                <div className=\"flex justify-between items-start mb-2\">\n                  <h3 className=\"font-semibold text-gray-800\">{recipe.name}</h3>\n                  <span className=\"text-sm text-green-600\">${recipe.basePrice}</span>\n                </div>\n\n                <div className=\"text-xs text-gray-500 mb-2\">\n                  {getDifficultyStars(recipe.difficulty)} • ⏱️ {formatTime(recipe.bakingTime)}\n                </div>\n\n                <div className=\"space-y-1 mb-3\">\n                  <div className=\"text-sm font-medium text-gray-700\">Ingredients:</div>\n                  {recipe.ingredients.map((ingredient, index) => {\n                    const inventoryItem = inventory.find(item => item.name === ingredient.name)\n                    const hasEnough = inventoryItem && inventoryItem.quantity >= ingredient.quantity\n                    \n                    return (\n                      <div\n                        key={index}\n                        className={`text-xs flex justify-between ${\n                          hasEnough ? 'text-green-600' : 'text-red-600'\n                        }`}\n                      >\n                        <span>{ingredient.name}</span>\n                        <span>\n                          {ingredient.quantity} \n                          {inventoryItem && (\n                            <span className=\"ml-1\">\n                              ({inventoryItem.quantity} available)\n                            </span>\n                          )}\n                        </span>\n                      </div>\n                    )\n                  })}\n                </div>\n\n                <div className=\"text-xs text-gray-500\">\n                  Unlocked at Level {recipe.unlockLevel}\n                </div>\n\n                {canCraft(recipe) && (\n                  <div className=\"mt-2\">\n                    <Button size=\"sm\" variant=\"success\" className=\"w-full\">\n                      ✅ Can Craft\n                    </Button>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n\n          {filteredRecipes.length === 0 && (\n            <div className=\"text-center py-8 text-gray-500\">\n              <div className=\"text-4xl mb-2\">📝</div>\n              <p>No recipes available in this category.</p>\n              <p className=\"text-sm\">Level up to unlock more recipes!</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS,YAAY,EAAE,MAAM,EAAE,OAAO,EAAoB;IAC/D,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACpC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,mBAAmB,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,KAAK;IACzD,MAAM,kBAAkB,qBAAqB,QACzC,mBACA,iBAAiB,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,KAAK;IAE1D,MAAM,WAAW,CAAC;QAChB,OAAO,OAAO,WAAW,CAAC,KAAK,CAAC,CAAA;YAC9B,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;YAC1E,OAAO,iBAAiB,cAAc,QAAQ,IAAI,WAAW,QAAQ;QACvE;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAO,IAAI,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI;IACjD;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM;YAAO,MAAM;QAAM;QACtC;YAAE,IAAI;YAAW,MAAM;YAAW,MAAM;QAAK;QAC7C;YAAE,IAAI;YAAS,MAAM;YAAS,MAAM;QAAK;QACzC;YAAE,IAAI;YAAS,MAAM;YAAS,MAAM;QAAK;QACzC;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAK;KAChD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,SAAS;0CAAS;;;;;;;;;;;;;;;;;8BAMlD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC,kIAAA,CAAA,SAAM;oCAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;oCACxD,MAAK;oCACL,SAAS,IAAM,oBAAoB,SAAS,EAAE;;wCAE7C,SAAS,IAAI;wCAAC;wCAAE,SAAS,IAAI;;mCALzB,SAAS,EAAE;;;;;;;;;;sCAWtB,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAA,uBACnB,8OAAC;oCAEC,WAAW,CAAC,wBAAwB,EAClC,SAAS,UACL,iCACA,8BACJ;;sDAEF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA+B,OAAO,IAAI;;;;;;8DACxD,8OAAC;oDAAK,WAAU;;wDAAyB;wDAAE,OAAO,SAAS;;;;;;;;;;;;;sDAG7D,8OAAC;4CAAI,WAAU;;gDACZ,mBAAmB,OAAO,UAAU;gDAAE;gDAAO,WAAW,OAAO,UAAU;;;;;;;sDAG5E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAoC;;;;;;gDAClD,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY;oDACnC,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;oDAC1E,MAAM,YAAY,iBAAiB,cAAc,QAAQ,IAAI,WAAW,QAAQ;oDAEhF,qBACE,8OAAC;wDAEC,WAAW,CAAC,6BAA6B,EACvC,YAAY,mBAAmB,gBAC/B;;0EAEF,8OAAC;0EAAM,WAAW,IAAI;;;;;;0EACtB,8OAAC;;oEACE,WAAW,QAAQ;oEACnB,+BACC,8OAAC;wEAAK,WAAU;;4EAAO;4EACnB,cAAc,QAAQ;4EAAC;;;;;;;;;;;;;;uDAV1B;;;;;gDAgBX;;;;;;;sDAGF,8OAAC;4CAAI,WAAU;;gDAAwB;gDAClB,OAAO,WAAW;;;;;;;wCAGtC,SAAS,yBACR,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAQ;gDAAU,WAAU;0DAAS;;;;;;;;;;;;mCAjDtD,OAAO,EAAE;;;;;;;;;;wBA0DnB,gBAAgB,MAAM,KAAK,mBAC1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrC", "debugId": null}}, {"offset": {"line": 1992, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/ShopModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { useGame } from '@/contexts/GameContext'\n\ninterface ShopModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport function ShopModal({ isOpen, onClose }: ShopModalProps) {\n  const { player, inventory, spendMoney, addIngredient } = useGame()\n  const [quantities, setQuantities] = useState<Record<string, number>>({})\n\n  if (!isOpen) return null\n\n  const handleQuantityChange = (ingredientName: string, quantity: number) => {\n    setQuantities(prev => ({\n      ...prev,\n      [ingredientName]: Math.max(0, quantity)\n    }))\n  }\n\n  const buyIngredient = (ingredientName: string, cost: number) => {\n    const quantity = quantities[ingredientName] || 1\n    const totalCost = cost * quantity\n    \n    if (spendMoney(totalCost)) {\n      addIngredient(ingredientName, quantity)\n      setQuantities(prev => ({ ...prev, [ingredientName]: 0 }))\n    }\n  }\n\n  const getTotalCost = (ingredientName: string, cost: number) => {\n    const quantity = quantities[ingredientName] || 1\n    return cost * quantity\n  }\n\n  const canAfford = (ingredientName: string, cost: number) => {\n    return player.money >= getTotalCost(ingredientName, cost)\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-2xl font-bold text-orange-800\">🛒 Ingredient Shop</h2>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"bg-green-100 px-3 py-1 rounded-full\">\n                <span className=\"text-green-800 font-medium\">${player.money}</span>\n              </div>\n              <Button variant=\"secondary\" onClick={onClose}>\n                ✕ Close\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          <div className=\"space-y-4 max-h-[60vh] overflow-y-auto\">\n            {inventory.map(ingredient => {\n              const quantity = quantities[ingredient.name] || 1\n              const totalCost = getTotalCost(ingredient.name, ingredient.cost)\n              const affordable = canAfford(ingredient.name, ingredient.cost)\n\n              return (\n                <div\n                  key={ingredient.name}\n                  className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\"\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"text-2xl\">{ingredient.icon}</div>\n                    <div>\n                      <h3 className=\"font-medium text-gray-800\">{ingredient.name}</h3>\n                      <p className=\"text-sm text-gray-600\">\n                        Current stock: {ingredient.quantity}\n                      </p>\n                      <p className=\"text-sm text-green-600\">\n                        ${ingredient.cost} each\n                      </p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"flex items-center space-x-2\">\n                      <Button\n                        size=\"sm\"\n                        variant=\"secondary\"\n                        onClick={() => handleQuantityChange(ingredient.name, quantity - 1)}\n                        disabled={quantity <= 1}\n                      >\n                        -\n                      </Button>\n                      <span className=\"w-12 text-center font-mono\">{quantity}</span>\n                      <Button\n                        size=\"sm\"\n                        variant=\"secondary\"\n                        onClick={() => handleQuantityChange(ingredient.name, quantity + 1)}\n                        disabled={!canAfford(ingredient.name, ingredient.cost) && quantity >= 1}\n                      >\n                        +\n                      </Button>\n                    </div>\n\n                    <div className=\"text-right min-w-[80px]\">\n                      <div className={`font-medium ${affordable ? 'text-green-600' : 'text-red-600'}`}>\n                        ${totalCost}\n                      </div>\n                      <Button\n                        size=\"sm\"\n                        variant={affordable ? 'success' : 'secondary'}\n                        onClick={() => buyIngredient(ingredient.name, ingredient.cost)}\n                        disabled={!affordable}\n                        className=\"mt-1\"\n                      >\n                        {affordable ? 'Buy' : 'Too Expensive'}\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n              )\n            })}\n          </div>\n\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n            <h3 className=\"font-medium text-blue-800 mb-2\">💡 Shopping Tips</h3>\n            <ul className=\"text-sm text-blue-700 space-y-1\">\n              <li>• Buy ingredients in bulk to save time</li>\n              <li>• Keep an eye on your stock levels</li>\n              <li>• Some recipes require rare ingredients</li>\n              <li>• Prices may vary based on availability</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWO,SAAS,UAAU,EAAE,MAAM,EAAE,OAAO,EAAkB;IAC3D,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAEtE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,uBAAuB,CAAC,gBAAwB;QACpD,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,eAAe,EAAE,KAAK,GAAG,CAAC,GAAG;YAChC,CAAC;IACH;IAEA,MAAM,gBAAgB,CAAC,gBAAwB;QAC7C,MAAM,WAAW,UAAU,CAAC,eAAe,IAAI;QAC/C,MAAM,YAAY,OAAO;QAEzB,IAAI,WAAW,YAAY;YACzB,cAAc,gBAAgB;YAC9B,cAAc,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,eAAe,EAAE;gBAAE,CAAC;QACzD;IACF;IAEA,MAAM,eAAe,CAAC,gBAAwB;QAC5C,MAAM,WAAW,UAAU,CAAC,eAAe,IAAI;QAC/C,OAAO,OAAO;IAChB;IAEA,MAAM,YAAY,CAAC,gBAAwB;QACzC,OAAO,OAAO,KAAK,IAAI,aAAa,gBAAgB;IACtD;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;;gDAA6B;gDAAE,OAAO,KAAK;;;;;;;;;;;;kDAE7D,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAY,SAAS;kDAAS;;;;;;;;;;;;;;;;;;;;;;;8BAOpD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAA;gCACb,MAAM,WAAW,UAAU,CAAC,WAAW,IAAI,CAAC,IAAI;gCAChD,MAAM,YAAY,aAAa,WAAW,IAAI,EAAE,WAAW,IAAI;gCAC/D,MAAM,aAAa,UAAU,WAAW,IAAI,EAAE,WAAW,IAAI;gCAE7D,qBACE,8OAAC;oCAEC,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAY,WAAW,IAAI;;;;;;8DAC1C,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA6B,WAAW,IAAI;;;;;;sEAC1D,8OAAC;4DAAE,WAAU;;gEAAwB;gEACnB,WAAW,QAAQ;;;;;;;sEAErC,8OAAC;4DAAE,WAAU;;gEAAyB;gEAClC,WAAW,IAAI;gEAAC;;;;;;;;;;;;;;;;;;;sDAKxB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,qBAAqB,WAAW,IAAI,EAAE,WAAW;4DAChE,UAAU,YAAY;sEACvB;;;;;;sEAGD,8OAAC;4DAAK,WAAU;sEAA8B;;;;;;sEAC9C,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,qBAAqB,WAAW,IAAI,EAAE,WAAW;4DAChE,UAAU,CAAC,UAAU,WAAW,IAAI,EAAE,WAAW,IAAI,KAAK,YAAY;sEACvE;;;;;;;;;;;;8DAKH,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,YAAY,EAAE,aAAa,mBAAmB,gBAAgB;;gEAAE;gEAC7E;;;;;;;sEAEJ,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS,aAAa,YAAY;4DAClC,SAAS,IAAM,cAAc,WAAW,IAAI,EAAE,WAAW,IAAI;4DAC7D,UAAU,CAAC;4DACX,WAAU;sEAET,aAAa,QAAQ;;;;;;;;;;;;;;;;;;;mCAhDvB,WAAW,IAAI;;;;;4BAsD1B;;;;;;sCAGF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}, {"offset": {"line": 2333, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/BakingModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Recipe, getAvailableRecipes, canCraftRecipe } from '@/lib/gameLogic'\nimport { useGame } from '@/contexts/GameContext'\n\ninterface BakingModalProps {\n  isOpen: boolean\n  onClose: () => void\n  equipmentId: string\n  equipmentName: string\n}\n\nexport function BakingModal({ isOpen, onClose, equipmentId, equipmentName }: BakingModalProps) {\n  const { player, inventory, updateEquipment, useIngredient } = useGame()\n  const [selectedRecipe, setSelectedRecipe] = useState<Recipe | null>(null)\n  \n  if (!isOpen) return null\n\n  const availableRecipes = getAvailableRecipes(player.level)\n  const craftableRecipes = availableRecipes.filter(recipe => \n    canCraftRecipe(recipe, inventory)\n  )\n\n  const startBaking = (recipe: Recipe) => {\n    // Check if we can craft the recipe\n    if (!canCraftRecipe(recipe, inventory)) {\n      return\n    }\n\n    // Consume ingredients\n    recipe.ingredients.forEach(ingredient => {\n      useIngredient(ingredient.name, ingredient.quantity)\n    })\n\n    // Start the equipment\n    updateEquipment(equipmentId, {\n      isActive: true,\n      timeRemaining: recipe.bakingTime,\n      currentRecipe: recipe.name\n    })\n\n    onClose()\n  }\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const getDifficultyStars = (difficulty: number) => {\n    return '⭐'.repeat(difficulty) + '☆'.repeat(5 - difficulty)\n  }\n\n  const getRecipeIcon = (category: string) => {\n    switch (category) {\n      case 'cookies': return '🍪'\n      case 'cakes': return '🧁'\n      case 'bread': return '🍞'\n      case 'pastries': return '🥐'\n      default: return '🍽️'\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-2xl font-bold text-orange-800\">\n              🔥 {equipmentName} - Select Recipe\n            </h2>\n            <Button variant=\"secondary\" onClick={onClose}>\n              ✕ Close\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          {craftableRecipes.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <div className=\"text-4xl mb-4\">😔</div>\n              <h3 className=\"text-lg font-medium text-gray-800 mb-2\">\n                No recipes available\n              </h3>\n              <p className=\"text-gray-600 mb-4\">\n                You don't have enough ingredients to craft any recipes.\n              </p>\n              <Button variant=\"primary\" onClick={onClose}>\n                Buy Ingredients\n              </Button>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[60vh] overflow-y-auto\">\n              {craftableRecipes.map(recipe => (\n                <div\n                  key={recipe.id}\n                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${\n                    selectedRecipe?.id === recipe.id\n                      ? 'border-orange-400 bg-orange-50'\n                      : 'border-gray-300 bg-gray-50 hover:border-orange-300'\n                  }`}\n                  onClick={() => setSelectedRecipe(recipe)}\n                >\n                  <div className=\"flex items-start justify-between mb-2\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-2xl\">{getRecipeIcon(recipe.category)}</span>\n                      <h3 className=\"font-semibold text-gray-800\">{recipe.name}</h3>\n                    </div>\n                    <span className=\"text-sm text-green-600\">${recipe.basePrice}</span>\n                  </div>\n\n                  <div className=\"text-xs text-gray-500 mb-2\">\n                    {getDifficultyStars(recipe.difficulty)} • ⏱️ {formatTime(recipe.bakingTime)}\n                  </div>\n\n                  <div className=\"space-y-1 mb-3\">\n                    <div className=\"text-sm font-medium text-gray-700\">Ingredients:</div>\n                    {recipe.ingredients.map((ingredient, index) => {\n                      const inventoryItem = inventory.find(item => item.name === ingredient.name)\n                      \n                      return (\n                        <div\n                          key={index}\n                          className=\"text-xs flex justify-between text-green-600\"\n                        >\n                          <span>{ingredient.name}</span>\n                          <span>\n                            {ingredient.quantity} \n                            <span className=\"ml-1\">\n                              ({inventoryItem?.quantity || 0} available)\n                            </span>\n                          </span>\n                        </div>\n                      )\n                    })}\n                  </div>\n\n                  {selectedRecipe?.id === recipe.id && (\n                    <Button\n                      variant=\"success\"\n                      size=\"sm\"\n                      className=\"w-full\"\n                      onClick={() => startBaking(recipe)}\n                    >\n                      🔥 Start Baking\n                    </Button>\n                  )}\n                </div>\n              ))}\n            </div>\n          )}\n\n          {selectedRecipe && craftableRecipes.length > 0 && (\n            <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n              <h3 className=\"font-medium text-blue-800 mb-2\">\n                📋 Baking Instructions for {selectedRecipe.name}\n              </h3>\n              <div className=\"text-sm text-blue-700 space-y-1\">\n                <p>• Baking time: {formatTime(selectedRecipe.bakingTime)}</p>\n                <p>• Difficulty: {getDifficultyStars(selectedRecipe.difficulty)}</p>\n                <p>• Expected reward: ${selectedRecipe.basePrice}</p>\n                <p>• Make sure you have all ingredients before starting!</p>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAcO,SAAS,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAoB;IAC3F,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,mBAAmB,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,KAAK;IACzD,MAAM,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,SAC/C,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;IAGzB,MAAM,cAAc,CAAC;QACnB,mCAAmC;QACnC,IAAI,CAAC,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,YAAY;YACtC;QACF;QAEA,sBAAsB;QACtB,OAAO,WAAW,CAAC,OAAO,CAAC,CAAA;YACzB,cAAc,WAAW,IAAI,EAAE,WAAW,QAAQ;QACpD;QAEA,sBAAsB;QACtB,gBAAgB,aAAa;YAC3B,UAAU;YACV,eAAe,OAAO,UAAU;YAChC,eAAe,OAAO,IAAI;QAC5B;QAEA;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAO,IAAI,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI;IACjD;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAqC;oCAC7C;oCAAc;;;;;;;0CAEpB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,SAAS;0CAAS;;;;;;;;;;;;;;;;;8BAMlD,8OAAC;oBAAI,WAAU;;wBACZ,iBAAiB,MAAM,KAAK,kBAC3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;8CAAS;;;;;;;;;;;iDAK9C,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAA,uBACpB,8OAAC;oCAEC,WAAW,CAAC,sDAAsD,EAChE,gBAAgB,OAAO,OAAO,EAAE,GAC5B,mCACA,sDACJ;oCACF,SAAS,IAAM,kBAAkB;;sDAEjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAY,cAAc,OAAO,QAAQ;;;;;;sEACzD,8OAAC;4DAAG,WAAU;sEAA+B,OAAO,IAAI;;;;;;;;;;;;8DAE1D,8OAAC;oDAAK,WAAU;;wDAAyB;wDAAE,OAAO,SAAS;;;;;;;;;;;;;sDAG7D,8OAAC;4CAAI,WAAU;;gDACZ,mBAAmB,OAAO,UAAU;gDAAE;gDAAO,WAAW,OAAO,UAAU;;;;;;;sDAG5E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAoC;;;;;;gDAClD,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY;oDACnC,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;oDAE1E,qBACE,8OAAC;wDAEC,WAAU;;0EAEV,8OAAC;0EAAM,WAAW,IAAI;;;;;;0EACtB,8OAAC;;oEACE,WAAW,QAAQ;kFACpB,8OAAC;wEAAK,WAAU;;4EAAO;4EACnB,eAAe,YAAY;4EAAE;;;;;;;;;;;;;;uDAP9B;;;;;gDAYX;;;;;;;wCAGD,gBAAgB,OAAO,OAAO,EAAE,kBAC/B,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,YAAY;sDAC5B;;;;;;;mCAhDE,OAAO,EAAE;;;;;;;;;;wBAyDrB,kBAAkB,iBAAiB,MAAM,GAAG,mBAC3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAiC;wCACjB,eAAe,IAAI;;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAE;gDAAgB,WAAW,eAAe,UAAU;;;;;;;sDACvD,8OAAC;;gDAAE;gDAAe,mBAAmB,eAAe,UAAU;;;;;;;sDAC9D,8OAAC;;gDAAE;gDAAqB,eAAe,SAAS;;;;;;;sDAChD,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB", "debugId": null}}, {"offset": {"line": 2708, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/NotificationSystem.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\nexport interface Notification {\n  id: string\n  type: 'success' | 'error' | 'warning' | 'info'\n  title: string\n  message: string\n  duration?: number\n}\n\ninterface NotificationSystemProps {\n  notifications: Notification[]\n  onRemove: (id: string) => void\n}\n\nexport function NotificationSystem({ notifications, onRemove }: NotificationSystemProps) {\n  useEffect(() => {\n    notifications.forEach(notification => {\n      if (notification.duration) {\n        const timer = setTimeout(() => {\n          onRemove(notification.id)\n        }, notification.duration)\n        \n        return () => clearTimeout(timer)\n      }\n    })\n  }, [notifications, onRemove])\n\n  const getNotificationStyle = (type: string) => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-100 border-green-400 text-green-800'\n      case 'error':\n        return 'bg-red-100 border-red-400 text-red-800'\n      case 'warning':\n        return 'bg-yellow-100 border-yellow-400 text-yellow-800'\n      case 'info':\n        return 'bg-blue-100 border-blue-400 text-blue-800'\n      default:\n        return 'bg-gray-100 border-gray-400 text-gray-800'\n    }\n  }\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'success':\n        return '✅'\n      case 'error':\n        return '❌'\n      case 'warning':\n        return '⚠️'\n      case 'info':\n        return 'ℹ️'\n      default:\n        return '📢'\n    }\n  }\n\n  if (notifications.length === 0) return null\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-sm\">\n      {notifications.map(notification => (\n        <div\n          key={notification.id}\n          className={`p-4 rounded-lg border-l-4 shadow-lg transition-all duration-300 ${getNotificationStyle(notification.type)}`}\n        >\n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex items-start space-x-2\">\n              <span className=\"text-lg\">{getNotificationIcon(notification.type)}</span>\n              <div>\n                <h4 className=\"font-medium\">{notification.title}</h4>\n                <p className=\"text-sm opacity-90\">{notification.message}</p>\n              </div>\n            </div>\n            <button\n              onClick={() => onRemove(notification.id)}\n              className=\"text-lg opacity-60 hover:opacity-100 transition-opacity\"\n            >\n              ×\n            </button>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n\n// Hook for managing notifications\nexport function useNotifications() {\n  const [notifications, setNotifications] = useState<Notification[]>([])\n\n  const addNotification = (notification: Omit<Notification, 'id'>) => {\n    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9)\n    const newNotification: Notification = {\n      ...notification,\n      id,\n      duration: notification.duration || 5000\n    }\n    setNotifications(prev => [...prev, newNotification])\n  }\n\n  const removeNotification = (id: string) => {\n    setNotifications(prev => prev.filter(n => n.id !== id))\n  }\n\n  const showSuccess = (title: string, message: string) => {\n    addNotification({ type: 'success', title, message })\n  }\n\n  const showError = (title: string, message: string) => {\n    addNotification({ type: 'error', title, message })\n  }\n\n  const showWarning = (title: string, message: string) => {\n    addNotification({ type: 'warning', title, message })\n  }\n\n  const showInfo = (title: string, message: string) => {\n    addNotification({ type: 'info', title, message })\n  }\n\n  return {\n    notifications,\n    addNotification,\n    removeNotification,\n    showSuccess,\n    showError,\n    showWarning,\n    showInfo\n  }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAiBO,SAAS,mBAAmB,EAAE,aAAa,EAAE,QAAQ,EAA2B;IACrF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc,OAAO,CAAC,CAAA;YACpB,IAAI,aAAa,QAAQ,EAAE;gBACzB,MAAM,QAAQ,WAAW;oBACvB,SAAS,aAAa,EAAE;gBAC1B,GAAG,aAAa,QAAQ;gBAExB,OAAO,IAAM,aAAa;YAC5B;QACF;IACF,GAAG;QAAC;QAAe;KAAS;IAE5B,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IAEvC,qBACE,8OAAC;QAAI,WAAU;kBACZ,cAAc,GAAG,CAAC,CAAA,6BACjB,8OAAC;gBAEC,WAAW,CAAC,gEAAgE,EAAE,qBAAqB,aAAa,IAAI,GAAG;0BAEvH,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAW,oBAAoB,aAAa,IAAI;;;;;;8CAChE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAe,aAAa,KAAK;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;sDAAsB,aAAa,OAAO;;;;;;;;;;;;;;;;;;sCAG3D,8OAAC;4BACC,SAAS,IAAM,SAAS,aAAa,EAAE;4BACvC,WAAU;sCACX;;;;;;;;;;;;eAdE,aAAa,EAAE;;;;;;;;;;AAsB9B;AAGO,SAAS;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,MAAM,kBAAkB,CAAC;QACvB,MAAM,KAAK,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QACxE,MAAM,kBAAgC;YACpC,GAAG,YAAY;YACf;YACA,UAAU,aAAa,QAAQ,IAAI;QACrC;QACA,iBAAiB,CAAA,OAAQ;mBAAI;gBAAM;aAAgB;IACrD;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACrD;IAEA,MAAM,cAAc,CAAC,OAAe;QAClC,gBAAgB;YAAE,MAAM;YAAW;YAAO;QAAQ;IACpD;IAEA,MAAM,YAAY,CAAC,OAAe;QAChC,gBAAgB;YAAE,MAAM;YAAS;YAAO;QAAQ;IAClD;IAEA,MAAM,cAAc,CAAC,OAAe;QAClC,gBAAgB;YAAE,MAAM;YAAW;YAAO;QAAQ;IACpD;IAEA,MAAM,WAAW,CAAC,OAAe;QAC/B,gBAAgB;YAAE,MAAM;YAAQ;YAAO;QAAQ;IACjD;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 2893, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/LevelUpModal.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { LevelReward } from '@/lib/progressionSystem'\n\ninterface LevelUpModalProps {\n  isOpen: boolean\n  onClose: () => void\n  newLevel: number\n  rewards: LevelReward[]\n}\n\nexport function LevelUpModal({ isOpen, onClose, newLevel, rewards }: LevelUpModalProps) {\n  if (!isOpen) return null\n\n  const getRewardIcon = (type: string) => {\n    switch (type) {\n      case 'recipe': return '📖'\n      case 'equipment': return '⚙️'\n      case 'money': return '💰'\n      case 'skill_point': return '⭐'\n      case 'achievement': return '🏆'\n      default: return '🎁'\n    }\n  }\n\n  const getRewardColor = (type: string) => {\n    switch (type) {\n      case 'recipe': return 'bg-blue-50 border-blue-300 text-blue-800'\n      case 'equipment': return 'bg-purple-50 border-purple-300 text-purple-800'\n      case 'money': return 'bg-green-50 border-green-300 text-green-800'\n      case 'skill_point': return 'bg-yellow-50 border-yellow-300 text-yellow-800'\n      case 'achievement': return 'bg-orange-50 border-orange-300 text-orange-800'\n      default: return 'bg-gray-50 border-gray-300 text-gray-800'\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-md w-full overflow-hidden\">\n        {/* Header with celebration */}\n        <div className=\"bg-gradient-to-r from-yellow-400 to-orange-500 p-6 text-center\">\n          <div className=\"text-6xl mb-2\">🎉</div>\n          <h2 className=\"text-3xl font-bold text-white mb-2\">Level Up!</h2>\n          <p className=\"text-xl text-yellow-100\">\n            You reached Level {newLevel}!\n          </p>\n        </div>\n\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">\n            🎁 Level Rewards\n          </h3>\n\n          <div className=\"space-y-3 mb-6\">\n            {rewards.map((reward, index) => (\n              <div\n                key={index}\n                className={`p-3 rounded-lg border ${getRewardColor(reward.type)}`}\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <span className=\"text-2xl\">{getRewardIcon(reward.type)}</span>\n                  <div className=\"flex-1\">\n                    <h4 className=\"font-medium\">{reward.name}</h4>\n                    <p className=\"text-sm opacity-80\">{reward.description}</p>\n                    {reward.value && (\n                      <p className=\"text-sm font-semibold\">\n                        {reward.type === 'money' ? `$${reward.value}` : `+${reward.value}`}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          <div className=\"bg-blue-50 p-4 rounded-lg mb-6\">\n            <h4 className=\"font-medium text-blue-800 mb-2\">💡 What's Next?</h4>\n            <ul className=\"text-sm text-blue-700 space-y-1\">\n              <li>• Check out new recipes in your recipe book</li>\n              <li>• Visit the shop for new equipment</li>\n              <li>• Take on more challenging orders</li>\n              <li>• Invest in skill upgrades</li>\n            </ul>\n          </div>\n\n          <Button\n            variant=\"primary\"\n            size=\"lg\"\n            className=\"w-full\"\n            onClick={onClose}\n          >\n            🚀 Continue Playing\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYO,SAAS,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAqB;IACpF,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAE,WAAU;;gCAA0B;gCAClB;gCAAS;;;;;;;;;;;;;8BAIhC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAIzD,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;oCAEC,WAAW,CAAC,sBAAsB,EAAE,eAAe,OAAO,IAAI,GAAG;8CAEjE,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAY,cAAc,OAAO,IAAI;;;;;;0DACrD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAe,OAAO,IAAI;;;;;;kEACxC,8OAAC;wDAAE,WAAU;kEAAsB,OAAO,WAAW;;;;;;oDACpD,OAAO,KAAK,kBACX,8OAAC;wDAAE,WAAU;kEACV,OAAO,IAAI,KAAK,UAAU,CAAC,CAAC,EAAE,OAAO,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE,OAAO,KAAK,EAAE;;;;;;;;;;;;;;;;;;mCAVrE;;;;;;;;;;sCAmBX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAIR,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;sCACV;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 3139, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/AchievementsModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Achievement } from '@/lib/progressionSystem'\n\ninterface AchievementsModalProps {\n  isOpen: boolean\n  onClose: () => void\n  achievements: Achievement[]\n}\n\nexport function AchievementsModal({ isOpen, onClose, achievements }: AchievementsModalProps) {\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n\n  if (!isOpen) return null\n\n  const categories = [\n    { id: 'all', name: 'All', icon: '🏆' },\n    { id: 'baking', name: 'Baking', icon: '👨‍🍳' },\n    { id: 'business', name: 'Business', icon: '💼' },\n    { id: 'efficiency', name: 'Efficiency', icon: '⚡' },\n    { id: 'collection', name: 'Collection', icon: '📚' },\n    { id: 'special', name: 'Special', icon: '⭐' }\n  ]\n\n  const filteredAchievements = selectedCategory === 'all' \n    ? achievements \n    : achievements.filter(achievement => achievement.category === selectedCategory)\n\n  const completedCount = achievements.filter(a => a.completed).length\n  const totalCount = achievements.length\n\n  const getProgressPercentage = (achievement: Achievement) => {\n    if (achievement.completed) return 100\n    if (!achievement.requirements[0].current) return 0\n    return Math.min(100, (achievement.requirements[0].current / achievement.requirements[0].target) * 100)\n  }\n\n  const getRewardIcon = (type: string) => {\n    switch (type) {\n      case 'recipe': return '📖'\n      case 'equipment': return '⚙️'\n      case 'money': return '💰'\n      case 'skill_point': return '⭐'\n      default: return '🎁'\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-orange-800\">🏆 Achievements</h2>\n              <p className=\"text-gray-600\">\n                {completedCount} of {totalCount} achievements completed\n              </p>\n            </div>\n            <Button variant=\"secondary\" onClick={onClose}>\n              ✕ Close\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Progress Bar */}\n          <div className=\"mb-6\">\n            <div className=\"flex justify-between items-center mb-2\">\n              <span className=\"text-sm font-medium text-gray-700\">Overall Progress</span>\n              <span className=\"text-sm text-gray-500\">\n                {Math.round((completedCount / totalCount) * 100)}%\n              </span>\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-3\">\n              <div \n                className=\"bg-gradient-to-r from-yellow-400 to-orange-500 h-3 rounded-full transition-all duration-500\"\n                style={{ width: `${(completedCount / totalCount) * 100}%` }}\n              ></div>\n            </div>\n          </div>\n\n          {/* Category Filter */}\n          <div className=\"flex flex-wrap gap-2 mb-6\">\n            {categories.map(category => (\n              <Button\n                key={category.id}\n                variant={selectedCategory === category.id ? 'primary' : 'secondary'}\n                size=\"sm\"\n                onClick={() => setSelectedCategory(category.id)}\n              >\n                {category.icon} {category.name}\n              </Button>\n            ))}\n          </div>\n\n          {/* Achievements Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[50vh] overflow-y-auto\">\n            {filteredAchievements.map(achievement => {\n              const progress = getProgressPercentage(achievement)\n              const isCompleted = achievement.completed\n              const isUnlocked = achievement.unlocked\n\n              return (\n                <div\n                  key={achievement.id}\n                  className={`p-4 rounded-lg border-2 ${\n                    isCompleted\n                      ? 'border-green-400 bg-green-50'\n                      : isUnlocked\n                      ? 'border-gray-300 bg-white'\n                      : 'border-gray-200 bg-gray-50 opacity-60'\n                  }`}\n                >\n                  <div className=\"flex items-start space-x-3\">\n                    <div className={`text-3xl ${isCompleted ? 'grayscale-0' : 'grayscale'}`}>\n                      {achievement.icon}\n                    </div>\n                    <div className=\"flex-1\">\n                      <h3 className={`font-semibold ${isCompleted ? 'text-green-800' : 'text-gray-800'}`}>\n                        {achievement.name}\n                        {isCompleted && <span className=\"ml-2\">✅</span>}\n                      </h3>\n                      <p className=\"text-sm text-gray-600 mb-2\">\n                        {achievement.description}\n                      </p>\n\n                      {/* Progress */}\n                      {isUnlocked && !isCompleted && (\n                        <div className=\"mb-2\">\n                          <div className=\"flex justify-between items-center mb-1\">\n                            <span className=\"text-xs text-gray-500\">Progress</span>\n                            <span className=\"text-xs text-gray-500\">\n                              {achievement.requirements[0].current || 0} / {achievement.requirements[0].target}\n                            </span>\n                          </div>\n                          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                            <div \n                              className=\"bg-blue-500 h-2 rounded-full transition-all duration-300\"\n                              style={{ width: `${progress}%` }}\n                            ></div>\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Reward */}\n                      <div className=\"flex items-center space-x-2 text-sm\">\n                        <span className=\"text-gray-500\">Reward:</span>\n                        <span className=\"text-lg\">{getRewardIcon(achievement.reward.type)}</span>\n                        <span className=\"text-gray-700\">{achievement.reward.name}</span>\n                        {achievement.reward.value && (\n                          <span className=\"text-green-600 font-medium\">\n                            {achievement.reward.type === 'money' ? `$${achievement.reward.value}` : `+${achievement.reward.value}`}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )\n            })}\n          </div>\n\n          {filteredAchievements.length === 0 && (\n            <div className=\"text-center py-8 text-gray-500\">\n              <div className=\"text-4xl mb-2\">🏆</div>\n              <p>No achievements in this category.</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYO,SAAS,kBAAkB,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAA0B;IACzF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM;YAAO,MAAM;QAAK;QACrC;YAAE,IAAI;YAAU,MAAM;YAAU,MAAM;QAAQ;QAC9C;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAK;QAC/C;YAAE,IAAI;YAAc,MAAM;YAAc,MAAM;QAAI;QAClD;YAAE,IAAI;YAAc,MAAM;YAAc,MAAM;QAAK;QACnD;YAAE,IAAI;YAAW,MAAM;YAAW,MAAM;QAAI;KAC7C;IAED,MAAM,uBAAuB,qBAAqB,QAC9C,eACA,aAAa,MAAM,CAAC,CAAA,cAAe,YAAY,QAAQ,KAAK;IAEhE,MAAM,iBAAiB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;IACnE,MAAM,aAAa,aAAa,MAAM;IAEtC,MAAM,wBAAwB,CAAC;QAC7B,IAAI,YAAY,SAAS,EAAE,OAAO;QAClC,IAAI,CAAC,YAAY,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO;QACjD,OAAO,KAAK,GAAG,CAAC,KAAK,AAAC,YAAY,YAAY,CAAC,EAAE,CAAC,OAAO,GAAG,YAAY,YAAY,CAAC,EAAE,CAAC,MAAM,GAAI;IACpG;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAE,WAAU;;4CACV;4CAAe;4CAAK;4CAAW;;;;;;;;;;;;;0CAGpC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,SAAS;0CAAS;;;;;;;;;;;;;;;;;8BAMlD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAoC;;;;;;sDACpD,8OAAC;4CAAK,WAAU;;gDACb,KAAK,KAAK,CAAC,AAAC,iBAAiB,aAAc;gDAAK;;;;;;;;;;;;;8CAGrD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,AAAC,iBAAiB,aAAc,IAAI,CAAC,CAAC;wCAAC;;;;;;;;;;;;;;;;;sCAMhE,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC,kIAAA,CAAA,SAAM;oCAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;oCACxD,MAAK;oCACL,SAAS,IAAM,oBAAoB,SAAS,EAAE;;wCAE7C,SAAS,IAAI;wCAAC;wCAAE,SAAS,IAAI;;mCALzB,SAAS,EAAE;;;;;;;;;;sCAWtB,8OAAC;4BAAI,WAAU;sCACZ,qBAAqB,GAAG,CAAC,CAAA;gCACxB,MAAM,WAAW,sBAAsB;gCACvC,MAAM,cAAc,YAAY,SAAS;gCACzC,MAAM,aAAa,YAAY,QAAQ;gCAEvC,qBACE,8OAAC;oCAEC,WAAW,CAAC,wBAAwB,EAClC,cACI,iCACA,aACA,6BACA,yCACJ;8CAEF,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,SAAS,EAAE,cAAc,gBAAgB,aAAa;0DACpE,YAAY,IAAI;;;;;;0DAEnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAW,CAAC,cAAc,EAAE,cAAc,mBAAmB,iBAAiB;;4DAC/E,YAAY,IAAI;4DAChB,6BAAe,8OAAC;gEAAK,WAAU;0EAAO;;;;;;;;;;;;kEAEzC,8OAAC;wDAAE,WAAU;kEACV,YAAY,WAAW;;;;;;oDAIzB,cAAc,CAAC,6BACd,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,8OAAC;wEAAK,WAAU;;4EACb,YAAY,YAAY,CAAC,EAAE,CAAC,OAAO,IAAI;4EAAE;4EAAI,YAAY,YAAY,CAAC,EAAE,CAAC,MAAM;;;;;;;;;;;;;0EAGpF,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,OAAO,GAAG,SAAS,CAAC,CAAC;oEAAC;;;;;;;;;;;;;;;;;kEAOvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAW,cAAc,YAAY,MAAM,CAAC,IAAI;;;;;;0EAChE,8OAAC;gEAAK,WAAU;0EAAiB,YAAY,MAAM,CAAC,IAAI;;;;;;4DACvD,YAAY,MAAM,CAAC,KAAK,kBACvB,8OAAC;gEAAK,WAAU;0EACb,YAAY,MAAM,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,EAAE,YAAY,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE,YAAY,MAAM,CAAC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;mCA/C3G,YAAY,EAAE;;;;;4BAuDzB;;;;;;wBAGD,qBAAqB,MAAM,KAAK,mBAC/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}, {"offset": {"line": 3556, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/SkillTreeModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { SkillTree } from '@/lib/progressionSystem'\n\ninterface SkillTreeModalProps {\n  isOpen: boolean\n  onClose: () => void\n  skills: SkillTree[]\n  skillPoints: number\n  playerLevel: number\n  onUpgradeSkill: (skillId: string) => void\n}\n\nexport function SkillTreeModal({ \n  isOpen, \n  onClose, \n  skills, \n  skillPoints, \n  playerLevel,\n  onUpgradeSkill \n}: SkillTreeModalProps) {\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n\n  if (!isOpen) return null\n\n  const categories = [\n    { id: 'all', name: 'All', icon: '🌟' },\n    { id: 'efficiency', name: 'Efficiency', icon: '⚡' },\n    { id: 'automation', name: 'Automation', icon: '🤖' },\n    { id: 'quality', name: 'Quality', icon: '💎' },\n    { id: 'business', name: 'Business', icon: '💼' }\n  ]\n\n  const filteredSkills = selectedCategory === 'all' \n    ? skills \n    : skills.filter(skill => skill.category === selectedCategory)\n\n  const canUpgradeSkill = (skill: SkillTree) => {\n    if (skill.level >= skill.maxLevel) return false\n    if (skillPoints < skill.cost) return false\n    if (skill.requirements.playerLevel && playerLevel < skill.requirements.playerLevel) return false\n    if (skill.requirements.skills) {\n      return skill.requirements.skills.every(requiredSkillId => {\n        const requiredSkill = skills.find(s => s.id === requiredSkillId)\n        return requiredSkill && requiredSkill.level > 0\n      })\n    }\n    return true\n  }\n\n  const getSkillStatus = (skill: SkillTree) => {\n    if (skill.level >= skill.maxLevel) return 'maxed'\n    if (!canUpgradeSkill(skill)) return 'locked'\n    return 'available'\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'maxed': return 'border-green-400 bg-green-50'\n      case 'available': return 'border-blue-400 bg-blue-50'\n      case 'locked': return 'border-gray-300 bg-gray-50'\n      default: return 'border-gray-300 bg-gray-50'\n    }\n  }\n\n  const getEffectDescription = (effect: any) => {\n    const percentage = Math.round(effect.value * 100)\n    switch (effect.type) {\n      case 'baking_speed': return `+${percentage}% baking speed`\n      case 'money_multiplier': return `+${percentage}% money earned`\n      case 'xp_multiplier': return `+${percentage}% experience gained`\n      case 'ingredient_efficiency': return `${percentage}% less ingredients used`\n      case 'automation_unlock': return 'Unlock automation features'\n      default: return `+${percentage}% bonus`\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-orange-800\">🌟 Skill Tree</h2>\n              <p className=\"text-gray-600\">\n                Available Skill Points: <span className=\"font-semibold text-blue-600\">{skillPoints}</span>\n              </p>\n            </div>\n            <Button variant=\"secondary\" onClick={onClose}>\n              ✕ Close\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Category Filter */}\n          <div className=\"flex flex-wrap gap-2 mb-6\">\n            {categories.map(category => (\n              <Button\n                key={category.id}\n                variant={selectedCategory === category.id ? 'primary' : 'secondary'}\n                size=\"sm\"\n                onClick={() => setSelectedCategory(category.id)}\n              >\n                {category.icon} {category.name}\n              </Button>\n            ))}\n          </div>\n\n          {/* Skills Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto\">\n            {filteredSkills.map(skill => {\n              const status = getSkillStatus(skill)\n              const canUpgrade = canUpgradeSkill(skill)\n\n              return (\n                <div\n                  key={skill.id}\n                  className={`p-4 rounded-lg border-2 ${getStatusColor(status)}`}\n                >\n                  <div className=\"flex items-start justify-between mb-3\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-2xl\">{skill.icon}</span>\n                      <div>\n                        <h3 className=\"font-semibold text-gray-800\">{skill.name}</h3>\n                        <p className=\"text-xs text-gray-500 uppercase tracking-wide\">\n                          {skill.category}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-sm font-medium text-gray-700\">\n                        Level {skill.level}/{skill.maxLevel}\n                      </div>\n                      {status !== 'maxed' && (\n                        <div className=\"text-xs text-blue-600\">\n                          Cost: {skill.cost} SP\n                        </div>\n                      )}\n                    </div>\n                  </div>\n\n                  <p className=\"text-sm text-gray-600 mb-3\">\n                    {skill.description}\n                  </p>\n\n                  {/* Effects */}\n                  <div className=\"mb-3\">\n                    <h4 className=\"text-xs font-medium text-gray-700 mb-1\">Effects:</h4>\n                    {skill.effects.map((effect, index) => (\n                      <div key={index} className=\"text-xs text-green-600\">\n                        • {getEffectDescription(effect)}\n                      </div>\n                    ))}\n                  </div>\n\n                  {/* Requirements */}\n                  {skill.requirements.playerLevel && playerLevel < skill.requirements.playerLevel && (\n                    <div className=\"mb-3\">\n                      <div className=\"text-xs text-red-600\">\n                        Requires Level {skill.requirements.playerLevel}\n                      </div>\n                    </div>\n                  )}\n\n                  {skill.requirements.skills && (\n                    <div className=\"mb-3\">\n                      <div className=\"text-xs text-gray-600\">\n                        Requires: {skill.requirements.skills.map(skillId => {\n                          const reqSkill = skills.find(s => s.id === skillId)\n                          return reqSkill?.name\n                        }).join(', ')}\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Progress Bar */}\n                  <div className=\"mb-3\">\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div \n                        className=\"bg-blue-500 h-2 rounded-full transition-all duration-300\"\n                        style={{ width: `${(skill.level / skill.maxLevel) * 100}%` }}\n                      ></div>\n                    </div>\n                  </div>\n\n                  {/* Upgrade Button */}\n                  {status === 'maxed' ? (\n                    <Button variant=\"success\" size=\"sm\" className=\"w-full\" disabled>\n                      ✅ Maxed\n                    </Button>\n                  ) : canUpgrade ? (\n                    <Button \n                      variant=\"primary\" \n                      size=\"sm\" \n                      className=\"w-full\"\n                      onClick={() => onUpgradeSkill(skill.id)}\n                    >\n                      ⬆️ Upgrade ({skill.cost} SP)\n                    </Button>\n                  ) : (\n                    <Button variant=\"secondary\" size=\"sm\" className=\"w-full\" disabled>\n                      🔒 Locked\n                    </Button>\n                  )}\n                </div>\n              )\n            })}\n          </div>\n\n          {filteredSkills.length === 0 && (\n            <div className=\"text-center py-8 text-gray-500\">\n              <div className=\"text-4xl mb-2\">🌟</div>\n              <p>No skills in this category.</p>\n            </div>\n          )}\n\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n            <h3 className=\"font-medium text-blue-800 mb-2\">💡 Skill Tips</h3>\n            <ul className=\"text-sm text-blue-700 space-y-1\">\n              <li>• Earn skill points by leveling up (1 point every 2 levels)</li>\n              <li>• Some skills require other skills to be unlocked first</li>\n              <li>• Focus on skills that match your playstyle</li>\n              <li>• Efficiency skills help with resource management</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAeO,SAAS,eAAe,EAC7B,MAAM,EACN,OAAO,EACP,MAAM,EACN,WAAW,EACX,WAAW,EACX,cAAc,EACM;IACpB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM;YAAO,MAAM;QAAK;QACrC;YAAE,IAAI;YAAc,MAAM;YAAc,MAAM;QAAI;QAClD;YAAE,IAAI;YAAc,MAAM;YAAc,MAAM;QAAK;QACnD;YAAE,IAAI;YAAW,MAAM;YAAW,MAAM;QAAK;QAC7C;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAK;KAChD;IAED,MAAM,iBAAiB,qBAAqB,QACxC,SACA,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;IAE9C,MAAM,kBAAkB,CAAC;QACvB,IAAI,MAAM,KAAK,IAAI,MAAM,QAAQ,EAAE,OAAO;QAC1C,IAAI,cAAc,MAAM,IAAI,EAAE,OAAO;QACrC,IAAI,MAAM,YAAY,CAAC,WAAW,IAAI,cAAc,MAAM,YAAY,CAAC,WAAW,EAAE,OAAO;QAC3F,IAAI,MAAM,YAAY,CAAC,MAAM,EAAE;YAC7B,OAAO,MAAM,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;gBACrC,MAAM,gBAAgB,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAChD,OAAO,iBAAiB,cAAc,KAAK,GAAG;YAChD;QACF;QACA,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,MAAM,KAAK,IAAI,MAAM,QAAQ,EAAE,OAAO;QAC1C,IAAI,CAAC,gBAAgB,QAAQ,OAAO;QACpC,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,aAAa,KAAK,KAAK,CAAC,OAAO,KAAK,GAAG;QAC7C,OAAQ,OAAO,IAAI;YACjB,KAAK;gBAAgB,OAAO,CAAC,CAAC,EAAE,WAAW,cAAc,CAAC;YAC1D,KAAK;gBAAoB,OAAO,CAAC,CAAC,EAAE,WAAW,cAAc,CAAC;YAC9D,KAAK;gBAAiB,OAAO,CAAC,CAAC,EAAE,WAAW,mBAAmB,CAAC;YAChE,KAAK;gBAAyB,OAAO,GAAG,WAAW,uBAAuB,CAAC;YAC3E,KAAK;gBAAqB,OAAO;YACjC;gBAAS,OAAO,CAAC,CAAC,EAAE,WAAW,OAAO,CAAC;QACzC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAE,WAAU;;4CAAgB;0DACH,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;0CAG3E,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,SAAS;0CAAS;;;;;;;;;;;;;;;;;8BAMlD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC,kIAAA,CAAA,SAAM;oCAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;oCACxD,MAAK;oCACL,SAAS,IAAM,oBAAoB,SAAS,EAAE;;wCAE7C,SAAS,IAAI;wCAAC;wCAAE,SAAS,IAAI;;mCALzB,SAAS,EAAE;;;;;;;;;;sCAWtB,8OAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAA;gCAClB,MAAM,SAAS,eAAe;gCAC9B,MAAM,aAAa,gBAAgB;gCAEnC,qBACE,8OAAC;oCAEC,WAAW,CAAC,wBAAwB,EAAE,eAAe,SAAS;;sDAE9D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAY,MAAM,IAAI;;;;;;sEACtC,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA+B,MAAM,IAAI;;;;;;8EACvD,8OAAC;oEAAE,WAAU;8EACV,MAAM,QAAQ;;;;;;;;;;;;;;;;;;8DAIrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEAAoC;gEAC1C,MAAM,KAAK;gEAAC;gEAAE,MAAM,QAAQ;;;;;;;wDAEpC,WAAW,yBACV,8OAAC;4DAAI,WAAU;;gEAAwB;gEAC9B,MAAM,IAAI;gEAAC;;;;;;;;;;;;;;;;;;;sDAM1B,8OAAC;4CAAE,WAAU;sDACV,MAAM,WAAW;;;;;;sDAIpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;gDACtD,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC1B,8OAAC;wDAAgB,WAAU;;4DAAyB;4DAC/C,qBAAqB;;uDADhB;;;;;;;;;;;wCAOb,MAAM,YAAY,CAAC,WAAW,IAAI,cAAc,MAAM,YAAY,CAAC,WAAW,kBAC7E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;oDAAuB;oDACpB,MAAM,YAAY,CAAC,WAAW;;;;;;;;;;;;wCAKnD,MAAM,YAAY,CAAC,MAAM,kBACxB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;oDAAwB;oDAC1B,MAAM,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;wDACvC,MAAM,WAAW,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;wDAC3C,OAAO,UAAU;oDACnB,GAAG,IAAI,CAAC;;;;;;;;;;;;sDAMd,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,AAAC,MAAM,KAAK,GAAG,MAAM,QAAQ,GAAI,IAAI,CAAC,CAAC;oDAAC;;;;;;;;;;;;;;;;wCAMhE,WAAW,wBACV,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;4CAAS,QAAQ;sDAAC;;;;;mDAG9D,2BACF,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,eAAe,MAAM,EAAE;;gDACvC;gDACc,MAAM,IAAI;gDAAC;;;;;;iEAG1B,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAY,MAAK;4CAAK,WAAU;4CAAS,QAAQ;sDAAC;;;;;;;mCApF/D,MAAM,EAAE;;;;;4BA0FnB;;;;;;wBAGD,eAAe,MAAM,KAAK,mBACzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;8CAAE;;;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}, {"offset": {"line": 4069, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/app/game/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useLanguage } from '@/contexts/LanguageContext'\nimport { GameProvider, useGame } from '@/contexts/GameContext'\nimport { Button } from '@/components/ui/Button'\nimport { Equipment } from '@/components/game/Equipment'\nimport { Order } from '@/components/game/Order'\nimport { RecipeModal } from '@/components/game/RecipeModal'\nimport { ShopModal } from '@/components/game/ShopModal'\nimport { BakingModal } from '@/components/game/BakingModal'\nimport { NotificationSystem, useNotifications } from '@/components/game/NotificationSystem'\nimport { LevelUpModal } from '@/components/game/LevelUpModal'\nimport { AchievementsModal } from '@/components/game/AchievementsModal'\nimport { SkillTreeModal } from '@/components/game/SkillTreeModal'\nimport { AutomationModal } from '@/components/game/AutomationModal'\nimport { EquipmentShopModal } from '@/components/game/EquipmentShopModal'\n\nfunction GameContent() {\n  const { t } = useLanguage()\n  const {\n    player,\n    equipment,\n    inventory,\n    orders,\n    achievements,\n    skills,\n    levelUpRewards,\n    showLevelUp,\n    updateEquipment,\n    acceptOrder,\n    completeOrder,\n    declineOrder,\n    generateNewOrder,\n    upgradeSkill,\n    checkAchievements,\n    dismissLevelUp\n  } = useGame()\n\n  const [showRecipeModal, setShowRecipeModal] = useState(false)\n  const [showShopModal, setShowShopModal] = useState(false)\n  const [showBakingModal, setShowBakingModal] = useState(false)\n  const [showAchievementsModal, setShowAchievementsModal] = useState(false)\n  const [showSkillTreeModal, setShowSkillTreeModal] = useState(false)\n  const [showAutomationModal, setShowAutomationModal] = useState(false)\n  const [showEquipmentShopModal, setShowEquipmentShopModal] = useState(false)\n  const [selectedEquipment, setSelectedEquipment] = useState<{id: string, name: string} | null>(null)\n\n  const { notifications, removeNotification, showSuccess, showError, showInfo } = useNotifications()\n\n  const handleEquipmentClick = (equipmentId: string, equipmentName: string) => {\n    setSelectedEquipment({ id: equipmentId, name: equipmentName })\n    setShowBakingModal(true)\n  }\n\n  const handleOrderAccept = (orderId: string) => {\n    acceptOrder(orderId)\n    showInfo('Order Accepted', 'You have accepted a new order!')\n  }\n\n  const handleOrderComplete = (orderId: string) => {\n    const order = orders.find(o => o.id === orderId)\n    if (order) {\n      completeOrder(orderId)\n      checkAchievements()\n      showSuccess('Order Completed!', `You earned $${order.reward} and gained experience!`)\n    }\n  }\n\n  const handleOrderDecline = (orderId: string) => {\n    declineOrder(orderId)\n    showInfo('Order Declined', 'Order has been removed from your queue.')\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-orange-50 to-yellow-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-orange-200 p-4\">\n        <div className=\"max-w-7xl mx-auto flex justify-between items-center\">\n          <div className=\"flex items-center space-x-6\">\n            <h1 className=\"text-2xl font-bold text-orange-800\">🥖 Bake It Out</h1>\n            <div className=\"flex items-center space-x-4 text-sm\">\n              <div className=\"bg-blue-100 px-3 py-1 rounded-full\">\n                <span className=\"text-blue-800\">Level {player.level}</span>\n              </div>\n              <div className=\"bg-green-100 px-3 py-1 rounded-full\">\n                <span className=\"text-green-800\">${player.money}</span>\n              </div>\n              <div className=\"bg-purple-100 px-3 py-1 rounded-full\">\n                <span className=\"text-purple-800\">XP: {player.experience}/{player.maxExperience}</span>\n              </div>\n              <div className=\"bg-yellow-100 px-3 py-1 rounded-full\">\n                <span className=\"text-yellow-800\">SP: {player.skillPoints}</span>\n              </div>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={() => setShowAchievementsModal(true)}\n              >\n                🏆 Achievements\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={() => setShowSkillTreeModal(true)}\n              >\n                🌟 Skills\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={() => setShowAutomationModal(true)}\n              >\n                🤖 Automation\n              </Button>\n            </div>\n          </div>\n          <Button variant=\"secondary\" onClick={() => window.location.href = '/'}>\n            🏠 Home\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto p-6 grid grid-cols-1 lg:grid-cols-4 gap-6\">\n        {/* Kitchen Area */}\n        <div className=\"lg:col-span-3 space-y-6\">\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-semibold text-orange-800 mb-4\">🏪 Kitchen</h2>\n\n            {/* Equipment Grid */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              {equipment.map((eq) => (\n                <Equipment\n                  key={eq.id}\n                  equipment={eq}\n                  onClick={handleEquipmentClick}\n                />\n              ))}\n            </div>\n          </div>\n\n          {/* Inventory */}\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-semibold text-orange-800 mb-4\">📦 Inventory</h2>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              {inventory.map((ingredient) => (\n                <div key={ingredient.name} className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                  <div className=\"text-2xl mb-1\">{ingredient.icon}</div>\n                  <div className=\"font-medium text-gray-800\">{ingredient.name}</div>\n                  <div className=\"text-sm text-gray-600\">Qty: {ingredient.quantity}</div>\n                  <div className=\"text-xs text-green-600\">${ingredient.cost} each</div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Orders Panel */}\n        <div className=\"space-y-6\">\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex justify-between items-center mb-4\">\n              <h2 className=\"text-xl font-semibold text-orange-800\">📋 Orders</h2>\n              <Button\n                size=\"sm\"\n                variant=\"primary\"\n                onClick={generateNewOrder}\n              >\n                + New Order\n              </Button>\n            </div>\n            <div className=\"space-y-4\">\n              {orders.map((order) => (\n                <Order\n                  key={order.id}\n                  order={order}\n                  onAccept={handleOrderAccept}\n                  onDecline={handleOrderDecline}\n                  onComplete={handleOrderComplete}\n                />\n              ))}\n            </div>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-semibold text-orange-800 mb-4\">⚡ Quick Actions</h2>\n            <div className=\"space-y-2\">\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                className=\"w-full\"\n                onClick={() => setShowShopModal(true)}\n              >\n                🛒 Buy Ingredients\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                className=\"w-full\"\n                onClick={() => setShowRecipeModal(true)}\n              >\n                📖 View Recipes\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                className=\"w-full\"\n                onClick={() => setShowEquipmentShopModal(true)}\n              >\n                🔧 Equipment Shop\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Modals */}\n      <RecipeModal\n        isOpen={showRecipeModal}\n        onClose={() => setShowRecipeModal(false)}\n      />\n      <ShopModal\n        isOpen={showShopModal}\n        onClose={() => setShowShopModal(false)}\n      />\n      <BakingModal\n        isOpen={showBakingModal}\n        onClose={() => setShowBakingModal(false)}\n        equipmentId={selectedEquipment?.id || ''}\n        equipmentName={selectedEquipment?.name || ''}\n      />\n      <AchievementsModal\n        isOpen={showAchievementsModal}\n        onClose={() => setShowAchievementsModal(false)}\n        achievements={achievements}\n      />\n      <SkillTreeModal\n        isOpen={showSkillTreeModal}\n        onClose={() => setShowSkillTreeModal(false)}\n        skills={skills}\n        skillPoints={player.skillPoints}\n        playerLevel={player.level}\n        onUpgradeSkill={upgradeSkill}\n      />\n      <LevelUpModal\n        isOpen={showLevelUp}\n        onClose={dismissLevelUp}\n        newLevel={player.level}\n        rewards={levelUpRewards}\n      />\n\n      {/* Notification System */}\n      <NotificationSystem\n        notifications={notifications}\n        onRemove={removeNotification}\n      />\n    </div>\n  )\n}\n\nexport default function GamePage() {\n  return (\n    <GameProvider>\n      <GameContent />\n    </GameProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;;;;AAkBA,SAAS;IACP,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IACxB,MAAM,EACJ,MAAM,EACN,SAAS,EACT,SAAS,EACT,MAAM,EACN,YAAY,EACZ,MAAM,EACN,cAAc,EACd,WAAW,EACX,eAAe,EACf,WAAW,EACX,aAAa,EACb,YAAY,EACZ,gBAAgB,EAChB,YAAY,EACZ,iBAAiB,EACjB,cAAc,EACf,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEV,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqC;IAE9F,MAAM,EAAE,aAAa,EAAE,kBAAkB,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD;IAE/F,MAAM,uBAAuB,CAAC,aAAqB;QACjD,qBAAqB;YAAE,IAAI;YAAa,MAAM;QAAc;QAC5D,mBAAmB;IACrB;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY;QACZ,SAAS,kBAAkB;IAC7B;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACxC,IAAI,OAAO;YACT,cAAc;YACd;YACA,YAAY,oBAAoB,CAAC,YAAY,EAAE,MAAM,MAAM,CAAC,uBAAuB,CAAC;QACtF;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,aAAa;QACb,SAAS,kBAAkB;IAC7B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;;oDAAgB;oDAAO,OAAO,KAAK;;;;;;;;;;;;sDAErD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;;oDAAiB;oDAAE,OAAO,KAAK;;;;;;;;;;;;sDAEjD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;;oDAAkB;oDAAK,OAAO,UAAU;oDAAC;oDAAE,OAAO,aAAa;;;;;;;;;;;;sDAEjF,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;;oDAAkB;oDAAK,OAAO,WAAW;;;;;;;;;;;;sDAE3D,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,yBAAyB;sDACzC;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,sBAAsB;sDACtC;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,uBAAuB;sDACvC;;;;;;;;;;;;;;;;;;sCAKL,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAY,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;sCAAK;;;;;;;;;;;;;;;;;0BAM3E,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6C;;;;;;kDAG3D,8OAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAC,mBACd,8OAAC,uIAAA,CAAA,YAAS;gDAER,WAAW;gDACX,SAAS;+CAFJ,GAAG,EAAE;;;;;;;;;;;;;;;;0CASlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6C;;;;;;kDAC3D,8OAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAC,2BACd,8OAAC;gDAA0B,WAAU;;kEACnC,8OAAC;wDAAI,WAAU;kEAAiB,WAAW,IAAI;;;;;;kEAC/C,8OAAC;wDAAI,WAAU;kEAA6B,WAAW,IAAI;;;;;;kEAC3D,8OAAC;wDAAI,WAAU;;4DAAwB;4DAAM,WAAW,QAAQ;;;;;;;kEAChE,8OAAC;wDAAI,WAAU;;4DAAyB;4DAAE,WAAW,IAAI;4DAAC;;;;;;;;+CAJlD,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;kCAYjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;0DACV;;;;;;;;;;;;kDAIH,8OAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC,mIAAA,CAAA,QAAK;gDAEJ,OAAO;gDACP,UAAU;gDACV,WAAW;gDACX,YAAY;+CAJP,MAAM,EAAE;;;;;;;;;;;;;;;;0CAWrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6C;;;;;;kDAC3D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,iBAAiB;0DACjC;;;;;;0DAGD,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,mBAAmB;0DACnC;;;;;;0DAGD,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,0BAA0B;0DAC1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC,yIAAA,CAAA,cAAW;gBACV,QAAQ;gBACR,SAAS,IAAM,mBAAmB;;;;;;0BAEpC,8OAAC,uIAAA,CAAA,YAAS;gBACR,QAAQ;gBACR,SAAS,IAAM,iBAAiB;;;;;;0BAElC,8OAAC,yIAAA,CAAA,cAAW;gBACV,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,aAAa,mBAAmB,MAAM;gBACtC,eAAe,mBAAmB,QAAQ;;;;;;0BAE5C,8OAAC,+IAAA,CAAA,oBAAiB;gBAChB,QAAQ;gBACR,SAAS,IAAM,yBAAyB;gBACxC,cAAc;;;;;;0BAEhB,8OAAC,4IAAA,CAAA,iBAAc;gBACb,QAAQ;gBACR,SAAS,IAAM,sBAAsB;gBACrC,QAAQ;gBACR,aAAa,OAAO,WAAW;gBAC/B,aAAa,OAAO,KAAK;gBACzB,gBAAgB;;;;;;0BAElB,8OAAC,0IAAA,CAAA,eAAY;gBACX,QAAQ;gBACR,SAAS;gBACT,UAAU,OAAO,KAAK;gBACtB,SAAS;;;;;;0BAIX,8OAAC,gJAAA,CAAA,qBAAkB;gBACjB,eAAe;gBACf,UAAU;;;;;;;;;;;;AAIlB;AAEe,SAAS;IACtB,qBACE,8OAAC,+HAAA,CAAA,eAAY;kBACX,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}