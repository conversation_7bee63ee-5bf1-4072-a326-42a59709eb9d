#!/usr/bin/env node

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🚀 Building Bake It Out Installer...\n')

// Configuration
const config = {
  platforms: process.argv.includes('--all') ? ['win', 'mac', 'linux'] : ['win'],
  skipBuild: process.argv.includes('--skip-build'),
  verbose: process.argv.includes('--verbose')
}

function log(message) {
  console.log(`[BUILD] ${message}`)
}

function error(message) {
  console.error(`[ERROR] ${message}`)
  process.exit(1)
}

function runCommand(command, description) {
  log(description)
  try {
    const options = { stdio: config.verbose ? 'inherit' : 'pipe' }
    const result = execSync(command, options)
    if (!config.verbose && result) {
      console.log(result.toString())
    }
    log(`✅ ${description} completed`)
  } catch (err) {
    error(`❌ ${description} failed: ${err.message}`)
  }
}

function checkPrerequisites() {
  log('Checking prerequisites...')
  
  // Check if Node.js is installed
  try {
    execSync('node --version', { stdio: 'pipe' })
  } catch (err) {
    error('Node.js is not installed or not in PATH')
  }
  
  // Check if npm is installed
  try {
    execSync('npm --version', { stdio: 'pipe' })
  } catch (err) {
    error('npm is not installed or not in PATH')
  }
  
  // Check if package.json exists
  if (!fs.existsSync('package.json')) {
    error('package.json not found. Please run this script from the project root.')
  }
  
  // Check if electron directory exists
  if (!fs.existsSync('electron')) {
    error('electron directory not found. Please ensure Electron setup is complete.')
  }
  
  log('✅ Prerequisites check passed')
}

function installDependencies() {
  if (fs.existsSync('node_modules')) {
    log('Dependencies already installed, skipping...')
    return
  }
  
  runCommand('npm install', 'Installing dependencies')
}

function buildNextApp() {
  if (config.skipBuild) {
    log('Skipping Next.js build (--skip-build flag)')
    return
  }
  
  // Clean previous build
  if (fs.existsSync('out')) {
    log('Cleaning previous build...')
    fs.rmSync('out', { recursive: true, force: true })
  }
  
  runCommand('npm run build', 'Building Next.js application')
  
  // Verify build output
  if (!fs.existsSync('out')) {
    error('Next.js build failed - out directory not created')
  }
  
  if (!fs.existsSync('out/index.html')) {
    error('Next.js build failed - index.html not found')
  }
  
  log('✅ Next.js build verified')
}

function buildElectronApp() {
  log('Building Electron application...')
  
  // Clean previous dist
  if (fs.existsSync('dist')) {
    log('Cleaning previous Electron build...')
    fs.rmSync('dist', { recursive: true, force: true })
  }
  
  // Build for each platform
  config.platforms.forEach(platform => {
    const platformCommands = {
      win: 'npm run dist-win',
      mac: 'npm run dist-mac',
      linux: 'npm run dist-linux'
    }
    
    const command = platformCommands[platform]
    if (command) {
      runCommand(command, `Building for ${platform}`)
    } else {
      log(`⚠️ Unknown platform: ${platform}`)
    }
  })
}

function verifyBuild() {
  log('Verifying build output...')
  
  if (!fs.existsSync('dist')) {
    error('Build verification failed - dist directory not found')
  }
  
  const distContents = fs.readdirSync('dist')
  if (distContents.length === 0) {
    error('Build verification failed - dist directory is empty')
  }
  
  log('✅ Build verification passed')
  log(`📦 Build artifacts created in dist/:`)
  
  distContents.forEach(file => {
    const filePath = path.join('dist', file)
    const stats = fs.statSync(filePath)
    const size = (stats.size / 1024 / 1024).toFixed(2)
    log(`   ${file} (${size} MB)`)
  })
}

function generateBuildInfo() {
  const buildInfo = {
    version: require('../package.json').version,
    buildDate: new Date().toISOString(),
    platforms: config.platforms,
    nodeVersion: process.version,
    electronVersion: require('../package.json').devDependencies.electron
  }
  
  fs.writeFileSync('dist/build-info.json', JSON.stringify(buildInfo, null, 2))
  log('✅ Build info generated')
}

function showUsage() {
  console.log(`
Usage: node scripts/build-installer.js [options]

Options:
  --all         Build for all platforms (Windows, macOS, Linux)
  --skip-build  Skip Next.js build step
  --verbose     Show detailed output
  --help        Show this help message

Examples:
  node scripts/build-installer.js                    # Build for Windows only
  node scripts/build-installer.js --all              # Build for all platforms
  node scripts/build-installer.js --skip-build       # Skip Next.js build
  node scripts/build-installer.js --verbose --all    # Verbose build for all platforms
`)
}

function main() {
  if (process.argv.includes('--help')) {
    showUsage()
    return
  }
  
  const startTime = Date.now()
  
  try {
    checkPrerequisites()
    installDependencies()
    buildNextApp()
    buildElectronApp()
    verifyBuild()
    generateBuildInfo()
    
    const duration = ((Date.now() - startTime) / 1000).toFixed(2)
    log(`🎉 Build completed successfully in ${duration}s`)
    log(`📦 Installers are ready in the dist/ directory`)
    
  } catch (err) {
    error(`Build failed: ${err.message}`)
  }
}

// Handle process termination
process.on('SIGINT', () => {
  log('Build interrupted by user')
  process.exit(1)
})

process.on('SIGTERM', () => {
  log('Build terminated')
  process.exit(1)
})

// Run the build
main()
