{"author": {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>", "url": "http://gajus.com"}, "ava": {"babel": {"compileAsTests": ["test/helpers/**/*"]}, "files": ["test/roarr/**/*"], "require": ["@babel/register"]}, "dependencies": {"boolean": "^3.0.1", "detect-node": "^2.0.4", "globalthis": "^1.0.1", "json-stringify-safe": "^5.0.1", "semver-compare": "^1.0.0", "sprintf-js": "^1.1.2"}, "description": "JSON logger for Node.js and browser.", "devDependencies": {"@ava/babel": "^1.0.1", "@babel/cli": "^7.11.6", "@babel/core": "^7.11.6", "@babel/node": "^7.10.5", "@babel/plugin-transform-flow-strip-types": "^7.10.4", "@babel/preset-env": "^7.11.5", "@babel/register": "^7.11.5", "ava": "^3.12.1", "babel-plugin-istanbul": "^6.0.0", "babel-plugin-transform-export-default-name": "^2.0.4", "coveralls": "^3.1.0", "domain-parent": "^1.0.0", "eslint": "^7.9.0", "eslint-config-canonical": "^24.1.1", "flow-bin": "^0.133.0", "flow-copy-source": "^2.0.9", "gitdown": "^3.1.3", "husky": "^4.3.0", "nyc": "^15.1.0", "semantic-release": "^17.1.1"}, "engines": {"node": ">=8.0"}, "husky": {"hooks": {"pre-commit": "npm run lint && npm run test && npm run build", "pre-push": "gitdown ./.README/README.md --output-file ./README.md --check"}}, "keywords": ["log", "logger", "json"], "main": "./dist/log.js", "name": "roarr", "nyc": {"include": ["src/**/*.js"], "instrument": false, "reporter": ["text-lcov"], "require": ["@babel/register"], "sourceMap": false}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "**************:gajus/roarr.git"}, "scripts": {"build": "rm -fr ./dist && NODE_ENV=production babel ./src --out-dir ./dist --copy-files --source-maps && flow-copy-source src dist", "create-readme": "gitdown ./.README/README.md --output-file ./README.md", "dev": "NODE_ENV=production babel ./src --out-dir ./dist --copy-files --source-maps --watch", "lint": "eslint ./src ./test && flow", "test": "NODE_ENV=test ava --serial --verbose"}, "version": "2.15.4"}