'use client'

import { Button } from '@/components/ui/Button'

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-100 to-yellow-100 flex items-center justify-center">
      <div className="text-center space-y-8 p-8">
        <div className="space-y-4">
          <h1 className="text-6xl font-bold text-orange-800 mb-4">
            🥖 Bake It Out
          </h1>
          <p className="text-xl text-orange-700 max-w-2xl mx-auto">
            Master the art of bakery management in this engaging multiplayer game.
            Complete orders, unlock recipes, automate your processes, and compete with friends!
          </p>
        </div>

        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-4">
              🎮 Start Playing
            </Button>
            <Button variant="secondary" size="lg" className="text-lg px-8 py-4">
              👥 Multiplayer
            </Button>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="secondary" size="md">
              🇺🇸 English
            </Button>
            <Button variant="secondary" size="md">
              🇨🇿 Čeština
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 max-w-4xl mx-auto">
          <div className="bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center">
            <div className="text-3xl mb-3">🏪</div>
            <h3 className="font-semibold text-orange-800 mb-2">Manage Your Bakery</h3>
            <p className="text-orange-700 text-sm">
              Take orders, bake delicious goods, and serve happy customers
            </p>
          </div>

          <div className="bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center">
            <div className="text-3xl mb-3">📈</div>
            <h3 className="font-semibold text-orange-800 mb-2">Level Up & Automate</h3>
            <p className="text-orange-700 text-sm">
              Unlock new recipes, buy equipment, and automate your processes
            </p>
          </div>

          <div className="bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center">
            <div className="text-3xl mb-3">👥</div>
            <h3 className="font-semibold text-orange-800 mb-2">Play Together</h3>
            <p className="text-orange-700 text-sm">
              Cooperative and competitive multiplayer modes with friends
            </p>
          </div>
        </div>

        <div className="mt-8 text-sm text-orange-600">
          <p>🚧 Game in Development - Phase 1: Project Setup Complete! 🚧</p>
        </div>
      </div>
    </div>
  );
}
