'use client'

import { Button } from '@/components/ui/Button'
import { useLanguage } from '@/contexts/LanguageContext'

export default function Home() {
  const { language, setLanguage, t } = useLanguage()

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-100 to-yellow-100 flex items-center justify-center">
      <div className="text-center space-y-8 p-8">
        <div className="space-y-4">
          <h1 className="text-6xl font-bold text-orange-800 mb-4">
            🥖 {t('game.title')}
          </h1>
          <p className="text-xl text-orange-700 max-w-2xl mx-auto">
            {t('game.subtitle')}
          </p>
        </div>

        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="text-lg px-8 py-4"
              onClick={() => window.location.href = '/game'}
            >
              {t('game.play')}
            </Button>
            <Button variant="secondary" size="lg" className="text-lg px-8 py-4">
              {t('game.multiplayer')}
            </Button>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant={language === 'en' ? 'primary' : 'secondary'}
              size="md"
              onClick={() => setLanguage('en')}
            >
              {t('game.english')}
            </Button>
            <Button
              variant={language === 'cs' ? 'primary' : 'secondary'}
              size="md"
              onClick={() => setLanguage('cs')}
            >
              {t('game.czech')}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 max-w-4xl mx-auto">
          <div className="bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center">
            <div className="text-3xl mb-3">🏪</div>
            <h3 className="font-semibold text-orange-800 mb-2">{t('features.manage.title')}</h3>
            <p className="text-orange-700 text-sm">
              {t('features.manage.description')}
            </p>
          </div>

          <div className="bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center">
            <div className="text-3xl mb-3">📈</div>
            <h3 className="font-semibold text-orange-800 mb-2">{t('features.levelup.title')}</h3>
            <p className="text-orange-700 text-sm">
              {t('features.levelup.description')}
            </p>
          </div>

          <div className="bg-white/50 backdrop-blur-sm rounded-lg p-6 text-center">
            <div className="text-3xl mb-3">👥</div>
            <h3 className="font-semibold text-orange-800 mb-2">{t('features.multiplayer.title')}</h3>
            <p className="text-orange-700 text-sm">
              {t('features.multiplayer.description')}
            </p>
          </div>
        </div>

        <div className="mt-8 text-sm text-orange-600">
          <p>{t('status.development')}</p>
        </div>
      </div>
    </div>
  );
}
