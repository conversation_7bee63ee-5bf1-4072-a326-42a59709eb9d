{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/lib/gameLogic.ts"], "sourcesContent": ["// Game logic and data structures\n\nexport interface Recipe {\n  id: string\n  name: string\n  ingredients: { name: string; quantity: number }[]\n  bakingTime: number // in seconds\n  difficulty: number // 1-5\n  unlockLevel: number\n  basePrice: number\n  category: 'cookies' | 'cakes' | 'bread' | 'pastries'\n}\n\nexport const RECIPES: Recipe[] = [\n  {\n    id: 'chocolate_chip_cookies',\n    name: 'Chocolate Chip Cookies',\n    ingredients: [\n      { name: 'Flour', quantity: 2 },\n      { name: 'Sugar', quantity: 1 },\n      { name: 'Butter', quantity: 1 },\n      { name: 'Chocolate Chips', quantity: 1 }\n    ],\n    bakingTime: 45,\n    difficulty: 1,\n    unlockLevel: 1,\n    basePrice: 25,\n    category: 'cookies'\n  },\n  {\n    id: 'vanilla_muffins',\n    name: 'Vanilla Muffins',\n    ingredients: [\n      { name: 'Flour', quantity: 2 },\n      { name: 'Sugar', quantity: 1 },\n      { name: 'Eggs', quantity: 1 },\n      { name: 'Vanilla', quantity: 1 }\n    ],\n    bakingTime: 60,\n    difficulty: 1,\n    unlockLevel: 1,\n    basePrice: 20,\n    category: 'cakes'\n  },\n  {\n    id: 'cinnamon_rolls',\n    name: 'Cinnamon Rolls',\n    ingredients: [\n      { name: 'Flour', quantity: 3 },\n      { name: 'Sugar', quantity: 2 },\n      { name: 'Butter', quantity: 2 },\n      { name: 'Eggs', quantity: 1 }\n    ],\n    bakingTime: 90,\n    difficulty: 2,\n    unlockLevel: 2,\n    basePrice: 35,\n    category: 'pastries'\n  },\n  {\n    id: 'chocolate_brownies',\n    name: 'Chocolate Brownies',\n    ingredients: [\n      { name: 'Flour', quantity: 2 },\n      { name: 'Sugar', quantity: 2 },\n      { name: 'Butter', quantity: 1 },\n      { name: 'Chocolate Chips', quantity: 2 }\n    ],\n    bakingTime: 75,\n    difficulty: 2,\n    unlockLevel: 2,\n    basePrice: 30,\n    category: 'cakes'\n  },\n  {\n    id: 'sourdough_bread',\n    name: 'Sourdough Bread',\n    ingredients: [\n      { name: 'Flour', quantity: 4 },\n      { name: 'Salt', quantity: 1 }\n    ],\n    bakingTime: 180,\n    difficulty: 3,\n    unlockLevel: 3,\n    basePrice: 45,\n    category: 'bread'\n  }\n]\n\nexport const CUSTOMER_NAMES = [\n  'Alice Johnson', 'Bob Smith', 'Carol Davis', 'David Wilson',\n  'Emma Brown', 'Frank Miller', 'Grace Taylor', 'Henry Anderson',\n  'Ivy Thomas', 'Jack Martinez', 'Kate Garcia', 'Liam Rodriguez',\n  'Mia Lopez', 'Noah Gonzalez', 'Olivia Hernandez', 'Paul Perez',\n  'Quinn Turner', 'Ruby Phillips', 'Sam Campbell', 'Tina Parker'\n]\n\nexport function generateRandomOrder(playerLevel: number): {\n  id: string\n  customerName: string\n  items: string[]\n  timeLimit: number\n  reward: number\n  status: 'pending'\n  difficulty: number\n} {\n  // Filter recipes based on player level\n  const availableRecipes = RECIPES.filter(recipe => recipe.unlockLevel <= playerLevel)\n  \n  if (availableRecipes.length === 0) {\n    // Fallback to basic recipe\n    availableRecipes.push(RECIPES[0])\n  }\n\n  // Select random recipe(s)\n  const numItems = Math.random() < 0.7 ? 1 : Math.random() < 0.9 ? 2 : 3\n  const selectedRecipes: Recipe[] = []\n  \n  for (let i = 0; i < numItems; i++) {\n    const recipe = availableRecipes[Math.floor(Math.random() * availableRecipes.length)]\n    selectedRecipes.push(recipe)\n  }\n\n  // Calculate order properties\n  const totalDifficulty = selectedRecipes.reduce((sum, recipe) => sum + recipe.difficulty, 0)\n  const avgDifficulty = Math.ceil(totalDifficulty / selectedRecipes.length)\n  const totalBasePrice = selectedRecipes.reduce((sum, recipe) => sum + recipe.basePrice, 0)\n  \n  // Add some randomness to price and time\n  const priceMultiplier = 0.8 + Math.random() * 0.4 // 0.8 to 1.2\n  const timeMultiplier = 1.5 + Math.random() * 1.0 // 1.5 to 2.5\n  \n  const reward = Math.floor(totalBasePrice * priceMultiplier)\n  const baseTime = selectedRecipes.reduce((sum, recipe) => sum + recipe.bakingTime, 0)\n  const timeLimit = Math.floor(baseTime * timeMultiplier)\n\n  return {\n    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n    customerName: CUSTOMER_NAMES[Math.floor(Math.random() * CUSTOMER_NAMES.length)],\n    items: selectedRecipes.map(recipe => recipe.name),\n    timeLimit,\n    reward,\n    status: 'pending',\n    difficulty: Math.min(5, avgDifficulty)\n  }\n}\n\nexport function calculateExperienceReward(difficulty: number, timeBonus: boolean = false): number {\n  const baseExp = difficulty * 10\n  const bonus = timeBonus ? Math.floor(baseExp * 0.5) : 0\n  return baseExp + bonus\n}\n\nexport function calculateLevelRequirement(level: number): number {\n  return level * 100 + (level - 1) * 50\n}\n\nexport function canCraftRecipe(recipe: Recipe, inventory: { name: string; quantity: number }[]): boolean {\n  return recipe.ingredients.every(ingredient => {\n    const inventoryItem = inventory.find(item => item.name === ingredient.name)\n    return inventoryItem && inventoryItem.quantity >= ingredient.quantity\n  })\n}\n\nexport function getRecipeById(id: string): Recipe | undefined {\n  return RECIPES.find(recipe => recipe.id === id)\n}\n\nexport function getAvailableRecipes(playerLevel: number): Recipe[] {\n  return RECIPES.filter(recipe => recipe.unlockLevel <= playerLevel)\n}\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;;;;;;AAa1B,MAAM,UAAoB;IAC/B;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAU,UAAU;YAAE;YAC9B;gBAAE,MAAM;gBAAmB,UAAU;YAAE;SACxC;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAQ,UAAU;YAAE;YAC5B;gBAAE,MAAM;gBAAW,UAAU;YAAE;SAChC;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAU,UAAU;YAAE;YAC9B;gBAAE,MAAM;gBAAQ,UAAU;YAAE;SAC7B;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAU,UAAU;YAAE;YAC9B;gBAAE,MAAM;gBAAmB,UAAU;YAAE;SACxC;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAQ,UAAU;YAAE;SAC7B;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;CACD;AAEM,MAAM,iBAAiB;IAC5B;IAAiB;IAAa;IAAe;IAC7C;IAAc;IAAgB;IAAgB;IAC9C;IAAc;IAAiB;IAAe;IAC9C;IAAa;IAAiB;IAAoB;IAClD;IAAgB;IAAiB;IAAgB;CAClD;AAEM,SAAS,oBAAoB,WAAmB;IASrD,uCAAuC;IACvC,MAAM,mBAAmB,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,WAAW,IAAI;IAExE,IAAI,iBAAiB,MAAM,KAAK,GAAG;QACjC,2BAA2B;QAC3B,iBAAiB,IAAI,CAAC,OAAO,CAAC,EAAE;IAClC;IAEA,0BAA0B;IAC1B,MAAM,WAAW,KAAK,MAAM,KAAK,MAAM,IAAI,KAAK,MAAM,KAAK,MAAM,IAAI;IACrE,MAAM,kBAA4B,EAAE;IAEpC,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;QACjC,MAAM,SAAS,gBAAgB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM,EAAE;QACpF,gBAAgB,IAAI,CAAC;IACvB;IAEA,6BAA6B;IAC7B,MAAM,kBAAkB,gBAAgB,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,UAAU,EAAE;IACzF,MAAM,gBAAgB,KAAK,IAAI,CAAC,kBAAkB,gBAAgB,MAAM;IACxE,MAAM,iBAAiB,gBAAgB,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,SAAS,EAAE;IAEvF,wCAAwC;IACxC,MAAM,kBAAkB,MAAM,KAAK,MAAM,KAAK,IAAI,aAAa;;IAC/D,MAAM,iBAAiB,MAAM,KAAK,MAAM,KAAK,IAAI,aAAa;;IAE9D,MAAM,SAAS,KAAK,KAAK,CAAC,iBAAiB;IAC3C,MAAM,WAAW,gBAAgB,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,UAAU,EAAE;IAClF,MAAM,YAAY,KAAK,KAAK,CAAC,WAAW;IAExC,OAAO;QACL,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QACjE,cAAc,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,eAAe,MAAM,EAAE;QAC/E,OAAO,gBAAgB,GAAG,CAAC,CAAA,SAAU,OAAO,IAAI;QAChD;QACA;QACA,QAAQ;QACR,YAAY,KAAK,GAAG,CAAC,GAAG;IAC1B;AACF;AAEO,SAAS,0BAA0B,UAAkB;QAAE,YAAA,iEAAqB;IACjF,MAAM,UAAU,aAAa;IAC7B,MAAM,QAAQ,YAAY,KAAK,KAAK,CAAC,UAAU,OAAO;IACtD,OAAO,UAAU;AACnB;AAEO,SAAS,0BAA0B,KAAa;IACrD,OAAO,QAAQ,MAAM,CAAC,QAAQ,CAAC,IAAI;AACrC;AAEO,SAAS,eAAe,MAAc,EAAE,SAA+C;IAC5F,OAAO,OAAO,WAAW,CAAC,KAAK,CAAC,CAAA;QAC9B,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;QAC1E,OAAO,iBAAiB,cAAc,QAAQ,IAAI,WAAW,QAAQ;IACvE;AACF;AAEO,SAAS,cAAc,EAAU;IACtC,OAAO,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;AAC9C;AAEO,SAAS,oBAAoB,WAAmB;IACrD,OAAO,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,WAAW,IAAI;AACxD", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/contexts/GameContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect } from 'react'\nimport { EquipmentData } from '@/components/game/Equipment'\nimport { OrderData } from '@/components/game/Order'\nimport { generateRandomOrder, calculateExperienceReward, canCraftRecipe, getRecipeById } from '@/lib/gameLogic'\n\nexport interface Player {\n  level: number\n  experience: number\n  money: number\n  maxExperience: number\n}\n\nexport interface Ingredient {\n  name: string\n  quantity: number\n  cost: number\n  icon: string\n}\n\ninterface GameContextType {\n  player: Player\n  equipment: EquipmentData[]\n  inventory: Ingredient[]\n  orders: OrderData[]\n  updatePlayer: (updates: Partial<Player>) => void\n  updateEquipment: (equipmentId: string, updates: Partial<EquipmentData>) => void\n  addExperience: (amount: number) => void\n  addMoney: (amount: number) => void\n  spendMoney: (amount: number) => boolean\n  useIngredient: (name: string, quantity: number) => boolean\n  addIngredient: (name: string, quantity: number) => void\n  acceptOrder: (orderId: string) => void\n  completeOrder: (orderId: string) => void\n  declineOrder: (orderId: string) => void\n  generateNewOrder: () => void\n}\n\nconst GameContext = createContext<GameContextType | undefined>(undefined)\n\nconst RECIPES = [\n  'Chocolate Chip Cookies',\n  'Vanilla Muffins',\n  'Cinnamon Rolls',\n  'Brownies',\n  'Croissants',\n  'Bread Loaf',\n  'Cupcakes',\n  'Apple Pie'\n]\n\nconst CUSTOMER_NAMES = [\n  'Alice Johnson', 'Bob Smith', 'Carol Davis', 'David Wilson',\n  'Emma Brown', 'Frank Miller', 'Grace Taylor', 'Henry Anderson',\n  'Ivy Thomas', 'Jack Martinez', 'Kate Garcia', 'Liam Rodriguez'\n]\n\nexport function GameProvider({ children }: { children: React.ReactNode }) {\n  const [player, setPlayer] = useState<Player>({\n    level: 1,\n    experience: 0,\n    money: 100,\n    maxExperience: 100\n  })\n\n  const [equipment, setEquipment] = useState<EquipmentData[]>([\n    { id: 'oven1', name: 'Basic Oven', type: 'oven', isActive: false, level: 1, efficiency: 1.0 },\n    { id: 'mixer1', name: 'Hand Mixer', type: 'mixer', isActive: false, level: 1, efficiency: 1.0 },\n    { id: 'counter1', name: 'Work Counter', type: 'counter', isActive: false, level: 1, efficiency: 1.0 }\n  ])\n\n  const [inventory, setInventory] = useState<Ingredient[]>([\n    { name: 'Flour', quantity: 15, cost: 5, icon: '🌾' },\n    { name: 'Sugar', quantity: 12, cost: 8, icon: '🍯' },\n    { name: 'Eggs', quantity: 10, cost: 12, icon: '🥚' },\n    { name: 'Butter', quantity: 8, cost: 15, icon: '🧈' },\n    { name: 'Chocolate Chips', quantity: 6, cost: 20, icon: '🍫' },\n    { name: 'Vanilla', quantity: 5, cost: 25, icon: '🌿' },\n    { name: 'Salt', quantity: 10, cost: 3, icon: '🧂' }\n  ])\n\n  const [orders, setOrders] = useState<OrderData[]>([\n    {\n      id: '1',\n      customerName: 'Alice Johnson',\n      items: ['Chocolate Chip Cookies'],\n      timeLimit: 300,\n      reward: 25,\n      status: 'pending',\n      difficulty: 1\n    }\n  ])\n\n  // Equipment timer effect\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setEquipment(prev => prev.map(eq => {\n        if (eq.isActive && eq.timeRemaining && eq.timeRemaining > 0) {\n          return { ...eq, timeRemaining: eq.timeRemaining - 1 }\n        } else if (eq.isActive && eq.timeRemaining === 0) {\n          // Baking completed - could add notification here\n          return { ...eq, isActive: false, timeRemaining: undefined, currentRecipe: undefined }\n        }\n        return eq\n      }))\n    }, 1000)\n\n    return () => clearInterval(interval)\n  }, [])\n\n  // Order timer effect\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setOrders(prev => prev.map(order => {\n        if ((order.status === 'accepted' || order.status === 'in_progress') && order.timeLimit > 0) {\n          const newTimeLimit = order.timeLimit - 1\n          if (newTimeLimit === 0) {\n            return { ...order, status: 'failed', timeLimit: 0 }\n          }\n          return { ...order, timeLimit: newTimeLimit }\n        }\n        return order\n      }))\n    }, 1000)\n\n    return () => clearInterval(interval)\n  }, [])\n\n  const updatePlayer = (updates: Partial<Player>) => {\n    setPlayer(prev => ({ ...prev, ...updates }))\n  }\n\n  const updateEquipment = (equipmentId: string, updates: Partial<EquipmentData>) => {\n    setEquipment(prev => prev.map(eq => \n      eq.id === equipmentId ? { ...eq, ...updates } : eq\n    ))\n  }\n\n  const addExperience = (amount: number) => {\n    setPlayer(prev => {\n      const newExp = prev.experience + amount\n      const newLevel = Math.floor(newExp / prev.maxExperience) + 1\n      const leveledUp = newLevel > prev.level\n      \n      return {\n        ...prev,\n        experience: newExp,\n        level: Math.max(prev.level, newLevel),\n        maxExperience: leveledUp ? newLevel * 100 : prev.maxExperience\n      }\n    })\n  }\n\n  const addMoney = (amount: number) => {\n    setPlayer(prev => ({ ...prev, money: prev.money + amount }))\n  }\n\n  const spendMoney = (amount: number): boolean => {\n    if (player.money >= amount) {\n      setPlayer(prev => ({ ...prev, money: prev.money - amount }))\n      return true\n    }\n    return false\n  }\n\n  const useIngredient = (name: string, quantity: number): boolean => {\n    const ingredient = inventory.find(ing => ing.name === name)\n    if (ingredient && ingredient.quantity >= quantity) {\n      setInventory(prev => prev.map(ing => \n        ing.name === name \n          ? { ...ing, quantity: ing.quantity - quantity }\n          : ing\n      ))\n      return true\n    }\n    return false\n  }\n\n  const addIngredient = (name: string, quantity: number) => {\n    setInventory(prev => prev.map(ing => \n      ing.name === name \n        ? { ...ing, quantity: ing.quantity + quantity }\n        : ing\n    ))\n  }\n\n  const acceptOrder = (orderId: string) => {\n    setOrders(prev => prev.map(order => \n      order.id === orderId \n        ? { ...order, status: 'accepted' }\n        : order\n    ))\n  }\n\n  const completeOrder = (orderId: string) => {\n    const order = orders.find(o => o.id === orderId)\n    if (order) {\n      // Check if player has required ingredients\n      const canComplete = order.items.every(itemName => {\n        const recipe = getRecipeById(itemName.toLowerCase().replace(/\\s+/g, '_'))\n        return recipe ? canCraftRecipe(recipe, inventory) : false\n      })\n\n      if (canComplete) {\n        // Consume ingredients\n        order.items.forEach(itemName => {\n          const recipe = getRecipeById(itemName.toLowerCase().replace(/\\s+/g, '_'))\n          if (recipe) {\n            recipe.ingredients.forEach(ingredient => {\n              useIngredient(ingredient.name, ingredient.quantity)\n            })\n          }\n        })\n\n        // Complete order\n        setOrders(prev => prev.map(o =>\n          o.id === orderId\n            ? { ...o, status: 'completed' }\n            : o\n        ))\n\n        // Calculate rewards\n        const timeBonus = order.timeLimit > 60 // Bonus if completed with time to spare\n        const expReward = calculateExperienceReward(order.difficulty, timeBonus)\n\n        addMoney(order.reward)\n        addExperience(expReward)\n      }\n    }\n  }\n\n  const declineOrder = (orderId: string) => {\n    setOrders(prev => prev.filter(order => order.id !== orderId))\n  }\n\n  const generateNewOrder = () => {\n    const newOrder = generateRandomOrder(player.level)\n    setOrders(prev => [...prev, newOrder])\n  }\n\n  return (\n    <GameContext.Provider value={{\n      player,\n      equipment,\n      inventory,\n      orders,\n      updatePlayer,\n      updateEquipment,\n      addExperience,\n      addMoney,\n      spendMoney,\n      useIngredient,\n      addIngredient,\n      acceptOrder,\n      completeOrder,\n      declineOrder,\n      generateNewOrder\n    }}>\n      {children}\n    </GameContext.Provider>\n  )\n}\n\nexport function useGame() {\n  const context = useContext(GameContext)\n  if (context === undefined) {\n    throw new Error('useGame must be used within a GameProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAGA;;;AALA;;;AAuCA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAE/D,MAAM,UAAU;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,iBAAiB;IACrB;IAAiB;IAAa;IAAe;IAC7C;IAAc;IAAgB;IAAgB;IAC9C;IAAc;IAAiB;IAAe;CAC/C;AAEM,SAAS,aAAa,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC3B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;QAC3C,OAAO;QACP,YAAY;QACZ,OAAO;QACP,eAAe;IACjB;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;QAC1D;YAAE,IAAI;YAAS,MAAM;YAAc,MAAM;YAAQ,UAAU;YAAO,OAAO;YAAG,YAAY;QAAI;QAC5F;YAAE,IAAI;YAAU,MAAM;YAAc,MAAM;YAAS,UAAU;YAAO,OAAO;YAAG,YAAY;QAAI;QAC9F;YAAE,IAAI;YAAY,MAAM;YAAgB,MAAM;YAAW,UAAU;YAAO,OAAO;YAAG,YAAY;QAAI;KACrG;IAED,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QACvD;YAAE,MAAM;YAAS,UAAU;YAAI,MAAM;YAAG,MAAM;QAAK;QACnD;YAAE,MAAM;YAAS,UAAU;YAAI,MAAM;YAAG,MAAM;QAAK;QACnD;YAAE,MAAM;YAAQ,UAAU;YAAI,MAAM;YAAI,MAAM;QAAK;QACnD;YAAE,MAAM;YAAU,UAAU;YAAG,MAAM;YAAI,MAAM;QAAK;QACpD;YAAE,MAAM;YAAmB,UAAU;YAAG,MAAM;YAAI,MAAM;QAAK;QAC7D;YAAE,MAAM;YAAW,UAAU;YAAG,MAAM;YAAI,MAAM;QAAK;QACrD;YAAE,MAAM;YAAQ,UAAU;YAAI,MAAM;YAAG,MAAM;QAAK;KACnD;IAED,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAChD;YACE,IAAI;YACJ,cAAc;YACd,OAAO;gBAAC;aAAyB;YACjC,WAAW;YACX,QAAQ;YACR,QAAQ;YACR,YAAY;QACd;KACD;IAED,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,WAAW;mDAAY;oBAC3B;2DAAa,CAAA,OAAQ,KAAK,GAAG;mEAAC,CAAA;oCAC5B,IAAI,GAAG,QAAQ,IAAI,GAAG,aAAa,IAAI,GAAG,aAAa,GAAG,GAAG;wCAC3D,OAAO;4CAAE,GAAG,EAAE;4CAAE,eAAe,GAAG,aAAa,GAAG;wCAAE;oCACtD,OAAO,IAAI,GAAG,QAAQ,IAAI,GAAG,aAAa,KAAK,GAAG;wCAChD,iDAAiD;wCACjD,OAAO;4CAAE,GAAG,EAAE;4CAAE,UAAU;4CAAO,eAAe;4CAAW,eAAe;wCAAU;oCACtF;oCACA,OAAO;gCACT;;;gBACF;kDAAG;YAEH;0CAAO,IAAM,cAAc;;QAC7B;iCAAG,EAAE;IAEL,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,WAAW;mDAAY;oBAC3B;2DAAU,CAAA,OAAQ,KAAK,GAAG;mEAAC,CAAA;oCACzB,IAAI,CAAC,MAAM,MAAM,KAAK,cAAc,MAAM,MAAM,KAAK,aAAa,KAAK,MAAM,SAAS,GAAG,GAAG;wCAC1F,MAAM,eAAe,MAAM,SAAS,GAAG;wCACvC,IAAI,iBAAiB,GAAG;4CACtB,OAAO;gDAAE,GAAG,KAAK;gDAAE,QAAQ;gDAAU,WAAW;4CAAE;wCACpD;wCACA,OAAO;4CAAE,GAAG,KAAK;4CAAE,WAAW;wCAAa;oCAC7C;oCACA,OAAO;gCACT;;;gBACF;kDAAG;YAEH;0CAAO,IAAM,cAAc;;QAC7B;iCAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC,CAAC;IAC5C;IAEA,MAAM,kBAAkB,CAAC,aAAqB;QAC5C,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,KAC5B,GAAG,EAAE,KAAK,cAAc;oBAAE,GAAG,EAAE;oBAAE,GAAG,OAAO;gBAAC,IAAI;IAEpD;IAEA,MAAM,gBAAgB,CAAC;QACrB,UAAU,CAAA;YACR,MAAM,SAAS,KAAK,UAAU,GAAG;YACjC,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS,KAAK,aAAa,IAAI;YAC3D,MAAM,YAAY,WAAW,KAAK,KAAK;YAEvC,OAAO;gBACL,GAAG,IAAI;gBACP,YAAY;gBACZ,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE;gBAC5B,eAAe,YAAY,WAAW,MAAM,KAAK,aAAa;YAChE;QACF;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,OAAO,KAAK,KAAK,GAAG;YAAO,CAAC;IAC5D;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,OAAO,KAAK,IAAI,QAAQ;YAC1B,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO,KAAK,KAAK,GAAG;gBAAO,CAAC;YAC1D,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC,MAAc;QACnC,MAAM,aAAa,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QACtD,IAAI,cAAc,WAAW,QAAQ,IAAI,UAAU;YACjD,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MAC5B,IAAI,IAAI,KAAK,OACT;wBAAE,GAAG,GAAG;wBAAE,UAAU,IAAI,QAAQ,GAAG;oBAAS,IAC5C;YAEN,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC,MAAc;QACnC,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MAC5B,IAAI,IAAI,KAAK,OACT;oBAAE,GAAG,GAAG;oBAAE,UAAU,IAAI,QAAQ,GAAG;gBAAS,IAC5C;IAER;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,QACzB,MAAM,EAAE,KAAK,UACT;oBAAE,GAAG,KAAK;oBAAE,QAAQ;gBAAW,IAC/B;IAER;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACxC,IAAI,OAAO;YACT,2CAA2C;YAC3C,MAAM,cAAc,MAAM,KAAK,CAAC,KAAK,CAAC,CAAA;gBACpC,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,QAAQ;gBACpE,OAAO,SAAS,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,aAAa;YACtD;YAEA,IAAI,aAAa;gBACf,sBAAsB;gBACtB,MAAM,KAAK,CAAC,OAAO,CAAC,CAAA;oBAClB,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,QAAQ;oBACpE,IAAI,QAAQ;;wBACV,OAAO,WAAW,CAAC,OAAO,IAAC,CAAA;;4BACzB,cAAc,WAAW,IAAI,EAAE,WAAW,QAAQ;wBACpD;;gCADE;;;oBAEJ;gBACF;gBAEA,iBAAiB;gBACjB,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACzB,EAAE,EAAE,KAAK,UACL;4BAAE,GAAG,CAAC;4BAAE,QAAQ;wBAAY,IAC5B;gBAGN,oBAAoB;gBACpB,MAAM,YAAY,MAAM,SAAS,GAAG,GAAG,wCAAwC;;gBAC/E,MAAM,YAAY,CAAA,GAAA,0HAAA,CAAA,4BAAyB,AAAD,EAAE,MAAM,UAAU,EAAE;gBAE9D,SAAS,MAAM,MAAM;gBACrB,cAAc;YAChB;QACF;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,mBAAmB;QACvB,MAAM,WAAW,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,KAAK;QACjD,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAS;IACvC;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;GA5MgB;KAAA;AA8MT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 589, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'danger' | 'success'\n  size?: 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nexport const Button: React.FC<ButtonProps> = ({\n  variant = 'primary',\n  size = 'md',\n  className = '',\n  children,\n  ...props\n}) => {\n  const baseClasses = 'font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2'\n\n  const variantClasses = {\n    primary: 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500',\n    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500',\n    danger: 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500',\n    success: 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500',\n  }\n\n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-base',\n    lg: 'px-6 py-3 text-lg',\n  }\n\n  const combinedClasses = [\n    baseClasses,\n    variantClasses[variant],\n    sizeClasses[size],\n    className\n  ].join(' ')\n\n  return (\n    <button\n      className={combinedClasses}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAQO,MAAM,SAAgC;QAAC,EAC5C,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB;QACA,cAAc,CAAC,QAAQ;QACvB,WAAW,CAAC,KAAK;QACjB;KACD,CAAC,IAAI,CAAC;IAEP,qBACE,6LAAC;QACC,WAAW;QACV,GAAG,KAAK;kBAER;;;;;;AAGP;KArCa", "debugId": null}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/Equipment.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\n\nexport interface EquipmentData {\n  id: string\n  name: string\n  type: 'oven' | 'mixer' | 'counter'\n  isActive: boolean\n  timeRemaining?: number\n  currentRecipe?: string\n  level: number\n  efficiency: number\n}\n\ninterface EquipmentProps {\n  equipment: EquipmentData\n  onClick: (equipmentId: string, equipmentName: string) => void\n}\n\nexport function Equipment({ equipment, onClick }: EquipmentProps) {\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const getEquipmentIcon = (type: string) => {\n    switch (type) {\n      case 'oven': return '🔥'\n      case 'mixer': return '🥄'\n      case 'counter': return '🍽️'\n      default: return '⚙️'\n    }\n  }\n\n  const getStatusColor = () => {\n    if (equipment.isActive) {\n      return 'border-green-400 bg-green-50'\n    }\n    return 'border-gray-200 bg-gray-50 hover:border-orange-300 hover:bg-orange-50'\n  }\n\n  return (\n    <div\n      className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${getStatusColor()}`}\n      onClick={() => !equipment.isActive && onClick(equipment.id, equipment.name)}\n    >\n      <div className=\"text-center\">\n        <div className=\"text-3xl mb-2\">\n          {getEquipmentIcon(equipment.type)}\n        </div>\n        <h3 className=\"font-medium text-gray-800\">{equipment.name}</h3>\n        <div className=\"text-xs text-gray-500\">Level {equipment.level}</div>\n        \n        {equipment.isActive && equipment.timeRemaining ? (\n          <div className=\"mt-2\">\n            <div className=\"text-sm text-green-600\">\n              Making: {equipment.currentRecipe}\n            </div>\n            <div className=\"text-lg font-mono text-green-700\">\n              {formatTime(equipment.timeRemaining)}\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-2 mt-2\">\n              <div \n                className=\"bg-green-500 h-2 rounded-full transition-all duration-1000\"\n                style={{ \n                  width: `${100 - (equipment.timeRemaining / 60) * 100}%` \n                }}\n              ></div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"text-sm text-gray-500 mt-2\">\n            {equipment.isActive ? 'Busy' : 'Click to use'}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAoBO,SAAS,UAAU,KAAsC;QAAtC,EAAE,SAAS,EAAE,OAAO,EAAkB,GAAtC;IACxB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,AAAC,GAAU,OAAR,MAAK,KAAoC,OAAjC,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAChD;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,UAAU,QAAQ,EAAE;YACtB,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,WAAW,AAAC,yDAAyE,OAAjB;QACpE,SAAS,IAAM,CAAC,UAAU,QAAQ,IAAI,QAAQ,UAAU,EAAE,EAAE,UAAU,IAAI;kBAE1E,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACZ,iBAAiB,UAAU,IAAI;;;;;;8BAElC,6LAAC;oBAAG,WAAU;8BAA6B,UAAU,IAAI;;;;;;8BACzD,6LAAC;oBAAI,WAAU;;wBAAwB;wBAAO,UAAU,KAAK;;;;;;;gBAE5D,UAAU,QAAQ,IAAI,UAAU,aAAa,iBAC5C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCAAyB;gCAC7B,UAAU,aAAa;;;;;;;sCAElC,6LAAC;4BAAI,WAAU;sCACZ,WAAW,UAAU,aAAa;;;;;;sCAErC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO,AAAC,GAA6C,OAA3C,MAAM,AAAC,UAAU,aAAa,GAAG,KAAM,KAAI;gCACvD;;;;;;;;;;;;;;;;yCAKN,6LAAC;oBAAI,WAAU;8BACZ,UAAU,QAAQ,GAAG,SAAS;;;;;;;;;;;;;;;;;AAM3C;KA5DgB", "debugId": null}}, {"offset": {"line": 776, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/Order.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/Button'\n\nexport interface OrderData {\n  id: string\n  customerName: string\n  items: string[]\n  timeLimit: number\n  reward: number\n  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'failed'\n  difficulty: number\n}\n\ninterface OrderProps {\n  order: OrderData\n  onAccept: (orderId: string) => void\n  onDecline: (orderId: string) => void\n  onComplete?: (orderId: string) => void\n}\n\nexport function Order({ order, onAccept, onDecline, onComplete }: OrderProps) {\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const getStatusStyle = () => {\n    switch (order.status) {\n      case 'pending':\n        return 'border-yellow-300 bg-yellow-50'\n      case 'accepted':\n      case 'in_progress':\n        return 'border-blue-300 bg-blue-50'\n      case 'completed':\n        return 'border-green-300 bg-green-50'\n      case 'failed':\n        return 'border-red-300 bg-red-50'\n      default:\n        return 'border-gray-300 bg-gray-50'\n    }\n  }\n\n  const getDifficultyStars = () => {\n    return '⭐'.repeat(order.difficulty) + '☆'.repeat(5 - order.difficulty)\n  }\n\n  const getCustomerAvatar = () => {\n    const avatars = ['👩', '👨', '👵', '👴', '👧', '👦']\n    const index = order.customerName.length % avatars.length\n    return avatars[index]\n  }\n\n  return (\n    <div className={`p-4 rounded-lg border ${getStatusStyle()}`}>\n      <div className=\"flex items-center justify-between mb-2\">\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-lg\">{getCustomerAvatar()}</span>\n          <h3 className=\"font-medium text-gray-800\">{order.customerName}</h3>\n        </div>\n        <span className=\"text-sm font-semibold text-green-600\">${order.reward}</span>\n      </div>\n      \n      <div className=\"text-sm text-gray-600 mb-2\">\n        {order.items.map((item, index) => (\n          <div key={index} className=\"flex items-center space-x-1\">\n            <span>🧁</span>\n            <span>{item}</span>\n          </div>\n        ))}\n      </div>\n      \n      <div className=\"flex justify-between items-center mb-3\">\n        <div className=\"text-xs text-gray-500\">\n          ⏰ {formatTime(order.timeLimit)}\n        </div>\n        <div className=\"text-xs\" title={`Difficulty: ${order.difficulty}/5`}>\n          {getDifficultyStars()}\n        </div>\n      </div>\n\n      {order.status === 'pending' && (\n        <div className=\"flex space-x-2\">\n          <Button\n            size=\"sm\"\n            variant=\"success\"\n            onClick={() => onAccept(order.id)}\n            className=\"flex-1\"\n          >\n            ✅ Accept\n          </Button>\n          <Button \n            size=\"sm\" \n            variant=\"danger\" \n            onClick={() => onDecline(order.id)}\n            className=\"flex-1\"\n          >\n            ❌ Decline\n          </Button>\n        </div>\n      )}\n      \n      {order.status === 'accepted' && (\n        <div className=\"text-center\">\n          <div className=\"text-blue-600 text-sm font-medium mb-2\">\n            📋 Order Accepted\n          </div>\n          <Button\n            size=\"sm\"\n            variant=\"primary\"\n            onClick={() => onComplete && onComplete(order.id)}\n            className=\"w-full\"\n          >\n            🎯 Complete Order\n          </Button>\n        </div>\n      )}\n      \n      {order.status === 'in_progress' && (\n        <div className=\"text-center text-orange-600 text-sm font-medium\">\n          🔄 In Progress...\n        </div>\n      )}\n      \n      {order.status === 'completed' && (\n        <div className=\"text-center text-green-600 text-sm font-medium\">\n          ✅ Completed!\n        </div>\n      )}\n      \n      {order.status === 'failed' && (\n        <div className=\"text-center text-red-600 text-sm font-medium\">\n          ❌ Failed\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAqBO,SAAS,MAAM,KAAsD;QAAtD,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAc,GAAtD;IACpB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,AAAC,GAAU,OAAR,MAAK,KAAoC,OAAjC,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAChD;IAEA,MAAM,iBAAiB;QACrB,OAAQ,MAAM,MAAM;YAClB,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,OAAO,IAAI,MAAM,CAAC,MAAM,UAAU,IAAI,IAAI,MAAM,CAAC,IAAI,MAAM,UAAU;IACvE;IAEA,MAAM,oBAAoB;QACxB,MAAM,UAAU;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;SAAK;QACpD,MAAM,QAAQ,MAAM,YAAY,CAAC,MAAM,GAAG,QAAQ,MAAM;QACxD,OAAO,OAAO,CAAC,MAAM;IACvB;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,yBAAyC,OAAjB;;0BACvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAW;;;;;;0CAC3B,6LAAC;gCAAG,WAAU;0CAA6B,MAAM,YAAY;;;;;;;;;;;;kCAE/D,6LAAC;wBAAK,WAAU;;4BAAuC;4BAAE,MAAM,MAAM;;;;;;;;;;;;;0BAGvE,6LAAC;gBAAI,WAAU;0BACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC;wBAAgB,WAAU;;0CACzB,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAM;;;;;;;uBAFC;;;;;;;;;;0BAOd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BAAwB;4BAClC,WAAW,MAAM,SAAS;;;;;;;kCAE/B,6LAAC;wBAAI,WAAU;wBAAU,OAAO,AAAC,eAA+B,OAAjB,MAAM,UAAU,EAAC;kCAC7D;;;;;;;;;;;;YAIJ,MAAM,MAAM,KAAK,2BAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,SAAS,MAAM,EAAE;wBAChC,WAAU;kCACX;;;;;;kCAGD,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,UAAU,MAAM,EAAE;wBACjC,WAAU;kCACX;;;;;;;;;;;;YAMJ,MAAM,MAAM,KAAK,4BAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAyC;;;;;;kCAGxD,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,cAAc,WAAW,MAAM,EAAE;wBAChD,WAAU;kCACX;;;;;;;;;;;;YAMJ,MAAM,MAAM,KAAK,+BAChB,6LAAC;gBAAI,WAAU;0BAAkD;;;;;;YAKlE,MAAM,MAAM,KAAK,6BAChB,6LAAC;gBAAI,WAAU;0BAAiD;;;;;;YAKjE,MAAM,MAAM,KAAK,0BAChB,6LAAC;gBAAI,WAAU;0BAA+C;;;;;;;;;;;;AAMtE;KArHgB", "debugId": null}}, {"offset": {"line": 1030, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/RecipeModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Recipe, getAvailableRecipes } from '@/lib/gameLogic'\nimport { useGame } from '@/contexts/GameContext'\n\ninterface RecipeModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport function RecipeModal({ isOpen, onClose }: RecipeModalProps) {\n  const { player, inventory } = useGame()\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n  \n  if (!isOpen) return null\n\n  const availableRecipes = getAvailableRecipes(player.level)\n  const filteredRecipes = selectedCategory === 'all' \n    ? availableRecipes \n    : availableRecipes.filter(recipe => recipe.category === selectedCategory)\n\n  const canCraft = (recipe: Recipe) => {\n    return recipe.ingredients.every(ingredient => {\n      const inventoryItem = inventory.find(item => item.name === ingredient.name)\n      return inventoryItem && inventoryItem.quantity >= ingredient.quantity\n    })\n  }\n\n  const getDifficultyStars = (difficulty: number) => {\n    return '⭐'.repeat(difficulty) + '☆'.repeat(5 - difficulty)\n  }\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const categories = [\n    { id: 'all', name: 'All', icon: '🍽️' },\n    { id: 'cookies', name: 'Cookies', icon: '🍪' },\n    { id: 'cakes', name: 'Cakes', icon: '🧁' },\n    { id: 'bread', name: 'Bread', icon: '🍞' },\n    { id: 'pastries', name: 'Pastries', icon: '🥐' }\n  ]\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-2xl font-bold text-orange-800\">📖 Recipe Book</h2>\n            <Button variant=\"secondary\" onClick={onClose}>\n              ✕ Close\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Category Filter */}\n          <div className=\"flex flex-wrap gap-2 mb-6\">\n            {categories.map(category => (\n              <Button\n                key={category.id}\n                variant={selectedCategory === category.id ? 'primary' : 'secondary'}\n                size=\"sm\"\n                onClick={() => setSelectedCategory(category.id)}\n              >\n                {category.icon} {category.name}\n              </Button>\n            ))}\n          </div>\n\n          {/* Recipes Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto\">\n            {filteredRecipes.map(recipe => (\n              <div\n                key={recipe.id}\n                className={`p-4 rounded-lg border-2 ${\n                  canCraft(recipe)\n                    ? 'border-green-300 bg-green-50'\n                    : 'border-gray-300 bg-gray-50'\n                }`}\n              >\n                <div className=\"flex justify-between items-start mb-2\">\n                  <h3 className=\"font-semibold text-gray-800\">{recipe.name}</h3>\n                  <span className=\"text-sm text-green-600\">${recipe.basePrice}</span>\n                </div>\n\n                <div className=\"text-xs text-gray-500 mb-2\">\n                  {getDifficultyStars(recipe.difficulty)} • ⏱️ {formatTime(recipe.bakingTime)}\n                </div>\n\n                <div className=\"space-y-1 mb-3\">\n                  <div className=\"text-sm font-medium text-gray-700\">Ingredients:</div>\n                  {recipe.ingredients.map((ingredient, index) => {\n                    const inventoryItem = inventory.find(item => item.name === ingredient.name)\n                    const hasEnough = inventoryItem && inventoryItem.quantity >= ingredient.quantity\n                    \n                    return (\n                      <div\n                        key={index}\n                        className={`text-xs flex justify-between ${\n                          hasEnough ? 'text-green-600' : 'text-red-600'\n                        }`}\n                      >\n                        <span>{ingredient.name}</span>\n                        <span>\n                          {ingredient.quantity} \n                          {inventoryItem && (\n                            <span className=\"ml-1\">\n                              ({inventoryItem.quantity} available)\n                            </span>\n                          )}\n                        </span>\n                      </div>\n                    )\n                  })}\n                </div>\n\n                <div className=\"text-xs text-gray-500\">\n                  Unlocked at Level {recipe.unlockLevel}\n                </div>\n\n                {canCraft(recipe) && (\n                  <div className=\"mt-2\">\n                    <Button size=\"sm\" variant=\"success\" className=\"w-full\">\n                      ✅ Can Craft\n                    </Button>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n\n          {filteredRecipes.length === 0 && (\n            <div className=\"text-center py-8 text-gray-500\">\n              <div className=\"text-4xl mb-2\">📝</div>\n              <p>No recipes available in this category.</p>\n              <p className=\"text-sm\">Level up to unlock more recipes!</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAYO,SAAS,YAAY,KAAqC;QAArC,EAAE,MAAM,EAAE,OAAO,EAAoB,GAArC;;IAC1B,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACpC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,mBAAmB,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,KAAK;IACzD,MAAM,kBAAkB,qBAAqB,QACzC,mBACA,iBAAiB,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,KAAK;IAE1D,MAAM,WAAW,CAAC;QAChB,OAAO,OAAO,WAAW,CAAC,KAAK,CAAC,CAAA;YAC9B,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;YAC1E,OAAO,iBAAiB,cAAc,QAAQ,IAAI,WAAW,QAAQ;QACvE;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAO,IAAI,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI;IACjD;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,AAAC,GAAU,OAAR,MAAK,KAAoC,OAAjC,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAChD;IAEA,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM;YAAO,MAAM;QAAM;QACtC;YAAE,IAAI;YAAW,MAAM;YAAW,MAAM;QAAK;QAC7C;YAAE,IAAI;YAAS,MAAM;YAAS,MAAM;QAAK;QACzC;YAAE,IAAI;YAAS,MAAM;YAAS,MAAM;QAAK;QACzC;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAK;KAChD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,SAAS;0CAAS;;;;;;;;;;;;;;;;;8BAMlD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC,qIAAA,CAAA,SAAM;oCAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;oCACxD,MAAK;oCACL,SAAS,IAAM,oBAAoB,SAAS,EAAE;;wCAE7C,SAAS,IAAI;wCAAC;wCAAE,SAAS,IAAI;;mCALzB,SAAS,EAAE;;;;;;;;;;sCAWtB,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAA,uBACnB,6LAAC;oCAEC,WAAW,AAAC,2BAIX,OAHC,SAAS,UACL,iCACA;;sDAGN,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA+B,OAAO,IAAI;;;;;;8DACxD,6LAAC;oDAAK,WAAU;;wDAAyB;wDAAE,OAAO,SAAS;;;;;;;;;;;;;sDAG7D,6LAAC;4CAAI,WAAU;;gDACZ,mBAAmB,OAAO,UAAU;gDAAE;gDAAO,WAAW,OAAO,UAAU;;;;;;;sDAG5E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAoC;;;;;;gDAClD,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY;oDACnC,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;oDAC1E,MAAM,YAAY,iBAAiB,cAAc,QAAQ,IAAI,WAAW,QAAQ;oDAEhF,qBACE,6LAAC;wDAEC,WAAW,AAAC,gCAEX,OADC,YAAY,mBAAmB;;0EAGjC,6LAAC;0EAAM,WAAW,IAAI;;;;;;0EACtB,6LAAC;;oEACE,WAAW,QAAQ;oEACnB,+BACC,6LAAC;wEAAK,WAAU;;4EAAO;4EACnB,cAAc,QAAQ;4EAAC;;;;;;;;;;;;;;uDAV1B;;;;;gDAgBX;;;;;;;sDAGF,6LAAC;4CAAI,WAAU;;gDAAwB;gDAClB,OAAO,WAAW;;;;;;;wCAGtC,SAAS,yBACR,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAQ;gDAAU,WAAU;0DAAS;;;;;;;;;;;;mCAjDtD,OAAO,EAAE;;;;;;;;;;wBA0DnB,gBAAgB,MAAM,KAAK,mBAC1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;8CAAE;;;;;;8CACH,6LAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrC;GAxIgB;;QACgB,kIAAA,CAAA,UAAO;;;KADvB", "debugId": null}}, {"offset": {"line": 1365, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/ShopModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { useGame } from '@/contexts/GameContext'\n\ninterface ShopModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport function ShopModal({ isOpen, onClose }: ShopModalProps) {\n  const { player, inventory, spendMoney, addIngredient } = useGame()\n  const [quantities, setQuantities] = useState<Record<string, number>>({})\n\n  if (!isOpen) return null\n\n  const handleQuantityChange = (ingredientName: string, quantity: number) => {\n    setQuantities(prev => ({\n      ...prev,\n      [ingredientName]: Math.max(0, quantity)\n    }))\n  }\n\n  const buyIngredient = (ingredientName: string, cost: number) => {\n    const quantity = quantities[ingredientName] || 1\n    const totalCost = cost * quantity\n    \n    if (spendMoney(totalCost)) {\n      addIngredient(ingredientName, quantity)\n      setQuantities(prev => ({ ...prev, [ingredientName]: 0 }))\n    }\n  }\n\n  const getTotalCost = (ingredientName: string, cost: number) => {\n    const quantity = quantities[ingredientName] || 1\n    return cost * quantity\n  }\n\n  const canAfford = (ingredientName: string, cost: number) => {\n    return player.money >= getTotalCost(ingredientName, cost)\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-2xl font-bold text-orange-800\">🛒 Ingredient Shop</h2>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"bg-green-100 px-3 py-1 rounded-full\">\n                <span className=\"text-green-800 font-medium\">${player.money}</span>\n              </div>\n              <Button variant=\"secondary\" onClick={onClose}>\n                ✕ Close\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          <div className=\"space-y-4 max-h-[60vh] overflow-y-auto\">\n            {inventory.map(ingredient => {\n              const quantity = quantities[ingredient.name] || 1\n              const totalCost = getTotalCost(ingredient.name, ingredient.cost)\n              const affordable = canAfford(ingredient.name, ingredient.cost)\n\n              return (\n                <div\n                  key={ingredient.name}\n                  className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\"\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"text-2xl\">{ingredient.icon}</div>\n                    <div>\n                      <h3 className=\"font-medium text-gray-800\">{ingredient.name}</h3>\n                      <p className=\"text-sm text-gray-600\">\n                        Current stock: {ingredient.quantity}\n                      </p>\n                      <p className=\"text-sm text-green-600\">\n                        ${ingredient.cost} each\n                      </p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"flex items-center space-x-2\">\n                      <Button\n                        size=\"sm\"\n                        variant=\"secondary\"\n                        onClick={() => handleQuantityChange(ingredient.name, quantity - 1)}\n                        disabled={quantity <= 1}\n                      >\n                        -\n                      </Button>\n                      <span className=\"w-12 text-center font-mono\">{quantity}</span>\n                      <Button\n                        size=\"sm\"\n                        variant=\"secondary\"\n                        onClick={() => handleQuantityChange(ingredient.name, quantity + 1)}\n                        disabled={!canAfford(ingredient.name, ingredient.cost) && quantity >= 1}\n                      >\n                        +\n                      </Button>\n                    </div>\n\n                    <div className=\"text-right min-w-[80px]\">\n                      <div className={`font-medium ${affordable ? 'text-green-600' : 'text-red-600'}`}>\n                        ${totalCost}\n                      </div>\n                      <Button\n                        size=\"sm\"\n                        variant={affordable ? 'success' : 'secondary'}\n                        onClick={() => buyIngredient(ingredient.name, ingredient.cost)}\n                        disabled={!affordable}\n                        className=\"mt-1\"\n                      >\n                        {affordable ? 'Buy' : 'Too Expensive'}\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n              )\n            })}\n          </div>\n\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n            <h3 className=\"font-medium text-blue-800 mb-2\">💡 Shopping Tips</h3>\n            <ul className=\"text-sm text-blue-700 space-y-1\">\n              <li>• Buy ingredients in bulk to save time</li>\n              <li>• Keep an eye on your stock levels</li>\n              <li>• Some recipes require rare ingredients</li>\n              <li>• Prices may vary based on availability</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAWO,SAAS,UAAU,KAAmC;QAAnC,EAAE,MAAM,EAAE,OAAO,EAAkB,GAAnC;;IACxB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAEtE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,uBAAuB,CAAC,gBAAwB;QACpD,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,eAAe,EAAE,KAAK,GAAG,CAAC,GAAG;YAChC,CAAC;IACH;IAEA,MAAM,gBAAgB,CAAC,gBAAwB;QAC7C,MAAM,WAAW,UAAU,CAAC,eAAe,IAAI;QAC/C,MAAM,YAAY,OAAO;QAEzB,IAAI,WAAW,YAAY;YACzB,cAAc,gBAAgB;YAC9B,cAAc,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,eAAe,EAAE;gBAAE,CAAC;QACzD;IACF;IAEA,MAAM,eAAe,CAAC,gBAAwB;QAC5C,MAAM,WAAW,UAAU,CAAC,eAAe,IAAI;QAC/C,OAAO,OAAO;IAChB;IAEA,MAAM,YAAY,CAAC,gBAAwB;QACzC,OAAO,OAAO,KAAK,IAAI,aAAa,gBAAgB;IACtD;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;;gDAA6B;gDAAE,OAAO,KAAK;;;;;;;;;;;;kDAE7D,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAY,SAAS;kDAAS;;;;;;;;;;;;;;;;;;;;;;;8BAOpD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAA;gCACb,MAAM,WAAW,UAAU,CAAC,WAAW,IAAI,CAAC,IAAI;gCAChD,MAAM,YAAY,aAAa,WAAW,IAAI,EAAE,WAAW,IAAI;gCAC/D,MAAM,aAAa,UAAU,WAAW,IAAI,EAAE,WAAW,IAAI;gCAE7D,qBACE,6LAAC;oCAEC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAY,WAAW,IAAI;;;;;;8DAC1C,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA6B,WAAW,IAAI;;;;;;sEAC1D,6LAAC;4DAAE,WAAU;;gEAAwB;gEACnB,WAAW,QAAQ;;;;;;;sEAErC,6LAAC;4DAAE,WAAU;;gEAAyB;gEAClC,WAAW,IAAI;gEAAC;;;;;;;;;;;;;;;;;;;sDAKxB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,qBAAqB,WAAW,IAAI,EAAE,WAAW;4DAChE,UAAU,YAAY;sEACvB;;;;;;sEAGD,6LAAC;4DAAK,WAAU;sEAA8B;;;;;;sEAC9C,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,qBAAqB,WAAW,IAAI,EAAE,WAAW;4DAChE,UAAU,CAAC,UAAU,WAAW,IAAI,EAAE,WAAW,IAAI,KAAK,YAAY;sEACvE;;;;;;;;;;;;8DAKH,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,AAAC,eAA6D,OAA/C,aAAa,mBAAmB;;gEAAkB;gEAC7E;;;;;;;sEAEJ,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS,aAAa,YAAY;4DAClC,SAAS,IAAM,cAAc,WAAW,IAAI,EAAE,WAAW,IAAI;4DAC7D,UAAU,CAAC;4DACX,WAAU;sEAET,aAAa,QAAQ;;;;;;;;;;;;;;;;;;;mCAhDvB,WAAW,IAAI;;;;;4BAsD1B;;;;;;sCAGF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GAhIgB;;QAC2C,kIAAA,CAAA,UAAO;;;KADlD", "debugId": null}}, {"offset": {"line": 1722, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/BakingModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Recipe, getAvailableRecipes, canCraftRecipe } from '@/lib/gameLogic'\nimport { useGame } from '@/contexts/GameContext'\n\ninterface BakingModalProps {\n  isOpen: boolean\n  onClose: () => void\n  equipmentId: string\n  equipmentName: string\n}\n\nexport function BakingModal({ isOpen, onClose, equipmentId, equipmentName }: BakingModalProps) {\n  const { player, inventory, updateEquipment, useIngredient } = useGame()\n  const [selectedRecipe, setSelectedRecipe] = useState<Recipe | null>(null)\n  \n  if (!isOpen) return null\n\n  const availableRecipes = getAvailableRecipes(player.level)\n  const craftableRecipes = availableRecipes.filter(recipe => \n    canCraftRecipe(recipe, inventory)\n  )\n\n  const startBaking = (recipe: Recipe) => {\n    // Check if we can craft the recipe\n    if (!canCraftRecipe(recipe, inventory)) {\n      return\n    }\n\n    // Consume ingredients\n    recipe.ingredients.forEach(ingredient => {\n      useIngredient(ingredient.name, ingredient.quantity)\n    })\n\n    // Start the equipment\n    updateEquipment(equipmentId, {\n      isActive: true,\n      timeRemaining: recipe.bakingTime,\n      currentRecipe: recipe.name\n    })\n\n    onClose()\n  }\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const getDifficultyStars = (difficulty: number) => {\n    return '⭐'.repeat(difficulty) + '☆'.repeat(5 - difficulty)\n  }\n\n  const getRecipeIcon = (category: string) => {\n    switch (category) {\n      case 'cookies': return '🍪'\n      case 'cakes': return '🧁'\n      case 'bread': return '🍞'\n      case 'pastries': return '🥐'\n      default: return '🍽️'\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-2xl font-bold text-orange-800\">\n              🔥 {equipmentName} - Select Recipe\n            </h2>\n            <Button variant=\"secondary\" onClick={onClose}>\n              ✕ Close\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          {craftableRecipes.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <div className=\"text-4xl mb-4\">😔</div>\n              <h3 className=\"text-lg font-medium text-gray-800 mb-2\">\n                No recipes available\n              </h3>\n              <p className=\"text-gray-600 mb-4\">\n                You don't have enough ingredients to craft any recipes.\n              </p>\n              <Button variant=\"primary\" onClick={onClose}>\n                Buy Ingredients\n              </Button>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[60vh] overflow-y-auto\">\n              {craftableRecipes.map(recipe => (\n                <div\n                  key={recipe.id}\n                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${\n                    selectedRecipe?.id === recipe.id\n                      ? 'border-orange-400 bg-orange-50'\n                      : 'border-gray-300 bg-gray-50 hover:border-orange-300'\n                  }`}\n                  onClick={() => setSelectedRecipe(recipe)}\n                >\n                  <div className=\"flex items-start justify-between mb-2\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-2xl\">{getRecipeIcon(recipe.category)}</span>\n                      <h3 className=\"font-semibold text-gray-800\">{recipe.name}</h3>\n                    </div>\n                    <span className=\"text-sm text-green-600\">${recipe.basePrice}</span>\n                  </div>\n\n                  <div className=\"text-xs text-gray-500 mb-2\">\n                    {getDifficultyStars(recipe.difficulty)} • ⏱️ {formatTime(recipe.bakingTime)}\n                  </div>\n\n                  <div className=\"space-y-1 mb-3\">\n                    <div className=\"text-sm font-medium text-gray-700\">Ingredients:</div>\n                    {recipe.ingredients.map((ingredient, index) => {\n                      const inventoryItem = inventory.find(item => item.name === ingredient.name)\n                      \n                      return (\n                        <div\n                          key={index}\n                          className=\"text-xs flex justify-between text-green-600\"\n                        >\n                          <span>{ingredient.name}</span>\n                          <span>\n                            {ingredient.quantity} \n                            <span className=\"ml-1\">\n                              ({inventoryItem?.quantity || 0} available)\n                            </span>\n                          </span>\n                        </div>\n                      )\n                    })}\n                  </div>\n\n                  {selectedRecipe?.id === recipe.id && (\n                    <Button\n                      variant=\"success\"\n                      size=\"sm\"\n                      className=\"w-full\"\n                      onClick={() => startBaking(recipe)}\n                    >\n                      🔥 Start Baking\n                    </Button>\n                  )}\n                </div>\n              ))}\n            </div>\n          )}\n\n          {selectedRecipe && craftableRecipes.length > 0 && (\n            <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n              <h3 className=\"font-medium text-blue-800 mb-2\">\n                📋 Baking Instructions for {selectedRecipe.name}\n              </h3>\n              <div className=\"text-sm text-blue-700 space-y-1\">\n                <p>• Baking time: {formatTime(selectedRecipe.bakingTime)}</p>\n                <p>• Difficulty: {getDifficultyStars(selectedRecipe.difficulty)}</p>\n                <p>• Expected reward: ${selectedRecipe.basePrice}</p>\n                <p>• Make sure you have all ingredients before starting!</p>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAcO,SAAS,YAAY,KAAiE;QAAjE,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAoB,GAAjE;;IAC1B,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,mBAAmB,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,KAAK;IACzD,MAAM,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,SAC/C,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;IAGzB,MAAM,cAAc,CAAC;;QACnB,mCAAmC;QACnC,IAAI,CAAC,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,YAAY;YACtC;QACF;QAEA,sBAAsB;QACtB,OAAO,WAAW,CAAC,OAAO,IAAC,CAAA;;YACzB,cAAc,WAAW,IAAI,EAAE,WAAW,QAAQ;QACpD;;gBADE;;;QAGF,sBAAsB;QACtB,gBAAgB,aAAa;YAC3B,UAAU;YACV,eAAe,OAAO,UAAU;YAChC,eAAe,OAAO,IAAI;QAC5B;QAEA;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,AAAC,GAAU,OAAR,MAAK,KAAoC,OAAjC,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAChD;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAO,IAAI,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI;IACjD;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAqC;oCAC7C;oCAAc;;;;;;;0CAEpB,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,SAAS;0CAAS;;;;;;;;;;;;;;;;;8BAMlD,6LAAC;oBAAI,WAAU;;wBACZ,iBAAiB,MAAM,KAAK,kBAC3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;8CAAS;;;;;;;;;;;iDAK9C,6LAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAA,uBACpB,6LAAC;oCAEC,WAAW,AAAC,yDAIX,OAHC,CAAA,2BAAA,qCAAA,eAAgB,EAAE,MAAK,OAAO,EAAE,GAC5B,mCACA;oCAEN,SAAS,IAAM,kBAAkB;;sDAEjC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAY,cAAc,OAAO,QAAQ;;;;;;sEACzD,6LAAC;4DAAG,WAAU;sEAA+B,OAAO,IAAI;;;;;;;;;;;;8DAE1D,6LAAC;oDAAK,WAAU;;wDAAyB;wDAAE,OAAO,SAAS;;;;;;;;;;;;;sDAG7D,6LAAC;4CAAI,WAAU;;gDACZ,mBAAmB,OAAO,UAAU;gDAAE;gDAAO,WAAW,OAAO,UAAU;;;;;;;sDAG5E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAoC;;;;;;gDAClD,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY;oDACnC,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;oDAE1E,qBACE,6LAAC;wDAEC,WAAU;;0EAEV,6LAAC;0EAAM,WAAW,IAAI;;;;;;0EACtB,6LAAC;;oEACE,WAAW,QAAQ;kFACpB,6LAAC;wEAAK,WAAU;;4EAAO;4EACnB,CAAA,0BAAA,oCAAA,cAAe,QAAQ,KAAI;4EAAE;;;;;;;;;;;;;;uDAP9B;;;;;gDAYX;;;;;;;wCAGD,CAAA,2BAAA,qCAAA,eAAgB,EAAE,MAAK,OAAO,EAAE,kBAC/B,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,YAAY;sDAC5B;;;;;;;mCAhDE,OAAO,EAAE;;;;;;;;;;wBAyDrB,kBAAkB,iBAAiB,MAAM,GAAG,mBAC3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAAiC;wCACjB,eAAe,IAAI;;;;;;;8CAEjD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;gDAAE;gDAAgB,WAAW,eAAe,UAAU;;;;;;;sDACvD,6LAAC;;gDAAE;gDAAe,mBAAmB,eAAe,UAAU;;;;;;;sDAC9D,6LAAC;;gDAAE;gDAAqB,eAAe,SAAS;;;;;;;sDAChD,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;GA9JgB;;QACgD,kIAAA,CAAA,UAAO;;;KADvD", "debugId": null}}, {"offset": {"line": 2119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/app/game/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useLanguage } from '@/contexts/LanguageContext'\nimport { GameProvider, useGame } from '@/contexts/GameContext'\nimport { Button } from '@/components/ui/Button'\nimport { Equipment } from '@/components/game/Equipment'\nimport { Order } from '@/components/game/Order'\nimport { RecipeModal } from '@/components/game/RecipeModal'\nimport { ShopModal } from '@/components/game/ShopModal'\nimport { BakingModal } from '@/components/game/BakingModal'\nimport { NotificationSystem, useNotifications } from '@/components/game/NotificationSystem'\n\nfunction GameContent() {\n  const { t } = useLanguage()\n  const {\n    player,\n    equipment,\n    inventory,\n    orders,\n    updateEquipment,\n    acceptOrder,\n    completeOrder,\n    declineOrder,\n    generateNewOrder\n  } = useGame()\n\n  const [showRecipeModal, setShowRecipeModal] = useState(false)\n  const [showShopModal, setShowShopModal] = useState(false)\n  const [showBakingModal, setShowBakingModal] = useState(false)\n  const [selectedEquipment, setSelectedEquipment] = useState<{id: string, name: string} | null>(null)\n\n  const handleEquipmentClick = (equipmentId: string, equipmentName: string) => {\n    setSelectedEquipment({ id: equipmentId, name: equipmentName })\n    setShowBakingModal(true)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-orange-50 to-yellow-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-orange-200 p-4\">\n        <div className=\"max-w-7xl mx-auto flex justify-between items-center\">\n          <div className=\"flex items-center space-x-6\">\n            <h1 className=\"text-2xl font-bold text-orange-800\">🥖 Bake It Out</h1>\n            <div className=\"flex items-center space-x-4 text-sm\">\n              <div className=\"bg-blue-100 px-3 py-1 rounded-full\">\n                <span className=\"text-blue-800\">Level {player.level}</span>\n              </div>\n              <div className=\"bg-green-100 px-3 py-1 rounded-full\">\n                <span className=\"text-green-800\">${player.money}</span>\n              </div>\n              <div className=\"bg-purple-100 px-3 py-1 rounded-full\">\n                <span className=\"text-purple-800\">XP: {player.experience}/{player.maxExperience}</span>\n              </div>\n            </div>\n          </div>\n          <Button variant=\"secondary\" onClick={() => window.location.href = '/'}>\n            🏠 Home\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto p-6 grid grid-cols-1 lg:grid-cols-4 gap-6\">\n        {/* Kitchen Area */}\n        <div className=\"lg:col-span-3 space-y-6\">\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-semibold text-orange-800 mb-4\">🏪 Kitchen</h2>\n\n            {/* Equipment Grid */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              {equipment.map((eq) => (\n                <Equipment\n                  key={eq.id}\n                  equipment={eq}\n                  onClick={handleEquipmentClick}\n                />\n              ))}\n            </div>\n          </div>\n\n          {/* Inventory */}\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-semibold text-orange-800 mb-4\">📦 Inventory</h2>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              {inventory.map((ingredient) => (\n                <div key={ingredient.name} className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                  <div className=\"text-2xl mb-1\">{ingredient.icon}</div>\n                  <div className=\"font-medium text-gray-800\">{ingredient.name}</div>\n                  <div className=\"text-sm text-gray-600\">Qty: {ingredient.quantity}</div>\n                  <div className=\"text-xs text-green-600\">${ingredient.cost} each</div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Orders Panel */}\n        <div className=\"space-y-6\">\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex justify-between items-center mb-4\">\n              <h2 className=\"text-xl font-semibold text-orange-800\">📋 Orders</h2>\n              <Button\n                size=\"sm\"\n                variant=\"primary\"\n                onClick={generateNewOrder}\n              >\n                + New Order\n              </Button>\n            </div>\n            <div className=\"space-y-4\">\n              {orders.map((order) => (\n                <Order\n                  key={order.id}\n                  order={order}\n                  onAccept={acceptOrder}\n                  onDecline={declineOrder}\n                  onComplete={completeOrder}\n                />\n              ))}\n            </div>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-semibold text-orange-800 mb-4\">⚡ Quick Actions</h2>\n            <div className=\"space-y-2\">\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                className=\"w-full\"\n                onClick={() => setShowShopModal(true)}\n              >\n                🛒 Buy Ingredients\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                className=\"w-full\"\n                onClick={() => setShowRecipeModal(true)}\n              >\n                📖 View Recipes\n              </Button>\n              <Button variant=\"secondary\" size=\"sm\" className=\"w-full\">\n                🔧 Upgrade Equipment\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Modals */}\n      <RecipeModal\n        isOpen={showRecipeModal}\n        onClose={() => setShowRecipeModal(false)}\n      />\n      <ShopModal\n        isOpen={showShopModal}\n        onClose={() => setShowShopModal(false)}\n      />\n      <BakingModal\n        isOpen={showBakingModal}\n        onClose={() => setShowBakingModal(false)}\n        equipmentId={selectedEquipment?.id || ''}\n        equipmentName={selectedEquipment?.name || ''}\n      />\n    </div>\n  )\n}\n\nexport default function GamePage() {\n  return (\n    <GameProvider>\n      <GameContent />\n    </GameProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAaA,SAAS;;IACP,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACxB,MAAM,EACJ,MAAM,EACN,SAAS,EACT,SAAS,EACT,MAAM,EACN,eAAe,EACf,WAAW,EACX,aAAa,EACb,YAAY,EACZ,gBAAgB,EACjB,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEV,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqC;IAE9F,MAAM,uBAAuB,CAAC,aAAqB;QACjD,qBAAqB;YAAE,IAAI;YAAa,MAAM;QAAc;QAC5D,mBAAmB;IACrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;;oDAAgB;oDAAO,OAAO,KAAK;;;;;;;;;;;;sDAErD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;;oDAAiB;oDAAE,OAAO,KAAK;;;;;;;;;;;;sDAEjD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;;oDAAkB;oDAAK,OAAO,UAAU;oDAAC;oDAAE,OAAO,aAAa;;;;;;;;;;;;;;;;;;;;;;;;sCAIrF,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAY,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;sCAAK;;;;;;;;;;;;;;;;;0BAM3E,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA6C;;;;;;kDAG3D,6LAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAC,mBACd,6LAAC,0IAAA,CAAA,YAAS;gDAER,WAAW;gDACX,SAAS;+CAFJ,GAAG,EAAE;;;;;;;;;;;;;;;;0CASlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA6C;;;;;;kDAC3D,6LAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAC,2BACd,6LAAC;gDAA0B,WAAU;;kEACnC,6LAAC;wDAAI,WAAU;kEAAiB,WAAW,IAAI;;;;;;kEAC/C,6LAAC;wDAAI,WAAU;kEAA6B,WAAW,IAAI;;;;;;kEAC3D,6LAAC;wDAAI,WAAU;;4DAAwB;4DAAM,WAAW,QAAQ;;;;;;;kEAChE,6LAAC;wDAAI,WAAU;;4DAAyB;4DAAE,WAAW,IAAI;4DAAC;;;;;;;;+CAJlD,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;kCAYjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;0DACV;;;;;;;;;;;;kDAIH,6LAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC,sIAAA,CAAA,QAAK;gDAEJ,OAAO;gDACP,UAAU;gDACV,WAAW;gDACX,YAAY;+CAJP,MAAM,EAAE;;;;;;;;;;;;;;;;0CAWrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA6C;;;;;;kDAC3D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,iBAAiB;0DACjC;;;;;;0DAGD,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,mBAAmB;0DACnC;;;;;;0DAGD,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAY,MAAK;gDAAK,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjE,6LAAC,4IAAA,CAAA,cAAW;gBACV,QAAQ;gBACR,SAAS,IAAM,mBAAmB;;;;;;0BAEpC,6LAAC,0IAAA,CAAA,YAAS;gBACR,QAAQ;gBACR,SAAS,IAAM,iBAAiB;;;;;;0BAElC,6LAAC,4IAAA,CAAA,cAAW;gBACV,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,aAAa,CAAA,8BAAA,wCAAA,kBAAmB,EAAE,KAAI;gBACtC,eAAe,CAAA,8BAAA,wCAAA,kBAAmB,IAAI,KAAI;;;;;;;;;;;;AAIlD;GA1JS;;QACO,sIAAA,CAAA,cAAW;QAWrB,kIAAA,CAAA,UAAO;;;KAZJ;AA4JM,SAAS;IACtB,qBACE,6LAAC,kIAAA,CAAA,eAAY;kBACX,cAAA,6LAAC;;;;;;;;;;AAGP;MANwB", "debugId": null}}]}