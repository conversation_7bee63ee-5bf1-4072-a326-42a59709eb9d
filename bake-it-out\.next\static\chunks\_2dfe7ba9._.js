(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/contexts/LanguageContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "LanguageProvider": ()=>LanguageProvider,
    "useLanguage": ()=>useLanguage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
const LanguageContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Comprehensive translations object
const translations = {
    en: {
        // Main game
        'game.title': 'Bake It Out',
        'game.subtitle': 'Master the art of bakery management in this engaging multiplayer game. Complete orders, unlock recipes, automate your processes, and compete with friends!',
        'game.play': '🎮 Start Playing',
        'game.multiplayer': '👥 Multiplayer',
        'game.english': '🇺🇸 English',
        'game.czech': '🇨🇿 Čeština',
        'game.home': '🏠 Home',
        'game.close': '✕ Close',
        'game.continue': '🚀 Continue Playing',
        // Features
        'features.manage.title': 'Manage Your Bakery',
        'features.manage.description': 'Take orders, bake delicious goods, and serve happy customers',
        'features.levelup.title': 'Level Up & Automate',
        'features.levelup.description': 'Unlock new recipes, buy equipment, and automate your processes',
        'features.multiplayer.title': 'Play Together',
        'features.multiplayer.description': 'Cooperative and competitive multiplayer modes with friends',
        'status.development': '🚧 Game in Development - Phase 5: Multilayer Support! 🚧',
        // Game interface
        'ui.level': 'Level {{level}}',
        'ui.money': '${{amount}}',
        'ui.experience': 'XP: {{current}}/{{max}}',
        'ui.skillPoints': 'SP: {{points}}',
        'ui.achievements': '🏆 Achievements',
        'ui.skills': '🌟 Skills',
        'ui.automation': '🤖 Automation',
        // Kitchen
        'kitchen.title': '🏪 Kitchen',
        'kitchen.clickToUse': 'Click to use',
        'kitchen.making': 'Making: {{recipe}}',
        'kitchen.timeRemaining': 'Time: {{time}}',
        // Inventory
        'inventory.title': '📦 Inventory',
        'inventory.quantity': 'Qty: {{qty}}',
        'inventory.cost': '${{cost}} each',
        // Orders
        'orders.title': '📋 Orders',
        'orders.newOrder': '+ New Order',
        'orders.accept': 'Accept',
        'orders.decline': 'Decline',
        'orders.complete': 'Complete',
        'orders.inProgress': 'In Progress',
        'orders.timeLimit': 'Time: {{time}}',
        'orders.reward': '${{amount}}',
        'orders.customer': 'Customer: {{name}}',
        // Quick Actions
        'actions.title': '⚡ Quick Actions',
        'actions.buyIngredients': '🛒 Buy Ingredients',
        'actions.viewRecipes': '📖 View Recipes',
        'actions.equipmentShop': '🔧 Equipment Shop',
        // Modals
        'modal.recipes.title': '📖 Recipe Book',
        'modal.shop.title': '🛒 Ingredient Shop',
        'modal.baking.title': '🔥 {{equipment}} - Select Recipe',
        'modal.achievements.title': '🏆 Achievements',
        'modal.skills.title': '🌟 Skill Tree',
        'modal.automation.title': '🤖 Automation Control',
        'modal.equipmentShop.title': '🏪 Equipment Shop',
        'modal.settings.title': '⚙️ Settings',
        'modal.bakeries.title': '🏪 Bakery Manager',
        'modal.levelUp.title': 'Level Up!',
        'modal.levelUp.subtitle': 'You reached Level {{level}}!',
        // Recipe Modal
        'recipes.all': 'All',
        'recipes.cookies': 'Cookies',
        'recipes.cakes': 'Cakes',
        'recipes.bread': 'Bread',
        'recipes.pastries': 'Pastries',
        'recipes.ingredients': 'Ingredients:',
        'recipes.difficulty': 'Difficulty:',
        'recipes.time': 'Time:',
        'recipes.canCraft': '✅ Can Craft',
        'recipes.unlockLevel': 'Unlocked at Level {{level}}',
        'recipes.noRecipes': 'No recipes available in this category.',
        'recipes.levelUpToUnlock': 'Level up to unlock more recipes!',
        // Shop Modal
        'shop.currentStock': 'Current stock: {{quantity}}',
        'shop.buy': 'Buy',
        'shop.tooExpensive': 'Too Expensive',
        'shop.tips.title': '💡 Shopping Tips',
        'shop.tips.bulk': '• Buy ingredients in bulk to save time',
        'shop.tips.stock': '• Keep an eye on your stock levels',
        'shop.tips.rare': '• Some recipes require rare ingredients',
        'shop.tips.prices': '• Prices may vary based on availability',
        // Baking Modal
        'baking.selectRecipe': 'Select Recipe',
        'baking.noRecipes': 'No recipes available',
        'baking.noIngredients': 'You don\'t have enough ingredients to craft any recipes.',
        'baking.buyIngredients': 'Buy Ingredients',
        'baking.startBaking': '🔥 Start Baking',
        'baking.instructions': '📋 Baking Instructions for {{recipe}}',
        'baking.expectedReward': 'Expected reward: ${{amount}}',
        'baking.makesSure': 'Make sure you have all ingredients before starting!',
        // Achievements Modal
        'achievements.completed': '{{completed}} of {{total}} achievements completed',
        'achievements.overallProgress': 'Overall Progress',
        'achievements.progress': 'Progress',
        'achievements.reward': 'Reward:',
        'achievements.noAchievements': 'No achievements in this category.',
        // Skills Modal
        'skills.availablePoints': 'Available Skill Points: {{points}}',
        'skills.efficiency': 'Efficiency',
        'skills.automation': 'Automation',
        'skills.quality': 'Quality',
        'skills.business': 'Business',
        'skills.effects': 'Effects:',
        'skills.requires': 'Requires: {{requirements}}',
        'skills.requiresLevel': 'Requires Level {{level}}',
        'skills.maxed': '✅ Maxed',
        'skills.upgrade': '⬆️ Upgrade ({{cost}} SP)',
        'skills.locked': '🔒 Locked',
        'skills.noSkills': 'No skills in this category.',
        'skills.tips.title': '💡 Skill Tips',
        'skills.tips.earnPoints': '• Earn skill points by leveling up (1 point every 2 levels)',
        'skills.tips.prerequisites': '• Some skills require other skills to be unlocked first',
        'skills.tips.playstyle': '• Focus on skills that match your playstyle',
        'skills.tips.efficiency': '• Efficiency skills help with resource management',
        // Automation Modal
        'automation.masterControl': '🎛️ Master Control',
        'automation.enableAutomation': 'Enable Automation',
        'automation.autoStart': 'Auto-start Equipment',
        'automation.priorityMode': '🎯 Priority Mode',
        'automation.efficiency': 'Efficiency (Orders First)',
        'automation.profit': 'Profit (Highest Value)',
        'automation.speed': 'Speed (Fastest Recipes)',
        'automation.priorityDescription': 'How automation chooses what to bake',
        'automation.performance': '⚡ Performance',
        'automation.maxJobs': 'Max Concurrent Jobs: {{jobs}}',
        'automation.safety': '🛡️ Safety',
        'automation.stopWhenLow': 'Stop when ingredients below: {{threshold}}',
        'automation.upgrades': '💡 Automation Upgrades',
        'automation.upgradesDescription': 'Improve your automation efficiency, speed, and intelligence with these upgrades.',
        'automation.purchase': 'Purchase',
        'automation.noUpgrades': 'No upgrades available at your current level.',
        'automation.levelUpForUpgrades': 'Level up to unlock more automation upgrades!',
        'automation.automatedEquipment': 'Automated Equipment',
        'automation.activeUpgrades': 'Active Upgrades',
        'automation.automationStatus': 'Automation Status',
        'automation.equipmentStatus': '🏭 Equipment Status',
        'automation.running': 'Running',
        'automation.idle': 'Idle',
        'automation.noAutomatedEquipment': 'No automated equipment available.',
        'automation.purchaseAutoEquipment': 'Purchase auto-equipment from the shop to get started!',
        // Equipment Shop Modal
        'equipmentShop.upgradeYourBakery': 'Upgrade your bakery with professional equipment',
        'equipmentShop.basic': 'Basic',
        'equipmentShop.automated': 'Automated',
        'equipmentShop.advanced': 'Advanced',
        'equipmentShop.efficiency': 'Efficiency: {{efficiency}}x',
        'equipmentShop.automation': 'Automation:',
        'equipmentShop.unlockLevel': 'Unlock Level: {{level}}',
        'equipmentShop.purchase': '💰 Purchase',
        'equipmentShop.noEquipment': 'No equipment available in this category.',
        'equipmentShop.levelUpForEquipment': 'Level up to unlock more equipment!',
        'equipmentShop.tips.title': '💡 Equipment Tips',
        'equipmentShop.tips.automated': '• Automated equipment can run without your supervision',
        'equipmentShop.tips.efficiency': '• Higher efficiency means faster production and better quality',
        'equipmentShop.tips.conveyor': '• Conveyor belts connect equipment for seamless workflow',
        'equipmentShop.tips.advanced': '• Advanced equipment unlocks at higher levels',
        // Level Up Modal
        'levelUp.levelRewards': '🎁 Level Rewards',
        'levelUp.whatsNext': '💡 What\'s Next?',
        'levelUp.checkRecipes': '• Check out new recipes in your recipe book',
        'levelUp.visitShop': '• Visit the shop for new equipment',
        'levelUp.challengingOrders': '• Take on more challenging orders',
        'levelUp.investSkills': '• Invest in skill upgrades',
        // Settings Modal
        'settings.title': '⚙️ Settings',
        'settings.general': 'General',
        'settings.audio': 'Audio',
        'settings.graphics': 'Graphics',
        'settings.save': 'Save & Data',
        'settings.language': '🌍 Language',
        'settings.gameplay': '🎮 Gameplay',
        'settings.notifications': 'Enable Notifications',
        'settings.tutorials': 'Show Tutorials',
        'settings.animationSpeed': 'Animation Speed',
        'settings.sound': 'Sound Effects',
        'settings.music': 'Background Music',
        'settings.quality': '🎨 Graphics Quality',
        'settings.autoSave': '💾 Auto-Save',
        'settings.enableAutoSave': 'Enable Auto-Save',
        'settings.dataManagement': '📁 Data Management',
        'settings.exportSave': '📤 Export Save',
        'settings.importSave': '📥 Import Save',
        'settings.cloudSync': '☁️ Cloud Sync',
        'settings.cloudSyncDescription': 'Cloud sync allows you to save your progress online and play across multiple devices.',
        'settings.comingSoon': 'Coming Soon',
        // Bakery Manager Modal
        'bakeries.title': '🏪 Bakery Manager',
        'bakeries.subtitle': 'Manage your bakery empire',
        'bakeries.owned': 'My Bakeries',
        'bakeries.available': 'Available',
        'bakeries.current': 'Current',
        'bakeries.level': 'Level',
        'bakeries.specialization': 'Specialization',
        'bakeries.equipment': 'Equipment',
        'bakeries.orders': 'Active Orders',
        'bakeries.switchTo': 'Switch To',
        'bakeries.noOwned': 'You don\'t own any bakeries yet.',
        'bakeries.purchase': '💰 Purchase',
        'bakeries.tooExpensive': '💸 Too Expensive',
        'bakeries.allOwned': 'You own all available bakeries!',
        'bakeries.tips': '💡 Bakery Tips',
        'bakeries.tip1': 'Each bakery specializes in different products for bonus efficiency',
        'bakeries.tip2': 'Switch between bakeries to manage multiple locations',
        'bakeries.tip3': 'Specialized bakeries attract customers looking for specific items',
        'bakeries.tip4': 'Upgrade each bakery independently for maximum profit',
        // Notifications
        'notifications.orderAccepted': 'Order Accepted',
        'notifications.orderAcceptedMessage': 'You have accepted a new order!',
        'notifications.orderCompleted': 'Order Completed!',
        'notifications.orderCompletedMessage': 'You earned ${{reward}} and gained experience!',
        'notifications.orderDeclined': 'Order Declined',
        'notifications.orderDeclinedMessage': 'Order has been removed from your queue.',
        'notifications.bakeryPurchased': 'Bakery Purchased!',
        'notifications.bakeryPurchasedMessage': 'You now own {{name}}!',
        'notifications.bakerySwitched': 'Bakery Switched',
        'notifications.bakerySwitchedMessage': 'Switched to {{name}}',
        // Common buttons and actions
        'common.accept': 'Accept',
        'common.decline': 'Decline',
        'common.complete': 'Complete',
        'common.purchase': 'Purchase',
        'common.upgrade': 'Upgrade',
        'common.cancel': 'Cancel',
        'common.confirm': 'Confirm',
        'common.save': 'Save',
        'common.load': 'Load',
        'common.delete': 'Delete',
        'common.edit': 'Edit',
        'common.back': 'Back',
        'common.next': 'Next',
        'common.previous': 'Previous',
        'common.yes': 'Yes',
        'common.no': 'No'
    },
    cs: {
        // Main game
        'game.title': 'Bake It Out',
        'game.subtitle': 'Ovládněte umění řízení pekárny v této poutavé multiplayerové hře. Plňte objednávky, odemykejte recepty, automatizujte procesy a soutěžte s přáteli!',
        'game.play': '🎮 Začít hrát',
        'game.multiplayer': '👥 Multiplayer',
        'game.english': '🇺🇸 English',
        'game.czech': '🇨🇿 Čeština',
        'game.home': '🏠 Domů',
        'game.close': '✕ Zavřít',
        'game.continue': '🚀 Pokračovat ve hře',
        // Features
        'features.manage.title': 'Spravujte svou pekárnu',
        'features.manage.description': 'Přijímejte objednávky, pečte lahodné výrobky a obsluhujte spokojené zákazníky',
        'features.levelup.title': 'Postupujte a automatizujte',
        'features.levelup.description': 'Odemykejte nové recepty, kupujte vybavení a automatizujte své procesy',
        'features.multiplayer.title': 'Hrajte společně',
        'features.multiplayer.description': 'Kooperativní a soutěžní multiplayerové režimy s přáteli',
        'status.development': '🚧 Hra ve vývoji - Fáze 5: Vícevrstvá podpora! 🚧',
        // Game interface
        'ui.level': 'Úroveň {{level}}',
        'ui.money': '{{amount}} Kč',
        'ui.experience': 'XP: {{current}}/{{max}}',
        'ui.skillPoints': 'SP: {{points}}',
        'ui.achievements': '🏆 Úspěchy',
        'ui.skills': '🌟 Dovednosti',
        'ui.automation': '🤖 Automatizace',
        // Kitchen
        'kitchen.title': '🏪 Kuchyně',
        'kitchen.clickToUse': 'Klikněte pro použití',
        'kitchen.making': 'Připravuje: {{recipe}}',
        'kitchen.timeRemaining': 'Čas: {{time}}',
        // Inventory
        'inventory.title': '📦 Sklad',
        'inventory.quantity': 'Množství: {{qty}}',
        'inventory.cost': '{{cost}} Kč za kus',
        // Orders
        'orders.title': '📋 Objednávky',
        'orders.newOrder': '+ Nová objednávka',
        'orders.accept': 'Přijmout',
        'orders.decline': 'Odmítnout',
        'orders.complete': 'Dokončit',
        'orders.inProgress': 'Probíhá',
        'orders.timeLimit': 'Čas: {{time}}',
        'orders.reward': '{{amount}} Kč',
        'orders.customer': 'Zákazník: {{name}}',
        // Quick Actions
        'actions.title': '⚡ Rychlé akce',
        'actions.buyIngredients': '🛒 Koupit suroviny',
        'actions.viewRecipes': '📖 Zobrazit recepty',
        'actions.equipmentShop': '🔧 Obchod s vybavením',
        // Modals
        'modal.recipes.title': '📖 Kniha receptů',
        'modal.shop.title': '🛒 Obchod se surovinami',
        'modal.baking.title': '🔥 {{equipment}} - Vyberte recept',
        'modal.achievements.title': '🏆 Úspěchy',
        'modal.skills.title': '🌟 Strom dovedností',
        'modal.automation.title': '🤖 Ovládání automatizace',
        'modal.equipmentShop.title': '🏪 Obchod s vybavením',
        'modal.settings.title': '⚙️ Nastavení',
        'modal.bakeries.title': '🏪 Správce pekáren',
        'modal.levelUp.title': 'Postup na vyšší úroveň!',
        'modal.levelUp.subtitle': 'Dosáhli jste úrovně {{level}}!',
        // Recipe Modal
        'recipes.all': 'Vše',
        'recipes.cookies': 'Sušenky',
        'recipes.cakes': 'Dorty',
        'recipes.bread': 'Chléb',
        'recipes.pastries': 'Pečivo',
        'recipes.ingredients': 'Suroviny:',
        'recipes.difficulty': 'Obtížnost:',
        'recipes.time': 'Čas:',
        'recipes.canCraft': '✅ Lze vyrobit',
        'recipes.unlockLevel': 'Odemčeno na úrovni {{level}}',
        'recipes.noRecipes': 'V této kategorii nejsou k dispozici žádné recepty.',
        'recipes.levelUpToUnlock': 'Postupte na vyšší úroveň pro odemčení dalších receptů!',
        // Shop Modal
        'shop.currentStock': 'Aktuální zásoba: {{quantity}}',
        'shop.buy': 'Koupit',
        'shop.tooExpensive': 'Příliš drahé',
        'shop.tips.title': '💡 Tipy pro nakupování',
        'shop.tips.bulk': '• Kupujte suroviny ve velkém množství pro úsporu času',
        'shop.tips.stock': '• Sledujte úroveň svých zásob',
        'shop.tips.rare': '• Některé recepty vyžadují vzácné suroviny',
        'shop.tips.prices': '• Ceny se mohou lišit podle dostupnosti',
        // Baking Modal
        'baking.selectRecipe': 'Vyberte recept',
        'baking.noRecipes': 'Žádné recepty k dispozici',
        'baking.noIngredients': 'Nemáte dostatek surovin pro výrobu jakéhokoli receptu.',
        'baking.buyIngredients': 'Koupit suroviny',
        'baking.startBaking': '🔥 Začít péct',
        'baking.instructions': '📋 Pokyny pro pečení {{recipe}}',
        'baking.expectedReward': 'Očekávaná odměna: {{amount}} Kč',
        'baking.makesSure': 'Ujistěte se, že máte všechny suroviny před začátkem!',
        // Achievements Modal
        'achievements.completed': '{{completed}} z {{total}} úspěchů dokončeno',
        'achievements.overallProgress': 'Celkový pokrok',
        'achievements.progress': 'Pokrok',
        'achievements.reward': 'Odměna:',
        'achievements.noAchievements': 'V této kategorii nejsou žádné úspěchy.',
        // Skills Modal
        'skills.availablePoints': 'Dostupné body dovedností: {{points}}',
        'skills.efficiency': 'Efektivita',
        'skills.automation': 'Automatizace',
        'skills.quality': 'Kvalita',
        'skills.business': 'Podnikání',
        'skills.effects': 'Efekty:',
        'skills.requires': 'Vyžaduje: {{requirements}}',
        'skills.requiresLevel': 'Vyžaduje úroveň {{level}}',
        'skills.maxed': '✅ Maximální',
        'skills.upgrade': '⬆️ Vylepšit ({{cost}} SP)',
        'skills.locked': '🔒 Uzamčeno',
        'skills.noSkills': 'V této kategorii nejsou žádné dovednosti.',
        'skills.tips.title': '💡 Tipy pro dovednosti',
        'skills.tips.earnPoints': '• Získávejte body dovedností postupem na vyšší úroveň (1 bod každé 2 úrovně)',
        'skills.tips.prerequisites': '• Některé dovednosti vyžadují nejprve odemčení jiných dovedností',
        'skills.tips.playstyle': '• Zaměřte se na dovednosti, které odpovídají vašemu stylu hry',
        'skills.tips.efficiency': '• Dovednosti efektivity pomáhají se správou zdrojů',
        // Automation Modal
        'automation.masterControl': '🎛️ Hlavní ovládání',
        'automation.enableAutomation': 'Povolit automatizaci',
        'automation.autoStart': 'Automatické spuštění vybavení',
        'automation.priorityMode': '🎯 Režim priority',
        'automation.efficiency': 'Efektivita (objednávky první)',
        'automation.profit': 'Zisk (nejvyšší hodnota)',
        'automation.speed': 'Rychlost (nejrychlejší recepty)',
        'automation.priorityDescription': 'Jak automatizace vybírá, co péct',
        'automation.performance': '⚡ Výkon',
        'automation.maxJobs': 'Max současných úloh: {{jobs}}',
        'automation.safety': '🛡️ Bezpečnost',
        'automation.stopWhenLow': 'Zastavit, když suroviny klesnou pod: {{threshold}}',
        'automation.upgrades': '💡 Vylepšení automatizace',
        'automation.upgradesDescription': 'Vylepšete efektivitu, rychlost a inteligenci vaší automatizace.',
        'automation.purchase': 'Koupit',
        'automation.noUpgrades': 'Na vaší současné úrovni nejsou k dispozici žádná vylepšení.',
        'automation.levelUpForUpgrades': 'Postupte na vyšší úroveň pro odemčení dalších vylepšení automatizace!',
        'automation.automatedEquipment': 'Automatizované vybavení',
        'automation.activeUpgrades': 'Aktivní vylepšení',
        'automation.automationStatus': 'Stav automatizace',
        'automation.equipmentStatus': '🏭 Stav vybavení',
        'automation.running': 'Běží',
        'automation.idle': 'Nečinné',
        'automation.noAutomatedEquipment': 'Žádné automatizované vybavení k dispozici.',
        'automation.purchaseAutoEquipment': 'Kupte si auto-vybavení z obchodu pro začátek!',
        // Equipment Shop Modal
        'equipmentShop.upgradeYourBakery': 'Vylepšete svou pekárnu profesionálním vybavením',
        'equipmentShop.basic': 'Základní',
        'equipmentShop.automated': 'Automatizované',
        'equipmentShop.advanced': 'Pokročilé',
        'equipmentShop.efficiency': 'Efektivita: {{efficiency}}x',
        'equipmentShop.automation': 'Automatizace:',
        'equipmentShop.unlockLevel': 'Úroveň odemčení: {{level}}',
        'equipmentShop.purchase': '💰 Koupit',
        'equipmentShop.noEquipment': 'V této kategorii není k dispozici žádné vybavení.',
        'equipmentShop.levelUpForEquipment': 'Postupte na vyšší úroveň pro odemčení dalšího vybavení!',
        'equipmentShop.tips.title': '💡 Tipy pro vybavení',
        'equipmentShop.tips.automated': '• Automatizované vybavení může běžet bez vašeho dohledu',
        'equipmentShop.tips.efficiency': '• Vyšší efektivita znamená rychlejší výrobu a lepší kvalitu',
        'equipmentShop.tips.conveyor': '• Dopravní pásy spojují vybavení pro bezproblémový pracovní tok',
        'equipmentShop.tips.advanced': '• Pokročilé vybavení se odemyká na vyšších úrovních',
        // Level Up Modal
        'levelUp.levelRewards': '🎁 Odměny za úroveň',
        'levelUp.whatsNext': '💡 Co dál?',
        'levelUp.checkRecipes': '• Podívejte se na nové recepty ve své knize receptů',
        'levelUp.visitShop': '• Navštivte obchod pro nové vybavení',
        'levelUp.challengingOrders': '• Přijměte náročnější objednávky',
        'levelUp.investSkills': '• Investujte do vylepšení dovedností',
        // Settings Modal
        'settings.title': '⚙️ Nastavení',
        'settings.general': 'Obecné',
        'settings.audio': 'Zvuk',
        'settings.graphics': 'Grafika',
        'settings.save': 'Uložení a data',
        'settings.language': '🌍 Jazyk',
        'settings.gameplay': '🎮 Hratelnost',
        'settings.notifications': 'Povolit oznámení',
        'settings.tutorials': 'Zobrazit návody',
        'settings.animationSpeed': 'Rychlost animace',
        'settings.sound': 'Zvukové efekty',
        'settings.music': 'Hudba na pozadí',
        'settings.quality': '🎨 Kvalita grafiky',
        'settings.autoSave': '💾 Automatické ukládání',
        'settings.enableAutoSave': 'Povolit automatické ukládání',
        'settings.dataManagement': '📁 Správa dat',
        'settings.exportSave': '📤 Exportovat uložení',
        'settings.importSave': '📥 Importovat uložení',
        'settings.cloudSync': '☁️ Cloudová synchronizace',
        'settings.cloudSyncDescription': 'Cloudová synchronizace vám umožňuje uložit pokrok online a hrát na více zařízeních.',
        'settings.comingSoon': 'Již brzy',
        // Bakery Manager Modal
        'bakeries.title': '🏪 Správce pekáren',
        'bakeries.subtitle': 'Spravujte své pekárenské impérium',
        'bakeries.owned': 'Moje pekárny',
        'bakeries.available': 'Dostupné',
        'bakeries.current': 'Aktuální',
        'bakeries.level': 'Úroveň',
        'bakeries.specialization': 'Specializace',
        'bakeries.equipment': 'Vybavení',
        'bakeries.orders': 'Aktivní objednávky',
        'bakeries.switchTo': 'Přepnout na',
        'bakeries.noOwned': 'Ještě nevlastníte žádné pekárny.',
        'bakeries.purchase': '💰 Koupit',
        'bakeries.tooExpensive': '💸 Příliš drahé',
        'bakeries.allOwned': 'Vlastníte všechny dostupné pekárny!',
        'bakeries.tips': '💡 Tipy pro pekárny',
        'bakeries.tip1': 'Každá pekárna se specializuje na různé produkty pro bonusovou efektivitu',
        'bakeries.tip2': 'Přepínejte mezi pekárnami pro správu více lokalit',
        'bakeries.tip3': 'Specializované pekárny přitahují zákazníky hledající konkrétní položky',
        'bakeries.tip4': 'Vylepšujte každou pekárnu nezávisle pro maximální zisk',
        // Notifications
        'notifications.orderAccepted': 'Objednávka přijata',
        'notifications.orderAcceptedMessage': 'Přijali jste novou objednávku!',
        'notifications.orderCompleted': 'Objednávka dokončena!',
        'notifications.orderCompletedMessage': 'Získali jste {{reward}} Kč a zkušenosti!',
        'notifications.orderDeclined': 'Objednávka odmítnuta',
        'notifications.orderDeclinedMessage': 'Objednávka byla odstraněna z vaší fronty.',
        'notifications.bakeryPurchased': 'Pekárna zakoupena!',
        'notifications.bakeryPurchasedMessage': 'Nyní vlastníte {{name}}!',
        'notifications.bakerySwitched': 'Pekárna přepnuta',
        'notifications.bakerySwitchedMessage': 'Přepnuto na {{name}}',
        // Common buttons and actions
        'common.accept': 'Přijmout',
        'common.decline': 'Odmítnout',
        'common.complete': 'Dokončit',
        'common.purchase': 'Koupit',
        'common.upgrade': 'Vylepšit',
        'common.cancel': 'Zrušit',
        'common.confirm': 'Potvrdit',
        'common.save': 'Uložit',
        'common.load': 'Načíst',
        'common.delete': 'Smazat',
        'common.edit': 'Upravit',
        'common.back': 'Zpět',
        'common.next': 'Další',
        'common.previous': 'Předchozí',
        'common.yes': 'Ano',
        'common.no': 'Ne'
    }
};
function LanguageProvider(param) {
    let { children } = param;
    _s();
    const [language, setLanguage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('en');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LanguageProvider.useEffect": ()=>{
            // Load language from localStorage on client side
            const savedLanguage = localStorage.getItem('language');
            if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'cs')) {
                setLanguage(savedLanguage);
            }
        }
    }["LanguageProvider.useEffect"], []);
    const handleSetLanguage = (lang)=>{
        setLanguage(lang);
        localStorage.setItem('language', lang);
    };
    const t = (key, params)=>{
        let translation = translations[language][key] || key;
        if (params) {
            Object.entries(params).forEach((param)=>{
                let [param1, value] = param;
                translation = translation.replace("{{".concat(param1, "}}"), value);
            });
        }
        return translation;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LanguageContext.Provider, {
        value: {
            language,
            setLanguage: handleSetLanguage,
            t
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/LanguageContext.tsx",
        lineNumber: 562,
        columnNumber: 5
    }, this);
}
_s(LanguageProvider, "W8orfgzKvvs5hVi8Wxnq8KXupyo=");
_c = LanguageProvider;
function useLanguage() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(LanguageContext);
    if (context === undefined) {
        throw new Error('useLanguage must be used within a LanguageProvider');
    }
    return context;
}
_s1(useLanguage, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "LanguageProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
/**
 * @license React
 * react-jsx-dev-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
"production" !== ("TURBOPACK compile-time value", "development") && function() {
    function getComponentNameFromType(type) {
        if (null == type) return null;
        if ("function" === typeof type) return type.$$typeof === REACT_CLIENT_REFERENCE ? null : type.displayName || type.name || null;
        if ("string" === typeof type) return type;
        switch(type){
            case REACT_FRAGMENT_TYPE:
                return "Fragment";
            case REACT_PROFILER_TYPE:
                return "Profiler";
            case REACT_STRICT_MODE_TYPE:
                return "StrictMode";
            case REACT_SUSPENSE_TYPE:
                return "Suspense";
            case REACT_SUSPENSE_LIST_TYPE:
                return "SuspenseList";
            case REACT_ACTIVITY_TYPE:
                return "Activity";
        }
        if ("object" === typeof type) switch("number" === typeof type.tag && console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), type.$$typeof){
            case REACT_PORTAL_TYPE:
                return "Portal";
            case REACT_CONTEXT_TYPE:
                return type.displayName || "Context";
            case REACT_CONSUMER_TYPE:
                return (type._context.displayName || "Context") + ".Consumer";
            case REACT_FORWARD_REF_TYPE:
                var innerType = type.render;
                type = type.displayName;
                type || (type = innerType.displayName || innerType.name || "", type = "" !== type ? "ForwardRef(" + type + ")" : "ForwardRef");
                return type;
            case REACT_MEMO_TYPE:
                return innerType = type.displayName || null, null !== innerType ? innerType : getComponentNameFromType(type.type) || "Memo";
            case REACT_LAZY_TYPE:
                innerType = type._payload;
                type = type._init;
                try {
                    return getComponentNameFromType(type(innerType));
                } catch (x) {}
        }
        return null;
    }
    function testStringCoercion(value) {
        return "" + value;
    }
    function checkKeyStringCoercion(value) {
        try {
            testStringCoercion(value);
            var JSCompiler_inline_result = !1;
        } catch (e) {
            JSCompiler_inline_result = !0;
        }
        if (JSCompiler_inline_result) {
            JSCompiler_inline_result = console;
            var JSCompiler_temp_const = JSCompiler_inline_result.error;
            var JSCompiler_inline_result$jscomp$0 = "function" === typeof Symbol && Symbol.toStringTag && value[Symbol.toStringTag] || value.constructor.name || "Object";
            JSCompiler_temp_const.call(JSCompiler_inline_result, "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.", JSCompiler_inline_result$jscomp$0);
            return testStringCoercion(value);
        }
    }
    function getTaskName(type) {
        if (type === REACT_FRAGMENT_TYPE) return "<>";
        if ("object" === typeof type && null !== type && type.$$typeof === REACT_LAZY_TYPE) return "<...>";
        try {
            var name = getComponentNameFromType(type);
            return name ? "<" + name + ">" : "<...>";
        } catch (x) {
            return "<...>";
        }
    }
    function getOwner() {
        var dispatcher = ReactSharedInternals.A;
        return null === dispatcher ? null : dispatcher.getOwner();
    }
    function UnknownOwner() {
        return Error("react-stack-top-frame");
    }
    function hasValidKey(config) {
        if (hasOwnProperty.call(config, "key")) {
            var getter = Object.getOwnPropertyDescriptor(config, "key").get;
            if (getter && getter.isReactWarning) return !1;
        }
        return void 0 !== config.key;
    }
    function defineKeyPropWarningGetter(props, displayName) {
        function warnAboutAccessingKey() {
            specialPropKeyWarningShown || (specialPropKeyWarningShown = !0, console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)", displayName));
        }
        warnAboutAccessingKey.isReactWarning = !0;
        Object.defineProperty(props, "key", {
            get: warnAboutAccessingKey,
            configurable: !0
        });
    }
    function elementRefGetterWithDeprecationWarning() {
        var componentName = getComponentNameFromType(this.type);
        didWarnAboutElementRef[componentName] || (didWarnAboutElementRef[componentName] = !0, console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."));
        componentName = this.props.ref;
        return void 0 !== componentName ? componentName : null;
    }
    function ReactElement(type, key, self, source, owner, props, debugStack, debugTask) {
        self = props.ref;
        type = {
            $$typeof: REACT_ELEMENT_TYPE,
            type: type,
            key: key,
            props: props,
            _owner: owner
        };
        null !== (void 0 !== self ? self : null) ? Object.defineProperty(type, "ref", {
            enumerable: !1,
            get: elementRefGetterWithDeprecationWarning
        }) : Object.defineProperty(type, "ref", {
            enumerable: !1,
            value: null
        });
        type._store = {};
        Object.defineProperty(type._store, "validated", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: 0
        });
        Object.defineProperty(type, "_debugInfo", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: null
        });
        Object.defineProperty(type, "_debugStack", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugStack
        });
        Object.defineProperty(type, "_debugTask", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugTask
        });
        Object.freeze && (Object.freeze(type.props), Object.freeze(type));
        return type;
    }
    function jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, debugStack, debugTask) {
        var children = config.children;
        if (void 0 !== children) if (isStaticChildren) if (isArrayImpl(children)) {
            for(isStaticChildren = 0; isStaticChildren < children.length; isStaticChildren++)validateChildKeys(children[isStaticChildren]);
            Object.freeze && Object.freeze(children);
        } else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
        else validateChildKeys(children);
        if (hasOwnProperty.call(config, "key")) {
            children = getComponentNameFromType(type);
            var keys = Object.keys(config).filter(function(k) {
                return "key" !== k;
            });
            isStaticChildren = 0 < keys.length ? "{key: someKey, " + keys.join(": ..., ") + ": ...}" : "{key: someKey}";
            didWarnAboutKeySpread[children + isStaticChildren] || (keys = 0 < keys.length ? "{" + keys.join(": ..., ") + ": ...}" : "{}", console.error('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />', isStaticChildren, children, keys, children), didWarnAboutKeySpread[children + isStaticChildren] = !0);
        }
        children = null;
        void 0 !== maybeKey && (checkKeyStringCoercion(maybeKey), children = "" + maybeKey);
        hasValidKey(config) && (checkKeyStringCoercion(config.key), children = "" + config.key);
        if ("key" in config) {
            maybeKey = {};
            for(var propName in config)"key" !== propName && (maybeKey[propName] = config[propName]);
        } else maybeKey = config;
        children && defineKeyPropWarningGetter(maybeKey, "function" === typeof type ? type.displayName || type.name || "Unknown" : type);
        return ReactElement(type, children, self, source, getOwner(), maybeKey, debugStack, debugTask);
    }
    function validateChildKeys(node) {
        "object" === typeof node && null !== node && node.$$typeof === REACT_ELEMENT_TYPE && node._store && (node._store.validated = 1);
    }
    var React = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"), REACT_ELEMENT_TYPE = Symbol.for("react.transitional.element"), REACT_PORTAL_TYPE = Symbol.for("react.portal"), REACT_FRAGMENT_TYPE = Symbol.for("react.fragment"), REACT_STRICT_MODE_TYPE = Symbol.for("react.strict_mode"), REACT_PROFILER_TYPE = Symbol.for("react.profiler"), REACT_CONSUMER_TYPE = Symbol.for("react.consumer"), REACT_CONTEXT_TYPE = Symbol.for("react.context"), REACT_FORWARD_REF_TYPE = Symbol.for("react.forward_ref"), REACT_SUSPENSE_TYPE = Symbol.for("react.suspense"), REACT_SUSPENSE_LIST_TYPE = Symbol.for("react.suspense_list"), REACT_MEMO_TYPE = Symbol.for("react.memo"), REACT_LAZY_TYPE = Symbol.for("react.lazy"), REACT_ACTIVITY_TYPE = Symbol.for("react.activity"), REACT_CLIENT_REFERENCE = Symbol.for("react.client.reference"), ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, hasOwnProperty = Object.prototype.hasOwnProperty, isArrayImpl = Array.isArray, createTask = console.createTask ? console.createTask : function() {
        return null;
    };
    React = {
        react_stack_bottom_frame: function(callStackForError) {
            return callStackForError();
        }
    };
    var specialPropKeyWarningShown;
    var didWarnAboutElementRef = {};
    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(React, UnknownOwner)();
    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));
    var didWarnAboutKeySpread = {};
    exports.Fragment = REACT_FRAGMENT_TYPE;
    exports.jsxDEV = function(type, config, maybeKey, isStaticChildren, source, self) {
        var trackActualOwner = 1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;
        return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, trackActualOwner ? Error("react-stack-top-frame") : unknownOwnerDebugStack, trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask);
    };
}();
}}),
"[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
;
else {
    module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)");
}
}}),
}]);

//# sourceMappingURL=_2dfe7ba9._.js.map