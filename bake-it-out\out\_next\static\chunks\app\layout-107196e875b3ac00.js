(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},637:(e,t,o)=>{"use strict";o.d(t,{MultiplayerProvider:()=>u,K:()=>m});var n=o(5155),r=o(2115),s=o(4298),a=o(9509);class c{connect(){var e;null!=(e=this.socket)&&e.connected||(this.socket=(0,s.io)(a.env.NEXT_PUBLIC_SOCKET_URL||"http://localhost:3001",{transports:["websocket","polling"],timeout:2e4,forceNew:!0}),this.setupEventListeners())}setupEventListeners(){this.socket&&(this.socket.on("connect",()=>{console.log("Connected to multiplayer server"),this.isConnected=!0,this.reconnectAttempts=0}),this.socket.on("disconnect",e=>{console.log("Disconnected from multiplayer server:",e),this.isConnected=!1,"io server disconnect"===e&&this.handleReconnect()}),this.socket.on("connect_error",e=>{console.error("Connection error:",e),this.handleReconnect()}),this.socket.on("error",e=>{console.error("Socket error:",e)}))}handleReconnect(){this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,console.log("Attempting to reconnect (".concat(this.reconnectAttempts,"/").concat(this.maxReconnectAttempts,")...")),setTimeout(()=>{this.connect()},1e3*Math.pow(2,this.reconnectAttempts))):console.error("Max reconnection attempts reached")}createRoom(e){return new Promise((t,o)=>{var n;if(!(null==(n=this.socket)?void 0:n.connected))return void o(Error("Not connected to server"));this.socket.emit("create_room",e),this.socket.once("room_created",e=>{this.currentRoom=e,t(e)}),this.socket.once("error",e=>{o(Error(e.message))})})}joinRoom(e,t){return new Promise((o,n)=>{var r;if(!(null==(r=this.socket)?void 0:r.connected))return void n(Error("Not connected to server"));this.socket.emit("join_room",e,t),this.socket.once("room_joined",(e,t)=>{this.currentRoom=e,this.currentPlayer=t,o({room:e,player:t})}),this.socket.once("error",e=>{n(Error(e.message))})})}leaveRoom(){var e;(null==(e=this.socket)?void 0:e.connected)&&this.currentRoom&&(this.socket.emit("leave_room",this.currentRoom.id),this.currentRoom=null,this.currentPlayer=null)}sendPlayerAction(e){var t;(null==(t=this.socket)?void 0:t.connected)&&this.currentPlayer&&this.socket.emit("player_action",{...e,playerId:this.currentPlayer.id,timestamp:Date.now()})}sendMessage(e){var t;(null==(t=this.socket)?void 0:t.connected)&&this.currentPlayer&&this.socket.emit("send_message",{playerId:this.currentPlayer.id,playerName:this.currentPlayer.name,content:e,timestamp:Date.now()})}on(e,t){var o;null==(o=this.socket)||o.on(e,t)}off(e,t){var o;null==(o=this.socket)||o.off(e,t)}once(e,t){var o;null==(o=this.socket)||o.once(e,t)}isSocketConnected(){var e;return this.isConnected&&(null==(e=this.socket)?void 0:e.connected)===!0}getCurrentRoom(){return this.currentRoom}getCurrentPlayer(){return this.currentPlayer}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null,this.isConnected=!1,this.currentRoom=null,this.currentPlayer=null)}constructor(){this.socket=null,this.isConnected=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.currentRoom=null,this.currentPlayer=null,this.connect()}}let l=new c,i=(0,r.createContext)(void 0);function u(e){let{children:t}=e,[o,s]=(0,r.useState)(!1),[a,c]=(0,r.useState)(!1),[u,m]=(0,r.useState)(null),[d,h]=(0,r.useState)(null),[p,y]=(0,r.useState)(null),[f,v]=(0,r.useState)([]),[_,k]=(0,r.useState)("waiting"),[g,C]=(0,r.useState)(null),[P,R]=(0,r.useState)([]);(0,r.useEffect)(()=>{let e=()=>{s(!0),m(null)},t=()=>{s(!1),c(!1),h(null),y(null),v([])},o=e=>{m(e.message||"Connection error"),console.error("Multiplayer error:",e)},n=e=>{h(e),v(e.players),c(!0),k(e.gameState);let t=e.players.find(e=>e.isHost);t&&y(t)},r=(e,t)=>{h(e),y(t),v(e.players),c(!0),k(e.gameState)},a=()=>{h(null),y(null),v([]),c(!1),k("waiting"),C(null),R([])},i=e=>{h(e),v(e.players),k(e.gameState)},u=e=>{v(t=>[...t,e]),b("".concat(e.name," joined the room"))},d=e=>{v(t=>{let o=t.find(t=>t.id===e);return o&&b("".concat(o.name," left the room")),t.filter(t=>t.id!==e)})},p=e=>{k("playing"),C(e),b("Game started!")},f=e=>{C(t=>t?{...t,...e}:null)},_=e=>{console.log("Player action received:",e)},g=e=>{let t={id:Date.now().toString()+Math.random().toString(36).substring(2,11),playerId:e.playerId,playerName:e.playerName,content:e.content,timestamp:e.timestamp};R(e=>[...e,t])};return l.on("connect",e),l.on("disconnect",t),l.on("error",o),l.on("room_created",n),l.on("room_joined",r),l.on("room_left",a),l.on("room_updated",i),l.on("player_joined",u),l.on("player_left",d),l.on("game_started",p),l.on("game_state_update",f),l.on("player_action",_),l.on("message_received",g),s(l.isSocketConnected()),()=>{l.off("connect",e),l.off("disconnect",t),l.off("error",o),l.off("room_created",n),l.off("room_joined",r),l.off("room_left",a),l.off("room_updated",i),l.off("player_joined",u),l.off("player_left",d),l.off("game_started",p),l.off("game_state_update",f),l.off("player_action",_),l.off("message_received",g)}},[]);let b=e=>{let t={id:Date.now().toString()+Math.random().toString(36).substring(2,11),playerId:"system",playerName:"System",content:e,timestamp:Date.now()};R(e=>[...e,t])},S=(0,r.useCallback)(async(e,t)=>{try{m(null),await l.createRoom({...e,hostName:t.name,hostAvatar:t.avatar,hostLevel:t.level})}catch(e){throw m(e.message),e}},[]),w=(0,r.useCallback)(async(e,t)=>{try{m(null);let{room:o,player:n}=await l.joinRoom(e,t)}catch(e){throw m(e.message),e}},[]),N=(0,r.useCallback)(()=>{l.leaveRoom()},[]),A=(0,r.useCallback)(()=>{d&&(null==p?void 0:p.isHost)&&l.sendPlayerAction({type:"start_game",data:{roomId:d.id}})},[d,p]),M=(0,r.useCallback)(e=>{l.sendMessage(e)},[]),E=(0,r.useCallback)(e=>{l.sendPlayerAction(e)},[]),j=(0,r.useCallback)(e=>{p&&E({type:"player_ready",data:{ready:e}})},[p,E]),x=(0,r.useCallback)(e=>{(null==p?void 0:p.isHost)&&E({type:"kick_player",data:{playerId:e}})},[p,E]),I=(0,r.useCallback)(e=>{(null==p?void 0:p.isHost)&&E({type:"update_room_settings",data:{settings:e}})},[p,E]);return(0,n.jsx)(i.Provider,{value:{isConnected:o,isInRoom:a,connectionError:u,currentRoom:d,currentPlayer:p,players:f,gameState:_,sharedGameState:g,messages:P,createRoom:S,joinRoom:w,leaveRoom:N,startGame:A,sendMessage:M,sendPlayerAction:E,setPlayerReady:j,kickPlayer:x,updateRoomSettings:I},children:t})}function m(){let e=(0,r.useContext)(i);return void 0===e?(console.warn("useMultiplayer called outside of MultiplayerProvider, using fallback"),{isConnected:!1,connectionStatus:"disconnected",currentRoom:null,gameState:null,createRoom:async()=>{},joinRoom:async()=>{},leaveRoom:()=>{},sendChatMessage:()=>{},updateGameState:()=>{},setPlayerReady:()=>{}}):e}},2093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},5544:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,2093,23)),Promise.resolve().then(o.t.bind(o,7735,23)),Promise.resolve().then(o.t.bind(o,347,23)),Promise.resolve().then(o.bind(o,9283)),Promise.resolve().then(o.bind(o,637))},7735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}}},e=>{e.O(0,[360,298,283,441,964,358],()=>e(e.s=5544)),_N_E=e.O()}]);