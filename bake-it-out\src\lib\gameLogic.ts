// Game logic and data structures

export interface Recipe {
  id: string
  name: string
  ingredients: { name: string; quantity: number }[]
  bakingTime: number // in seconds
  difficulty: number // 1-5
  unlockLevel: number
  basePrice: number
  category: 'cookies' | 'cakes' | 'bread' | 'pastries'
}

export const RECIPES: Recipe[] = [
  {
    id: 'chocolate_chip_cookies',
    name: 'Chocolate Chip Cookies',
    ingredients: [
      { name: 'Flour', quantity: 2 },
      { name: 'Sugar', quantity: 1 },
      { name: 'Butter', quantity: 1 },
      { name: 'Chocolate Chips', quantity: 1 }
    ],
    bakingTime: 45,
    difficulty: 1,
    unlockLevel: 1,
    basePrice: 25,
    category: 'cookies'
  },
  {
    id: 'vanilla_muffins',
    name: 'Vanilla Muffins',
    ingredients: [
      { name: 'Flour', quantity: 2 },
      { name: 'Sugar', quantity: 1 },
      { name: 'Eggs', quantity: 1 },
      { name: 'Vanilla', quantity: 1 }
    ],
    bakingTime: 60,
    difficulty: 1,
    unlockLevel: 1,
    basePrice: 20,
    category: 'cakes'
  },
  {
    id: 'cinnamon_rolls',
    name: 'Cinnamon Rolls',
    ingredients: [
      { name: 'Flour', quantity: 3 },
      { name: 'Sugar', quantity: 2 },
      { name: 'Butter', quantity: 2 },
      { name: 'Eggs', quantity: 1 }
    ],
    bakingTime: 90,
    difficulty: 2,
    unlockLevel: 2,
    basePrice: 35,
    category: 'pastries'
  },
  {
    id: 'chocolate_brownies',
    name: 'Chocolate Brownies',
    ingredients: [
      { name: 'Flour', quantity: 2 },
      { name: 'Sugar', quantity: 2 },
      { name: 'Butter', quantity: 1 },
      { name: 'Chocolate Chips', quantity: 2 }
    ],
    bakingTime: 75,
    difficulty: 2,
    unlockLevel: 2,
    basePrice: 30,
    category: 'cakes'
  },
  {
    id: 'sourdough_bread',
    name: 'Sourdough Bread',
    ingredients: [
      { name: 'Flour', quantity: 4 },
      { name: 'Salt', quantity: 1 }
    ],
    bakingTime: 180,
    difficulty: 3,
    unlockLevel: 3,
    basePrice: 45,
    category: 'bread'
  }
]

export const CUSTOMER_NAMES = [
  'Alice Johnson', 'Bob Smith', 'Carol Davis', 'David Wilson',
  'Emma Brown', 'Frank Miller', 'Grace Taylor', 'Henry Anderson',
  'Ivy Thomas', 'Jack Martinez', 'Kate Garcia', 'Liam Rodriguez',
  'Mia Lopez', 'Noah Gonzalez', 'Olivia Hernandez', 'Paul Perez',
  'Quinn Turner', 'Ruby Phillips', 'Sam Campbell', 'Tina Parker'
]

export function generateRandomOrder(playerLevel: number): {
  id: string
  customerName: string
  items: string[]
  timeLimit: number
  reward: number
  status: 'pending'
  difficulty: number
} {
  // Filter recipes based on player level
  const availableRecipes = RECIPES.filter(recipe => recipe.unlockLevel <= playerLevel)
  
  if (availableRecipes.length === 0) {
    // Fallback to basic recipe
    availableRecipes.push(RECIPES[0])
  }

  // Select random recipe(s)
  const numItems = Math.random() < 0.7 ? 1 : Math.random() < 0.9 ? 2 : 3
  const selectedRecipes: Recipe[] = []
  
  for (let i = 0; i < numItems; i++) {
    const recipe = availableRecipes[Math.floor(Math.random() * availableRecipes.length)]
    selectedRecipes.push(recipe)
  }

  // Calculate order properties
  const totalDifficulty = selectedRecipes.reduce((sum, recipe) => sum + recipe.difficulty, 0)
  const avgDifficulty = Math.ceil(totalDifficulty / selectedRecipes.length)
  const totalBasePrice = selectedRecipes.reduce((sum, recipe) => sum + recipe.basePrice, 0)
  
  // Add some randomness to price and time
  const priceMultiplier = 0.8 + Math.random() * 0.4 // 0.8 to 1.2
  const timeMultiplier = 1.5 + Math.random() * 1.0 // 1.5 to 2.5
  
  const reward = Math.floor(totalBasePrice * priceMultiplier)
  const baseTime = selectedRecipes.reduce((sum, recipe) => sum + recipe.bakingTime, 0)
  const timeLimit = Math.floor(baseTime * timeMultiplier)

  return {
    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
    customerName: CUSTOMER_NAMES[Math.floor(Math.random() * CUSTOMER_NAMES.length)],
    items: selectedRecipes.map(recipe => recipe.name),
    timeLimit,
    reward,
    status: 'pending',
    difficulty: Math.min(5, avgDifficulty)
  }
}

export function calculateExperienceReward(difficulty: number, timeBonus: boolean = false): number {
  const baseExp = difficulty * 10
  const bonus = timeBonus ? Math.floor(baseExp * 0.5) : 0
  return baseExp + bonus
}

export function calculateLevelRequirement(level: number): number {
  return level * 100 + (level - 1) * 50
}

export function canCraftRecipe(recipe: Recipe, inventory: { name: string; quantity: number }[]): boolean {
  return recipe.ingredients.every(ingredient => {
    const inventoryItem = inventory.find(item => item.name === ingredient.name)
    return inventoryItem && inventoryItem.quantity >= ingredient.quantity
  })
}

export function getRecipeById(id: string): Recipe | undefined {
  return RECIPES.find(recipe => recipe.id === id)
}

export function getAvailableRecipes(playerLevel: number): Recipe[] {
  return RECIPES.filter(recipe => recipe.unlockLevel <= playerLevel)
}
