"use strict";exports.id=181,exports.ids=[181],exports.modules={2643:(a,b,c)=>{c.d(b,{$:()=>e});var d=c(687);c(3210);let e=({variant:a="primary",size:b="md",className:c="",children:e,...f})=>{let g=["font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2",{primary:"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500",secondary:"bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500",danger:"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500",success:"bg-green-600 hover:bg-green-700 text-white focus:ring-green-500"}[a],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"}[b],c].join(" ");return(0,d.jsx)("button",{className:g,...f,children:e})}},3211:(a,b,c)=>{c.d(b,{$:()=>f});var d=c(687),e=c(4393);function f({equipment:a,onClick:b}){let{t:c}=(0,e.o)();return(0,d.jsx)("div",{className:`p-4 rounded-lg border-2 cursor-pointer transition-all ${a.isActive?"border-green-400 bg-green-50":"border-gray-200 bg-gray-50 hover:border-orange-300 hover:bg-orange-50"}`,onClick:()=>!a.isActive&&b(a.id,a.name),children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl mb-2",children:(a=>{switch(a){case"oven":return"\uD83D\uDD25";case"mixer":return"\uD83E\uDD44";case"counter":return"\uD83C\uDF7D️";default:return"⚙️"}})(a.type)}),(0,d.jsx)("h3",{className:"font-medium text-gray-800",children:a.name}),(0,d.jsxs)("div",{className:"text-xs text-gray-500",children:["Level ",a.level]}),a.isActive&&a.timeRemaining?(0,d.jsxs)("div",{className:"mt-2",children:[(0,d.jsx)("div",{className:"text-sm text-green-600",children:c("kitchen.making",{recipe:a.currentRecipe||""})}),(0,d.jsx)("div",{className:"text-lg font-mono text-green-700",children:(a=>{let b=Math.floor(a/60);return`${b}:${(a%60).toString().padStart(2,"0")}`})(a.timeRemaining)}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,d.jsx)("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-1000",style:{width:`${100-a.timeRemaining/60*100}%`}})})]}):(0,d.jsx)("div",{className:"text-sm text-gray-500 mt-2",children:a.isActive?"Busy":c("kitchen.clickToUse")})]})})}},4579:(a,b,c)=>{c.d(b,{p:()=>g});var d=c(687),e=c(2643),f=c(4393);function g({order:a,onAccept:b,onDecline:c,onComplete:g}){let{t:h}=(0,f.o)();return(0,d.jsxs)("div",{className:`p-4 rounded-lg border ${(()=>{switch(a.status){case"pending":return"border-yellow-300 bg-yellow-50";case"accepted":case"in_progress":return"border-blue-300 bg-blue-50";case"completed":return"border-green-300 bg-green-50";case"failed":return"border-red-300 bg-red-50";default:return"border-gray-300 bg-gray-50"}})()}`,children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-lg",children:(()=>{let b=["\uD83D\uDC69","\uD83D\uDC68","\uD83D\uDC75","\uD83D\uDC74","\uD83D\uDC67","\uD83D\uDC66"],c=a.customerName.length%b.length;return b[c]})()}),(0,d.jsx)("h3",{className:"font-medium text-gray-800",children:a.customerName})]}),(0,d.jsx)("span",{className:"text-sm font-semibold text-green-600",children:h("orders.reward",{amount:a.reward.toString()})})]}),(0,d.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:a.items.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)("span",{children:"\uD83E\uDDC1"}),(0,d.jsx)("span",{children:a})]},b))}),(0,d.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,d.jsxs)("div",{className:"text-xs text-gray-500",children:["⏰ ",h("orders.timeLimit",{time:(a=>{let b=Math.floor(a/60);return`${b}:${(a%60).toString().padStart(2,"0")}`})(a.timeLimit)})]}),(0,d.jsx)("div",{className:"text-xs",title:`Difficulty: ${a.difficulty}/5`,children:"⭐".repeat(a.difficulty)+"☆".repeat(5-a.difficulty)})]}),"pending"===a.status&&(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsxs)(e.$,{size:"sm",variant:"success",onClick:()=>b(a.id),className:"flex-1",children:["✅ ",h("orders.accept")]}),(0,d.jsxs)(e.$,{size:"sm",variant:"danger",onClick:()=>c(a.id),className:"flex-1",children:["❌ ",h("orders.decline")]})]}),"accepted"===a.status&&(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-blue-600 text-sm font-medium mb-2",children:"\uD83D\uDCCB Order Accepted"}),(0,d.jsxs)(e.$,{size:"sm",variant:"primary",onClick:()=>g&&g(a.id),className:"w-full",children:["\uD83C\uDFAF ",h("orders.complete")]})]}),"in_progress"===a.status&&(0,d.jsxs)("div",{className:"text-center text-orange-600 text-sm font-medium",children:["\uD83D\uDD04 ",h("orders.inProgress")]}),"completed"===a.status&&(0,d.jsx)("div",{className:"text-center text-green-600 text-sm font-medium",children:"✅ Completed!"}),"failed"===a.status&&(0,d.jsx)("div",{className:"text-center text-red-600 text-sm font-medium",children:"❌ Failed"})]})}},5745:(a,b,c)=>{c.d(b,{b:()=>i});var d=c(687),e=c(3210),f=c(2643),g=c(4393),h=c(6005);function i({isOpen:a,onClose:b,settings:c,onSettingsChange:i}){let{language:j,setLanguage:k,t:l}=(0,g.o)(),[m,n]=(0,e.useState)("general");if(!a)return null;let o=(a,b)=>{i({[a]:b})},p=a=>{k(a),o("language",a)},q=[{id:"general",name:l("settings.general")||"General",icon:"⚙️"},{id:"audio",name:l("settings.audio")||"Audio",icon:"\uD83D\uDD0A"},{id:"graphics",name:l("settings.graphics")||"Graphics",icon:"\uD83C\uDFA8"},{id:"save",name:l("settings.save")||"Save & Data",icon:"\uD83D\uDCBE"}];return(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:l("settings.title")||"⚙️ Settings"}),(0,d.jsx)(f.$,{variant:"secondary",onClick:b,children:l("game.close")||"✕ Close"})]})}),(0,d.jsx)("div",{className:"border-b border-gray-200",children:(0,d.jsx)("div",{className:"flex space-x-0",children:q.map(a=>(0,d.jsxs)("button",{onClick:()=>n(a.id),className:`px-4 py-3 font-medium text-sm border-b-2 transition-colors ${m===a.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"}`,children:[a.icon," ",a.name]},a.id))})}),(0,d.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["general"===m&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:l("settings.language")||"\uD83C\uDF0D Language"}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(f.$,{variant:"en"===j?"primary":"secondary",size:"sm",onClick:()=>p("en"),children:"\uD83C\uDDFA\uD83C\uDDF8 English"}),(0,d.jsx)(f.$,{variant:"cs"===j?"primary":"secondary",size:"sm",onClick:()=>p("cs"),children:"\uD83C\uDDE8\uD83C\uDDFF Čeština"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:l("settings.gameplay")||"\uD83C\uDFAE Gameplay"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("label",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{children:l("settings.notifications")||"Enable Notifications"}),(0,d.jsx)("input",{type:"checkbox",checked:c.notificationsEnabled,onChange:a=>o("notificationsEnabled",a.target.checked),className:"rounded"})]}),(0,d.jsxs)("label",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{children:l("settings.tutorials")||"Show Tutorials"}),(0,d.jsx)("input",{type:"checkbox",checked:c.showTutorials,onChange:a=>o("showTutorials",a.target.checked),className:"rounded"})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[l("settings.animationSpeed")||"Animation Speed",": ",c.animationSpeed,"x"]}),(0,d.jsx)("input",{type:"range",min:"0.5",max:"2",step:"0.1",value:c.animationSpeed,onChange:a=>o("animationSpeed",parseFloat(a.target.value)),className:"w-full"})]})]})]})]}),"audio"===m&&(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("label",{className:"flex items-center justify-between",children:[(0,d.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{children:"\uD83D\uDD0A"}),(0,d.jsx)("span",{children:l("settings.sound")||"Sound Effects"})]}),(0,d.jsx)("input",{type:"checkbox",checked:c.soundEnabled,onChange:a=>o("soundEnabled",a.target.checked),className:"rounded"})]}),(0,d.jsxs)("label",{className:"flex items-center justify-between",children:[(0,d.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{children:"\uD83C\uDFB5"}),(0,d.jsx)("span",{children:l("settings.music")||"Background Music"})]}),(0,d.jsx)("input",{type:"checkbox",checked:c.musicEnabled,onChange:a=>o("musicEnabled",a.target.checked),className:"rounded"})]})]})}),"graphics"===m&&(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:l("settings.quality")||"\uD83C\uDFA8 Graphics Quality"}),(0,d.jsx)("div",{className:"space-x-2",children:["low","medium","high"].map(a=>(0,d.jsx)(f.$,{variant:c.graphicsQuality===a?"primary":"secondary",size:"sm",onClick:()=>o("graphicsQuality",a),children:a.charAt(0).toUpperCase()+a.slice(1)},a))})]})}),"save"===m&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:l("settings.autoSave")||"\uD83D\uDCBE Auto-Save"}),(0,d.jsxs)("label",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{children:l("settings.enableAutoSave")||"Enable Auto-Save"}),(0,d.jsx)("input",{type:"checkbox",checked:c.autoSaveEnabled,onChange:a=>o("autoSaveEnabled",a.target.checked),className:"rounded"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:l("settings.dataManagement")||"\uD83D\uDCC1 Data Management"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)(f.$,{variant:"secondary",onClick:()=>{let a={version:"1.0.0",timestamp:Date.now(),player:{},equipment:[],inventory:[],achievements:[],skills:[],automationSettings:{},gameSettings:c,bakeries:[],currentBakeryId:"main"},b=new Blob([h.B.exportSave(a)],{type:"application/json"}),d=URL.createObjectURL(b),e=document.createElement("a");e.href=d,e.download=`bake-it-out-save-${Date.now()}.json`,e.click(),URL.revokeObjectURL(d)},className:"w-full",children:l("settings.exportSave")||"\uD83D\uDCE4 Export Save"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("input",{type:"file",accept:".json",onChange:a=>{let b=a.target.files?.[0];if(!b)return;let c=new FileReader;c.onload=a=>{try{let b=a.target?.result,c=h.B.importSave(b);c?(console.log("Save imported successfully:",c),alert("Save imported successfully!")):alert("Failed to import save file")}catch(a){alert("Invalid save file")}},c.readAsText(b)},className:"hidden",id:"import-save"}),(0,d.jsx)(f.$,{variant:"secondary",onClick:()=>document.getElementById("import-save")?.click(),className:"w-full",children:l("settings.importSave")||"\uD83D\uDCE5 Import Save"})]})]})]}),(0,d.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,d.jsx)("h4",{className:"font-medium text-yellow-800 mb-2",children:l("settings.cloudSync")||"☁️ Cloud Sync"}),(0,d.jsx)("p",{className:"text-sm text-yellow-700 mb-3",children:l("settings.cloudSyncDescription")||"Cloud sync allows you to save your progress online and play across multiple devices."}),(0,d.jsx)(f.$,{variant:"secondary",size:"sm",disabled:!0,children:l("settings.comingSoon")||"Coming Soon"})]})]})]})]})})}},6005:(a,b,c)=>{c.d(b,{B:()=>g});let d="1.0.0",e="bakeItOut_gameSave";class f{constructor(){this.autoSaveInterval=null,this.cloudSyncEnabled=!1,this.initializeAutoSave()}saveToLocal(a){try{let b={version:d,timestamp:Date.now(),...a},c=JSON.stringify(b);return localStorage.setItem(e,c),console.log("Game saved to local storage"),!0}catch(a){return console.error("Failed to save game to local storage:",a),!1}}loadFromLocal(){try{let a=localStorage.getItem(e);if(!a)return null;let b=JSON.parse(a);if(b.version!==d)return console.warn("Save version mismatch, attempting migration"),this.migrateSave(b);return console.log("Game loaded from local storage"),b}catch(a){return console.error("Failed to load game from local storage:",a),null}}deleteLocalSave(){try{return localStorage.removeItem(e),console.log("Local save deleted"),!0}catch(a){return console.error("Failed to delete local save:",a),!1}}initializeAutoSave(){}triggerAutoSave(){let a=new CustomEvent("autoSave");window.dispatchEvent(a)}stopAutoSave(){this.autoSaveInterval&&(clearInterval(this.autoSaveInterval),this.autoSaveInterval=null)}async saveToCloud(a,b){try{let c={id:`${b}_${Date.now()}`,userId:b,deviceId:this.getDeviceId(),lastModified:Date.now(),gameVersion:d,bakeryCount:a.bakeries?.length||1,playerLevel:a.player.level};return console.log("Cloud save would be implemented here",{gameData:a,metadata:c}),!0}catch(a){return console.error("Failed to save to cloud:",a),!1}}async loadFromCloud(a){try{return console.log("Cloud load would be implemented here",{userId:a}),null}catch(a){return console.error("Failed to load from cloud:",a),null}}async syncWithCloud(a,b){try{let c=await this.loadFromCloud(b);if(!c)return await this.saveToCloud(a,b),a;if(c.timestamp>a.timestamp)return console.log("Cloud save is newer, using cloud data"),c;return console.log("Local save is newer, uploading to cloud"),await this.saveToCloud(a,b),a}catch(b){return console.error("Failed to sync with cloud:",b),a}}migrateSave(a){try{let b={version:d,timestamp:a.timestamp||Date.now(),player:{level:a.player?.level||1,experience:a.player?.experience||0,money:a.player?.money||100,skillPoints:a.player?.skillPoints||0,totalMoneyEarned:a.player?.totalMoneyEarned||0,totalOrdersCompleted:a.player?.totalOrdersCompleted||0,totalItemsBaked:a.player?.totalItemsBaked||0,unlockedRecipes:a.player?.unlockedRecipes||["chocolate_chip_cookies","vanilla_muffins"],automationUpgrades:a.player?.automationUpgrades||[]},equipment:a.equipment||[],inventory:a.inventory||[],achievements:a.achievements||[],skills:a.skills||[],automationSettings:a.automationSettings||{},gameSettings:{language:a.gameSettings?.language||"en",soundEnabled:a.gameSettings?.soundEnabled??!0,musicEnabled:a.gameSettings?.musicEnabled??!0,notificationsEnabled:a.gameSettings?.notificationsEnabled??!0,autoSaveEnabled:a.gameSettings?.autoSaveEnabled??!0},bakeries:a.bakeries||[],currentBakeryId:a.currentBakeryId||"main"};return console.log("Save migrated successfully"),b}catch(a){return console.error("Failed to migrate save:",a),null}}getDeviceId(){let a=localStorage.getItem("deviceId");return a||(a="device_"+Date.now()+"_"+Math.random().toString(36).substr(2,9),localStorage.setItem("deviceId",a)),a}exportSave(a){return JSON.stringify(a,null,2)}importSave(a){try{let b=JSON.parse(a);return this.migrateSave(b)}catch(a){return console.error("Failed to import save:",a),null}}createBackup(a){try{let b=`${e}_backup_${Date.now()}`;return localStorage.setItem(b,JSON.stringify(a)),this.cleanupOldBackups(),!0}catch(a){return console.error("Failed to create backup:",a),!1}}cleanupOldBackups(){let a=Object.keys(localStorage).filter(a=>a.startsWith(`${e}_backup_`)).sort();for(;a.length>5;){let b=a.shift();b&&localStorage.removeItem(b)}}getBackups(){let a=[];return Object.keys(localStorage).forEach(b=>{if(b.startsWith(`${e}_backup_`))try{let c=JSON.parse(localStorage.getItem(b)||"{}"),d=parseInt(b.split("_").pop()||"0");a.push({key:b,timestamp:d,data:c})}catch(a){console.error("Failed to parse backup:",a)}}),a.sort((a,b)=>b.timestamp-a.timestamp)}}let g=new f}};