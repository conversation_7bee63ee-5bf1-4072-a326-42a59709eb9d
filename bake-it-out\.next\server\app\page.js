(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},346:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,1204)),"C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\app\\page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,4431)),"C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\app\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1204:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\coding stuff\\\\bake it out\\\\bake-it-out\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\app\\page.tsx","default")},1630:a=>{"use strict";a.exports=require("http")},1645:a=>{"use strict";a.exports=require("net")},1820:a=>{"use strict";a.exports=require("os")},1968:(a,b)=>{"use strict";function c(a){return a.split("/").map(a=>encodeURIComponent(a)).join("/")}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"encodeURIPath",{enumerable:!0,get:function(){return c}})},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:a=>{"use strict";a.exports=require("path")},3997:a=>{"use strict";a.exports=require("tty")},4075:a=>{"use strict";a.exports=require("zlib")},4631:a=>{"use strict";a.exports=require("tls")},4735:a=>{"use strict";a.exports=require("events")},4777:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"PreloadChunks",{enumerable:!0,get:function(){return h}});let d=c(687),e=c(1215),f=c(9294),g=c(1968);function h(a){let{moduleIds:b}=a,c=f.workAsyncStorage.getStore();if(void 0===c)return null;let h=[];if(c.reactLoadableManifest&&b){let a=c.reactLoadableManifest;for(let c of b){if(!a[c])continue;let b=a[c].files;h.push(...b)}}return 0===h.length?null:(0,d.jsx)(d.Fragment,{children:h.map(a=>{let b=c.assetPrefix+"/_next/"+(0,g.encodeURIPath)(a);return a.endsWith(".css")?(0,d.jsx)("link",{precedence:"dynamic",href:b,rel:"stylesheet",as:"style"},a):((0,e.preload)(b,{as:"script",fetchPriority:"low"}),null)})})}},4963:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return j}});let d=c(687),e=c(3210),f=c(6780),g=c(4777);function h(a){return{default:a&&"default"in a?a.default:a}}let i={loader:()=>Promise.resolve(h(()=>null)),loading:null,ssr:!0},j=function(a){let b={...i,...a},c=(0,e.lazy)(()=>b.loader().then(h)),j=b.loading;function k(a){let h=j?(0,d.jsx)(j,{isLoading:!0,pastDelay:!0,error:null}):null,i=!b.ssr||!!b.loading,k=i?e.Suspense:e.Fragment,l=b.ssr?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g.PreloadChunks,{moduleIds:b.modules}),(0,d.jsx)(c,{...a})]}):(0,d.jsx)(f.BailoutToCSR,{reason:"next/dynamic",children:(0,d.jsx)(c,{...a})});return(0,d.jsx)(k,{...i?{fallback:h}:{},children:l})}return k.displayName="LoadableComponent",k}},5439:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>r});var d=c(687),e=c(3210),f=c(4393),g=c(2643),h=c(985);function i({isOpen:a,onClose:b}){let{t:c}=(0,f.o)(),{isConnected:i,isInRoom:j,connectionError:k,currentRoom:l,currentPlayer:m,players:n,gameState:o,messages:p,createRoom:q,joinRoom:r,leaveRoom:s,startGame:t,sendMessage:u,setPlayerReady:v}=(0,h.K)(),[w,x]=(0,e.useState)("create"),[y,z]=(0,e.useState)(""),[A,B]=(0,e.useState)(""),[C,D]=(0,e.useState)(""),[E,F]=(0,e.useState)("cooperative"),[G,H]=(0,e.useState)(4),[I,J]=(0,e.useState)(""),[K,L]=(0,e.useState)(!1);if(!a)return null;let M=async()=>{if(A.trim()&&y.trim())try{await q({name:y,mode:E,maxPlayers:G,settings:{gameMode:E,difficulty:"medium",allowSpectators:!0}},{name:A,avatar:"\uD83D\uDC68‍\uD83C\uDF73",level:1}),x("room")}catch(a){console.error("Failed to create room:",a)}},N=async()=>{if(A.trim()&&C.trim())try{await r(C.toUpperCase(),{name:A,avatar:"\uD83D\uDC68‍\uD83C\uDF73",level:1}),x("room")}catch(a){console.error("Failed to join room:",a)}},O=()=>{I.trim()&&(u(I),J(""))},P=[{id:"create",name:c("multiplayer.createRoom"),icon:"\uD83C\uDFD7️"},{id:"join",name:c("multiplayer.joinRoom"),icon:"\uD83D\uDEAA"},...j?[{id:"room",name:c("multiplayer.room"),icon:"\uD83C\uDFE0"}]:[]];return(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,d.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:c("multiplayer.lobby")}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:`px-3 py-1 rounded-full text-sm ${i?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:i?c("multiplayer.connected"):c("multiplayer.disconnected")}),(0,d.jsx)(g.$,{variant:"secondary",onClick:b,children:c("game.close")})]})]}),k&&(0,d.jsx)("div",{className:"mt-2 p-2 bg-red-100 text-red-800 rounded text-sm",children:c("multiplayer.connection.error",{error:k})})]}),(0,d.jsx)("div",{className:"border-b border-gray-200",children:(0,d.jsx)("div",{className:"flex space-x-0",children:P.map(a=>(0,d.jsxs)("button",{onClick:()=>x(a.id),className:`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${w===a.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"}`,children:[a.icon," ",a.name]},a.id))})}),(0,d.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["create"===w&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("multiplayer.yourName")}),(0,d.jsx)("input",{type:"text",value:A,onChange:a=>B(a.target.value),placeholder:c("multiplayer.enterName"),className:"w-full p-3 border rounded-lg",maxLength:20})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("multiplayer.roomName")}),(0,d.jsx)("input",{type:"text",value:y,onChange:a=>z(a.target.value),placeholder:c("multiplayer.enterRoomName"),className:"w-full p-3 border rounded-lg",maxLength:30})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("multiplayer.gameMode")}),(0,d.jsxs)("select",{value:E,onChange:a=>F(a.target.value),className:"w-full p-3 border rounded-lg",children:[(0,d.jsx)("option",{value:"cooperative",children:c("multiplayer.cooperative")}),(0,d.jsx)("option",{value:"competitive",children:c("multiplayer.competitive")})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("multiplayer.maxPlayers",{count:G.toString()})}),(0,d.jsx)("input",{type:"range",min:"2",max:"8",value:G,onChange:a=>H(parseInt(a.target.value)),className:"w-full"})]})]}),(0,d.jsx)(g.$,{variant:"primary",size:"lg",className:"w-full",onClick:M,disabled:!i||!A.trim()||!y.trim(),children:c("multiplayer.create.title")})]}),"join"===w&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("multiplayer.yourName")}),(0,d.jsx)("input",{type:"text",value:A,onChange:a=>B(a.target.value),placeholder:c("multiplayer.enterName"),className:"w-full p-3 border rounded-lg",maxLength:20})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("multiplayer.roomId")}),(0,d.jsx)("input",{type:"text",value:C,onChange:a=>D(a.target.value.toUpperCase()),placeholder:c("multiplayer.enterRoomId"),className:"w-full p-3 border rounded-lg font-mono",maxLength:6})]})]}),(0,d.jsx)(g.$,{variant:"primary",size:"lg",className:"w-full",onClick:N,disabled:!i||!A.trim()||!C.trim(),children:c("multiplayer.join.title")})]}),"room"===w&&l&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,d.jsx)("h3",{className:"font-semibold text-blue-800",children:l.name}),(0,d.jsxs)("div",{className:"text-sm text-blue-600",children:[c("multiplayer.roomId"),": ",(0,d.jsx)("span",{className:"font-mono font-bold",children:l.id})]})]}),(0,d.jsx)("div",{className:"text-sm text-blue-700",children:c("multiplayer.room.info",{mode:l.mode,current:l.currentPlayers.toString(),max:l.maxPlayers.toString()})})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:c("multiplayer.players",{count:n.length.toString()})}),(0,d.jsx)("div",{className:"space-y-2",children:n.map(a=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("span",{className:"text-2xl",children:a.avatar}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"font-medium",children:[a.name,a.isHost&&(0,d.jsx)("span",{className:"ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded",children:c("multiplayer.host")})]}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:c("multiplayer.level",{level:a.level.toString()})})]})]}),(0,d.jsx)("div",{className:`px-2 py-1 rounded text-xs ${a.isReady?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"}`,children:a.isReady?c("common.ready"):c("common.notReady")})]},a.id))})]}),(0,d.jsxs)("div",{className:"flex space-x-3",children:[(0,d.jsx)(g.$,{variant:K?"success":"secondary",onClick:()=>{let a=!K;L(a),v(a)},className:"flex-1",children:K?c("multiplayer.room.readyUp"):c("multiplayer.room.notReady")}),m?.isHost&&(0,d.jsx)(g.$,{variant:"primary",onClick:()=>{m?.isHost&&t()},disabled:!n.every(a=>a.isReady)||n.length<2,children:c("multiplayer.room.startGame")}),(0,d.jsx)(g.$,{variant:"secondary",onClick:()=>{s(),x("create"),L(!1)},children:c("multiplayer.room.leaveRoom")})]}),(0,d.jsxs)("div",{className:"border-t pt-4",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:c("multiplayer.chat")}),(0,d.jsx)("div",{className:"bg-gray-50 p-3 rounded-lg h-32 overflow-y-auto mb-3",children:p.map(a=>(0,d.jsxs)("div",{className:"text-sm mb-1",children:[(0,d.jsxs)("span",{className:`font-medium ${"system"===a.playerId?"text-blue-600":"text-gray-800"}`,children:[a.playerName,":"]}),(0,d.jsx)("span",{className:"ml-2",children:a.content})]},a.id))}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)("input",{type:"text",value:I,onChange:a=>J(a.target.value),onKeyDown:a=>"Enter"===a.key&&O(),placeholder:c("multiplayer.typeMessage"),className:"flex-1 p-2 border rounded",maxLength:100}),(0,d.jsx)(g.$,{size:"sm",onClick:O,children:c("common.send")})]})]})]})]})]})})}var j=c(3211),k=c(4579);function l({isOpen:a,onClose:b}){let{t:c}=(0,f.o)(),{currentRoom:i,currentPlayer:l,players:m,gameState:n,sharedGameState:o,sendPlayerAction:p,leaveRoom:q}=(0,h.K)(),[r,s]=(0,e.useState)("game");if(!a||!i||"playing"!==n)return null;let t=(a,b)=>{p({type:"use_equipment",data:{equipmentId:a,equipmentName:b,playerId:l?.id}})},u=(a,b)=>{p({type:"order_action",data:{orderId:a,action:b,playerId:l?.id}})},v=[{id:"game",name:c("multiplayer.game.tabs.game"),icon:"\uD83C\uDFAE"},{id:"players",name:c("multiplayer.game.tabs.players"),icon:"\uD83D\uDC65"},{id:"chat",name:c("multiplayer.game.tabs.chat"),icon:"\uD83D\uDCAC"}];return(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:c("multiplayer.game.title",{roomName:i.name})}),(0,d.jsxs)("p",{className:"text-gray-600",children:[c("multiplayer.game.mode",{mode:i.mode})," • ",c("multiplayer.game.playersCount",{count:m.length.toString()})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,d.jsx)("span",{className:"text-green-800 text-sm",children:c("multiplayer.game.playing")})}),(0,d.jsx)(g.$,{variant:"secondary",onClick:()=>{q(),b()},children:c("multiplayer.game.leaveGame")})]})]})}),(0,d.jsx)("div",{className:"border-b border-gray-200",children:(0,d.jsx)("div",{className:"flex space-x-0",children:v.map(a=>(0,d.jsxs)("button",{onClick:()=>s(a.id),className:`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${r===a.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"}`,children:[a.icon," ",a.name]},a.id))})}),(0,d.jsxs)("div",{className:"p-6 max-h-[70vh] overflow-y-auto",children:["game"===r&&(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{className:"lg:col-span-2",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,d.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:c("multiplayer.sharedKitchen")}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[{id:"oven1",name:"Shared Oven",type:"oven",isActive:!1,level:1,efficiency:1,automationLevel:0},{id:"mixer1",name:"Shared Mixer",type:"mixer",isActive:!1,level:1,efficiency:1,automationLevel:0},{id:"counter1",name:"Shared Counter",type:"counter",isActive:!1,level:1,efficiency:1,automationLevel:0}].map(a=>(0,d.jsx)(j.$,{equipment:a,onClick:t},a.id))})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mt-6",children:[(0,d.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:c("multiplayer.sharedOrders")}),(0,d.jsx)("div",{className:"space-y-4",children:[{id:"1",customerName:"Shared Customer",items:["Chocolate Chip Cookies"],timeLimit:300,reward:50,status:"pending",difficulty:1}].map(a=>(0,d.jsx)(k.p,{order:a,onAccept:a=>u(a,"accept"),onDecline:a=>u(a,"decline"),onComplete:a=>u(a,"complete")},a.id))})]})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,d.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:c("multiplayer.sharedInventory")}),(0,d.jsx)("div",{className:"space-y-3",children:[{name:"Flour",quantity:20,cost:2},{name:"Sugar",quantity:15,cost:3},{name:"Eggs",quantity:12,cost:4}].map((a,b)=>(0,d.jsx)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium text-gray-800",children:a.name}),(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:["Qty: ",a.quantity]})]})},b))})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,d.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:c("multiplayer.teamStats")}),(0,d.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:c("multiplayer.ordersCompleted")}),(0,d.jsx)("span",{className:"font-medium",children:"0"})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:c("multiplayer.totalRevenue")}),(0,d.jsx)("span",{className:"font-medium",children:"$0"})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:c("multiplayer.teamExperience")}),(0,d.jsx)("span",{className:"font-medium",children:"0 XP"})]})]})]})]})]}),"players"===r&&(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"text-xl font-semibold text-orange-800",children:c("multiplayer.players",{count:m.length.toString()})}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:m.map(a=>(0,d.jsxs)("div",{className:`p-4 rounded-lg border-2 ${a.id===l?.id?"border-orange-400 bg-orange-50":"border-gray-300 bg-white"}`,children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,d.jsx)("span",{className:"text-3xl",children:a.avatar}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("h4",{className:"font-semibold text-gray-800",children:[a.name,a.id===l?.id&&(0,d.jsx)("span",{className:"ml-2 text-sm text-orange-600",children:c("multiplayer.you")})]}),(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:[c("multiplayer.level",{level:a.level.toString()}),a.isHost&&(0,d.jsx)("span",{className:"ml-2 bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs",children:c("multiplayer.host")})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:c("multiplayer.status")}),(0,d.jsx)("span",{className:"text-green-600",children:c("multiplayer.online")})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:c("multiplayer.contribution")}),(0,d.jsx)("span",{className:"font-medium",children:"0 orders"})]})]})]},a.id))})]}),"chat"===r&&(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"text-xl font-semibold text-orange-800",children:c("multiplayer.teamChat")}),(0,d.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg h-64 overflow-y-auto",children:(0,d.jsx)("div",{className:"text-sm text-gray-500 text-center",children:c("multiplayer.chatPlaceholder")})}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)("input",{type:"text",placeholder:c("multiplayer.typeMessage"),className:"flex-1 p-3 border rounded-lg"}),(0,d.jsx)(g.$,{children:c("common.send")})]})]})]}),(0,d.jsx)("div",{className:"p-4 bg-blue-50 border-t border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("div",{className:"text-sm text-blue-700",children:"cooperative"===i.mode?c("multiplayer.mode.cooperative.description"):c("multiplayer.mode.competitive.description")}),(0,d.jsx)("div",{className:"text-sm text-blue-600",children:c("multiplayer.gameTime",{time:"00:00"})})]})})]})})}function m({onStartSinglePlayer:a,onStartMultiplayer:b,onShowSettings:c,onShowCredits:h,onExit:i}){let{language:j,setLanguage:k,t:l}=(0,f.o)(),[m,n]=(0,e.useState)(!1);return(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-orange-100 via-yellow-50 to-orange-200 flex items-center justify-center relative overflow-hidden",children:[(0,d.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[(0,d.jsx)("div",{className:"absolute top-10 left-10 text-6xl",children:"\uD83E\uDD56"}),(0,d.jsx)("div",{className:"absolute top-20 right-20 text-4xl",children:"\uD83E\uDDC1"}),(0,d.jsx)("div",{className:"absolute bottom-20 left-20 text-5xl",children:"\uD83C\uDF70"}),(0,d.jsx)("div",{className:"absolute bottom-10 right-10 text-3xl",children:"\uD83E\uDD50"}),(0,d.jsx)("div",{className:"absolute top-1/2 left-1/4 text-4xl",children:"\uD83C\uDF6A"}),(0,d.jsx)("div",{className:"absolute top-1/3 right-1/3 text-5xl",children:"\uD83C\uDF82"})]}),(0,d.jsxs)("div",{className:"relative z-10 text-center space-y-8 p-8 max-w-md w-full",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"text-6xl mb-4",children:"\uD83E\uDD56"}),(0,d.jsx)("h1",{className:"text-5xl font-bold text-orange-800 mb-2",children:"Bake It Out"}),(0,d.jsx)("p",{className:"text-lg text-orange-600 font-medium",children:l("game.subtitle","Multiplayer Bakery Management")})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)(g.$,{size:"lg",className:"w-full text-lg py-4 bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600 text-white font-semibold shadow-lg transform hover:scale-105 transition-all duration-200",onClick:a,children:["\uD83C\uDFAE ",l("menu.singlePlayer","Single Player")]}),(0,d.jsxs)(g.$,{size:"lg",className:"w-full text-lg py-4 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold shadow-lg transform hover:scale-105 transition-all duration-200",onClick:b,children:["\uD83D\uDC65 ",l("menu.multiplayer","Multiplayer")]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,d.jsxs)(g.$,{variant:"secondary",size:"md",className:"py-3 bg-white/80 hover:bg-white shadow-md",onClick:c,children:["⚙️ ",l("menu.settings","Settings")]}),(0,d.jsxs)(g.$,{variant:"secondary",size:"md",className:"py-3 bg-white/80 hover:bg-white shadow-md",onClick:()=>n(!m),children:["\uD83C\uDF0D ","en"===j?"English":"Čeština"]})]}),m&&(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-4 space-y-2",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:l("menu.selectLanguage","Select Language")}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,d.jsx)(g.$,{variant:"en"===j?"primary":"secondary",size:"sm",onClick:()=>{k("en"),n(!1)},children:"\uD83C\uDDFA\uD83C\uDDF8 English"}),(0,d.jsx)(g.$,{variant:"cs"===j?"primary":"secondary",size:"sm",onClick:()=>{k("cs"),n(!1)},children:"\uD83C\uDDE8\uD83C\uDDFF Čeština"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,d.jsxs)(g.$,{variant:"secondary",size:"md",className:"py-3 bg-white/80 hover:bg-white shadow-md",onClick:h,children:["ℹ️ ",l("menu.about","About")]}),i&&(0,d.jsxs)(g.$,{variant:"secondary",size:"md",className:"py-3 bg-red-100 hover:bg-red-200 text-red-700 shadow-md",onClick:i,children:["\uD83D\uDEAA ",l("menu.exit","Exit")]})]})]}),(0,d.jsxs)("div",{className:"text-sm text-orange-500 opacity-75",children:["v1.0.0 - ",l("menu.version","Beta Version")]})]})]})}function n({isOpen:a,onClose:b}){let{t:c}=(0,f.o)();return a?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-orange-500 to-yellow-500 p-6 text-white",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h2",{className:"text-2xl font-bold",children:["ℹ️ ",c("credits.title","About Bake It Out")]}),(0,d.jsx)("p",{className:"text-orange-100 text-sm",children:c("credits.subtitle","Game Information & Credits")})]}),(0,d.jsx)(g.$,{variant:"secondary",size:"sm",className:"bg-white/20 hover:bg-white/30 text-white border-white/30",onClick:b,children:"✕"})]})}),(0,d.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[70vh]",children:[(0,d.jsxs)("div",{className:"text-center mb-8",children:[(0,d.jsx)("div",{className:"text-6xl mb-4",children:"\uD83E\uDD56"}),(0,d.jsx)("h3",{className:"text-3xl font-bold text-orange-800 mb-2",children:"Bake It Out"}),(0,d.jsx)("p",{className:"text-lg text-gray-600 mb-4",children:c("credits.description","A multiplayer bakery management game with real-time collaboration and localization support")}),(0,d.jsx)("div",{className:"bg-orange-100 rounded-lg p-4 inline-block",children:(0,d.jsxs)("div",{className:"text-sm text-orange-800",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("strong",{children:[c("credits.version","Version"),":"]})," 1.0.0"]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("strong",{children:[c("credits.release","Release"),":"]})," ",c("credits.releaseDate","December 2024")]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("strong",{children:[c("credits.platform","Platform"),":"]})," ",c("credits.platforms","Windows, macOS, Linux")]})]})})]}),(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsxs)("h4",{className:"text-xl font-semibold text-gray-800 mb-4",children:["\uD83C\uDF1F ",c("credits.features","Key Features")]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,d.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDC65"}),(0,d.jsx)("h5",{className:"font-semibold text-blue-800",children:c("credits.multiplayer","Real-time Multiplayer")}),(0,d.jsx)("p",{className:"text-sm text-blue-600",children:c("credits.multiplayerDesc","Collaborate with friends in real-time bakery management")})]}),(0,d.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,d.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDF0D"}),(0,d.jsx)("h5",{className:"font-semibold text-green-800",children:c("credits.localization","Localization")}),(0,d.jsx)("p",{className:"text-sm text-green-600",children:c("credits.localizationDesc","Full support for English and Czech languages")})]}),(0,d.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[(0,d.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDFC6"}),(0,d.jsx)("h5",{className:"font-semibold text-purple-800",children:c("credits.progression","Progression System")}),(0,d.jsx)("p",{className:"text-sm text-purple-600",children:c("credits.progressionDesc","Achievements, skills, and equipment upgrades")})]}),(0,d.jsxs)("div",{className:"bg-orange-50 rounded-lg p-4",children:[(0,d.jsx)("div",{className:"text-2xl mb-2",children:"\uD83E\uDD16"}),(0,d.jsx)("h5",{className:"font-semibold text-orange-800",children:c("credits.automation","Automation")}),(0,d.jsx)("p",{className:"text-sm text-orange-600",children:c("credits.automationDesc","Advanced automation and efficiency systems")})]})]})]}),(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsxs)("h4",{className:"text-xl font-semibold text-gray-800 mb-4",children:["\uD83D\uDD27 ",c("credits.technology","Technology Stack")]}),(0,d.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{className:"text-gray-700",children:"Frontend:"}),(0,d.jsx)("div",{className:"text-gray-600",children:"Next.js, React, TypeScript"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{className:"text-gray-700",children:"Styling:"}),(0,d.jsx)("div",{className:"text-gray-600",children:"Tailwind CSS"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{className:"text-gray-700",children:"Desktop:"}),(0,d.jsx)("div",{className:"text-gray-600",children:"Electron"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{className:"text-gray-700",children:"Multiplayer:"}),(0,d.jsx)("div",{className:"text-gray-600",children:"Socket.IO"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{className:"text-gray-700",children:"Database:"}),(0,d.jsx)("div",{className:"text-gray-600",children:"Supabase"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{className:"text-gray-700",children:"i18n:"}),(0,d.jsx)("div",{className:"text-gray-600",children:"Custom Context"})]})]})})]}),(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsxs)("h4",{className:"text-xl font-semibold text-gray-800 mb-4",children:["\uD83D\uDC68‍\uD83D\uDCBB ",c("credits.team","Development Team")]}),(0,d.jsx)("div",{className:"bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDFAE"}),(0,d.jsx)("div",{className:"font-semibold text-gray-800",children:c("credits.developedBy","Developed by the Bake It Out Team")}),(0,d.jsx)("div",{className:"text-sm text-gray-600 mt-2",children:c("credits.teamDesc","Built with passion for gaming and baking!")})]})})]}),(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsxs)("h4",{className:"text-xl font-semibold text-gray-800 mb-4",children:["\uD83D\uDE4F ",c("credits.thanks","Special Thanks")]}),(0,d.jsxs)("div",{className:"text-sm text-gray-600 space-y-2",children:[(0,d.jsxs)("p",{children:["• ",c("credits.thanksPlayers","All beta testers and players for their feedback")]}),(0,d.jsxs)("p",{children:["• ",c("credits.thanksTranslators","Czech language translators and cultural consultants")]}),(0,d.jsxs)("p",{children:["• ",c("credits.thanksOpenSource","Open source community for amazing tools and libraries")]}),(0,d.jsxs)("p",{children:["• ",c("credits.thanksBakers","Real bakers who inspired the game mechanics")]})]})]}),(0,d.jsxs)("div",{className:"border-t pt-6",children:[(0,d.jsxs)("h4",{className:"text-lg font-semibold text-gray-800 mb-3",children:["\uD83D\uDCDE ",c("credits.contact","Contact & Support")]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("strong",{className:"text-gray-700",children:[c("credits.website","Website"),":"]}),(0,d.jsx)("div",{className:"text-blue-600",children:"www.bakeitout.game"})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("strong",{className:"text-gray-700",children:[c("credits.support","Support"),":"]}),(0,d.jsx)("div",{className:"text-blue-600",children:"<EMAIL>"})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("strong",{className:"text-gray-700",children:[c("credits.github","GitHub"),":"]}),(0,d.jsx)("div",{className:"text-blue-600",children:"github.com/bakeitout/game"})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("strong",{className:"text-gray-700",children:[c("credits.discord","Discord"),":"]}),(0,d.jsx)("div",{className:"text-blue-600",children:"discord.gg/bakeitout"})]})]})]})]}),(0,d.jsx)("div",{className:"bg-gray-50 px-6 py-4 text-center",children:(0,d.jsx)(g.$,{variant:"primary",onClick:b,className:"bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600",children:c("credits.close","Close")})})]})}):null}var o=c(131),p=c(9587);let q=c.n(p)()(async()=>{},{loadableGenerated:{modules:["app\\page.tsx -> ./game/page"]},ssr:!1});function r(){let{language:a,setLanguage:b,t:c}=(0,f.o)(),{gameState:g}=(0,h.K)(),[j,k]=(0,e.useState)(!1),[p,r]=(0,e.useState)(!1),[s,t]=(0,e.useState)(!1),[u,v]=(0,e.useState)(!1),[w,x]=(0,e.useState)(!1),[y,z]=(0,e.useState)({soundEnabled:!0,musicEnabled:!0,notifications:!0,autoSave:!0});return s?(0,d.jsx)(q,{}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(m,{onStartSinglePlayer:()=>t(!0),onStartMultiplayer:()=>k(!0),onShowSettings:()=>x(!0),onShowCredits:()=>v(!0),onExit:void 0}),(0,d.jsx)(n,{isOpen:u,onClose:()=>v(!1)}),(0,d.jsx)(o.b,{isOpen:w,onClose:()=>x(!1),settings:y,onSettingsChange:a=>{z(a)}}),(0,d.jsx)(i,{isOpen:j,onClose:()=>k(!1)}),(0,d.jsx)(l,{isOpen:p||"playing"===g,onClose:()=>r(!1)})]})}},5511:a=>{"use strict";a.exports=require("crypto")},5591:a=>{"use strict";a.exports=require("https")},5719:(a,b,c)=>{Promise.resolve().then(c.bind(c,1204))},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},6780:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"BailoutToCSR",{enumerable:!0,get:function(){return e}});let d=c(1208);function e(a){let{reason:b,children:c}=a;throw Object.defineProperty(new d.BailoutToCSRError(b),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},7265:a=>{"use strict";a.exports=require("child_process")},7910:a=>{"use strict";a.exports=require("stream")},8354:a=>{"use strict";a.exports=require("util")},9021:a=>{"use strict";a.exports=require("fs")},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:a=>{"use strict";a.exports=require("buffer")},9551:a=>{"use strict";a.exports=require("url")},9587:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return e}});let d=c(4985)._(c(4963));function e(a,b){var c;let e={};"function"==typeof a&&(e.loader=a);let f={...e,...b};return(0,d.default)({...f,modules:null==(c=f.loadableGenerated)?void 0:c.modules})}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},9772:(a,b,c)=>{Promise.resolve().then(c.bind(c,5439))}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[985,976,871,887],()=>b(b.s=346));module.exports=c})();